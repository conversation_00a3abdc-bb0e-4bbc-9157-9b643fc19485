import"./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js";import{I as D}from"./IconButton-DbC-jsk_.js";import{C as q}from"./Clear-By3xiIwg.js";import{D as L}from"./Download-DVtk-Jv3.js";import{E as P}from"./Edit-BpRIf5rU.js";import{U as S}from"./Undo-DCjBnnSO.js";import{I as W}from"./IconButtonWrapper-DrWC4NJv.js";import{D as j}from"./DownloadLink-QIttOhoR.js";const{SvelteComponent:v,check_outros:C,create_component:$,create_slot:z,destroy_component:b,detach:g,flush:k,get_all_dirty_from_scope:A,get_slot_changes:F,group_outros:E,init:G,insert:h,mount_component:p,safe_not_equal:H,space:I,transition_in:c,transition_out:_,update_slot_base:J}=window.__gradio__svelte__internal,{createEventDispatcher:K}=window.__gradio__svelte__internal;function U(r){let e,o;return e=new D({props:{Icon:P,label:r[3]("common.edit")}}),e.$on("click",r[6]),{c(){$(e.$$.fragment)},m(n,i){p(e,n,i),o=!0},p(n,i){const f={};i&8&&(f.label=n[3]("common.edit")),e.$set(f)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){_(e.$$.fragment,n),o=!1},d(n){b(e,n)}}}function B(r){let e,o;return e=new D({props:{Icon:S,label:r[3]("common.undo")}}),e.$on("click",r[7]),{c(){$(e.$$.fragment)},m(n,i){p(e,n,i),o=!0},p(n,i){const f={};i&8&&(f.label=n[3]("common.undo")),e.$set(f)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){_(e.$$.fragment,n),o=!1},d(n){b(e,n)}}}function M(r){let e,o;return e=new j({props:{href:r[2],download:!0,$$slots:{default:[O]},$$scope:{ctx:r}}}),{c(){$(e.$$.fragment)},m(n,i){p(e,n,i),o=!0},p(n,i){const f={};i&4&&(f.href=n[2]),i&520&&(f.$$scope={dirty:i,ctx:n}),e.$set(f)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){_(e.$$.fragment,n),o=!1},d(n){b(e,n)}}}function O(r){let e,o;return e=new D({props:{Icon:L,label:r[3]("common.download")}}),{c(){$(e.$$.fragment)},m(n,i){p(e,n,i),o=!0},p(n,i){const f={};i&8&&(f.label=n[3]("common.download")),e.$set(f)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){_(e.$$.fragment,n),o=!1},d(n){b(e,n)}}}function Q(r){let e,o,n,i,f,d,a=r[0]&&U(r),u=r[1]&&B(r),s=r[2]&&M(r);const w=r[5].default,m=z(w,r,r[9],null);return f=new D({props:{Icon:q,label:r[3]("common.clear")}}),f.$on("click",r[8]),{c(){a&&a.c(),e=I(),u&&u.c(),o=I(),s&&s.c(),n=I(),m&&m.c(),i=I(),$(f.$$.fragment)},m(t,l){a&&a.m(t,l),h(t,e,l),u&&u.m(t,l),h(t,o,l),s&&s.m(t,l),h(t,n,l),m&&m.m(t,l),h(t,i,l),p(f,t,l),d=!0},p(t,l){t[0]?a?(a.p(t,l),l&1&&c(a,1)):(a=U(t),a.c(),c(a,1),a.m(e.parentNode,e)):a&&(E(),_(a,1,1,()=>{a=null}),C()),t[1]?u?(u.p(t,l),l&2&&c(u,1)):(u=B(t),u.c(),c(u,1),u.m(o.parentNode,o)):u&&(E(),_(u,1,1,()=>{u=null}),C()),t[2]?s?(s.p(t,l),l&4&&c(s,1)):(s=M(t),s.c(),c(s,1),s.m(n.parentNode,n)):s&&(E(),_(s,1,1,()=>{s=null}),C()),m&&m.p&&(!d||l&512)&&J(m,w,t,t[9],d?F(w,t[9],l,null):A(t[9]),null);const N={};l&8&&(N.label=t[3]("common.clear")),f.$set(N)},i(t){d||(c(a),c(u),c(s),c(m,t),c(f.$$.fragment,t),d=!0)},o(t){_(a),_(u),_(s),_(m,t),_(f.$$.fragment,t),d=!1},d(t){t&&(g(e),g(o),g(n),g(i)),a&&a.d(t),u&&u.d(t),s&&s.d(t),m&&m.d(t),b(f,t)}}}function R(r){let e,o;return e=new W({props:{$$slots:{default:[Q]},$$scope:{ctx:r}}}),{c(){$(e.$$.fragment)},m(n,i){p(e,n,i),o=!0},p(n,[i]){const f={};i&527&&(f.$$scope={dirty:i,ctx:n}),e.$set(f)},i(n){o||(c(e.$$.fragment,n),o=!0)},o(n){_(e.$$.fragment,n),o=!1},d(n){b(e,n)}}}function T(r,e,o){let{$$slots:n={},$$scope:i}=e,{editable:f=!1}=e,{undoable:d=!1}=e,{download:a=null}=e,{i18n:u}=e;const s=K(),w=()=>s("edit"),m=()=>s("undo"),t=l=>{s("clear"),l.stopPropagation()};return r.$$set=l=>{"editable"in l&&o(0,f=l.editable),"undoable"in l&&o(1,d=l.undoable),"download"in l&&o(2,a=l.download),"i18n"in l&&o(3,u=l.i18n),"$$scope"in l&&o(9,i=l.$$scope)},[f,d,a,u,s,n,w,m,t,i]}class oe extends v{constructor(e){super(),G(this,e,T,R,H,{editable:0,undoable:1,download:2,i18n:3})}get editable(){return this.$$.ctx[0]}set editable(e){this.$$set({editable:e}),k()}get undoable(){return this.$$.ctx[1]}set undoable(e){this.$$set({undoable:e}),k()}get download(){return this.$$.ctx[2]}set download(e){this.$$set({download:e}),k()}get i18n(){return this.$$.ctx[3]}set i18n(e){this.$$set({i18n:e}),k()}}export{oe as M};
//# sourceMappingURL=ModifyUpload-DAmzHHEC.js.map
