import os
import re
import glob
import logging
from pathlib import Path
import gradio as gr

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("MinimalReader")

def get_chapter_files(novel_dir):
    """获取小说目录中的所有章节文件"""
    if not novel_dir or not os.path.exists(novel_dir):
        return []

    # 首先尝试从chapters目录查找
    chapters_dir = os.path.join(novel_dir, "chapters")
    chapter_files = []

    if os.path.exists(chapters_dir):
        chapter_files = glob.glob(os.path.join(chapters_dir, "chapter_*_final.txt"))
        if not chapter_files:
            chapter_files = glob.glob(os.path.join(chapters_dir, "chapter_*.txt"))

    # 如果chapters目录没有文件，从根目录查找
    if not chapter_files:
        chapter_files = glob.glob(os.path.join(novel_dir, "chapter_*_final.txt"))
        if not chapter_files:
            chapter_files = glob.glob(os.path.join(novel_dir, "chapter_*.txt"))

    # 排序章节文件
    chapter_files.sort(key=lambda x: int(re.search(r'chapter_(\d+)', os.path.basename(x)).group(1)))

    return chapter_files

def get_chapter_title(chapter_file):
    """从章节文件名中提取章节标题"""
    match = re.search(r'chapter_(\d+)', os.path.basename(chapter_file))
    if match:
        chapter_num = match.group(1)
        return f"第{chapter_num}章"
    return os.path.basename(chapter_file)

def get_illustration_for_chapter(novel_dir, chapter_num):
    """获取章节对应的插图"""
    if not novel_dir or not os.path.exists(novel_dir):
        return None
    
    # 查找插图目录
    illustrations_dir = os.path.join(novel_dir, "illustrations")
    if not os.path.exists(illustrations_dir):
        return None
    
    # 查找章节对应的插图
    illustration_files = [
        os.path.join(illustrations_dir, f) 
        for f in os.listdir(illustrations_dir) 
        if f.startswith(f"chapter_{chapter_num}") and (f.endswith(".png") or f.endswith(".jpg"))
    ]
    
    # 如果找不到精确匹配的插图，返回任何可能的插图
    if not illustration_files:
        illustration_files = [
            os.path.join(illustrations_dir, f) 
            for f in os.listdir(illustrations_dir) 
            if f.endswith(".png") or f.endswith(".jpg")
        ]
    
    return illustration_files[0] if illustration_files else None

def read_chapter_content(chapter_file):
    """读取章节内容"""
    try:
        with open(chapter_file, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.error(f"读取章节内容时出错: {e}")
        return f"读取章节内容时出错: {e}"

def get_novel_info(novel_dir):
    """获取小说信息"""
    if not novel_dir or not os.path.exists(novel_dir):
        return {"title": "未知小说", "author": "未知作者", "genre": "未知类型", "style": "未知风格"}
    
    # 尝试从novel_info.json读取小说信息
    import json
    novel_info_file = os.path.join(novel_dir, "novel_info.json")
    if os.path.exists(novel_info_file):
        try:
            with open(novel_info_file, "r", encoding="utf-8") as f:
                novel_info = json.load(f)
            return {
                "title": novel_info.get("title", "未知小说"),
                "author": novel_info.get("author", "AI作家"),
                "genre": novel_info.get("genre", "未知类型"),
                "style": novel_info.get("style", "未知风格")
            }
        except Exception as e:
            logger.error(f"读取小说信息时出错: {e}")
    
    # 如果无法从novel_info.json读取，尝试从目录名称提取
    novel_name = os.path.basename(novel_dir)
    return {
        "title": novel_name,
        "author": "AI作家",
        "genre": "未知类型",
        "style": "未知风格"
    }

def get_subdirectories(parent_dir):
    """获取指定目录下的所有子目录"""
    if not parent_dir or not os.path.exists(parent_dir):
        return []
    
    try:
        subdirs = [d for d in os.listdir(parent_dir) if os.path.isdir(os.path.join(parent_dir, d))]
        return subdirs
    except Exception as e:
        logger.error(f"获取子目录时出错: {e}")
        return []

def launch_reader(novel_dir=None):
    """启动极简阅读器"""
    # 确保output目录存在
    output_dir = "output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 如果没有提供小说目录，默认使用output目录
    if not novel_dir:
        novel_dir = output_dir
    
    with gr.Blocks(css=custom_css()) as reader:
        gr.Markdown("# 📚 小说阅读器 (极简版)")
        
        with gr.Row():
            with gr.Column(scale=1):
                # 目录浏览器
                parent_dir = gr.Textbox(label="父目录", value=os.path.abspath(output_dir))
                
                # 子目录列表
                subdirs = get_subdirectories(parent_dir.value)
                subdir_dropdown = gr.Dropdown(
                    label="选择小说目录", 
                    choices=subdirs,
                    interactive=True
                )
                
                # 加载按钮
                load_novel_btn = gr.Button("加载选中的小说")
                
                # 小说信息
                novel_info_md = gr.Markdown("### 小说信息\n\n请选择小说目录")
                
                # 章节列表
                chapter_list = gr.Dropdown(label="选择章节", choices=[], interactive=True)
            
            with gr.Column(scale=2):
                # 章节标题
                chapter_title_md = gr.Markdown("## 请选择章节")
                
                # 导航按钮
                with gr.Row():
                    prev_chapter_button = gr.Button("上一章")
                    next_chapter_button = gr.Button("下一章")
                
                # 插图
                chapter_illustration = gr.Image(label="章节插图", type="filepath", interactive=False)
                
                # 章节内容
                chapter_content = gr.Textbox(
                    label="章节内容", 
                    lines=25, 
                    max_lines=50, 
                    interactive=False
                )
        
        # 刷新父目录函数
        def refresh_parent_directory(parent_dir):
            subdirs = get_subdirectories(parent_dir)
            return gr.Dropdown(choices=subdirs)
        
        # 加载小说函数
        def load_novel(parent_dir, subdir):
            if not subdir:
                return [], "### 小说信息\n\n请选择小说目录"
            
            # 构建完整的小说目录路径
            novel_dir = os.path.join(parent_dir, subdir)
            if not os.path.exists(novel_dir):
                return [], f"### 小说信息\n\n错误: 目录不存在: {novel_dir}"
            
            # 获取章节文件
            chapter_files = get_chapter_files(novel_dir)
            chapter_choices = [(get_chapter_title(f), f) for f in chapter_files]
            
            # 获取小说信息
            novel_info = get_novel_info(novel_dir)
            info_md = f"### 小说信息\n\n**标题**: {novel_info['title']}\n\n**作者**: {novel_info['author']}\n\n**类型**: {novel_info['genre']}\n\n**风格**: {novel_info['style']}\n\n**章节数**: {len(chapter_files)}"
            
            return chapter_choices, info_md
        
        # 加载章节内容函数
        def load_chapter_content(chapter_file):
            if not chapter_file or not os.path.exists(chapter_file):
                return "## 请选择章节", "请选择有效的章节", None
            
            chapter_title = get_chapter_title(chapter_file)
            chapter_content = read_chapter_content(chapter_file)
            
            # 提取章节编号
            match = re.search(r'chapter_(\d+)', os.path.basename(chapter_file))
            chapter_num = match.group(1) if match else "0"
            
            # 获取插图
            novel_dir = os.path.dirname(chapter_file)
            illustration = get_illustration_for_chapter(novel_dir, chapter_num)
            
            return f"## {chapter_title}", chapter_content, illustration
        
        # 导航函数
        def navigate_chapter(direction, current_chapter, chapter_choices):
            if not chapter_choices:
                return current_chapter, "## 请选择章节", "请先选择小说目录", None
            
            # 找到当前章节的索引
            current_index = -1
            for i, (_, file) in enumerate(chapter_choices):
                if file == current_chapter:
                    current_index = i
                    break
            
            # 计算新的索引
            if direction == "next":
                new_index = min(current_index + 1, len(chapter_choices) - 1)
            else:  # prev
                new_index = max(current_index - 1, 0)
            
            # 如果没有找到当前章节或者没有变化，返回第一章
            if current_index == -1 or new_index == current_index:
                if direction == "next" and current_index == -1:
                    new_index = 0
                elif direction == "prev" and current_index == -1:
                    new_index = 0
                else:
                    return current_chapter, "## 已经是最" + ("后" if direction == "next" else "前") + "一章", chapter_content.value, chapter_illustration.value
            
            # 加载新章节
            new_chapter_file = chapter_choices[new_index][1]
            chapter_title, content, illustration = load_chapter_content(new_chapter_file)
            
            return new_chapter_file, chapter_title, content, illustration
        
        # 绑定事件
        parent_dir.change(
            fn=refresh_parent_directory,
            inputs=[parent_dir],
            outputs=[subdir_dropdown]
        )
        
        load_novel_btn.click(
            fn=load_novel,
            inputs=[parent_dir, subdir_dropdown],
            outputs=[chapter_list, novel_info_md]
        )
        
        chapter_list.change(
            fn=load_chapter_content,
            inputs=[chapter_list],
            outputs=[chapter_title_md, chapter_content, chapter_illustration]
        )
        
        prev_chapter_button.click(
            fn=lambda ch, choices: navigate_chapter("prev", ch, choices),
            inputs=[chapter_list, chapter_list],
            outputs=[chapter_list, chapter_title_md, chapter_content, chapter_illustration]
        )
        
        next_chapter_button.click(
            fn=lambda ch, choices: navigate_chapter("next", ch, choices),
            inputs=[chapter_list, chapter_list],
            outputs=[chapter_list, chapter_title_md, chapter_content, chapter_illustration]
        )
        
        # 如果提供了小说目录，自动加载章节列表
        if novel_dir and os.path.exists(novel_dir) and novel_dir != output_dir:
            # 获取相对路径
            rel_path = os.path.relpath(novel_dir, output_dir)
            if rel_path == ".":
                # 如果是output目录本身，不自动加载
                pass
            elif os.path.dirname(rel_path) == "":
                # 如果是output的直接子目录
                reader.load(
                    fn=lambda: (rel_path, get_subdirectories(output_dir)),
                    inputs=None,
                    outputs=[subdir_dropdown, subdir_dropdown]
                ).then(
                    fn=load_novel,
                    inputs=[parent_dir, subdir_dropdown],
                    outputs=[chapter_list, novel_info_md]
                )
    
    # 启动阅读器，不使用队列
    reader.launch(server_name="127.0.0.1", server_port=4556, share=False, prevent_thread_lock=True)
    
    # 返回阅读器URL
    return "http://127.0.0.1:4556"

def custom_css():
    """自定义CSS样式"""
    return """
    /* 整体样式 */
    body {
        font-family: 'Microsoft YaHei', sans-serif;
        background-color: #f8f9fa;
    }
    
    /* 标题样式 */
    h1, h2, h3 {
        font-family: 'SimSun', serif;
        color: #2c3e50;
    }
    
    /* 章节内容样式 */
    textarea {
        font-family: 'SimSun', serif !important;
        font-size: 18px !important;
        line-height: 1.8 !important;
        padding: 20px !important;
    }
    
    /* 按钮样式 */
    button {
        border-radius: 4px !important;
        font-weight: 500 !important;
    }
    """

if __name__ == "__main__":
    # 如果直接运行此脚本，启动阅读器
    launch_reader()
