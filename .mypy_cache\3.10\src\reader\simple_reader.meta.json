{"data_mtime": 1745128539, "dep_lines": [1, 2, 3, 4, 5, 6, 87, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "re", "glob", "logging", "gradio", "pathlib", "json", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "gradio.blocks", "gradio.blocks_events", "gradio.component_meta", "gradio.components", "gradio.components.base", "gradio.components.button", "gradio.components.dropdown", "gradio.components.image", "gradio.components.markdown", "gradio.components.radio", "gradio.components.textbox", "gradio.components.timer", "gradio.layouts", "gradio.layouts.column", "gradio.layouts.group", "gradio.layouts.row", "gradio.themes", "gradio.themes.base", "io", "json.decoder", "numpy", "typing", "typing_extensions"], "hash": "9ce58678a1cb8ecee512e999a44cead913ca8bf2", "id": "src.reader.simple_reader", "ignore_all": false, "interface_hash": "ac05005084162e320183547aeed50113425911e9", "mtime": 1751379434, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\storymc\\src\\reader\\simple_reader.py", "plugin_data": null, "size": 28585, "suppressed": [], "version_id": "1.15.0"}