{"version": 3, "file": "Login-phx6DJWM.js", "sources": ["../../../../js/core/src/Login.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport Form from \"@gradio/form\";\n\timport { BaseTextbox as Textbox } from \"@gradio/textbox\";\n\timport { BaseButton } from \"@gradio/button\";\n\timport Column from \"@gradio/column\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { _ } from \"svelte-i18n\";\n\texport let root: string;\n\texport let auth_message: string | null;\n\texport let app_mode: boolean;\n\texport let space_id: string | null;\n\n\tlet username = \"\";\n\tlet password = \"\";\n\tlet incorrect_credentials = false;\n\n\tconst submit = async (): Promise<void> => {\n\t\tconst formData = new FormData();\n\t\tformData.append(\"username\", username);\n\t\tformData.append(\"password\", password);\n\n\t\tlet response = await fetch(root + \"/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: formData\n\t\t});\n\t\tif (response.status === 400) {\n\t\t\tincorrect_credentials = true;\n\t\t\tusername = \"\";\n\t\t\tpassword = \"\";\n\t\t} else if (response.status == 200) {\n\t\t\tlocation.reload();\n\t\t}\n\t};\n</script>\n\n<div class=\"wrap\" class:min-h-screen={app_mode}>\n\t<Column variant=\"panel\" min_width={480}>\n\t\t<h2>{$_(\"login.login\")}</h2>\n\t\t{#if auth_message}\n\t\t\t<p class=\"auth\">{@html auth_message}</p>\n\t\t{/if}\n\t\t{#if space_id}\n\t\t\t<p class=\"auth\">\n\t\t\t\t{$_(\"login.enable_cookies\")}\n\t\t\t</p>\n\t\t{/if}\n\t\t{#if incorrect_credentials}\n\t\t\t<p class=\"creds\">{$_(\"login.incorrect_credentials\")}</p>\n\t\t{/if}\n\t\t<Form>\n\t\t\t<Block>\n\t\t\t\t<Textbox\n\t\t\t\t\t{root}\n\t\t\t\t\tlabel={$_(\"login.username\")}\n\t\t\t\t\tlines={1}\n\t\t\t\t\tshow_label={true}\n\t\t\t\t\tmax_lines={1}\n\t\t\t\t\ton:submit={submit}\n\t\t\t\t\tbind:value={username}\n\t\t\t\t/>\n\t\t\t</Block>\n\n\t\t\t<Block>\n\t\t\t\t<Textbox\n\t\t\t\t\t{root}\n\t\t\t\t\tlabel={$_(\"login.password\")}\n\t\t\t\t\tlines={1}\n\t\t\t\t\tshow_label={true}\n\t\t\t\t\tmax_lines={1}\n\t\t\t\t\ttype=\"password\"\n\t\t\t\t\ton:submit={submit}\n\t\t\t\t\tbind:value={password}\n\t\t\t\t/>\n\t\t\t</Block>\n\t\t</Form>\n\n\t\t<BaseButton size=\"lg\" variant=\"primary\" on:click={submit}\n\t\t\t>{$_(\"login.login\")}</BaseButton\n\t\t>\n\t</Column>\n</div>\n\n<style>\n\t.wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-top: var(--size-3);\n\t\tbackground: var(--background-fill-primary);\n\t\twidth: var(--size-full);\n\t}\n\n\th2 {\n\t\tmargin-bottom: var(--size-3);\n\t\tcolor: var(--body-text-color);\n\t\tfont-weight: var(--section-header-text-weight);\n\t\tfont-size: var(--text-xl);\n\t}\n\n\t.auth {\n\t\tmargin-top: var(--size-1);\n\t\tmargin-bottom: var(--size-1);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.creds {\n\t\tmargin-top: var(--size-4);\n\t\tmargin-bottom: var(--size-4);\n\t\tcolor: var(--error-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t}\n</style>\n"], "names": ["insert", "target", "p", "anchor", "ctx", "t_value", "dirty", "set_data", "t", "textbox_changes", "t0_value", "create_if_block_2", "create_if_block_1", "create_if_block", "h2", "current", "t0", "div", "root", "$$props", "auth_message", "app_mode", "space_id", "username", "password", "incorrect_credentials", "submit", "formData", "response", "value"], "mappings": "0gDAuCGA,EAAuCC,EAAAC,EAAAC,CAAA,cAAhBC,EAAY,CAAA,4BAAZA,EAAY,CAAA,uCAIjCC,EAAAD,KAAG,sBAAsB,EAAA,2EAD3BJ,EAEGC,EAAAC,EAAAC,CAAA,iBADDG,EAAA,KAAAD,KAAAA,EAAAD,KAAG,sBAAsB,EAAA,KAAAG,EAAAC,EAAAH,CAAA,sCAITA,EAAAD,KAAG,6BAA6B,EAAA,4EAAlDJ,EAAuDC,EAAAC,EAAAC,CAAA,iBAArCG,EAAA,KAAAD,KAAAA,EAAAD,KAAG,6BAA6B,EAAA,KAAAG,EAAAC,EAAAH,CAAA,iFAMzC,MAAAD,KAAG,gBAAgB,QACnB,aACK,aACD,UAECA,EAAQ,CAAA,IAAA,iBAARA,EAAQ,CAAA,gEADTA,EAAM,CAAA,CAAA,oFAJVE,EAAA,MAAAG,EAAA,MAAAL,KAAG,gBAAgB,2BAKdA,EAAQ,CAAA,yKAOb,MAAAA,KAAG,gBAAgB,QACnB,aACK,aACD,0BAGCA,EAAQ,CAAA,IAAA,iBAARA,EAAQ,CAAA,gEADTA,EAAM,CAAA,CAAA,oFALVE,EAAA,MAAAG,EAAA,MAAAL,KAAG,gBAAgB,2BAMdA,EAAQ,CAAA,8lBAMpB,IAAAC,EAAAD,KAAG,aAAa,EAAA,gDAAhBE,EAAA,KAAAD,KAAAA,EAAAD,KAAG,aAAa,EAAA,KAAAG,EAAA,EAAAF,CAAA,uCAxCdK,EAAAN,KAAG,aAAa,EAAA,uBAChBA,EAAY,CAAA,GAAAO,EAAAP,CAAA,IAGZA,EAAQ,CAAA,GAAAQ,EAAAR,CAAA,IAKRA,EAAqB,CAAA,GAAAS,EAAAT,CAAA,qKA8BwBA,EAAM,CAAA,CAAA,yJAvCxDJ,EAA2BC,EAAAa,EAAAX,CAAA,0HAAtB,CAAAY,GAAAT,EAAA,MAAAI,KAAAA,EAAAN,KAAG,aAAa,EAAA,KAAAG,EAAAS,EAAAN,CAAA,EAChBN,EAAY,CAAA,mEAGZA,EAAQ,CAAA,mEAKRA,EAAqB,CAAA,6bAVQ,qIADEA,EAAQ,CAAA,CAAA,UAA9CJ,EA6CKC,EAAAgB,EAAAd,CAAA,oHA7CiCC,EAAQ,CAAA,CAAA,oIA5BlC,GAAA,CAAA,KAAAc,CAAA,EAAAC,EACA,CAAA,aAAAC,CAAA,EAAAD,EACA,CAAA,SAAAE,CAAA,EAAAF,EACA,CAAA,SAAAG,CAAA,EAAAH,EAEPI,EAAW,GACXC,EAAW,GACXC,EAAwB,GAEtB,MAAAC,EAAA,SAAA,OACCC,EAAe,IAAA,SACrBA,EAAS,OAAO,WAAYJ,CAAQ,EACpCI,EAAS,OAAO,WAAYH,CAAQ,EAEhC,IAAAI,EAAA,MAAiB,MAAMV,EAAO,SACjC,CAAA,OAAQ,OACR,KAAMS,CAAA,CAAA,EAEHC,EAAS,SAAW,SACvBH,EAAwB,EAAA,MACxBF,EAAW,EAAA,MACXC,EAAW,EAAA,GACDI,EAAS,QAAU,KAC7B,SAAS,OAAA,iBA4BKL,EAAQM,uBAaRL,EAAQK"}