"""
插图生成模块
用于生成小说插图
"""

import os
import json
import base64
import requests
from typing import Dict, Any, List, Optional, Union, Tuple

# 导入模型配置
try:
    from src.core.model_config import AI_MODEL_CONFIG
except ImportError:
    try:
        from core.model_config import AI_MODEL_CONFIG
    except ImportError:
        # 创建一个简单的配置替代品
        AI_MODEL_CONFIG = {
            "models": {},
            "preset_models": {
                "image_models": ["cogview-3-flash"]
            }
        }

class IllustrationGenerator:
    """插图生成器类"""

    def __init__(self, model_name: str = None, api_key: str = None):
        """
        初始化插图生成器

        参数:
            model_name: 模型名称，默认使用配置中的默认图像模型
            api_key: API密钥，默认使用模型配置中的API密钥
        """
        # 获取默认图像模型
        self.model_name = model_name or AI_MODEL_CONFIG.get("preset_models", {}).get("image_models", ["cogview-3-flash"])[0]

        # 获取模型配置
        self.model_config = AI_MODEL_CONFIG.get("models", {}).get(self.model_name, {})

        # 设置API密钥
        self.api_key = api_key or self.model_config.get("api_key", "")

        # 设置API基础URL
        self.base_url = self.model_config.get("base_url", "")

        # 设置API类型
        self.api_type = self.model_config.get("api_type", "")

        # 支持的图像尺寸
        self.image_sizes = self.model_config.get("image_sizes", ["512x512", "768x768", "1024x1024"])

        print(f"初始化插图生成器: {self.model_name} ({self.api_type})")

    def generate_illustration(self, prompt: str, negative_prompt: str = "", size: str = "1024x1024",
                             output_path: str = None) -> Tuple[bool, str]:
        """
        生成插图

        参数:
            prompt: 生成提示词
            negative_prompt: 负面提示词，指定不希望出现的内容
            size: 图像尺寸，例如"1024x1024"
            output_path: 输出路径，如果不指定则返回图像数据

        返回:
            (成功标志, 结果信息)
            如果成功且指定了output_path，则结果信息为保存路径
            如果成功且未指定output_path，则结果信息为base64编码的图像数据
            如果失败，则结果信息为错误信息
        """
        # 添加负面提示词，禁止生成人物
        if not negative_prompt:
            negative_prompt = "人物, 人脸, 人像, 人体, 人类, 手, 脚, 手指, 人形, 人影, 人群, 人物特写, 人物肖像"
        else:
            negative_prompt += ", 人物, 人脸, 人像, 人体, 人类, 手, 脚, 手指, 人形, 人影, 人群, 人物特写, 人物肖像"

        # 检查图像尺寸是否支持
        if size not in self.image_sizes:
            size = self.image_sizes[0]
            print(f"警告: 不支持的图像尺寸，已使用默认尺寸 {size}")

        # 根据API类型选择不同的生成方法
        if self.api_type == "cogview":
            return self._generate_with_cogview(prompt, negative_prompt, size, output_path)
        elif self.api_type == "siliconflow" and self.model_config.get("model_type") == "kolors":
            return self._generate_with_kolors(prompt, negative_prompt, size, output_path)
        elif self.api_type == "stability":
            return self._generate_with_stability(prompt, negative_prompt, size, output_path)
        else:
            return False, f"不支持的API类型: {self.api_type}"

    def _generate_with_cogview(self, prompt: str, negative_prompt: str, size: str, output_path: str) -> Tuple[bool, str]:
        """使用CogView生成图像"""
        try:
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # 构建请求体
            data = {
                "model": "cogview-3-flash",
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "size": size,
                "n": 1,
                "response_format": "b64_json"
            }

            # 发送请求
            response = requests.post(
                f"{self.base_url}/images/generations",
                headers=headers,
                json=data,
                timeout=60
            )

            # 检查响应
            if response.status_code != 200:
                return False, f"API请求失败: {response.status_code} - {response.text}"

            # 解析响应
            result = response.json()

            # 获取图像数据
            if "data" in result and len(result["data"]) > 0:
                image_data = result["data"][0].get("b64_json", "")

                # 如果指定了输出路径，保存图像
                if output_path:
                    # 确保目录存在
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)

                    # 保存图像
                    with open(output_path, "wb") as f:
                        f.write(base64.b64decode(image_data))

                    return True, output_path
                else:
                    return True, image_data
            else:
                return False, "API响应中没有图像数据"
        except Exception as e:
            return False, f"生成图像时出错: {e}"

    def _generate_with_kolors(self, prompt: str, negative_prompt: str, size: str, output_path: str) -> Tuple[bool, str]:
        """使用Kolors生成图像"""
        try:
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # 解析尺寸
            width, height = map(int, size.split("x"))

            # 构建请求体
            data = {
                "model": "Kwai-Kolors/Kolors",
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "width": width,
                "height": height,
                "n": 1,
                "response_format": "b64_json"
            }

            # 发送请求
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=60
            )

            # 检查响应
            if response.status_code != 200:
                return False, f"API请求失败: {response.status_code} - {response.text}"

            # 解析响应
            result = response.json()

            # 获取图像数据
            if "data" in result and len(result["data"]) > 0:
                image_data = result["data"][0].get("b64_json", "")

                # 如果指定了输出路径，保存图像
                if output_path:
                    # 确保目录存在
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)

                    # 保存图像
                    with open(output_path, "wb") as f:
                        f.write(base64.b64decode(image_data))

                    return True, output_path
                else:
                    return True, image_data
            else:
                return False, "API响应中没有图像数据"
        except Exception as e:
            return False, f"生成图像时出错: {e}"

    def _generate_with_stability(self, prompt: str, negative_prompt: str, size: str, output_path: str) -> Tuple[bool, str]:
        """使用Stability AI生成图像"""
        try:
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # 解析尺寸
            width, height = map(int, size.split("x"))

            # 获取模型名称，从self.model_name中提取
            engine_id = self.model_name.replace("stable-diffusion-", "")

            # 构建请求体
            data = {
                "text_prompts": [
                    {
                        "text": prompt,
                        "weight": 1.0
                    },
                    {
                        "text": negative_prompt,
                        "weight": -1.0
                    }
                ],
                "cfg_scale": 7.0,
                "height": height,
                "width": width,
                "samples": 1,
                "steps": 30
            }

            # 发送请求
            response = requests.post(
                f"{self.base_url}/generation/{engine_id}/text-to-image",
                headers=headers,
                json=data,
                timeout=60
            )

            # 检查响应
            if response.status_code != 200:
                return False, f"API请求失败: {response.status_code} - {response.text}"

            # 解析响应
            result = response.json()

            # 获取图像数据
            if "artifacts" in result and len(result["artifacts"]) > 0:
                image_data = result["artifacts"][0].get("base64", "")

                # 如果指定了输出路径，保存图像
                if output_path:
                    # 确保目录存在
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)

                    # 保存图像
                    with open(output_path, "wb") as f:
                        f.write(base64.b64decode(image_data))

                    return True, output_path
                else:
                    return True, image_data
            else:
                return False, "API响应中没有图像数据"
        except Exception as e:
            return False, f"生成图像时出错: {e}"

def generate_illustration(scene_description: str, style: str = "写实风格",
                         model_name: str = None, output_path: str = None) -> Tuple[bool, str]:
    """
    生成小说插图的便捷函数

    参数:
        scene_description: 场景描述
        style: 风格描述
        model_name: 模型名称
        output_path: 输出路径

    返回:
        (成功标志, 结果信息)
    """
    # 构建提示词
    prompt = f"中国风插画，{style}，{scene_description}，高清细节，无人物，无人形，自然风景"

    # 负面提示词
    negative_prompt = "人物, 人脸, 人像, 人体, 人类, 手, 脚, 手指, 人形, 人影, 人群, 人物特写, 人物肖像"

    # 创建生成器
    generator = IllustrationGenerator(model_name)

    # 生成插图
    return generator.generate_illustration(prompt, negative_prompt, output_path=output_path)

# 测试代码
if __name__ == "__main__":
    # 测试生成插图
    success, result = generate_illustration(
        "一座古老的山间寺庙，晨雾缭绕，阳光透过云层洒下金色光芒",
        "水墨国画风格",
        output_path="test_illustration.png"
    )

    if success:
        print(f"插图生成成功: {result}")
    else:
        print(f"插图生成失败: {result}")
