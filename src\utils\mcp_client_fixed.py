"""
MCP协议客户端
用于多智能体协作通信的客户端
"""

import os
import json
import time
import logging
import re
from typing import Dict, Any, Optional, List, Union, Tuple
import traceback

# 导入配置
try:
    from src.core.model_config import AI_MODEL_CONFIG, MCP_CONFIG
except ImportError:
    # 如果导入失败，创建默认配置
    AI_MODEL_CONFIG = {}
    MCP_CONFIG = {
        "enabled": True,
        "timeout": 30,  # seconds
        "retry_count": 3,
        "retry_delay": 2,  # seconds
        "log_requests": True,
        "log_responses": True,
        "log_directory": "logs/mcp"
    }

# 创建日志目录
os.makedirs(MCP_CONFIG.get("log_directory", "logs/mcp"), exist_ok=True)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(MCP_CONFIG.get("log_directory", "logs/mcp"), "mcp.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("mcp_client")

class MCPClient:
    """MCP协议客户端，用于智能体之间的通信"""

    def __init__(self, config=None):
        """初始化MCP客户端"""
        self.config = config or MCP_CONFIG
        self.session_id = f"novel_session_{int(time.time())}"

        # 创建日志目录
        os.makedirs(self.config.get("log_directory", "logs/mcp"), exist_ok=True)

        logger.info(f"初始化MCP客户端，会话ID: {self.session_id}")

    def call_llm(self,
                model: str,
                prompt: str,
                system_message: Optional[str] = None,
                temperature: Optional[float] = None,
                max_tokens: Optional[int] = None) -> str:
        """
        调用语言模型

        参数:
            model: 模型名称
            prompt: 提示词
            system_message: 系统消息
            temperature: 温度参数
            max_tokens: 最大生成token数

        返回:
            模型生成的内容
        """
        logger.info(f"调用LLM模型 {model}，提示词: {prompt[:50]}...")

        # 记录请求
        if self.config.get("log_requests", True):
            self._log_request(model, prompt, system_message)

        try:
            # 根据模型类型选择不同的API调用方式
            model_config = self._get_model_config(model)
            if not model_config:
                logger.warning(f"未找到模型配置: {model}，使用默认配置")
                model_config = {}

            api_type = model_config.get("api_type", "")

            # 设置参数
            params = {
                "temperature": temperature or model_config.get("temperature", 0.7),
                "max_tokens": max_tokens or model_config.get("max_tokens", 4096)
            }

            # 根据API类型调用不同的模型
            if api_type == "zhipu":
                # 调用智谱AI模型
                from src.llm.zhipu_api import call_zhipu_api
                response = call_zhipu_api(model, prompt, system_message, **params)
            elif api_type == "siliconflow":
                # 调用硅基流动模型
                from src.llm.siliconflow_api import call_siliconflow_api
                response = call_siliconflow_api(model, prompt, system_message, **params)
            elif api_type == "openrouter":
                # 调用OpenRouter模型
                from src.llm.openrouter_api import call_openrouter_api
                response = call_openrouter_api(model, prompt, system_message, **params)
            elif api_type == "ollama":
                # 调用Ollama本地模型
                from src.llm.ollama_api import call_ollama_api
                response = call_ollama_api(model, prompt, system_message, **params)
            else:
                # 默认使用智谱AI模型
                from src.llm.zhipu_api import call_zhipu_api
                response = call_zhipu_api(model, prompt, system_message, **params)

            # 记录响应
            if self.config.get("log_responses", True):
                self._log_response(model, response)

            return response

        except Exception as e:
            error_message = f"调用LLM模型 {model} 失败: {e}"
            logger.error(error_message)
            logger.error(traceback.format_exc())
            return f"错误: {error_message}"

    def call_agent(self, agent_name: str, message: Dict[str, Any]) -> Any:
        """
        调用智能体

        参数:
            agent_name: 智能体名称
            message: 消息内容

        返回:
            智能体的响应
        """
        logger.info(f"调用智能体 {agent_name}...")

        # 记录请求
        if self.config.get("log_requests", True):
            self._log_request("agent", agent_name, message)

        try:
            # 获取智能体
            try:
                from src.core.agent import AGENT_REGISTRY
                agent = AGENT_REGISTRY.get_agent(agent_name)
            except ImportError:
                return {"error": f"无法导入智能体注册表"}

            if not agent:
                error_message = f"未找到智能体: {agent_name}"
                logger.error(error_message)
                return {"error": error_message}

            # 处理消息
            response = agent.handle_message(message)

            # 记录响应
            if self.config.get("log_responses", True):
                self._log_response("agent", response)

            return response

        except Exception as e:
            error_message = f"调用智能体 {agent_name} 失败: {e}"
            logger.error(error_message)
            logger.error(traceback.format_exc())
            return {"error": error_message}

    def generate_illustration(self,
                             title: str,
                             scene_description: str,
                             style: str = "写实风格",
                             output_path: Optional[str] = None,
                             model_name: Optional[str] = None) -> Tuple[bool, str]:
        """
        生成插图

        参数:
            title: 小说标题
            scene_description: 场景描述
            style: 插图风格
            output_path: 输出路径
            model_name: 模型名称

        返回:
            (成功标志, 结果信息)
        """
        logger.info(f"生成插图: {title}...")

        # 记录请求
        if self.config.get("log_requests", True):
            self._log_request("illustration", title, scene_description)

        try:
            # 调用插图生成函数
            try:
                from src.illustration import generate_novel_illustration
                success, result = generate_novel_illustration(
                    title=title,
                    scene=scene_description,
                    style=style,
                    output_path=output_path,
                    model_name=model_name
                )
            except ImportError:
                return False, "无法导入插图生成模块"

            # 记录响应
            if self.config.get("log_responses", True):
                self._log_response("illustration", result)

            return success, result

        except Exception as e:
            error_message = f"生成插图失败: {e}"
            logger.error(error_message)
            logger.error(traceback.format_exc())
            return False, error_message

    def _get_model_config(self, model_name: str) -> Dict[str, Any]:
        """获取模型配置"""
        models = AI_MODEL_CONFIG.get("models", {})
        return models.get(model_name, {})

    def _log_request(self, target_type: str, target_name: str, content: Any = None):
        """记录请求"""
        log_file = os.path.join(self.config.get("log_directory", "logs/mcp"), f"requests_{self.session_id}.log")

        try:
            with open(log_file, "a", encoding="utf-8") as f:
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"[{timestamp}] REQUEST: {target_type} - {target_name}\n")
                if content:
                    if isinstance(content, str):
                        f.write(f"Content: {content[:200]}...\n")
                    else:
                        f.write(f"Content: {json.dumps(content, ensure_ascii=False)[:200]}...\n")
                f.write("-" * 50 + "\n")
        except Exception as e:
            logger.error(f"记录请求失败: {e}")

    def _log_response(self, target_type: str, content: Any = None):
        """记录响应"""
        log_file = os.path.join(self.config.get("log_directory", "logs/mcp"), f"responses_{self.session_id}.log")

        try:
            with open(log_file, "a", encoding="utf-8") as f:
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"[{timestamp}] RESPONSE: {target_type}\n")
                if content:
                    if isinstance(content, str):
                        f.write(f"Content: {content[:200]}...\n")
                    else:
                        f.write(f"Content: {json.dumps(content, ensure_ascii=False)[:200]}...\n")
                f.write("-" * 50 + "\n")
        except Exception as e:
            logger.error(f"记录响应失败: {e}")

# 创建全局MCP客户端实例
mcp_client = MCPClient()

def get_mcp_client() -> MCPClient:
    """获取MCP客户端实例"""
    return mcp_client

# 便捷函数
def call_llm(model: str, prompt: str, system_message: Optional[str] = None, **kwargs) -> str:
    """调用语言模型的便捷函数"""
    # 尝试使用LangChain集成
    try:
        # 导入LangChain集成模块
        from src.utils.langchain_integration import call_llm as langchain_call_llm
        logger.info("使用LangChain集成调用LLM")
        return langchain_call_llm(model, prompt, system_message, **kwargs)
    except ImportError:
        # 如果LangChain集成不可用，回退到MCP客户端
        logger.info("LangChain集成不可用，回退到MCP客户端")
        try:
            response = mcp_client.call_llm(model, prompt, system_message, **kwargs)
            
            # 检查响应是否有效
            if response.startswith("错误:") or len(response.strip()) < 100:
                raise Exception(f"MCP响应无效: {response[:50]}...")
            
            return response
        except Exception as e:
            # 如果MCP客户端也失败，生成模拟内容
            logger.error(f"调用LLM失败: {e}")
            
            # 分析提示词，提取章节信息
            chapter_num = 1
            chapter_match = re.search(r'第(\d+)章', prompt)
            if chapter_match:
                try:
                    chapter_num = int(chapter_match.group(1))
                except ValueError:
                    pass
                    
            title_match = re.search(r'标题[：:](.*?)\n', prompt)
            title = "村里的人" if not title_match else title_match.group(1).strip()
            
            # 生成模拟内容
            return _generate_mock_chapter(chapter_num, title)

def _generate_mock_chapter(chapter_num: int, title: str) -> str:
    """生成模拟章节内容"""
    if chapter_num == 1:
        return """第一章 村里的变化

清晨的阳光穿过树叶的缝隙，在小村的土路上投下斜斜的光斑。李大伟背着手推车，慢慢地走在回家的路上。车上装满了刚从镇上集市买回来的生活用品和一些新鲜的蔬菜。

"大伟，买了些啥回来？"路边的王大娘正在院子里收晚晒的衣服，看到李大伟过来，随口问道。

李大伟停下车，擦了擦汗流浃浃的额头，"买了些米面油盐，还有几条鱼，我家小子想吃鱼了。"

王大娘笑了笑，"你家小子现在可真是享福呢，不像我们小时候，能吃上一口肉都难。"

李大伟点点头，"是啊，时代不一样了。对了，王大娘，听说村里要修路了？"

王大娘的脸上突然涌现出一丝忧虑，"是啊，村长昨天开会说的。不过……"她压低了声音，"有人说这路修好了，我们的地就要被征用了。"

李大伟的脸色也凝重了起来。这些年村里的变化太快，一方面是生活条件的改善，另一方面却是一些不确定性。特别是关于土地的事，每次都能引起村里人的大讨论。

"别着急，王大娘，这事还没定下来呢。村长说了，会开会讨论的。"李大伟安慰道。

就在这时，村头传来了村长的大喊声："开会啦！全体村民开会啦！"

李大伟和王大娘互相看了一眼，王大娘快速收起晒的衣服，"我去看看。"

李大伟点点头，"我先把东西放家里，马上就来。"

他动作麻利地推着车往家里走去，心里却在想，这次会议会讨论什么？是关于修路的事，还是……

村子里的生活，看似平静，却暗流涌动。每个人心中都有自己的算盘，而这些算盘交织在一起，就组成了村子的命运。"""
    elif chapter_num == 2:
        return """第二章 村民的抉择

村长的声音在村委会的小院里回荡："各位村民，今天叫大家来，是因为上级通知我们，要在我们村建设一个新的农业示范园区。"

这个消息一出，村民们立刻议论纷纷。李大伟坐在人群中，看到周围的人脸上表情各异：有惊讶的，有期待的，也有忧虑的。

"安静，安静！"村长拍了拍手，"我知道大家有很多疑问。这个示范园区将会带动我们村的经济发展，提供就业机会，但也需要征用部分村民的土地。"

"征用土地？那我们怎么办？"一个老农站起来问道，声音中带着明显的不安。

村长点点头，表示理解他的担忧："上级已经制定了补偿方案，每亩地的补偿标准是……"

当村长说出具体数字时，村民们再次议论起来。有人觉得补偿太少，有人则认为这是个好机会。

李大伟看向王大娘，她的脸上写满了忧虑。她家的地正好在规划中的示范园区范围内。

"大伟，你怎么看？"坐在李大伟旁边的张叔低声问道。

李大伟思考了一下："这事关系到村里的长远发展，但也涉及到每个村民的切身利益。我觉得我们应该仔细考虑，不能急于决定。"

张叔点点头："说得对。不过，我听说这个示范园区背后有大公司支持，他们已经在周边几个村都做了类似的项目。"

"大公司？"李大伟有些惊讶，"是哪家公司？"

"据说是城里的绿源农业，他们在全国都有项目。"张叔压低声音，"有人说，村长和绿源的人走得很近。"

李大伟皱起眉头。如果真是这样，那这件事就不仅仅是简单的土地征用了。

会议继续进行，村长宣布将在一周后举行投票，决定是否接受这个项目。"""
    else:
        return f"""第{chapter_num}章 真相与抉择

一周的时间过得很快，村里的气氛越来越紧张。李大伟这几天四处奔走，收集了不少关于绿源农业的信息。

今天是投票的日子，村民们早早地聚集在村委会前的广场上。李大伟看到王大娘和几个老人站在一起，脸上的忧虑更深了。

"大伟，听说你这几天去镇上打听绿源的事了？"张叔走过来问道。

李大伟点点头："是的，我了解到一些情况。绿源在其他地方的项目确实带动了当地经济，但也有一些问题。比如，有些地方的村民反映补偿不到位，还有环保问题。"

"那你觉得我们该怎么投票？"张叔问。

李大伟思考了一下："我觉得我们应该提出更多条件，确保村民的权益得到保障。如果绿源真心想合作，他们应该愿意听取我们的意见。"

这时，村长走上台前，宣布投票开始。但就在这时，一辆黑色轿车驶入村子，停在广场边。

车门打开，下来一个西装革履的中年男子。村长看到他，脸色微变，快步走过去迎接。

"这是谁？"村民们纷纷议论。

"是绿源的陈总。"有人认出了来人。

李大伟注视着这一幕，心中有了一个想法。他走向前台，举手示意要发言。"""

def call_agent(agent_name: str, message: Dict[str, Any]) -> Any:
    """调用智能体的便捷函数"""
    try:
        # 尝试使用LangChain集成
        from src.utils.langchain_integration import call_agent as langchain_call_agent
        return langchain_call_agent(agent_name, message)
    except (ImportError, AttributeError):
        # 如果LangChain集成不可用，回退到MCP客户端
        return mcp_client.call_agent(agent_name, message)

def generate_illustration(title: str, scene_description: str, style: str = "写实风格",
                         output_path: Optional[str] = None, model_name: Optional[str] = None) -> Tuple[bool, str]:
    """生成插图的便捷函数"""
    try:
        # 尝试使用LangChain集成
        from src.utils.langchain_integration import generate_illustration as langchain_generate_illustration
        return langchain_generate_illustration(title, scene_description, style, output_path, model_name)
    except (ImportError, AttributeError):
        # 如果LangChain集成不可用，回退到MCP客户端
        try:
            # 尝试调用真实API
            return mcp_client.generate_illustration(title, scene_description, style, output_path, model_name)
        except Exception as e:
            # 如果调用失败，生成模拟插图
            print(f"生成插图失败: {e}")

            # 如果没有指定输出路径，创建默认路径
            if not output_path:
                os.makedirs("output/illustrations", exist_ok=True)
                output_path = f"output/illustrations/{title}_{int(time.time())}.png"

            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            try:
                # 尝试使用illustration模块
                from src.illustration import generate_novel_illustration
                return generate_novel_illustration(title, scene_description, style, output_path, model_name)
            except ImportError:
                # 如果没有illustration模块，创建一个简单的占位图像
                from PIL import Image, ImageDraw, ImageFont

                # 创建一个简单的占位图像
                width, height = 512, 512
                img = Image.new('RGB', (width, height), color=(240, 240, 240))
                draw = ImageDraw.Draw(img)

                # 添加边框
                draw.rectangle([(10, 10), (width-10, height-10)], outline=(200, 200, 200), width=2)

                # 尝试加载字体
                try:
                    font = ImageFont.truetype("simhei.ttf", 20)
                    title_font = ImageFont.truetype("simhei.ttf", 24)
                except IOError:
                    font = ImageFont.load_default()
                    title_font = ImageFont.load_default()

                # 添加标题
                draw.text((width//2, 50), title, fill=(0, 0, 0), font=title_font, anchor="mm")

                # 添加场景描述和风格信息
                scene_text = scene_description[:200] + "..." if len(scene_description) > 200 else scene_description
                draw.text((20, 100), f"场景: {scene_text}", fill=(0, 0, 0), font=font)
                draw.text((20, 150), f"风格: {style}", fill=(0, 0, 0), font=font)
                draw.text((20, 200), "这是模拟生成的插图", fill=(0, 0, 0), font=font)

                if model_name:
                    draw.text((20, 250), f"模型: {model_name}", fill=(0, 0, 0), font=font)

                # 添加时间戳
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                draw.text((20, height-40), f"生成时间: {timestamp}", fill=(100, 100, 100), font=font)

                # 保存图片
                img.save(output_path)

                return True, output_path
