import"./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js";import{B as Vt}from"./BlockLabel-JbMBN4MZ.js";import{I as xe}from"./IconButton-DbC-jsk_.js";import{E as Dt}from"./Empty-BgOmVBFg.js";import{S as qt}from"./ShareButton-BfWeO6Sd.js";import{D as Ct}from"./Download-DVtk-Jv3.js";import{V as kt}from"./Video-fsmLZWjA.js";import{I as Mt}from"./IconButtonWrapper-DrWC4NJv.js";import{f as de,u as Rt}from"./utils-BsGrhMNe.js";import{D as Lt}from"./DownloadLink-QIttOhoR.js";import{T as Pt,P as $t}from"./Trim-JQYgj7Jd.js";import{P as Bt}from"./Play-B0Q0U1Qz.js";import{U as yt}from"./Undo-DCjBnnSO.js";import{b as It,t as At,V as Ht}from"./Video-C-llMUaJ.js";import{h as Nt}from"./index-V1UhiEW8.js";/* empty css                                             */import{M as Xt}from"./ModifyUpload-DAmzHHEC.js";const{SvelteComponent:Ut,append:jt,attr:O,detach:Ft,init:Ot,insert:zt,noop:We,safe_not_equal:Wt,svg_element:tt}=window.__gradio__svelte__internal;function Gt(i){let e,n;return{c(){e=tt("svg"),n=tt("path"),O(n,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),O(e,"xmlns","http://www.w3.org/2000/svg"),O(e,"width","100%"),O(e,"height","100%"),O(e,"viewBox","0 0 24 24"),O(e,"fill","none"),O(e,"stroke","currentColor"),O(e,"stroke-width","1.5"),O(e,"stroke-linecap","round"),O(e,"stroke-linejoin","round")},m(t,l){zt(t,e,l),jt(e,n)},p:We,i:We,o:We,d(t){t&&Ft(e)}}}class Jt extends Ut{constructor(e){super(),Ot(this,e,null,Gt,Wt,{})}}const{SvelteComponent:Kt,append:ae,attr:B,destroy_block:Qt,detach:Ie,element:se,ensure_array_like:nt,flush:ve,init:Yt,insert:Ae,listen:ue,noop:Qe,run_all:Zt,safe_not_equal:xt,set_style:x,space:Ge,src_url_equal:it,update_keyed_each:en}=window.__gradio__svelte__internal,{onMount:lt,onDestroy:tn}=window.__gradio__svelte__internal;function ot(i,e,n){const t=i.slice();return t[20]=e[n],t[22]=n,t}function nn(i){let e,n,t,l,o,r=[],s=new Map,f,g,_,u,d=nt(i[1]);const c=w=>w[22];for(let w=0;w<d.length;w+=1){let v=ot(i,d,w),k=c(v);s.set(k,r[w]=rt(k,v))}return{c(){e=se("div"),n=se("button"),t=Ge(),l=se("div"),o=Ge();for(let w=0;w<r.length;w+=1)r[w].c();f=Ge(),g=se("button"),B(n,"aria-label","start drag handle for trimming video"),B(n,"class","handle left svelte-10c4beq"),x(n,"left",i[2]+"%"),B(l,"class","opaque-layer svelte-10c4beq"),x(l,"left",i[2]+"%"),x(l,"right",100-i[3]+"%"),B(g,"aria-label","end drag handle for trimming video"),B(g,"class","handle right svelte-10c4beq"),x(g,"left",i[3]+"%"),B(e,"id","timeline"),B(e,"class","thumbnail-wrapper svelte-10c4beq")},m(w,v){Ae(w,e,v),ae(e,n),ae(e,t),ae(e,l),ae(e,o);for(let k=0;k<r.length;k+=1)r[k]&&r[k].m(e,null);ae(e,f),ae(e,g),_||(u=[ue(n,"mousedown",i[10]),ue(n,"blur",i[5]),ue(n,"keydown",i[11]),ue(g,"mousedown",i[12]),ue(g,"blur",i[5]),ue(g,"keydown",i[13])],_=!0)},p(w,v){v&4&&x(n,"left",w[2]+"%"),v&4&&x(l,"left",w[2]+"%"),v&8&&x(l,"right",100-w[3]+"%"),v&2&&(d=nt(w[1]),r=en(r,v,c,1,w,d,s,e,Qt,rt,f,ot)),v&8&&x(g,"left",w[3]+"%")},d(w){w&&Ie(e);for(let v=0;v<r.length;v+=1)r[v].d();_=!1,Zt(u)}}}function ln(i){let e;return{c(){e=se("div"),e.innerHTML='<span aria-label="loading timeline" class="loader svelte-10c4beq"></span>',B(e,"class","load-wrap svelte-10c4beq")},m(n,t){Ae(n,e,t)},p:Qe,d(n){n&&Ie(e)}}}function rt(i,e){let n,t,l;return{key:i,first:null,c(){n=se("img"),it(n.src,t=e[20])||B(n,"src",t),B(n,"alt",l=`frame-${e[22]}`),B(n,"draggable","false"),B(n,"class","svelte-10c4beq"),this.first=n},m(o,r){Ae(o,n,r)},p(o,r){e=o,r&2&&!it(n.src,t=e[20])&&B(n,"src",t),r&2&&l!==(l=`frame-${e[22]}`)&&B(n,"alt",l)},d(o){o&&Ie(n)}}}function on(i){let e;function n(o,r){return o[0]?ln:nn}let t=n(i),l=t(i);return{c(){e=se("div"),l.c(),B(e,"class","container svelte-10c4beq")},m(o,r){Ae(o,e,r),l.m(e,null)},p(o,[r]){t===(t=n(o))&&l?l.p(o,r):(l.d(1),l=t(o),l&&(l.c(),l.m(e,null)))},i:Qe,o:Qe,d(o){o&&Ie(e),l.d()}}}let Je=10;function rn(i,e,n){let{videoElement:t}=e,{trimmedDuration:l}=e,{dragStart:o}=e,{dragEnd:r}=e,{loadingTimeline:s}=e,f=[],g,_=0,u=100,d=null;const c=h=>{d=h},w=()=>{d=null},v=(h,E)=>{if(d){const y=document.getElementById("timeline");if(!y)return;const m=y.getBoundingClientRect();let V=(h.clientX-m.left)/m.width*100;if(E?V=d==="left"?_+E:u+E:V=(h.clientX-m.left)/m.width*100,V=Math.max(0,Math.min(V,100)),d==="left"){n(2,_=Math.min(V,u));const R=_/100*g;n(6,t.currentTime=R,t),n(8,o=R)}else if(d==="right"){n(3,u=Math.max(V,_));const R=u/100*g;n(6,t.currentTime=R,t),n(9,r=R)}const J=_/100*g,p=u/100*g;n(7,l=p-J),n(2,_),n(3,u)}},k=h=>{if(d){const E=1/g*100;h.key==="ArrowLeft"?v({clientX:0},-E):h.key==="ArrowRight"&&v({clientX:0},E)}},P=()=>{const h=document.createElement("canvas"),E=h.getContext("2d");if(!E)return;h.width=t.videoWidth,h.height=t.videoHeight,E.drawImage(t,0,0,h.width,h.height);const y=h.toDataURL("image/jpeg",.7);n(1,f=[...f,y])};lt(()=>{const h=()=>{g=t.duration;const E=g/Je;let y=0;const m=()=>{P(),y++,y<Je?n(6,t.currentTime+=E,t):t.removeEventListener("seeked",m)};t.addEventListener("seeked",m),n(6,t.currentTime=0,t)};t.readyState>=1?h():t.addEventListener("loadedmetadata",h)}),tn(()=>{window.removeEventListener("mousemove",v),window.removeEventListener("mouseup",w),window.removeEventListener("keydown",k)}),lt(()=>{window.addEventListener("mousemove",v),window.addEventListener("mouseup",w),window.addEventListener("keydown",k)});const I=()=>c("left"),$=h=>{(h.key==="ArrowLeft"||h.key=="ArrowRight")&&c("left")},N=()=>c("right"),M=h=>{(h.key==="ArrowLeft"||h.key=="ArrowRight")&&c("right")};return i.$$set=h=>{"videoElement"in h&&n(6,t=h.videoElement),"trimmedDuration"in h&&n(7,l=h.trimmedDuration),"dragStart"in h&&n(8,o=h.dragStart),"dragEnd"in h&&n(9,r=h.dragEnd),"loadingTimeline"in h&&n(0,s=h.loadingTimeline)},i.$$.update=()=>{i.$$.dirty&2&&n(0,s=f.length!==Je)},[s,f,_,u,c,w,t,l,o,r,I,$,N,M]}class sn extends Kt{constructor(e){super(),Yt(this,e,rn,on,xt,{videoElement:6,trimmedDuration:7,dragStart:8,dragEnd:9,loadingTimeline:0})}get videoElement(){return this.$$.ctx[6]}set videoElement(e){this.$$set({videoElement:e}),ve()}get trimmedDuration(){return this.$$.ctx[7]}set trimmedDuration(e){this.$$set({trimmedDuration:e}),ve()}get dragStart(){return this.$$.ctx[8]}set dragStart(e){this.$$set({dragStart:e}),ve()}get dragEnd(){return this.$$.ctx[9]}set dragEnd(e){this.$$set({dragEnd:e}),ve()}get loadingTimeline(){return this.$$.ctx[0]}set loadingTimeline(e){this.$$set({loadingTimeline:e}),ve()}}const{SvelteComponent:an,add_flush_callback:Ce,append:fe,attr:W,bind:Me,binding_callbacks:Re,check_outros:Ye,create_component:He,destroy_component:Ne,detach:Y,element:te,empty:un,flush:X,group_outros:Ze,init:_n,insert:Z,listen:st,mount_component:Xe,noop:dn,run_all:fn,safe_not_equal:cn,set_data:mn,space:Se,text:hn,toggle_class:ee,transition_in:H,transition_out:G}=window.__gradio__svelte__internal,{onMount:gn}=window.__gradio__svelte__internal;function at(i){let e,n,t,l,o,r,s;function f(c){i[18](c)}function g(c){i[19](c)}function _(c){i[20](c)}function u(c){i[21](c)}let d={videoElement:i[2]};return i[14]!==void 0&&(d.dragStart=i[14]),i[15]!==void 0&&(d.dragEnd=i[15]),i[12]!==void 0&&(d.trimmedDuration=i[12]),i[16]!==void 0&&(d.loadingTimeline=i[16]),n=new sn({props:d}),Re.push(()=>Me(n,"dragStart",f)),Re.push(()=>Me(n,"dragEnd",g)),Re.push(()=>Me(n,"trimmedDuration",_)),Re.push(()=>Me(n,"loadingTimeline",u)),{c(){e=te("div"),He(n.$$.fragment),W(e,"class","timeline-wrapper svelte-7yrr5f")},m(c,w){Z(c,e,w),Xe(n,e,null),s=!0},p(c,w){const v={};w&4&&(v.videoElement=c[2]),!t&&w&16384&&(t=!0,v.dragStart=c[14],Ce(()=>t=!1)),!l&&w&32768&&(l=!0,v.dragEnd=c[15],Ce(()=>l=!1)),!o&&w&4096&&(o=!0,v.trimmedDuration=c[12],Ce(()=>o=!1)),!r&&w&65536&&(r=!0,v.loadingTimeline=c[16],Ce(()=>r=!1)),n.$set(v)},i(c){s||(H(n.$$.fragment,c),s=!0)},o(c){G(n.$$.fragment,c),s=!1},d(c){c&&Y(e),Ne(n)}}}function bn(i){let e;return{c(){e=te("div"),W(e,"class","svelte-7yrr5f")},m(n,t){Z(n,e,t)},p:dn,d(n){n&&Y(e)}}}function wn(i){let e,n=de(i[12])+"",t,l,o,r,s,f,g,_;return{c(){e=te("time"),t=hn(n),l=Se(),o=te("div"),r=te("button"),r.textContent="Trim",s=Se(),f=te("button"),f.textContent="Cancel",W(e,"aria-label","duration of selected region in seconds"),W(e,"class","svelte-7yrr5f"),ee(e,"hidden",i[16]),W(r,"class","text-button svelte-7yrr5f"),ee(r,"hidden",i[16]),W(f,"class","text-button svelte-7yrr5f"),ee(f,"hidden",i[16]),W(o,"class","edit-buttons svelte-7yrr5f")},m(u,d){Z(u,e,d),fe(e,t),Z(u,l,d),Z(u,o,d),fe(o,r),fe(o,s),fe(o,f),g||(_=[st(r,"click",i[22]),st(f,"click",i[17])],g=!0)},p(u,d){d&4096&&n!==(n=de(u[12])+"")&&mn(t,n),d&65536&&ee(e,"hidden",u[16]),d&65536&&ee(r,"hidden",u[16]),d&65536&&ee(f,"hidden",u[16])},d(u){u&&(Y(e),Y(l),Y(o)),g=!1,fn(_)}}}function ut(i){let e,n;return e=new xe({props:{Icon:yt,label:"Reset video to initial value",disabled:i[1]||!i[11]}}),e.$on("click",i[23]),{c(){He(e.$$.fragment)},m(t,l){Xe(e,t,l),n=!0},p(t,l){const o={};l&2050&&(o.disabled=t[1]||!t[11]),e.$set(o)},i(t){n||(H(e.$$.fragment,t),n=!0)},o(t){G(e.$$.fragment,t),n=!1},d(t){Ne(e,t)}}}function _t(i){let e,n;return e=new xe({props:{Icon:Pt,label:"Trim video to selection",disabled:i[1]}}),e.$on("click",i[17]),{c(){He(e.$$.fragment)},m(t,l){Xe(e,t,l),n=!0},p(t,l){const o={};l&2&&(o.disabled=t[1]),e.$set(o)},i(t){n||(H(e.$$.fragment,t),n=!0)},o(t){G(e.$$.fragment,t),n=!1},d(t){Ne(e,t)}}}function vn(i){let e,n,t,l=i[3]&&i[0]===""&&ut(i),o=i[4]&&i[0]===""&&_t(i);return{c(){l&&l.c(),e=Se(),o&&o.c(),n=un()},m(r,s){l&&l.m(r,s),Z(r,e,s),o&&o.m(r,s),Z(r,n,s),t=!0},p(r,s){r[3]&&r[0]===""?l?(l.p(r,s),s&9&&H(l,1)):(l=ut(r),l.c(),H(l,1),l.m(e.parentNode,e)):l&&(Ze(),G(l,1,1,()=>{l=null}),Ye()),r[4]&&r[0]===""?o?(o.p(r,s),s&17&&H(o,1)):(o=_t(r),o.c(),H(o,1),o.m(n.parentNode,n)):o&&(Ze(),G(o,1,1,()=>{o=null}),Ye())},i(r){t||(H(l),H(o),t=!0)},o(r){G(l),G(o),t=!1},d(r){r&&(Y(e),Y(n)),l&&l.d(r),o&&o.d(r)}}}function pn(i){let e,n,t,l,o,r,s=i[0]==="edit"&&at(i);function f(u,d){return u[0]==="edit"&&u[12]!==null?wn:bn}let g=f(i),_=g(i);return o=new Xt({props:{i18n:i[7],download:i[9]?i[8]?.url:null,$$slots:{default:[vn]},$$scope:{ctx:i}}}),o.$on("clear",i[24]),{c(){e=te("div"),s&&s.c(),n=Se(),t=te("div"),_.c(),l=Se(),He(o.$$.fragment),W(t,"class","controls svelte-7yrr5f"),W(t,"data-testid","waveform-controls"),W(e,"class","container svelte-7yrr5f"),ee(e,"hidden",i[0]!=="edit")},m(u,d){Z(u,e,d),s&&s.m(e,null),fe(e,n),fe(e,t),_.m(t,null),Z(u,l,d),Xe(o,u,d),r=!0},p(u,[d]){u[0]==="edit"?s?(s.p(u,d),d&1&&H(s,1)):(s=at(u),s.c(),H(s,1),s.m(e,n)):s&&(Ze(),G(s,1,1,()=>{s=null}),Ye()),g===(g=f(u))&&_?_.p(u,d):(_.d(1),_=g(u),_&&(_.c(),_.m(t,null))),(!r||d&1)&&ee(e,"hidden",u[0]!=="edit");const c={};d&128&&(c.i18n=u[7]),d&768&&(c.download=u[9]?u[8]?.url:null),d&33556539&&(c.$$scope={dirty:d,ctx:u}),o.$set(c)},i(u){r||(H(s),H(o.$$.fragment,u),r=!0)},o(u){G(s),G(o.$$.fragment,u),r=!1},d(u){u&&(Y(e),Y(l)),s&&s.d(),_.d(),Ne(o,u)}}}function kn(i,e,n){let{videoElement:t}=e,{showRedo:l=!1}=e,{interactive:o=!0}=e,{mode:r=""}=e,{handle_reset_value:s}=e,{handle_trim_video:f}=e,{processingVideo:g=!1}=e,{i18n:_}=e,{value:u=null}=e,{show_download_button:d=!1}=e,{handle_clear:c=()=>{}}=e,{has_change_history:w=!1}=e,v;gn(async()=>{n(13,v=await It())});let k=null,P=0,I=0,$=!1;const N=()=>{r==="edit"?(n(0,r=""),n(12,k=t.duration)):n(0,r="edit")};function M(p){P=p,n(14,P)}function h(p){I=p,n(15,I)}function E(p){k=p,n(12,k),n(0,r),n(2,t)}function y(p){$=p,n(16,$)}const m=()=>{n(0,r=""),n(1,g=!0),At(v,P,I,t).then(p=>{f(p)}).then(()=>{n(1,g=!1)})},V=()=>{s(),n(0,r="")},J=()=>c();return i.$$set=p=>{"videoElement"in p&&n(2,t=p.videoElement),"showRedo"in p&&n(3,l=p.showRedo),"interactive"in p&&n(4,o=p.interactive),"mode"in p&&n(0,r=p.mode),"handle_reset_value"in p&&n(5,s=p.handle_reset_value),"handle_trim_video"in p&&n(6,f=p.handle_trim_video),"processingVideo"in p&&n(1,g=p.processingVideo),"i18n"in p&&n(7,_=p.i18n),"value"in p&&n(8,u=p.value),"show_download_button"in p&&n(9,d=p.show_download_button),"handle_clear"in p&&n(10,c=p.handle_clear),"has_change_history"in p&&n(11,w=p.has_change_history)},i.$$.update=()=>{i.$$.dirty&4101&&r==="edit"&&k===null&&t&&n(12,k=t.duration)},[r,g,t,l,o,s,f,_,u,d,c,w,k,v,P,I,$,N,M,h,E,y,m,V,J]}class yn extends an{constructor(e){super(),_n(this,e,kn,pn,cn,{videoElement:2,showRedo:3,interactive:4,mode:0,handle_reset_value:5,handle_trim_video:6,processingVideo:1,i18n:7,value:8,show_download_button:9,handle_clear:10,has_change_history:11})}get videoElement(){return this.$$.ctx[2]}set videoElement(e){this.$$set({videoElement:e}),X()}get showRedo(){return this.$$.ctx[3]}set showRedo(e){this.$$set({showRedo:e}),X()}get interactive(){return this.$$.ctx[4]}set interactive(e){this.$$set({interactive:e}),X()}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),X()}get handle_reset_value(){return this.$$.ctx[5]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),X()}get handle_trim_video(){return this.$$.ctx[6]}set handle_trim_video(e){this.$$set({handle_trim_video:e}),X()}get processingVideo(){return this.$$.ctx[1]}set processingVideo(e){this.$$set({processingVideo:e}),X()}get i18n(){return this.$$.ctx[7]}set i18n(e){this.$$set({i18n:e}),X()}get value(){return this.$$.ctx[8]}set value(e){this.$$set({value:e}),X()}get show_download_button(){return this.$$.ctx[9]}set show_download_button(e){this.$$set({show_download_button:e}),X()}get handle_clear(){return this.$$.ctx[10]}set handle_clear(e){this.$$set({handle_clear:e}),X()}get has_change_history(){return this.$$.ctx[11]}set has_change_history(e){this.$$set({has_change_history:e}),X()}}const{SvelteComponent:En,add_flush_callback:ye,append:A,attr:C,bind:Ee,binding_callbacks:Te,bubble:pe,check_outros:dt,create_component:ce,destroy_component:me,detach:Le,element:Q,empty:Tn,flush:q,group_outros:ft,init:Sn,insert:Pe,listen:re,mount_component:he,prevent_default:ct,run_all:Vn,safe_not_equal:Dn,set_data:mt,space:ke,src_url_equal:ht,stop_propagation:qn,text:Ke,toggle_class:gt,transition_in:j,transition_out:z}=window.__gradio__svelte__internal,{createEventDispatcher:Cn}=window.__gradio__svelte__internal;function Mn(i){let e,n;return{c(){e=Q("track"),C(e,"kind","captions"),ht(e.src,n=i[1])||C(e,"src",n),e.default=!0},m(t,l){Pe(t,e,l)},p(t,l){l[0]&2&&!ht(e.src,n=t[1])&&C(e,"src",n)},d(t){t&&Le(e)}}}function Rn(i){let e,n;return e=new $t({}),{c(){ce(e.$$.fragment)},m(t,l){he(e,t,l),n=!0},i(t){n||(j(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){me(e,t)}}}function Ln(i){let e,n;return e=new Bt({}),{c(){ce(e.$$.fragment)},m(t,l){he(e,t,l),n=!0},i(t){n||(j(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){me(e,t)}}}function Pn(i){let e,n;return e=new yt({}),{c(){ce(e.$$.fragment)},m(t,l){he(e,t,l),n=!0},i(t){n||(j(e.$$.fragment,t),n=!0)},o(t){z(e.$$.fragment,t),n=!1},d(t){me(e,t)}}}function bt(i){let e,n,t;function l(r){i[37](r)}let o={videoElement:i[17],showRedo:!0,handle_trim_video:i[23],handle_reset_value:i[7],value:i[11],i18n:i[9],show_download_button:i[10],handle_clear:i[12],has_change_history:i[13]};return i[18]!==void 0&&(o.processingVideo=i[18]),e=new yn({props:o}),Te.push(()=>Ee(e,"processingVideo",l)),{c(){ce(e.$$.fragment)},m(r,s){he(e,r,s),t=!0},p(r,s){const f={};s[0]&131072&&(f.videoElement=r[17]),s[0]&128&&(f.handle_reset_value=r[7]),s[0]&2048&&(f.value=r[11]),s[0]&512&&(f.i18n=r[9]),s[0]&1024&&(f.show_download_button=r[10]),s[0]&4096&&(f.handle_clear=r[12]),s[0]&8192&&(f.has_change_history=r[13]),!n&&s[0]&262144&&(n=!0,f.processingVideo=r[18],ye(()=>n=!1)),e.$set(f)},i(r){t||(j(e.$$.fragment,r),t=!0)},o(r){z(e.$$.fragment,r),t=!1},d(r){me(e,r)}}}function $n(i){let e,n,t,l,o,r,s,f,g,_,u,d,c,w,v,k=de(i[14])+"",P,I,$=de(i[15])+"",N,M,h,E,y,m,V,J,p,R,we,Ve;function Ue(b){i[28](b)}function je(b){i[29](b)}function Fe(b){i[30](b)}function Oe(b){i[31](b)}let oe={src:i[0],preload:"auto",autoplay:i[3],loop:i[4],is_stream:i[8],"data-testid":`${i[5]}-player`,processingVideo:i[18],$$slots:{default:[Mn]},$$scope:{ctx:i}};i[14]!==void 0&&(oe.currentTime=i[14]),i[15]!==void 0&&(oe.duration=i[15]),i[16]!==void 0&&(oe.paused=i[16]),i[17]!==void 0&&(oe.node=i[17]),t=new Ht({props:oe}),Te.push(()=>Ee(t,"currentTime",Ue)),Te.push(()=>Ee(t,"duration",je)),Te.push(()=>Ee(t,"paused",Fe)),Te.push(()=>Ee(t,"node",Oe)),t.$on("click",i[20]),t.$on("play",i[32]),t.$on("pause",i[33]),t.$on("ended",i[22]),t.$on("loadstart",i[34]),t.$on("loadeddata",i[35]),t.$on("loadedmetadata",i[36]);const De=[Pn,Ln,Rn],K=[];function qe(b,a){return b[14]===b[15]?0:b[16]?1:2}d=qe(i),c=K[d]=De[d](i),V=new Jt({});let T=i[6]&&bt(i);return{c(){e=Q("div"),n=Q("div"),ce(t.$$.fragment),f=ke(),g=Q("div"),_=Q("div"),u=Q("span"),c.c(),w=ke(),v=Q("span"),P=Ke(k),I=Ke(" / "),N=Ke($),M=ke(),h=Q("progress"),y=ke(),m=Q("div"),ce(V.$$.fragment),J=ke(),T&&T.c(),p=Tn(),C(n,"class","mirror-wrap svelte-euo1cw"),gt(n,"mirror",i[2]),C(u,"role","button"),C(u,"tabindex","0"),C(u,"class","icon svelte-euo1cw"),C(u,"aria-label","play-pause-replay-button"),C(v,"class","time svelte-euo1cw"),h.value=E=i[14]/i[15]||0,C(h,"class","svelte-euo1cw"),C(m,"role","button"),C(m,"tabindex","0"),C(m,"class","icon svelte-euo1cw"),C(m,"aria-label","full-screen"),C(_,"class","inner svelte-euo1cw"),C(g,"class","controls svelte-euo1cw"),C(e,"class","wrap svelte-euo1cw")},m(b,a){Pe(b,e,a),A(e,n),he(t,n,null),A(e,f),A(e,g),A(g,_),A(_,u),K[d].m(u,null),A(_,w),A(_,v),A(v,P),A(v,I),A(v,N),A(_,M),A(_,h),A(_,y),A(_,m),he(V,m,null),Pe(b,J,a),T&&T.m(b,a),Pe(b,p,a),R=!0,we||(Ve=[re(u,"click",i[20]),re(u,"keydown",i[20]),re(h,"mousemove",i[19]),re(h,"touchmove",ct(i[19])),re(h,"click",qn(ct(i[21]))),re(m,"click",i[24]),re(m,"keypress",i[24])],we=!0)},p(b,a){const S={};a[0]&1&&(S.src=b[0]),a[0]&8&&(S.autoplay=b[3]),a[0]&16&&(S.loop=b[4]),a[0]&256&&(S.is_stream=b[8]),a[0]&32&&(S["data-testid"]=`${b[5]}-player`),a[0]&262144&&(S.processingVideo=b[18]),a[0]&2|a[1]&256&&(S.$$scope={dirty:a,ctx:b}),!l&&a[0]&16384&&(l=!0,S.currentTime=b[14],ye(()=>l=!1)),!o&&a[0]&32768&&(o=!0,S.duration=b[15],ye(()=>o=!1)),!r&&a[0]&65536&&(r=!0,S.paused=b[16],ye(()=>r=!1)),!s&&a[0]&131072&&(s=!0,S.node=b[17],ye(()=>s=!1)),t.$set(S),(!R||a[0]&4)&&gt(n,"mirror",b[2]);let F=d;d=qe(b),d!==F&&(ft(),z(K[F],1,1,()=>{K[F]=null}),dt(),c=K[d],c||(c=K[d]=De[d](b),c.c()),j(c,1),c.m(u,null)),(!R||a[0]&16384)&&k!==(k=de(b[14])+"")&&mt(P,k),(!R||a[0]&32768)&&$!==($=de(b[15])+"")&&mt(N,$),(!R||a[0]&49152&&E!==(E=b[14]/b[15]||0))&&(h.value=E),b[6]?T?(T.p(b,a),a[0]&64&&j(T,1)):(T=bt(b),T.c(),j(T,1),T.m(p.parentNode,p)):T&&(ft(),z(T,1,1,()=>{T=null}),dt())},i(b){R||(j(t.$$.fragment,b),j(c),j(V.$$.fragment,b),j(T),R=!0)},o(b){z(t.$$.fragment,b),z(c),z(V.$$.fragment,b),z(T),R=!1},d(b){b&&(Le(e),Le(J),Le(p)),me(t),K[d].d(),me(V),T&&T.d(b),we=!1,Vn(Ve)}}}function Bn(i,e,n){let{root:t=""}=e,{src:l}=e,{subtitle:o=null}=e,{mirror:r}=e,{autoplay:s}=e,{loop:f}=e,{label:g="test"}=e,{interactive:_=!1}=e,{handle_change:u=()=>{}}=e,{handle_reset_value:d=()=>{}}=e,{upload:c}=e,{is_stream:w}=e,{i18n:v}=e,{show_download_button:k=!1}=e,{value:P=null}=e,{handle_clear:I=()=>{}}=e,{has_change_history:$=!1}=e;const N=Cn();let M=0,h,E=!0,y,m=!1;function V(a){if(!h)return;if(a.type==="click"){p(a);return}if(a.type!=="touchmove"&&!(a.buttons&1))return;const S=a.type==="touchmove"?a.touches[0].clientX:a.clientX,{left:F,right:ze}=a.currentTarget.getBoundingClientRect();n(14,M=h*(S-F)/(ze-F))}async function J(){document.fullscreenElement!=y&&(y.currentTime>0&&!y.paused&&!y.ended&&y.readyState>y.HAVE_CURRENT_DATA?y.pause():await y.play())}function p(a){const{left:S,right:F}=a.currentTarget.getBoundingClientRect();n(14,M=h*(a.clientX-S)/(F-S))}function R(){N("stop"),N("end")}const we=async a=>{let S=new File([a],"video.mp4");const F=await Nt([S]);let ze=(await c(F,t))?.filter(Boolean)[0];u(ze)};function Ve(){y.requestFullscreen()}function Ue(a){M=a,n(14,M)}function je(a){h=a,n(15,h)}function Fe(a){E=a,n(16,E)}function Oe(a){y=a,n(17,y)}function oe(a){pe.call(this,i,a)}function De(a){pe.call(this,i,a)}function K(a){pe.call(this,i,a)}function qe(a){pe.call(this,i,a)}function T(a){pe.call(this,i,a)}function b(a){m=a,n(18,m)}return i.$$set=a=>{"root"in a&&n(25,t=a.root),"src"in a&&n(0,l=a.src),"subtitle"in a&&n(1,o=a.subtitle),"mirror"in a&&n(2,r=a.mirror),"autoplay"in a&&n(3,s=a.autoplay),"loop"in a&&n(4,f=a.loop),"label"in a&&n(5,g=a.label),"interactive"in a&&n(6,_=a.interactive),"handle_change"in a&&n(26,u=a.handle_change),"handle_reset_value"in a&&n(7,d=a.handle_reset_value),"upload"in a&&n(27,c=a.upload),"is_stream"in a&&n(8,w=a.is_stream),"i18n"in a&&n(9,v=a.i18n),"show_download_button"in a&&n(10,k=a.show_download_button),"value"in a&&n(11,P=a.value),"handle_clear"in a&&n(12,I=a.handle_clear),"has_change_history"in a&&n(13,$=a.has_change_history)},i.$$.update=()=>{i.$$.dirty[0]&16384&&n(14,M=M||0),i.$$.dirty[0]&32768&&n(15,h=h||0)},[l,o,r,s,f,g,_,d,w,v,k,P,I,$,M,h,E,y,m,V,J,p,R,we,Ve,t,u,c,Ue,je,Fe,Oe,oe,De,K,qe,T,b]}class In extends En{constructor(e){super(),Sn(this,e,Bn,$n,Dn,{root:25,src:0,subtitle:1,mirror:2,autoplay:3,loop:4,label:5,interactive:6,handle_change:26,handle_reset_value:7,upload:27,is_stream:8,i18n:9,show_download_button:10,value:11,handle_clear:12,has_change_history:13},null,[-1,-1])}get root(){return this.$$.ctx[25]}set root(e){this.$$set({root:e}),q()}get src(){return this.$$.ctx[0]}set src(e){this.$$set({src:e}),q()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),q()}get mirror(){return this.$$.ctx[2]}set mirror(e){this.$$set({mirror:e}),q()}get autoplay(){return this.$$.ctx[3]}set autoplay(e){this.$$set({autoplay:e}),q()}get loop(){return this.$$.ctx[4]}set loop(e){this.$$set({loop:e}),q()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),q()}get interactive(){return this.$$.ctx[6]}set interactive(e){this.$$set({interactive:e}),q()}get handle_change(){return this.$$.ctx[26]}set handle_change(e){this.$$set({handle_change:e}),q()}get handle_reset_value(){return this.$$.ctx[7]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),q()}get upload(){return this.$$.ctx[27]}set upload(e){this.$$set({upload:e}),q()}get is_stream(){return this.$$.ctx[8]}set is_stream(e){this.$$set({is_stream:e}),q()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),q()}get show_download_button(){return this.$$.ctx[10]}set show_download_button(e){this.$$set({show_download_button:e}),q()}get value(){return this.$$.ctx[11]}set value(e){this.$$set({value:e}),q()}get handle_clear(){return this.$$.ctx[12]}set handle_clear(e){this.$$set({handle_clear:e}),q()}get has_change_history(){return this.$$.ctx[13]}set has_change_history(e){this.$$set({has_change_history:e}),q()}}const An=In,{SvelteComponent:Hn,attr:Nn,bubble:_e,check_outros:$e,create_component:ne,destroy_component:ie,detach:ge,element:Xn,empty:Et,flush:U,group_outros:Be,init:Un,insert:be,mount_component:le,noop:Tt,safe_not_equal:St,space:et,transition_in:D,transition_out:L}=window.__gradio__svelte__internal,{createEventDispatcher:jn,afterUpdate:Fn,tick:On}=window.__gradio__svelte__internal;function zn(i){let e=i[0].url,n,t,l,o,r=wt(i);return l=new Mt({props:{display_top_corner:i[10],$$slots:{default:[Jn]},$$scope:{ctx:i}}}),{c(){r.c(),n=et(),t=Xn("div"),ne(l.$$.fragment),Nn(t,"data-testid","download-div")},m(s,f){r.m(s,f),be(s,n,f),be(s,t,f),le(l,t,null),o=!0},p(s,f){f&1&&St(e,e=s[0].url)?(Be(),L(r,1,1,Tt),$e(),r=wt(s),r.c(),D(r,1),r.m(n.parentNode,n)):r.p(s,f);const g={};f&1024&&(g.display_top_corner=s[10]),f&4194657&&(g.$$scope={dirty:f,ctx:s}),l.$set(g)},i(s){o||(D(r),D(l.$$.fragment,s),o=!0)},o(s){L(r),L(l.$$.fragment,s),o=!1},d(s){s&&(ge(n),ge(t)),r.d(s),ie(l)}}}function Wn(i){let e,n;return e=new Dt({props:{unpadded_box:!0,size:"large",$$slots:{default:[Kn]},$$scope:{ctx:i}}}),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p(t,l){const o={};l&4194304&&(o.$$scope={dirty:l,ctx:t}),e.$set(o)},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function wt(i){let e,n;return e=new An({props:{src:i[0].url,subtitle:i[1]?.url,is_stream:i[0].is_stream,autoplay:i[4],mirror:!1,label:i[2],loop:i[7],interactive:!1,upload:i[9],i18n:i[8]}}),e.$on("play",i[12]),e.$on("pause",i[13]),e.$on("stop",i[14]),e.$on("end",i[15]),e.$on("loadedmetadata",i[16]),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p(t,l){const o={};l&1&&(o.src=t[0].url),l&2&&(o.subtitle=t[1]?.url),l&1&&(o.is_stream=t[0].is_stream),l&16&&(o.autoplay=t[4]),l&4&&(o.label=t[2]),l&128&&(o.loop=t[7]),l&512&&(o.upload=t[9]),l&256&&(o.i18n=t[8]),e.$set(o)},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function vt(i){let e,n;return e=new Lt({props:{href:i[0].is_stream?i[0].url?.replace("playlist.m3u8","playlist-file"):i[0].url,download:i[0].orig_name||i[0].path,$$slots:{default:[Gn]},$$scope:{ctx:i}}}),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p(t,l){const o={};l&1&&(o.href=t[0].is_stream?t[0].url?.replace("playlist.m3u8","playlist-file"):t[0].url),l&1&&(o.download=t[0].orig_name||t[0].path),l&4194304&&(o.$$scope={dirty:l,ctx:t}),e.$set(o)},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function Gn(i){let e,n;return e=new xe({props:{Icon:Ct,label:"Download"}}),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p:Tt,i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function pt(i){let e,n;return e=new qt({props:{i18n:i[8],value:i[0],formatter:i[17]}}),e.$on("error",i[18]),e.$on("share",i[19]),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},p(t,l){const o={};l&256&&(o.i18n=t[8]),l&1&&(o.value=t[0]),e.$set(o)},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function Jn(i){let e,n,t,l=i[6]&&vt(i),o=i[5]&&pt(i);return{c(){l&&l.c(),e=et(),o&&o.c(),n=Et()},m(r,s){l&&l.m(r,s),be(r,e,s),o&&o.m(r,s),be(r,n,s),t=!0},p(r,s){r[6]?l?(l.p(r,s),s&64&&D(l,1)):(l=vt(r),l.c(),D(l,1),l.m(e.parentNode,e)):l&&(Be(),L(l,1,1,()=>{l=null}),$e()),r[5]?o?(o.p(r,s),s&32&&D(o,1)):(o=pt(r),o.c(),D(o,1),o.m(n.parentNode,n)):o&&(Be(),L(o,1,1,()=>{o=null}),$e())},i(r){t||(D(l),D(o),t=!0)},o(r){L(l),L(o),t=!1},d(r){r&&(ge(e),ge(n)),l&&l.d(r),o&&o.d(r)}}}function Kn(i){let e,n;return e=new kt({}),{c(){ne(e.$$.fragment)},m(t,l){le(e,t,l),n=!0},i(t){n||(D(e.$$.fragment,t),n=!0)},o(t){L(e.$$.fragment,t),n=!1},d(t){ie(e,t)}}}function Qn(i){let e,n,t,l,o,r;e=new Vt({props:{show_label:i[3],Icon:kt,label:i[2]||"Video"}});const s=[Wn,zn],f=[];function g(_,u){return!_[0]||_[0].url===void 0?0:1}return t=g(i),l=f[t]=s[t](i),{c(){ne(e.$$.fragment),n=et(),l.c(),o=Et()},m(_,u){le(e,_,u),be(_,n,u),f[t].m(_,u),be(_,o,u),r=!0},p(_,[u]){const d={};u&8&&(d.show_label=_[3]),u&4&&(d.label=_[2]||"Video"),e.$set(d);let c=t;t=g(_),t===c?f[t].p(_,u):(Be(),L(f[c],1,1,()=>{f[c]=null}),$e(),l=f[t],l?l.p(_,u):(l=f[t]=s[t](_),l.c()),D(l,1),l.m(o.parentNode,o))},i(_){r||(D(e.$$.fragment,_),D(l),r=!0)},o(_){L(e.$$.fragment,_),L(l),r=!1},d(_){_&&(ge(n),ge(o)),ie(e,_),f[t].d(_)}}}function Yn(i,e,n){let{value:t=null}=e,{subtitle:l=null}=e,{label:o=void 0}=e,{show_label:r=!0}=e,{autoplay:s}=e,{show_share_button:f=!0}=e,{show_download_button:g=!0}=e,{loop:_}=e,{i18n:u}=e,{upload:d}=e,{display_icon_button_wrapper_top_corner:c=!1}=e,w=null,v=null;const k=jn();Fn(async()=>{t!==w&&l!==v&&v!==null&&(w=t,n(0,t=null),await On(),n(0,t=w)),w=t,v=l});function P(m){_e.call(this,i,m)}function I(m){_e.call(this,i,m)}function $(m){_e.call(this,i,m)}function N(m){_e.call(this,i,m)}const M=()=>{k("load")},h=async m=>m?await Rt(m.data):"";function E(m){_e.call(this,i,m)}function y(m){_e.call(this,i,m)}return i.$$set=m=>{"value"in m&&n(0,t=m.value),"subtitle"in m&&n(1,l=m.subtitle),"label"in m&&n(2,o=m.label),"show_label"in m&&n(3,r=m.show_label),"autoplay"in m&&n(4,s=m.autoplay),"show_share_button"in m&&n(5,f=m.show_share_button),"show_download_button"in m&&n(6,g=m.show_download_button),"loop"in m&&n(7,_=m.loop),"i18n"in m&&n(8,u=m.i18n),"upload"in m&&n(9,d=m.upload),"display_icon_button_wrapper_top_corner"in m&&n(10,c=m.display_icon_button_wrapper_top_corner)},i.$$.update=()=>{i.$$.dirty&1&&t&&k("change",t)},[t,l,o,r,s,f,g,_,u,d,c,k,P,I,$,N,M,h,E,y]}class Zn extends Hn{constructor(e){super(),Un(this,e,Yn,Qn,St,{value:0,subtitle:1,label:2,show_label:3,autoplay:4,show_share_button:5,show_download_button:6,loop:7,i18n:8,upload:9,display_icon_button_wrapper_top_corner:10})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),U()}get subtitle(){return this.$$.ctx[1]}set subtitle(e){this.$$set({subtitle:e}),U()}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),U()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),U()}get autoplay(){return this.$$.ctx[4]}set autoplay(e){this.$$set({autoplay:e}),U()}get show_share_button(){return this.$$.ctx[5]}set show_share_button(e){this.$$set({show_share_button:e}),U()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),U()}get loop(){return this.$$.ctx[7]}set loop(e){this.$$set({loop:e}),U()}get i18n(){return this.$$.ctx[8]}set i18n(e){this.$$set({i18n:e}),U()}get upload(){return this.$$.ctx[9]}set upload(e){this.$$set({upload:e}),U()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[10]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),U()}}const bi=Object.freeze(Object.defineProperty({__proto__:null,default:Zn},Symbol.toStringTag,{value:"Module"}));export{An as P,Zn as V,bi as a};
//# sourceMappingURL=VideoPreview-Bmn4fgzX.js.map
