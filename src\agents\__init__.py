"""
代理模块包
包含各种智能代理
"""

from .writing_agents import *
from .chapter_generator import *
from .illustration_generator import *
from .chapter_transition import *
from .outline_generator import OutlineGeneratorAgent, register_outline_generator_agent

# 确保 register_writing_agents 函数可用
from .writing_pipeline import register_writing_agents

__all__ = [
    'Agent',
    'WritingAgent',
    'PolishingAgent',
    'ExpansionAgent',
    'ReviewAgent',
    'OptimizationAgent',
    'IllustrationAgent',
    'OutlineGeneratorAgent',
    'register_writing_agents',
    'register_outline_generator_agent',
    'create_writing_pipeline',
    'NovelManager',
    'ChapterGenerator',
    'IllustrationGenerator',
    'ChapterTransition'
]
