"""
改进的章节生成模块
用于根据大纲生成更加丰富、连贯的章节内容
"""

import re
from typing import Dict, List, Optional, Any
from collections import Counter

# 尝试导入过渡处理模块
try:
    from src.transition_handler import create_transition, create_chapter_ending
    has_transition_module = True
except ImportError:
    has_transition_module = False
    # 如果导入失败，创建简单的替代函数
    def create_transition(prev_chapter_content, next_chapter_outline):
        return ""

    def create_chapter_ending(chapter_content, next_chapter_outline=None):
        return chapter_content

# 尝试导入中国文化元素模块
try:
    from src.chinese_cultural_elements import enrich_novel_with_chinese_elements, ChineseCulturalElements
    cultural_elements = ChineseCulturalElements()
    has_cultural_module = True
except ImportError:
    has_cultural_module = False
    # 如果导入失败，创建简单的替代类
    class ChineseCulturalElements:
        def generate_chinese_style_description(self, scene, season=None):
            if season == "春":
                return f"{scene}的春天，万物复苏，生机勃勃。"
            elif season == "夏":
                return f"{scene}的夏天，骄阳似火，热情如潮。"
            elif season == "秋":
                return f"{scene}的秋天，硕果累累，金风送爽。"
            elif season == "冬":
                return f"{scene}的冬天，白雪皑皑，寒风刺骨。"
            else:
                return f"{scene}的景色十分美丽。"

        def generate_chinese_style_emotion(self, emotion):
            emotions = {
                "好奇": f"心中涌起一股难以抑制的好奇心，如同小溪般流淌。",
                "期待": f"心中充满期待，如同等待春天的种子。",
                "紧张": f"紧张的情绪如同绷紧的弦，随时可能断裂。",
                "激烈": f"情绪如同沸腾的水，激烈地翻滚着。",
                "决然": f"决心如同坚硬的磐石，不可动摇。",
                "悬疑": f"心中的疑问如同迷雾，笼罩着思绪。",
                "喜悦": f"喜悦如同春风，温暖着心田。"
            }
            return emotions.get(emotion, f"他感到非常{emotion}。")

    cultural_elements = ChineseCulturalElements()

    def enrich_novel_with_chinese_elements(chapter_content, genre, chapter_num):
        return chapter_content

def extract_chapter_elements(chapter_outline: str) -> Dict[str, str]:
    """
    从章节大纲中提取各个元素

    参数:
        chapter_outline: 章节大纲

    返回:
        包含章节标题、核心事件等元素的字典
    """
    elements = {
        "title": "",
        "core_events": "",
        "characters": "",
        "story_relation": "",
        "emotional_tone": ""
    }

    # 提取章节标题
    title_patterns = [
        # 新格式：1. **章节标题**：开篇章节
        r'1\. \*\*章节标题\*\*：(.+?)(?:\n|$)',
        # 新格式：#### **第1章：开篇章节**
        r'####\s*\*\*第\d+章[：:](.+?)\*\*',
        # 旧格式
        r'第\d+章[：:]\s*(.+?)(?:\n|$)',
        r'第\d+章[：:]\s*(.+)',
        r'[：:]\s*(.+?)(?:\n|$)',
        r'[：:]\s*(.+)'
    ]

    for pattern in title_patterns:
        title_match = re.search(pattern, chapter_outline)
        if title_match:
            elements["title"] = title_match.group(1).strip()
            break

    # 提取核心事件
    core_events_match = re.search(r'2\. \*\*本章核心事件\*\*：(.+?)(?:\n|$)', chapter_outline)
    if core_events_match:
        elements["core_events"] = core_events_match.group(1).strip()
    else:
        # 尝试其他格式
        core_events_match = re.search(r'本章核心事件[：:]*(.+?)(?:\n|$)', chapter_outline)
        if core_events_match:
            elements["core_events"] = core_events_match.group(1).strip()

    # 提取出场角色
    characters_match = re.search(r'3\. \*\*本章出场角色\*\*：(.+?)(?:\n|$)', chapter_outline)
    if characters_match:
        elements["characters"] = characters_match.group(1).strip()

    # 提取与整体故事的关系
    relation_match = re.search(r'4\. \*\*本章与整体故事的关系\*\*：(.+?)(?:\n|$)', chapter_outline)
    if relation_match:
        elements["story_relation"] = relation_match.group(1).strip()

    # 提取情感基调
    tone_match = re.search(r'5\. \*\*本章情感基调\*\*：(.+?)(?:\n|$)', chapter_outline)
    if tone_match:
        elements["emotional_tone"] = tone_match.group(1).strip()

    return elements

def extract_character_names(characters_str: str) -> List[str]:
    """
    从角色字符串中提取角色名称列表

    参数:
        characters_str: 角色字符串，可能包含多个角色，用顿号、逗号或英文逗号分隔

    返回:
        角色名称列表
    """
    if not characters_str:
        return []

    # 尝试不同的分隔符
    if '、' in characters_str:
        return [name.strip() for name in characters_str.split('、') if name.strip()]
    elif '，' in characters_str:
        return [name.strip() for name in characters_str.split('，') if name.strip()]
    elif ',' in characters_str:
        return [name.strip() for name in characters_str.split(',') if name.strip()]
    else:
        return [characters_str.strip()]

def extract_main_character_from_prev_content(prev_content: str) -> str:
    """
    从前一章内容中提取主角名称

    参数:
        prev_content: 前一章内容

    返回:
        提取的主角名称，如果无法提取则返回空字符串
    """
    if not prev_content:
        return ""

    # 使用正则表达式查找可能的中文人名（2-3个字符）
    name_matches = re.findall(r'([\u4e00-\u9fa5]{2,3})(?=[说道想着回答])', prev_content)
    if name_matches:
        # 使用出现频率最高的名字作为主角名称
        name_counter = Counter(name_matches)
        return name_counter.most_common(1)[0][0]

    # 如果没有找到，使用一个随机的中文名字
    chinese_names = ["李明", "张伟", "王强", "赵华", "陈刚", "杨光", "周鹏"]
    import random
    return random.choice(chinese_names)

def generate_improved_chapter(
    title: str,
    genre: str,
    style: str,
    scene: str,
    characters: str,
    chapter_num: int,
    chapter_outline: str,
    prev_chapter_content: Optional[str] = None,
    next_chapter_outline: Optional[str] = None
) -> str:
    """
    生成改进的章节内容

    参数:
        title: 小说标题
        genre: 类型
        style: 风格
        scene: 场景
        characters: 角色
        chapter_num: 章节号
        chapter_outline: 章节大纲
        prev_chapter_content: 前一章内容
        next_chapter_outline: 下一章大纲

    返回:
        生成的章节内容
    """
    # 提取章节元素
    chapter_elements = extract_chapter_elements(chapter_outline)

    # 如果没有提取到核心事件，使用默认值
    if not chapter_elements["core_events"]:
        if chapter_num == 1:
            chapter_elements["core_events"] = f"介绍了{characters}的背景和{scene}的环境。主角{characters}面临着一个重要的选择。"
        elif chapter_num % 2 == 0:
            chapter_elements["core_events"] = f"{characters}遇到了一个重大的障碍。场景从{scene}转移到了一个新的地点。冲突开始升级，紧张感增加。"
        else:
            chapter_elements["core_events"] = f"{characters}发现了一个重要的线索。{scene}的环境中隐藏着一个秘密。故事的节奏变得更加缓慢，但充满了悬念。"

    # 如果没有提取到出场角色，使用默认值
    if not chapter_elements["characters"]:
        chapter_elements["characters"] = characters

    # 如果没有提取到与整体故事的关系，使用默认值
    if not chapter_elements["story_relation"]:
        if chapter_num == 1:
            chapter_elements["story_relation"] = "建立故事背景和主角形象，引入故事的主要冲突"
        elif chapter_num % 2 == 0:
            chapter_elements["story_relation"] = "推动情节发展，加深故事冲突，展示主角的成长"
        else:
            chapter_elements["story_relation"] = "揭示故事中的重要秘密，为后续情节做链接"

    # 如果没有提取到情感基调，使用默认值
    if not chapter_elements["emotional_tone"]:
        if chapter_num == 1:
            chapter_elements["emotional_tone"] = "好奇、期待、稳定中带有一丝紧张"
        elif chapter_num % 2 == 0:
            chapter_elements["emotional_tone"] = "紧张、激烈、决然"
        else:
            chapter_elements["emotional_tone"] = "悬疑、好奇、紧张中带有发现的喜悦"

    # 生成章节标题
    chapter_title = f"第{chapter_num}章"
    if chapter_elements["title"]:
        chapter_title = f"第{chapter_num}章：{chapter_elements['title']}"

    # 处理角色名称
    character_list = extract_character_names(chapter_elements["characters"])

    # 如果没有角色，使用默认角色
    if not character_list:
        character_list = extract_character_names(characters)

    # 确保至少有一个角色
    if not character_list:
        character_list = ["主角"]

    # 获取主角和配角
    main_character = character_list[0]
    supporting_characters = character_list[1:] if len(character_list) > 1 else []

    # 如果主角是通用描述（如"一位年轻人"），尝试从前一章内容中提取具体的人名
    if main_character in ["一位年轻人", "主角"] and prev_chapter_content and chapter_num > 1:
        extracted_name = extract_main_character_from_prev_content(prev_chapter_content)
        if extracted_name:
            main_character = extracted_name

    # 如果没有配角，创建一个配角
    if not supporting_characters and chapter_num > 1:
        if '老师' not in main_character and '教授' not in main_character:
            supporting_characters = ["老师"]
        else:
            supporting_characters = ["学生"]

    # 生成章节内容
    content = f"{chapter_title}\n\n"

    # 生成过渡段落
    transition_paragraph = ""
    if prev_chapter_content and chapter_num > 1:
        if has_transition_module:
            # 使用transition_handler模块创建过渡段落
            transition_paragraph = create_transition(prev_chapter_content, chapter_outline)
        else:
            # 备用方法：提取前一章的最后一段作为过渡参考
            prev_paragraphs = prev_chapter_content.split('\n\n')
            if len(prev_paragraphs) > 2:
                last_paragraph = prev_paragraphs[-2]  # 倒数第二段，因为最后一段可能是结束语
                transition_paragraph = f"承接上一章的内容，{last_paragraph[0:100]}..."

    # 生成开场段落
    opening_paragraphs = []

    # 添加过渡段落（如果有）
    if transition_paragraph:
        opening_paragraphs.append(transition_paragraph)

    # 根据情感基调设置开场氛围
    emotions = re.findall(r'[，,、\s]?([^\s，,、]+)', chapter_elements['emotional_tone'])
    if emotions:
        main_emotion = emotions[0]
        emotion_description = cultural_elements.generate_chinese_style_emotion(main_emotion)
        opening_paragraphs.append(emotion_description)

    # 根据章节类型生成不同的开场
    if chapter_num == 1:
        # 第一章通常是引入和背景介绍
        scene_description = cultural_elements.generate_chinese_style_description(scene, "春")
        opening_paragraphs.append(scene_description)

        opening_paragraphs.append(f"这是{main_character}故事的开始。在{scene}的背景下，一段不平凡的旅程即将展开。")

        # 介绍主角
        opening_paragraphs.append(f"清晨的阳光透过窗帘，洒在{main_character}的脸上。这是一个平凡的早晨，但他不知道的是，这一天将彻底改变他的人生轨迹。")

        # 介绍环境
        opening_paragraphs.append(f"{scene}的早晨充满了生机，人们忙碌地奔走在街头，而{main_character}却有着不同的计划。")
    else:
        # 非第一章，根据核心事件设置开场
        core_events = chapter_elements['core_events']

        # 根据章节的奇偶性设置不同的开场
        if chapter_num % 2 == 0:
            # 偶数章通常是冲突和行动
            scene_description = cultural_elements.generate_chinese_style_description(scene, "夏")
            opening_paragraphs.append(scene_description)

            opening_paragraphs.append(f"雨点打在窗户上，{main_character}的心情如同天气一般阴暗。他知道自己必须做出决定，而这个决定可能会带来严重的后果。")

            # 添加配角互动
            if supporting_characters:
                supporting_char = supporting_characters[0]
                opening_paragraphs.append(f""{main_character}，你确定要这么做吗？"一旁的{supporting_char}担忧地问道。")
                opening_paragraphs.append(f""{supporting_char}，我别无选择，"{ main_character}坚定地回答，"这是唯一的方法。"")
        else:
            # 奇数章通常是探索和发现
            scene_description = cultural_elements.generate_chinese_style_description(scene, "秋")
            opening_paragraphs.append(scene_description)

            opening_paragraphs.append(f"月光如水，洒在{scene}的每一个角落。{main_character}静静地站在那里，思考着这些天发生的一切。他知道自己已经走得太远，无法回头。")

            # 添加配角互动
            if supporting_characters:
                supporting_char = supporting_characters[0]
                opening_paragraphs.append(f""{main_character}，你找到了什么？"{ supporting_char}轻声问道，生怕打破这份宁静。")
                opening_paragraphs.append(f""{supporting_char}，我想我发现了一些重要的东西，"{ main_character}回答，眼中闪烁着兴奋的光芒。")

    # 生成主要内容段落
    main_content_paragraphs = []

    # 根据核心事件生成内容
    core_events = chapter_elements['core_events']
    events = re.split(r'[，。,]', core_events)
    events = [event.strip() for event in events if event.strip()]

    # 为每个事件创建内容
    for event in events:
        main_content_paragraphs.append(f"{event}。")

    # 添加场景描写
    if chapter_num % 3 == 0:
        # 每三章添加一次详细的场景描写
        main_content_paragraphs.append(f"{scene}的景色在阳光的照耀下显得格外美丽。远处的山峰若隐若现，云雾缭绕在山间，如同仙境一般。")
    elif chapter_num % 3 == 1:
        main_content_paragraphs.append(f"夜幕降临，{scene}的灯光渐渐点亮。街道上的行人变得稀疏，只有偶尔经过的汽车光线划破黑暗。")
    else:
        main_content_paragraphs.append(f"雨水冲刷着{scene}的街道，行人绮绮撑起雨伞，形成一道独特的风景线。水滴打在地面上，发出节奏感强烈的声音。")

    # 添加对话和互动
    if supporting_characters:
        supporting_char = supporting_characters[0]
        dialogue = [
            f""{main_character}，你真的相信这一切吗？"{ supporting_char}问道，眼中充满了怀疑。",
            f""{supporting_char}，有些事情必须相信，即使没有证据，"{ main_character}回答，"有时候直觉比证据更重要。"",
            f""{main_character}..."{ supporting_char}欲言又止，最终只是叹了口气。"
        ]
        main_content_paragraphs.extend(dialogue)

    # 添加内心独白
    monologue = [
        f"{main_character}在心中思考："我到底在做什么？这一切值得吗？"但他知道，自己已经无法停下。",
        f""有时候我想知道，如果当初选择了不同的道路，一切是否会不一样，"{main_character}想道，"但这些思考毫无意义。"",
        f"{main_character}深吸一口气，让自己平静下来。他知道接下来的决定将改变一切，但他别无选择。"
    ]
    main_content_paragraphs.extend(monologue)

    # 添加情节转折
    plot_twist = [
        f"突然，一道闪电划过天空，照亮了整个{scene}。随后而来的雷声似乎预示着什么。{main_character}知道，风暴就要来了。",
        f"时间似乎在这一刻冻结。{main_character}看着眼前的场景，一切他以为理所当然的事物突然变得陌生而遥远。",
        f"当{main_character}打开那扇门时，他知道自己的生活将永远不同。门后的景象超出了他的想象，他只能站在那里，目瞪口呆。"
    ]
    main_content_paragraphs.extend(plot_twist)

    # 生成结尾段落
    ending_paragraphs = []

    # 添加与整体故事的关系
    ending_paragraphs.append(f"这一切的发生，{chapter_elements['story_relation']}。故事的脉络逐渐清晰。")

    # 添加章节总结
    if chapter_elements["title"]:
        ending_paragraphs.append(f"{chapter_elements['title']}的篇章暂告一段落，但{main_character}的旅程远未结束。")

    # 添加悬念，为下一章做铺垫
    if next_chapter_outline:
        next_elements = extract_chapter_elements(next_chapter_outline)
        if next_elements["core_events"]:
            events = re.split(r'[，。,]', next_elements["core_events"])
            events = [event.strip() for event in events if event.strip()]
            if events:
                hint = events[0][:10] + "..."
                ending_paragraphs.append(f"而接下来，{hint} 这将会是怎样的发展？")
        else:
            ending_paragraphs.append(f"而接下来，{main_character}将面临更大的挑战。这将会是怎样的发展？")
    else:
        ending_paragraphs.append(f"而接下来，{main_character}将面临更大的挑战。这将会是怎样的发展？")

    # 添加章节结束标记
    ending_paragraphs.append(f"第{chapter_num}章结束。")

    # 组合所有段落
    all_paragraphs = opening_paragraphs + main_content_paragraphs + ending_paragraphs

    # 过滤空段落
    filtered_paragraphs = [p for p in all_paragraphs if p.strip()]

    # 将段落添加到内容中
    content += "\n\n".join(filtered_paragraphs)

    # 使用中国文化元素丰富内容
    if has_cultural_module:
        content = enrich_novel_with_chinese_elements(content, genre, chapter_num)

    # 添加章节结尾
    if has_transition_module:
        content = create_chapter_ending(content, next_chapter_outline)

    return content

def generate_chapter(
    chapter_num: int,
    chapter_outline: str,
    prev_chapter_content: Optional[str] = None,
    next_chapter_outline: Optional[str] = None,
    novel_info: Optional[Dict[str, Any]] = None
) -> str:
    """
    生成章节内容的便捷函数

    参数:
        chapter_num: 章节号
        chapter_outline: 章节大纲
        prev_chapter_content: 前一章内容
        next_chapter_outline: 下一章大纲
        novel_info: 小说信息

    返回:
        生成的章节内容
    """
    # 提取小说信息
    title = novel_info.get("title", "未知标题") if novel_info else "未知标题"
    genre = novel_info.get("genre", "现代小说") if novel_info else "现代小说"
    style = novel_info.get("style", "写实主义") if novel_info else "写实主义"
    scene = novel_info.get("scene", "城市") if novel_info else "城市"
    characters = novel_info.get("characters", "主角") if novel_info else "主角"

    # 生成章节内容
    return generate_improved_chapter(
        title=title,
        genre=genre,
        style=style,
        scene=scene,
        characters=characters,
        chapter_num=chapter_num,
        chapter_outline=chapter_outline,
        prev_chapter_content=prev_chapter_content,
        next_chapter_outline=next_chapter_outline
    )

if __name__ == "__main__":
    # 测试代码
    chapter_outline = """#### **第1章：开篇章节**
1. **章节标题**：开篇章节
2. **本章核心事件**：介绍了李明的背景和城市的环境。主角李明面临着一个重要的选择。
3. **本章出场角色**：李明、张华、王芳
4. **本章与整体故事的关系**：建立故事背景和主角形象，引入故事的主要冲突。
5. **本章情感基调**：好奇、期待、稳定中带有一丝紧张。
"""

    next_chapter_outline = """#### **第2章：冲突与行动**
1. **章节标题**：冲突与行动
2. **本章核心事件**：李明遇到了一个重大的障碍。场景从城市转移到了一个新的地点。冲突开始升级，紧张感增加。
3. **本章出场角色**：李明、张华、赵强
4. **本章与整体故事的关系**：推动情节发展，加深故事冲突，展示主角的成长。
5. **本章情感基调**：紧张、激烈、决然。
"""

    novel_info = {
        "title": "城市之光",
        "genre": "现代都市",
        "style": "写实主义",
        "scene": "繁华都市",
        "characters": "李明"
    }

    chapter_content = generate_chapter(
        1, chapter_outline, None, next_chapter_outline, novel_info
    )

    print("生成的章节内容:")
    print(chapter_content)
