"""
插图生成模块
用于生成小说插图
"""

import os
import time
from typing import Tuple, Optional
from PIL import Image, ImageDraw, ImageFont

def generate_novel_illustration(
    title: str,
    scene: str,
    style: str = "写实风格",
    output_path: Optional[str] = None,
    model_name: Optional[str] = None
) -> Tuple[bool, str]:
    """
    生成小说插图
    
    参数:
        title: 小说标题
        scene: 场景描述
        style: 插图风格
        output_path: 输出路径
        model_name: 模型名称
        
    返回:
        (成功标志, 结果信息)
    """
    try:
        # 如果没有指定输出路径，创建默认路径
        if not output_path:
            os.makedirs("output/illustrations", exist_ok=True)
            output_path = f"output/illustrations/{title}_{int(time.time())}.png"
            
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 创建一个简单的占位图像
        width, height = 512, 512
        img = Image.new('RGB', (width, height), color=(240, 240, 240))
        draw = ImageDraw.Draw(img)
        
        # 添加边框
        draw.rectangle([(10, 10), (width-10, height-10)], outline=(200, 200, 200), width=2)
        
        # 尝试加载字体
        try:
            font = ImageFont.truetype("simhei.ttf", 20)
            title_font = ImageFont.truetype("simhei.ttf", 24)
        except IOError:
            font = ImageFont.load_default()
            title_font = ImageFont.load_default()
        
        # 添加标题
        draw.text((width//2, 50), title, fill=(0, 0, 0), font=title_font, anchor="mm")
        
        # 添加场景描述和风格信息
        scene_text = scene[:200] + "..." if len(scene) > 200 else scene
        draw.text((20, 100), f"场景: {scene_text}", fill=(0, 0, 0), font=font)
        draw.text((20, 150), f"风格: {style}", fill=(0, 0, 0), font=font)
        draw.text((20, 200), "这是模拟生成的插图", fill=(0, 0, 0), font=font)
        
        if model_name:
            draw.text((20, 250), f"模型: {model_name}", fill=(0, 0, 0), font=font)
        
        # 添加时间戳
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        draw.text((20, height-40), f"生成时间: {timestamp}", fill=(100, 100, 100), font=font)
        
        # 保存图片
        img.save(output_path)
        
        return True, output_path
    except Exception as e:
        return False, f"生成插图失败: {e}"
