"""
写作智能体模块 - 重定向导入
此模块已被拆分为多个单独的文件，为了向后兼容，保留此文件作为导入重定向
"""

# 从拆分后的文件导入智能体类
from src.agents.writing_agent import WritingAgent
from src.agents.polishing_agent import PolishingAgent
from src.agents.expansion_agent import ExpansionAgent
from src.agents.review_agent import ReviewAgent
from src.agents.optimization_agent import OptimizationAgent
from src.agents.illustration_agent import IllustrationAgent
from src.agents.novel_manager import NovelManager, Chapter, Novel
from src.agents.writing_pipeline import create_writing_pipeline, run_pipeline, check_chapter_exists, register_writing_agents

# 为了向后兼容，保留这些导入
try:
    from src.core.agent import Agent, AGENT_REGISTRY
except ImportError:
    print("警告: 无法导入Agent和AGENT_REGISTRY类")

try:
    from src.core.model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES
except ImportError:
    print("警告: 无法导入AI_MODEL_CONFIG和DEFAULT_MODEL_NAMES")

try:
    from src.core.prompt_manager import PromptManager
except ImportError:
    print("警告: 无法导入PromptManager")

# 导入常用的类型和模块，以便向后兼容
import os
import json
import re
from typing import Dict, List, Any, Optional, Union, Tuple, cast
from datetime import datetime
import traceback
