{"data_mtime": 1751380081, "dep_lines": [11, 34, 42, 658, 661, 695, 6, 7, 8, 735, 696, 1, 1, 1, 1, 405], "dep_prios": [5, 5, 5, 20, 20, 20, 10, 10, 5, 20, 20, 5, 30, 30, 30, 20], "dependencies": ["src.core.agent", "src.core.prompt_manager", "src.core.model_config", "src.utils.api_client", "src.utils.mcp_client", "src.agents.outline_generator", "os", "json", "typing", "traceback", "logging", "builtins", "_frozen_importlib", "abc", "src.core"], "hash": "cee6ad095c813028a19831a56f180fcd8aa5cb9a", "id": "src.agents.writing_agent", "ignore_all": true, "interface_hash": "2591d4780949bef4d5d75ebfa1725b772b1d221f", "mtime": 1751380021, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\storymc\\src\\agents\\writing_agent.py", "plugin_data": null, "size": 33402, "suppressed": ["run"], "version_id": "1.15.0"}