"""
大纲生成器模块 - 工业级实现

提供三级大纲生成架构：
1. 总纲（Master Outline）：整体故事结构、阶段划分和主要伏笔
2. 卷纲（Volume Outline）：每个阶段的章节框架和事件安排
3. 章节大纲（Chapter Outline）：单章节的详细内容规划

特点：
- 三级缓冲机制：总纲→卷纲→章节大纲，逐级细化
- 预生成缓冲池：保持多章内容的缓冲，避免生成延迟
- 伏笔生命周期管理：注册→跟踪→解决的全流程管理
- 与记忆系统和一致性检查集成
"""

import os
import re
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable, Tuple
from pydantic import BaseModel, Field, validator

# 尝试导入Agent类
try:
    from src.core.agent import Agent, AGENT_REGISTRY
except ImportError:
    # 如果导入失败，创建简单的Agent类
    class Agent:
        def __init__(self, name, model=None):
            self.name = name
            self.model = model

        def send_message(self, recipient, message):
            print(f"[{self.name}] 发送消息给 {recipient}: {message['action']}")

            # 如果有AGENT_REGISTRY，尝试使用它的dispatch_message方法
            try:
                if 'AGENT_REGISTRY' in globals():
                    AGENT_REGISTRY.dispatch_message(self.name, recipient, message)
                    return
            except Exception as e:
                print(f"[{self.name}] 使用AGENT_REGISTRY分发消息时出错: {e}")

            # 如果没有AGENT_REGISTRY或分发失败，尝试直接获取接收者并调用其handle_message方法
            try:
                # 尝试从全局变量中获取AGENT_REGISTRY
                import sys
                if 'src.core.agent' in sys.modules and hasattr(sys.modules['src.core.agent'], 'AGENT_REGISTRY'):
                    registry = sys.modules['src.core.agent'].AGENT_REGISTRY
                    recipient_agent = registry.get_agent(recipient)
                    if recipient_agent:
                        recipient_agent.handle_message(message)
                        return
            except Exception as e:
                print(f"[{self.name}] 直接调用接收者的handle_message时出错: {e}")

    # 创建简单的AGENT_REGISTRY类
    class AgentRegistry:
        def __init__(self):
            self.agents = {}

        def register(self, agent):
            self.agents[agent.name] = agent
            print(f"智能体 {agent.name} 已注册，使用模型: {agent.model}")
            return agent

        def get_agent(self, name):
            return self.agents.get(name)

    AGENT_REGISTRY = AgentRegistry()

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("OutlineGenerator")

# 添加控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

# 检查是否已经添加了处理器
handlers_exist = False
for handler in logger.handlers:
    if isinstance(handler, logging.StreamHandler):
        handlers_exist = True
        break

if not handlers_exist:
    logger.addHandler(console_handler)
    logger.info("大纲生成器日志系统初始化完成")

# 尝试导入高级功能模块
try:
    from src.memory.memory_integration import (
        get_memory_manager, enhance_prompt_with_memory,
        update_memory_from_chapter, add_segment_to_memory
    )
    from src.consistency.consistency_checker import (
        get_consistency_checker, check_consistency
    )
    from src.narrative.narrative_controller import (
        get_narrative_controller, generate_branch_options,
        select_optimal_path
    )
    from src.clues.clue_manager import (
        register_clue, update_clue_status, get_active_clues
    )
    ADVANCED_FEATURES_ENABLED = True
    logger.info("高级功能已在大纲生成器中启用")
except ImportError as e:
    logger.warning(f"大纲生成器无法导入高级功能模块: {e}")
    ADVANCED_FEATURES_ENABLED = False

# 尝试导入AI模型配置
try:
    from src.core.model_config import AI_MODEL_CONFIG
    from src.utils.api_client import get_api_client
    MODEL_CONFIG_AVAILABLE = True
except ImportError as e:
    logger.warning(f"无法导入模型配置: {e}")
    MODEL_CONFIG_AVAILABLE = False


# ----------------- 数据模型定义 -----------------

class WorldSetting(BaseModel):
    """世界观基础设定"""
    title: str = Field(..., description="小说标题")
    genre: str = Field(..., description="小说类型，如奇幻、科幻、现代")
    style: str = Field(..., description="写作风格，如硬核、轻松、严肃")
    phases: List[str] = Field(default_factory=list, description="总纲阶段列表，如'序章'、'崛起'、'危机'等")
    total_chapters: int = Field(default=30, description="计划的总章节数")
    scene: str = Field(default="", description="主要场景设定")
    characters: str = Field(default="", description="主要角色设定")
    tech_rules: List[str] = Field(default_factory=list, description="技术/魔法规则")
    character_roster: Dict[str, Dict] = Field(default_factory=dict, description="角色名->角色档案")

    @validator('phases')
    def validate_phases(cls, v):
        if not v:
            return ["序章", "发展", "高潮", "结局"]
        return v

class VolumeOutline(BaseModel):
    """卷纲数据结构"""
    volume_id: int = Field(..., description="卷ID")
    phase: str = Field(..., description="所属阶段")
    chapters: List[Dict] = Field(..., description="章节框架列表")
    start_chapter: int = Field(..., description="起始章节编号")
    end_chapter: int = Field(..., description="结束章节编号")
    unresolved_plots: List[str] = Field(default_factory=list, description="未解决的伏笔")
    main_conflicts: List[str] = Field(default_factory=list, description="主要冲突")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="创建时间")

class ChapterOutline(BaseModel):
    """章节详细大纲"""
    chapter_num: int = Field(..., description="章节编号")
    title: str = Field(..., description="章节标题")
    summary: str = Field(..., description="章节摘要")
    key_scenes: List[Dict] = Field(default_factory=list, description="关键场景")
    plot_actions: Dict[str, str] = Field(default_factory=dict, description="伏笔处理动作")
    characters_involved: List[str] = Field(default_factory=list, description="涉及角色")
    next_preview: str = Field(default="", description="下一章预告")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="创建时间")

class MasterOutline(BaseModel):
    """总纲数据结构"""
    title: str = Field(..., description="小说标题")
    genre: str = Field(..., description="小说类型")
    style: str = Field(..., description="写作风格")
    phases: List[Dict] = Field(..., description="阶段列表")
    cross_plots: List[str] = Field(default_factory=list, description="跨阶段伏笔")
    total_chapters: int = Field(..., description="计划总章节数")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="创建时间")
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="更新时间")

class PlotItem(BaseModel):
    """伏笔项目"""
    id: str = Field(..., description="伏笔ID")
    description: str = Field(..., description="伏笔描述")
    intro_chapter: int = Field(..., description="引入章节")
    due_range: Tuple[int, int] = Field(..., description="预计解决范围(起始章节,结束章节)")
    status: str = Field(default="pending", description="状态: pending/active/resolved")
    resolution: str = Field(default="", description="解决方式")
    related_characters: List[str] = Field(default_factory=list, description="相关角色")
    importance: int = Field(default=5, description="重要性(1-10)")


# ----------------- 辅助模块 -----------------

class PlotManager:
    """伏笔生命周期管理"""
    def __init__(self, novel_dir: str = None):
        self.active_plots = []  # {plot_id, intro_chapter, due_chapter, status}
        self.plot_counter = 0
        self.novel_dir = novel_dir
        self.plots_file = os.path.join(novel_dir, "plots.json") if novel_dir else None

        # 如果有小说目录，尝试加载现有伏笔
        if self.plots_file and os.path.exists(self.plots_file):
            self._load_plots()

    def _load_plots(self):
        """加载现有伏笔"""
        try:
            with open(self.plots_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                self.active_plots = data.get("plots", [])
                self.plot_counter = data.get("counter", 0)
            logger.info(f"从{self.plots_file}加载了{len(self.active_plots)}个伏笔")
        except Exception as e:
            logger.error(f"加载伏笔文件时出错: {e}")

    def _save_plots(self):
        """保存伏笔到文件"""
        if not self.plots_file:
            return

        try:
            with open(self.plots_file, "w", encoding="utf-8") as f:
                json.dump({
                    "plots": self.active_plots,
                    "counter": self.plot_counter,
                    "updated_at": datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存{len(self.active_plots)}个伏笔到{self.plots_file}")
        except Exception as e:
            logger.error(f"保存伏笔文件时出错: {e}")

    def add_plot(self, description: str, intro_chapter: int, due_range: tuple, importance: int = 5, related_characters: List[str] = None) -> str:
        """注册新伏笔

        参数:
            description: 伏笔描述
            intro_chapter: 引入章节
            due_range: 预计解决范围(起始章节,结束章节)
            importance: 重要性(1-10)
            related_characters: 相关角色列表

        返回:
            伏笔ID
        """
        self.plot_counter += 1
        plot_id = f"P{self.plot_counter:04d}"

        new_plot = {
            "id": plot_id,
            "description": description,
            "intro_chapter": intro_chapter,
            "due_range": due_range,
            "status": "pending",
            "importance": importance,
            "related_characters": related_characters or [],
            "created_at": datetime.now().isoformat()
        }

        self.active_plots.append(new_plot)
        self._save_plots()

        # 如果启用了高级功能，同步到伏笔管理系统
        if ADVANCED_FEATURES_ENABLED and self.novel_dir and 'register_clue' in globals():
            try:
                register_clue(
                    self.novel_dir,
                    plot_id,
                    description,
                    intro_chapter,
                    due_range,
                    importance,
                    related_characters
                )
                logger.info(f"已将伏笔{plot_id}同步到高级伏笔管理系统")
            except Exception as e:
                logger.warning(f"同步伏笔到高级系统时出错: {e}")

        return plot_id

    def update_plot_status(self, plot_id: str, status: str, resolution: str = ""):
        """更新伏笔状态

        参数:
            plot_id: 伏笔ID
            status: 新状态(pending/active/resolved)
            resolution: 解决方式(如果状态为resolved)
        """
        for plot in self.active_plots:
            if plot["id"] == plot_id:
                plot["status"] = status
                if status == "resolved" and resolution:
                    plot["resolution"] = resolution
                plot["updated_at"] = datetime.now().isoformat()
                self._save_plots()

                # 如果启用了高级功能，同步到伏笔管理系统
                if ADVANCED_FEATURES_ENABLED and self.novel_dir and 'update_clue_status' in globals():
                    try:
                        update_clue_status(self.novel_dir, plot_id, status, resolution)
                    except Exception as e:
                        logger.warning(f"同步伏笔状态到高级系统时出错: {e}")

                return True
        return False

    def get_due_plots(self, current_chapter: int) -> List[Dict]:
        """获取当前章节需要处理的伏笔

        参数:
            current_chapter: 当前章节编号

        返回:
            需要处理的伏笔列表
        """
        due_plots = [
            p for p in self.active_plots
            if p["status"] in ["pending", "active"]
            and p["due_range"][0] <= current_chapter <= p["due_range"][1]
        ]

        # 按重要性排序
        due_plots.sort(key=lambda x: x.get("importance", 5), reverse=True)
        return due_plots

    def get_active_plots(self) -> List[Dict]:
        """获取所有活跃的伏笔"""
        return [p for p in self.active_plots if p["status"] != "resolved"]


class ChapterBuffer:
    """章节缓冲池"""
    def __init__(self, buffer_size: int = 10):
        self.buffer = []
        self.size = buffer_size

    def add(self, chapter: ChapterOutline):
        """添加章节到缓冲池"""
        if len(self.buffer) < self.size:
            self.buffer.append(chapter)
            return True
        return False

    def pop(self) -> Optional[ChapterOutline]:
        """获取并移除第一个章节"""
        if self.buffer:
            return self.buffer.pop(0)
        return None

    def peek(self) -> Optional[ChapterOutline]:
        """查看第一个章节，不移除"""
        if self.buffer:
            return self.buffer[0]
        return None

    def is_empty(self) -> bool:
        """检查缓冲池是否为空"""
        return len(self.buffer) == 0

    def __len__(self) -> int:
        """获取缓冲池中的章节数量"""
        return len(self.buffer)


# ----------------- 核心生成器 -----------------

class OutlineGenerator:
    """大纲生成器核心类"""
    def __init__(self, world_setting: WorldSetting, novel_dir: str, llm_caller: Callable = None):
        """
        初始化大纲生成器

        参数:
            world_setting: 世界观设定
            novel_dir: 小说目录
            llm_caller: 调用LLM的函数，如果为None则使用默认的API客户端
        """
        self.world = world_setting
        self.novel_dir = novel_dir
        self.llm_caller = llm_caller or self._default_llm_caller

        # 初始化伏笔管理器
        self.plot_mgr = PlotManager(novel_dir)

        # 初始化章节缓冲池
        self.buffer = ChapterBuffer(buffer_size=5)

        # 初始化状态变量
        self.current_chapter = 0
        self.current_phase_idx = 0
        self.volumes = []

        # 创建必要的目录
        self._ensure_directories()

        # 加载或创建总纲
        self.master_outline_file = os.path.join(novel_dir, "master_outline.json")
        if os.path.exists(self.master_outline_file):
            self._load_master_outline()
        else:
            self.master_outline = self._generate_master_outline()
            self._save_master_outline()

        # 加载或创建第一卷卷纲
        self.current_volume = self._load_or_generate_volume(0)

        # 预填充缓冲池
        self._fill_buffer()

    def _ensure_directories(self):
        """确保必要的目录存在"""
        os.makedirs(self.novel_dir, exist_ok=True)
        os.makedirs(os.path.join(self.novel_dir, "chapters"), exist_ok=True)
        os.makedirs(os.path.join(self.novel_dir, "outlines"), exist_ok=True)

    def _default_llm_caller(self, prompt: str) -> str:
        """默认的LLM调用函数

        参数:
            prompt: 提示词

        返回:
            LLM响应
        """
        if not MODEL_CONFIG_AVAILABLE:
            raise ImportError("无法使用默认LLM调用函数，请提供自定义的llm_caller")

        # 获取API客户端
        api_client = get_api_client()

        # 使用默认模型
        model_name = AI_MODEL_CONFIG.get("default_model", "glm-4")
        temperature = 0.7  # 默认温度

        # 调用API
        try:
            response = api_client.chat_completion(
                model=model_name,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature
            )
            return response.get("choices", [{}])[0].get("message", {}).get("content", "")
        except Exception as e:
            logger.error(f"调用LLM时出错: {e}")
            raise

    def _load_master_outline(self):
        """加载总纲"""
        try:
            with open(self.master_outline_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                self.master_outline = data
            logger.info(f"从{self.master_outline_file}加载总纲成功")
        except Exception as e:
            logger.error(f"加载总纲时出错: {e}")
            # 如果加载失败，创建新的总纲
            self.master_outline = self._generate_master_outline()
            self._save_master_outline()

    def _save_master_outline(self):
        """保存总纲"""
        try:
            with open(self.master_outline_file, "w", encoding="utf-8") as f:
                json.dump(self.master_outline, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存总纲到{self.master_outline_file}")

            # 同时保存一份可读性更强的文本版本
            readable_outline = self._convert_outline_to_readable(self.master_outline)
            with open(os.path.join(self.novel_dir, "novel_outline.txt"), "w", encoding="utf-8") as f:
                f.write(readable_outline)
        except Exception as e:
            logger.error(f"保存总纲时出错: {e}")

    def _convert_outline_to_readable(self, outline: Dict) -> str:
        """将JSON格式的大纲转换为可读性更强的文本格式

        参数:
            outline: JSON格式的大纲

        返回:
            可读性更强的文本格式
        """
        result = []
        result.append(f"# {outline.get('title', '小说大纲')}\n")
        result.append(f"**类型**: {outline.get('genre', '')}")
        result.append(f"**风格**: {outline.get('style', '')}\n")

        result.append("## 核心主题")
        result.append(outline.get('theme', '未指定核心主题'))
        result.append("")

        result.append("## 总体结构")
        for i, phase in enumerate(outline.get('phases', [])):
            phase_name = phase.get('name', f"阶段{i+1}")
            result.append(f"### {phase_name}")

            # 添加冲突
            result.append("**核心冲突**:")
            for conflict in phase.get('conflicts', []):
                result.append(f"- {conflict}")

            # 添加势力
            result.append("**新势力**:")
            for faction in phase.get('factions', []):
                result.append(f"- {faction}")

            # 添加伏笔
            result.append("**伏笔**:")
            for plot in phase.get('plots', []):
                result.append(f"- {plot}")

            result.append("")

        # 添加跨阶段伏笔
        result.append("## 跨阶段伏笔")
        for plot in outline.get('cross_plots', []):
            result.append(f"- {plot}")

        return "\n".join(result)

    def _generate_master_outline(self) -> Dict:
        """生成总纲核心逻辑"""
        # 使用更加结构化的大纲提示词，从 writing_agent.py 中移植过来
        structured_outline_prompt = f"""你是一位专业的中文小说创作者，请为以下小说创作一个详细且结构化的内容大纲：

标题：{self.world.title}
类型：{self.world.genre}
风格：{self.world.style}
场景：{self.world.scene}
角色：{self.world.characters}

请创建一个具有以下元素的完整世界：

1. 世界观设定：
   - 基础规则：物理/魔法体系、时间线、地理环境、社会结构
   - 特殊设定：科技水平、种族/物种设定
   - 关键矛盾：世界核心冲突

2. 角色设定：
   - 主角团：至少包含3个主要角色，每个都有性格、背景和目标
   - 反派角色：至少包含1个主要反派和多个次要反派
   - 角色关系网：角色间的爱恨/利益关系

3. 情节结构：
   - 每个阶段的主要冲突和目标
   - 伏笔系统：提前规划关键道具/人物/地点的复用
   - 转折点：每个阶段的关键转折

请确保角色设定具体且一致，情节发展合理且连贯，符合{self.world.genre}类型和{self.world.style}风格的特点。

请将大纲组织为以下结构，以便转换为JSON格式：
{{
    "title": "{self.world.title}",
    "genre": "{self.world.genre}",
    "style": "{self.world.style}",
    "theme": "核心主题和中心思想",
    "total_chapters": {self.world.total_chapters},
    "world_setting": {{
        "basic_rules": ["基础规则描述", "基础规则描述"],
        "special_settings": ["特殊设定描述", "特殊设定描述"],
        "key_conflicts": ["关键矛盾描述", "关键矛盾描述"]
    }},
    "characters": {{
        "protagonists": [
            {{
                "name": "主角名称",
                "description": "外貌与性格描述",
                "background": "背景故事",
                "goal": "核心目标",
                "weakness": "弱点"
            }},
            // 更多主角...
        ],
        "antagonists": [
            {{
                "name": "反派名称",
                "description": "外貌与性格描述",
                "background": "背景故事",
                "goal": "核心目标",
                "strength": "强项"
            }},
            // 更多反派...
        ],
        "relationships": [
            {{
                "character1": "角色名称1",
                "character2": "角色名称2",
                "type": "关系类型",
                "description": "关系描述"
            }},
            // 更多关系...
        ]
    }},
    "phases": [
        {{
            "name": "开端",
            "conflicts": ["主要冲突描述", "次要冲突描述"],
            "factions": ["主要势力描述"],
            "plots": ["主要伏笔描述", "次要伏笔描述"],
            "key_events": ["关键事件描述", "关键事件描述"],
            "turning_points": ["转折点描述"]
        }},
        {{
            "name": "发展",
            "conflicts": ["主要冲突描述", "次要冲突描述"],
            "factions": ["主要势力描述"],
            "plots": ["主要伏笔描述", "次要伏笔描述"],
            "key_events": ["关键事件描述", "关键事件描述"],
            "turning_points": ["转折点描述"]
        }},
        {{
            "name": "高潮",
            "conflicts": ["主要冲突描述", "次要冲突描述"],
            "factions": ["主要势力描述"],
            "plots": ["主要伏笔描述", "次要伏笔描述"],
            "key_events": ["关键事件描述", "关键事件描述"],
            "turning_points": ["转折点描述"]
        }},
        {{
            "name": "结局",
            "conflicts": ["主要冲突描述", "次要冲突描述"],
            "factions": ["主要势力描述"],
            "plots": ["主要伏笔描述", "次要伏笔描述"],
            "key_events": ["关键事件描述", "关键事件描述"],
            "turning_points": ["转折点描述"]
        }}
    ],
    "cross_plots": ["跨阶段伏笔一", "跨阶段伏笔二", "跨阶段伏笔三"]
}}

请确保输出是有效的JSON格式，不要添加任何额外的文本或解释。"""

        # 如果需要更简单的JSON格式，使用原来的提示词
        json_outline_prompt = f"""你是一位专业小说架构师，请为《{self.world.title}》生成总纲，要求：
- 小说类型：{self.world.genre}
- 写作风格：{self.world.style}
- 包含{len(self.world.phases)}个阶段：{', '.join(self.world.phases)}
- 每个阶段需有：核心冲突×2、新势力×1、伏笔×2
- 预用3个跨阶段伏笔
- 总章节数：{self.world.total_chapters}

输出JSON格式：
{{
    "title": "小说标题",
    "genre": "小说类型",
    "style": "写作风格",
    "theme": "核心主题和中心思想",
    "total_chapters": 总章节数,
    "phases": [
        {{
            "name": "阶段名称",
            "conflicts": ["冲突描述"],
            "factions": ["新势力"],
            "plots": ["伏笔描述"]
        }}
    ],
    "cross_plots": ["跨阶段伏笔"]
}}

请确保输出是有效的JSON格式，不要添加任何额外的文本或解释。"""

        # 选择使用哪个提示词，默认使用更详细的结构化提示词
        prompt = structured_outline_prompt

        # 添加日志
        logger.info(f"开始生成《{self.world.title}》的总纲")

        try:
            response = self.llm_caller(prompt)
            # 尝试解析JSON
            try:
                outline = json.loads(response)
                # 确保必要的字段存在
                if "title" not in outline:
                    outline["title"] = self.world.title
                if "genre" not in outline:
                    outline["genre"] = self.world.genre
                if "style" not in outline:
                    outline["style"] = self.world.style
                if "total_chapters" not in outline:
                    outline["total_chapters"] = self.world.total_chapters
                if "phases" not in outline or not outline["phases"]:
                    outline["phases"] = [{
                        "name": phase,
                        "conflicts": ["未指定冲突"],
                        "factions": ["未指定势力"],
                        "plots": ["未指定伏笔"]
                    } for phase in self.world.phases]

                # 添加创建时间和更新时间
                outline["created_at"] = datetime.now().isoformat()
                outline["updated_at"] = datetime.now().isoformat()

                return outline
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}\n响应内容: {response}")
                # 尝试提取JSON部分
                import re
                json_match = re.search(r'\{[\s\S]*\}', response)
                if json_match:
                    try:
                        outline = json.loads(json_match.group(0))
                        return outline
                    except:
                        pass

                # 如果仍然失败，创建一个基本的大纲
                return self._create_fallback_outline()
        except Exception as e:
            logger.error(f"生成总纲时出错: {e}")
            return self._create_fallback_outline()

    def _create_fallback_outline(self) -> Dict:
        """创建备用的基本大纲"""
        outline = {
            "title": self.world.title,
            "genre": self.world.genre,
            "style": self.world.style,
            "theme": f"{self.world.title}的核心主题",
            "total_chapters": self.world.total_chapters,
            "phases": [],
            "cross_plots": ["跨越整个故事的主要伏笔", "负面角色的秘密计划", "主角的成长轨迹"],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        # 为每个阶段创建基本内容
        chapters_per_phase = self.world.total_chapters // len(self.world.phases)
        for i, phase in enumerate(self.world.phases):
            outline["phases"].append({
                "name": phase,
                "conflicts": [f"{phase}中的主要冲突", f"{phase}中的次要冲突"],
                "factions": [f"{phase}中出现的新势力"],
                "plots": [f"{phase}中的伏笔一", f"{phase}中的伏笔二"]
            })

        return outline

    def _load_or_generate_volume(self, phase_idx: int) -> VolumeOutline:
        """加载或生成卷纲

        参数:
            phase_idx: 阶段索引

        返回:
            卷纲对象
        """
        # 检查是否已经有该阶段的卷纲
        volume_file = os.path.join(self.novel_dir, "outlines", f"volume_{phase_idx+1}.json")
        if os.path.exists(volume_file):
            try:
                with open(volume_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    volume = VolumeOutline(**data)
                logger.info(f"从{volume_file}加载卷纲成功")
                return volume
            except Exception as e:
                logger.error(f"加载卷纲时出错: {e}")

        # 如果没有或加载失败，生成新的卷纲
        volume = self._generate_volume(phase_idx)

        # 保存卷纲
        try:
            with open(volume_file, "w", encoding="utf-8") as f:
                json.dump(volume.model_dump(), f, ensure_ascii=False, indent=2)
            logger.info(f"已保存卷纲到{volume_file}")
        except Exception as e:
            logger.error(f"保存卷纲时出错: {e}")

        return volume

    def _generate_volume(self, phase_idx: int) -> VolumeOutline:
        """生成新卷纲

        参数:
            phase_idx: 阶段索引

        返回:
            卷纲对象
        """
        # 确保总纲已加载
        if not hasattr(self, "master_outline") or not self.master_outline:
            self.master_outline = self._generate_master_outline()

        # 获取当前阶段信息
        if phase_idx >= len(self.master_outline["phases"]):
            phase_idx = len(self.master_outline["phases"]) - 1
            logger.warning(f"请求的阶段索引{phase_idx}超出范围，使用最后一个阶段")

        phase = self.master_outline["phases"][phase_idx]

        # 计算该卷的章节范围
        total_phases = len(self.master_outline["phases"])
        chapters_per_phase = self.master_outline["total_chapters"] // total_phases
        start_chapter = phase_idx * chapters_per_phase + 1
        end_chapter = (phase_idx + 1) * chapters_per_phase if phase_idx < total_phases - 1 else self.master_outline["total_chapters"]

        # 添加日志
        logger.info(f"开始生成《{self.master_outline['title']}》的卷纲，阶段: {phase['name']}，章节范围: {start_chapter}-{end_chapter}")

        # 生成更详细的卷纲提示词
        detailed_prompt = f"""你是一位专业的中文小说章节规划师，请根据以下小说总纲中的【{phase['name']}】阶段，详细规划第{start_chapter}至第{end_chapter}章的内容框架。

小说信息：
- 标题：{self.master_outline['title']}
- 类型：{self.master_outline['genre']}
- 风格：{self.master_outline['style']}
- 核心主题：{self.master_outline.get('theme', '')}

当前阶段【{phase['name']}】的要素：
- 主要冲突：{', '.join(phase['conflicts'])}
- 活跃势力：{', '.join(phase['factions'])}
- 需处理伏笔：{', '.join(phase['plots'])}
- 跨阶段伏笔：{', '.join(self.master_outline.get('cross_plots', []))}

请为每一章设计以下内容：
1. 章节标题：简洁且引人入胜
2. 章节类型：如战斗、谈判、探索、揭秘、转折等
3. 章节概要：100-150字的简要描述
4. 涉及角色：至少2-3个主要角色
5. 伏笔处理：指明哪些伏笔将在本章被引入、发展或解决

要求：
- 确保章节之间的情节连贯性和逐渐递进的紧张感
- 合理安排伏笔的引入、发展和解决节奏
- 每个章节都应有明确的功能，推动情节或揭示角色
- 章节结尾应设置适当的悬念，引导读者继续阅读

请将结果组织为以下的JSON格式：
{{
    "chapters": [
        {{
            "number": 章节编号,
            "title": "章节标题",
            "type": "章节类型",
            "summary": "章节概要",
            "characters": ["角色名称", "角色名称"],
            "plot_actions": {{
                "伏笔描述": "引入/发展/解决"
            }}
        }}
    ]
}}

请确保输出是有效的JSON格式，不要添加任何额外的文本或解释。"""

        # 原来的简单提示词
        simple_prompt = f"""你是一位专业小说架构师，请根据总纲阶段【{phase['name']}】生成卷纲：

小说信息：
- 标题：{self.master_outline['title']}
- 类型：{self.master_outline['genre']}
- 风格：{self.master_outline['style']}

阶段要素：
- 待解决冲突：{', '.join(phase['conflicts'])}
- 可用势力：{', '.join(phase['factions'])}
- 需处理伏笔：{', '.join(phase['plots'])}

要求：
- 生成{end_chapter - start_chapter + 1}个章节框架（第{start_chapter}章至第{end_chapter}章）
- 每章节需包含：
  * 章节标题
  * 事件类型（战斗/谈判/探索等）
  * 关联角色（至少2人）
  * 伏笔操作（引入/发展/回收）
- 结尾需设置悬念

输出JSON格式：
{{
    "chapters": [
        {{
            "number": 章节编号,
            "title": "章节标题",
            "type": "事件类型",
            "summary": "章节概要",
            "characters": ["角色列表"],
            "plot_actions": {{
                "伏笔描述": "动作类型"
            }}
        }}
    ]
}}

请确保输出是有效的JSON格式，不要添加任何额外的文本或解释。"""

        # 选择使用哪个提示词，默认使用更详细的提示词
        prompt = detailed_prompt

        try:
            response = self.llm_caller(prompt)
            # 尝试解析JSON
            try:
                data = json.loads(response)
                chapters = data.get("chapters", [])

                # 确保章节编号正确
                for i, chapter in enumerate(chapters):
                    chapter["number"] = start_chapter + i

                # 创建卷纲对象
                volume = VolumeOutline(
                    volume_id=phase_idx + 1,
                    phase=phase["name"],
                    chapters=chapters,
                    start_chapter=start_chapter,
                    end_chapter=end_chapter,
                    unresolved_plots=phase["plots"],
                    main_conflicts=phase["conflicts"]
                )

                return volume
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}\n响应内容: {response}")
                # 尝试提取JSON部分
                import re
                json_match = re.search(r'\{[\s\S]*\}', response)
                if json_match:
                    try:
                        data = json.loads(json_match.group(0))
                        chapters = data.get("chapters", [])

                        # 确保章节编号正确
                        for i, chapter in enumerate(chapters):
                            chapter["number"] = start_chapter + i

                        # 创建卷纲对象
                        volume = VolumeOutline(
                            volume_id=phase_idx + 1,
                            phase=phase["name"],
                            chapters=chapters,
                            start_chapter=start_chapter,
                            end_chapter=end_chapter,
                            unresolved_plots=phase["plots"],
                            main_conflicts=phase["conflicts"]
                        )

                        return volume
                    except:
                        pass
        except Exception as e:
            logger.error(f"生成卷纲时出错: {e}")

        # 如果生成失败，创建一个基本的卷纲
        return self._create_fallback_volume(phase_idx, phase, start_chapter, end_chapter)

    def _create_fallback_volume(self, phase_idx: int, phase: Dict, start_chapter: int, end_chapter: int) -> VolumeOutline:
        """创建备用的基本卷纲

        参数:
            phase_idx: 阶段索引
            phase: 阶段信息
            start_chapter: 起始章节
            end_chapter: 结束章节

        返回:
            卷纲对象
        """
        chapters = []
        for i in range(start_chapter, end_chapter + 1):
            chapters.append({
                "number": i,
                "title": f"第{i}章",
                "type": "探索" if i % 3 == 0 else "战斗" if i % 3 == 1 else "谈判",
                "summary": f"第{i}章的内容概要",
                "characters": ["主角", "配角"],
                "plot_actions": {
                    f"伏笔{i}": "引入" if i == start_chapter else "发展" if i < end_chapter else "回收"
                }
            })

        return VolumeOutline(
            volume_id=phase_idx + 1,
            phase=phase["name"],
            chapters=chapters,
            start_chapter=start_chapter,
            end_chapter=end_chapter,
            unresolved_plots=phase.get("plots", []),
            main_conflicts=phase.get("conflicts", [])
        )

    def _fill_buffer(self, min_size: int = 3):
        """填充章节缓冲池

        参数:
            min_size: 最小缓冲池大小
        """
        while len(self.buffer) < min_size:
            # 获取当前卷的章节框架
            if self.current_chapter >= self.current_volume.end_chapter:
                self._advance_volume()

            # 确保当前章节不小于卷起始章节
            if self.current_chapter < self.current_volume.start_chapter:
                logger.warning(f"当前章节{self.current_chapter}小于卷起始章节{self.current_volume.start_chapter}，将当前章节设置为卷起始章节")
                self.current_chapter = self.current_volume.start_chapter

            # 找到当前章节在卷纲中的索引
            chapter_idx = self.current_chapter - self.current_volume.start_chapter
            if chapter_idx < 0 or chapter_idx >= len(self.current_volume.chapters):
                logger.error(f"章节索引超出范围: {chapter_idx}, 当前章节: {self.current_chapter}, 卷起始章节: {self.current_volume.start_chapter}")
                # 如果索引为负，将当前章节设置为卷起始章节
                if chapter_idx < 0:
                    self.current_chapter = self.current_volume.start_chapter
                    chapter_idx = 0
                # 如果索引超出上限，前进到下一卷
                else:
                    self._advance_volume()
                    continue

            # 确保章节索引在有效范围内
            if chapter_idx >= len(self.current_volume.chapters):
                logger.error(f"章节索引{chapter_idx}超出卷纲章节数{len(self.current_volume.chapters)}，将前进到下一卷")
                self._advance_volume()
                continue

            framework = self.current_volume.chapters[chapter_idx]

            # 生成详细大纲
            detail = self._generate_chapter_detail(framework)

            # 处理伏笔
            self._handle_plots(detail)

            # 添加到缓冲池
            self.buffer.add(detail)
            self.current_chapter += 1

    def _advance_volume(self):
        """推进到下一卷"""
        # 检查是否达到大纲级别更换的阈值（50章）
        if self.current_chapter >= 50 and self.current_phase_idx < len(self.master_outline["phases"]) - 1:
            self.current_phase_idx += 1
            logger.info(f"已达到第{self.current_chapter}章，更换到下一个大纲级别: {self.master_outline['phases'][self.current_phase_idx]['name']}")
        elif self.current_phase_idx >= len(self.master_outline["phases"]) - 1:
            # 如果已经是最后一个阶段，尝试扩展总纲
            self._expand_master_outline()
            logger.info(f"已扩展总纲，添加了新的阶段")

        # 加载或生成新的卷纲
        self.current_volume = self._load_or_generate_volume(self.current_phase_idx)
        self.volumes.append(self.current_volume)

        # 如果启用了高级功能，更新记忆系统
        if ADVANCED_FEATURES_ENABLED and 'update_memory_from_chapter' in globals():
            try:
                # 将卷纲信息添加到记忆系统
                volume_info = f"当前小说进入新的阶段: {self.current_volume.phase}\n"
                volume_info += f"主要冲突: {', '.join(self.current_volume.main_conflicts)}\n"
                volume_info += f"未解决的伏笔: {', '.join(self.current_volume.unresolved_plots)}"

                # 使用正确的参数调用update_memory_from_chapter
                update_memory_from_chapter(volume_info, 0, self.novel_dir)
                logger.info(f"已将卷纲信息添加到记忆")
            except Exception as e:
                logger.warning(f"更新记忆系统时出错: {e}")

    def _expand_master_outline(self):
        """扩展总纲，添加新的阶段"""
        # 当前阶段数
        current_phases = len(self.master_outline["phases"])

        # 生成新阶段的提示词
        prompt = f"""你是一位专业小说架构师，请为《{self.master_outline['title']}》扩展总纲，添加新的阶段。

当前小说已有{current_phases}个阶段：
{', '.join([phase['name'] for phase in self.master_outline['phases']])}

请生成一个新的阶段，包含：
- 阶段名称
- 核心冲突×2
- 新势力×1
- 伏笔×2

输出JSON格式：
{{
    "name": "阶段名称",
    "conflicts": ["冲突描述", "冲突描述"],
    "factions": ["新势力"],
    "plots": ["伏笔描述", "伏笔描述"]
}}

请确保输出是有效的JSON格式，不要添加任何额外的文本或解释。"""

        try:
            response = self.llm_caller(prompt)
            # 尝试解析JSON
            try:
                new_phase = json.loads(response)

                # 添加新阶段
                self.master_outline["phases"].append(new_phase)

                # 更新总章节数
                chapters_per_phase = self.master_outline["total_chapters"] // current_phases
                self.master_outline["total_chapters"] += chapters_per_phase

                # 更新时间
                self.master_outline["updated_at"] = datetime.now().isoformat()

                # 保存更新后的总纲
                self._save_master_outline()

                logger.info(f"成功扩展总纲，添加了新阶段: {new_phase['name']}")
                return True
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}\n响应内容: {response}")
        except Exception as e:
            logger.error(f"扩展总纲时出错: {e}")

        # 如果扩展失败，创建一个基本的新阶段
        new_phase = {
            "name": f"阶段{current_phases+1}",
            "conflicts": ["新的主要冲突", "新的次要冲突"],
            "factions": ["新出现的势力"],
            "plots": ["新的伏笔一", "新的伏笔二"]
        }

        # 添加新阶段
        self.master_outline["phases"].append(new_phase)

        # 更新总章节数
        chapters_per_phase = self.master_outline["total_chapters"] // current_phases
        self.master_outline["total_chapters"] += chapters_per_phase

        # 更新时间
        self.master_outline["updated_at"] = datetime.now().isoformat()

        # 保存更新后的总纲
        self._save_master_outline()

        logger.info(f"创建了基本的新阶段: {new_phase['name']}")
        return True

    def _generate_chapter_detail(self, framework: Dict) -> ChapterOutline:
        """生成章节详细大纲

        参数:
            framework: 章节框架

        返回:
            章节大纲对象
        """
        # 获取章节编号
        chapter_num = framework.get("number", self.current_chapter + 1)

        # 添加日志
        logger.info(f"开始生成第{chapter_num}章的详细大纲，标题: {framework.get('title', f'第{chapter_num}章')}")

        # 生成更详细的章节大纲提示词，从 writing_agent.py 中移植过来
        detailed_prompt = f"""你是一位专业的中文小说章节设计师，请根据以下框架信息，设计一个详细的章节大纲：

# 章节基本信息
- 章节编号：{chapter_num}
- 章节标题：{framework.get('title', f'第{chapter_num}章')}
- 章节类型：{framework.get('type', '探索')}
- 概要：{framework.get('summary', '未提供概要')}
- 相关角色：{', '.join(framework.get('characters', ['主角', '配角']))}
- 伏笔操作：{json.dumps(framework.get('plot_actions', {}), ensure_ascii=False)}

# 设计要求
1. 请设计三个关键场景，每个场景应包含：
   - 场景地点：描述具体的环境和氛围
   - 参与角色：至少2-3个角色
   - 场景冲突：清晰的冲突点或矛盾
   - 场景目的：该场景在情节中的作用

2. 角色互动设计：
   - 对话风格应体现角色的性格特点
   - 角色之间的关系变化
   - 角色的内心活动和情绪变化

3. 伏笔处理：
   - 清晰指出哪些伏笔将在本章被引入、发展或解决
   - 每个伏笔的具体处理方式
   - 伏笔与情节发展的自然结合

4. 章节节奏：
   - 开端应引人入胜，建立情境
   - 中段应有明确的冲突升级
   - 结尾必须设置悬念，引导读者继续阅读

5. 下一章预告：
   - 简要描述下一章可能发生的事件
   - 设置读者期待

请将结果组织为以下的JSON格式：
{{
    "chapter_num": {chapter_num},
    "title": "章节标题",
    "summary": "200-300字的详细摘要",
    "key_scenes": [
        {{
            "location": "场景地点及环境描述",
            "participants": ["参与角色列表"],
            "conflict": "场景主要冲突或矛盾",
            "purpose": "场景在情节中的作用"
        }}
    ],
    "plot_actions": {{
        "伏笔描述": "引入/发展/解决",
        "具体处理方式": "详细描述如何处理该伏笔"
    }},
    "characters_involved": ["所有相关角色列表"],
    "next_preview": "下一章的预告内容"
}}

请确保输出是有效的JSON格式，不要添加任何额外的文本或解释。"""

        # 原来的简单提示词
        simple_prompt = f"""你是一位资深小说作家，请根据以下框架生成详细章节大纲：

# 章节信息
- 章节编号：{chapter_num}
- 章节标题：{framework.get('title', f'第{chapter_num}章')}
- 事件类型：{framework.get('type', '探索')}
- 概要：{framework.get('summary', '未提供概要')}
- 相关角色：{', '.join(framework.get('characters', ['主角', '配角']))}
- 伏笔操作：{json.dumps(framework.get('plot_actions', {}), ensure_ascii=False)}

# 补充要求
1. 包含3个关键场景，每个场景需有：
   - 地点描述
   - 参与角色
   - 冲突点
2. 对话需体现角色性格
3. 结尾必须有悬念

输出JSON格式：
{{
    "chapter_num": {chapter_num},
    "title": "章节标题",
    "summary": "200字摘要",
    "key_scenes": [
        {{
            "location": "地点",
            "participants": ["角色"],
            "conflict": "冲突描述"
        }}
    ],
    "plot_actions": {{
        "伏笔描述": "处理方式"
    }},
    "characters_involved": ["角色列表"],
    "next_preview": "下一章预告"
}}

请确保输出是有效的JSON格式，不要添加任何额外的文本或解释。"""

        # 选择使用哪个提示词，默认使用更详细的提示词
        prompt = detailed_prompt

        try:
            response = self.llm_caller(prompt)
            # 尝试解析JSON
            try:
                data = json.loads(response)
                # 确保必要的字段存在
                if "chapter_num" not in data:
                    data["chapter_num"] = chapter_num
                if "title" not in data:
                    data["title"] = framework.get("title", f"第{chapter_num}章")
                if "summary" not in data:
                    data["summary"] = framework.get("summary", f"第{chapter_num}章的内容概要")
                if "key_scenes" not in data or not data["key_scenes"]:
                    data["key_scenes"] = [
                        {
                            "location": "主要场景地点",
                            "participants": framework.get("characters", ["主角", "配角"]),
                            "conflict": "主要冲突"
                        }
                    ]
                if "plot_actions" not in data:
                    data["plot_actions"] = framework.get("plot_actions", {})
                if "characters_involved" not in data:
                    data["characters_involved"] = framework.get("characters", [])
                if "next_preview" not in data:
                    data["next_preview"] = "下一章预告"

                # 创建章节大纲对象
                return ChapterOutline(**data)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {e}\n响应内容: {response}")
                # 尝试提取JSON部分
                import re
                json_match = re.search(r'\{[\s\S]*\}', response)
                if json_match:
                    try:
                        data = json.loads(json_match.group(0))
                        return ChapterOutline(**data)
                    except:
                        pass
        except Exception as e:
            logger.error(f"生成章节大纲时出错: {e}")

        # 如果生成失败，创建一个基本的章节大纲
        return self._create_fallback_chapter_detail(framework)

    def _create_fallback_chapter_detail(self, framework: Dict) -> ChapterOutline:
        """创建备用的基本章节大纲

        参数:
            framework: 章节框架

        返回:
            章节大纲对象
        """
        chapter_num = framework.get("number", self.current_chapter + 1)
        title = framework.get("title", f"第{chapter_num}章")
        summary = framework.get("summary", f"第{chapter_num}章的内容概要")
        characters = framework.get("characters", ["主角", "配角"])
        plot_actions = framework.get("plot_actions", {})

        # 创建基本的关键场景
        key_scenes = [
            {
                "location": "主要场景地点",
                "participants": characters,
                "conflict": "主要冲突"
            },
            {
                "location": "次要场景地点",
                "participants": characters,
                "conflict": "次要冲突"
            },
            {
                "location": "结尾场景地点",
                "participants": characters,
                "conflict": "结尾冲突"
            }
        ]

        return ChapterOutline(
            chapter_num=chapter_num,
            title=title,
            summary=summary,
            key_scenes=key_scenes,
            plot_actions=plot_actions,
            characters_involved=characters,
            next_preview=f"下一章预告"
        )

    def _handle_plots(self, chapter: ChapterOutline):
        """处理伏笔生命周期

        参数:
            chapter: 章节大纲
        """
        # 处理章节中的伏笔操作
        for plot_desc, action in chapter.plot_actions.items():
            # 如果是引入新伏笔
            if action.lower() in ["引入", "introduce", "introduction", "new"]:
                # 计算预计解决范围
                due_start = chapter.chapter_num + 3  # 至少3章后才开始解决
                due_end = chapter.chapter_num + 10   # 最迟10章后解决

                # 注册新伏笔
                plot_id = self.plot_mgr.add_plot(
                    description=plot_desc,
                    intro_chapter=chapter.chapter_num,
                    due_range=(due_start, due_end),
                    related_characters=chapter.characters_involved
                )
                logger.info(f"在第{chapter.chapter_num}章注册了新伏笔: {plot_desc} (ID: {plot_id})")

            # 如果是解决伏笔
            elif action.lower() in ["解决", "回收", "resolve", "resolution", "solved"]:
                # 查找匹配的伏笔
                for plot in self.plot_mgr.get_active_plots():
                    if plot_desc.lower() in plot["description"].lower():
                        # 更新伏笔状态
                        self.plot_mgr.update_plot_status(
                            plot_id=plot["id"],
                            status="resolved",
                            resolution=f"在第{chapter.chapter_num}章解决"
                        )
                        logger.info(f"在第{chapter.chapter_num}章解决了伏笔: {plot['description']} (ID: {plot['id']})")
                        break

            # 如果是发展伏笔
            elif action.lower() in ["发展", "develop", "development", "progress"]:
                # 查找匹配的伏笔
                for plot in self.plot_mgr.get_active_plots():
                    if plot_desc.lower() in plot["description"].lower():
                        # 更新伏笔状态
                        if plot["status"] == "pending":
                            self.plot_mgr.update_plot_status(
                                plot_id=plot["id"],
                                status="active"
                            )
                            logger.info(f"在第{chapter.chapter_num}章发展了伏笔: {plot['description']} (ID: {plot['id']})")
                        break

    # ----------------- 公共API方法 -----------------

    def get_next_chapter_outline(self) -> ChapterOutline:
        """获取下一章的大纲

        返回:
            章节大纲对象
        """
        # 如果缓冲池为空，填充缓冲池
        if self.buffer.is_empty():
            self._fill_buffer()

        # 返回缓冲池中的第一个章节大纲
        return self.buffer.peek()

    def advance_chapter(self) -> ChapterOutline:
        """前进到下一章

        返回:
            下一章的大纲对象
        """
        # 如果缓冲池为空，填充缓冲池
        if self.buffer.is_empty():
            self._fill_buffer()

        # 获取并移除第一个章节大纲
        chapter = self.buffer.pop()

        # 确保缓冲池中始终有足够的章节
        self._fill_buffer()

        return chapter

    def get_master_outline(self) -> Dict:
        """获取总纲

        返回:
            总纲字典
        """
        return self.master_outline

    def get_current_volume(self) -> VolumeOutline:
        """获取当前卷纲

        返回:
            当前卷纲对象
        """
        return self.current_volume

    def get_chapter_outline_by_number(self, chapter_num: int) -> Optional[ChapterOutline]:
        """根据章节编号获取章节大纲

        参数:
            chapter_num: 章节编号

        返回:
            章节大纲对象，如果不存在则返回None
        """
        # 检查缓冲池中是否有该章节
        for chapter in self.buffer.buffer:
            if chapter.chapter_num == chapter_num:
                return chapter

        # 如果缓冲池中没有，尝试从文件中加载
        chapter_file = os.path.join(self.novel_dir, "outlines", f"chapter_{chapter_num}.json")
        if os.path.exists(chapter_file):
            try:
                with open(chapter_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    return ChapterOutline(**data)
            except Exception as e:
                logger.error(f"加载章节大纲时出错: {e}")

        # 如果文件不存在，尝试生成该章节的大纲
        # 首先需要确定该章节属于哪个卷
        for volume in self.volumes:
            if volume.start_chapter <= chapter_num <= volume.end_chapter:
                # 找到对应的章节框架
                chapter_idx = chapter_num - volume.start_chapter
                if 0 <= chapter_idx < len(volume.chapters):
                    framework = volume.chapters[chapter_idx]
                    # 生成章节大纲
                    chapter = self._generate_chapter_detail(framework)
                    # 保存到文件
                    try:
                        with open(chapter_file, "w", encoding="utf-8") as f:
                            json.dump(chapter.model_dump(), f, ensure_ascii=False, indent=2)
                    except Exception as e:
                        logger.error(f"保存章节大纲时出错: {e}")
                    return chapter

        # 如果所有方法都失败，返回None
        return None

    def save_chapter_outline(self, chapter: ChapterOutline) -> bool:
        """保存章节大纲

        参数:
            chapter: 章节大纲对象

        返回:
            是否保存成功
        """
        chapter_file = os.path.join(self.novel_dir, "outlines", f"chapter_{chapter.chapter_num}.json")
        try:
            with open(chapter_file, "w", encoding="utf-8") as f:
                json.dump(chapter.model_dump(), f, ensure_ascii=False, indent=2)
            logger.info(f"已保存章节大纲到{chapter_file}")
            return True
        except Exception as e:
            logger.error(f"保存章节大纲时出错: {e}")
            return False

    def get_due_plots(self, chapter_num: int) -> List[Dict]:
        """获取当前章节需要处理的伏笔

        参数:
            chapter_num: 章节编号

        返回:
            需要处理的伏笔列表
        """
        return self.plot_mgr.get_due_plots(chapter_num)

    def get_active_plots(self) -> List[Dict]:
        """获取所有活跃的伏笔

        返回:
            活跃的伏笔列表
        """
        return self.plot_mgr.get_active_plots()

    def convert_outline_to_text(self, chapter: ChapterOutline) -> str:
        """将章节大纲转换为文本格式

        参数:
            chapter: 章节大纲对象

        返回:
            文本格式的章节大纲
        """
        result = []
        result.append(f"## 第{chapter.chapter_num}章：{chapter.title}\n")

        result.append("### 章节摘要")
        result.append(chapter.summary)
        result.append("")

        result.append("### 关键场景")
        for i, scene in enumerate(chapter.key_scenes):
            result.append(f"#### 场景{i+1}：{scene.get('location', '未指定地点')}")
            result.append(f"**参与角色**: {', '.join(scene.get('participants', []))}")
            result.append(f"**冲突点**: {scene.get('conflict', '未指定冲突')}")
            result.append("")

        result.append("### 伏笔操作")
        for plot, action in chapter.plot_actions.items():
            result.append(f"- {plot}: {action}")
        result.append("")

        result.append("### 相关角色")
        for character in chapter.characters_involved:
            result.append(f"- {character}")
        result.append("")

        if chapter.next_preview:
            result.append("### 下一章预告")
            result.append(chapter.next_preview)

        return "\n".join(result)


# ----------------- 智能体定义 -----------------

class OutlineGeneratorAgent(Agent):
    """大纲生成器智能体
    负责生成小说的总纲、卷纲和章节大纲
    """

    def __init__(self, name: str = "OutlineGeneratorAgent", model: str = None):
        """初始化大纲生成器智能体

        参数:
            name: 智能体名称
            model: 使用的模型
        """
        super().__init__(name, model)
        self.outline_generator = None
        self.prompt_manager = None
        try:
            from src.core.prompt_manager import PromptManager
            self.prompt_manager = PromptManager()
        except ImportError:
            logger.warning("无法导入PromptManager，将使用默认提示词")

    def send_message(self, recipient, message):
        """发送消息给其他智能体

        参数:
            recipient: 接收者名称
            message: 消息内容
        """
        logger.info(f"[{self.name}] 发送消息给 {recipient}: {message['action']}")

        # 尝试使用AGENT_REGISTRY分发消息
        try:
            # 导入必要的模块
            from src.core.agent import AGENT_REGISTRY

            # 使用AGENT_REGISTRY分发消息
            AGENT_REGISTRY.dispatch_message(self.name, recipient, message)
            return
        except Exception as e:
            logger.warning(f"[{self.name}] 使用AGENT_REGISTRY分发消息时出错: {e}")

        # 如果AGENT_REGISTRY分发失败，尝试直接获取接收者并调用其handle_message方法
        try:
            # 导入必要的模块
            from src.core.agent import AGENT_REGISTRY

            # 获取接收者
            recipient_agent = AGENT_REGISTRY.get_agent(recipient)
            if recipient_agent:
                recipient_agent.handle_message(message)
                return
        except Exception as e:
            logger.warning(f"[{self.name}] 直接调用接收者的handle_message时出错: {e}")

        # 如果所有方法都失败，记录错误
        logger.error(f"[{self.name}] 无法发送消息给 {recipient}")

    def initialize_generator(self, novel_title: str, novel_genre: str, novel_style: str, novel_dir: str,
                             total_chapters: int = 30) -> OutlineGenerator:
        """初始化大纲生成器

        参数:
            novel_title: 小说标题
            novel_genre: 小说类型
            novel_style: 写作风格
            novel_dir: 小说目录
            total_chapters: 总章节数

        返回:
            大纲生成器对象
        """
        # 创建世界观设定
        world_setting = WorldSetting(
            title=novel_title,
            genre=novel_genre,
            style=novel_style,
            total_chapters=total_chapters
        )

        # 创建大纲生成器
        self.outline_generator = OutlineGenerator(world_setting, novel_dir, self._llm_caller)
        return self.outline_generator

    def _llm_caller(self, prompt: str) -> str:
        """调用LLM的函数

        参数:
            prompt: 提示词

        返回:
            LLM响应
        """
        try:
            from src.utils.api_client import get_api_client

            # 获取API客户端
            api_client = get_api_client()

            # 使用指定模型或默认模型
            model_name = self.model or AI_MODEL_CONFIG.get("default_model", "glm-4")
            temperature = 0.7  # 默认温度

            # 调用API
            response = api_client.chat_completion(
                model=model_name,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature
            )
            return response.get("choices", [{}])[0].get("message", {}).get("content", "")
        except Exception as e:
            logger.error(f"调用LLM时出错: {e}")
            raise

    def handle_message(self, message: Dict[str, Any]):
        """处理接收到的消息

        参数:
            message: 消息内容
        """
        action = message.get("action")
        if action == "initialize":
            # 初始化大纲生成器
            novel_title = message.get("title", "无标题小说")
            novel_genre = message.get("genre", "奇幻")
            novel_style = message.get("style", "轻松")
            novel_dir = message.get("novel_dir", "output/novel")
            total_chapters = message.get("total_chapters", 30)

            self.initialize_generator(
                novel_title=novel_title,
                novel_genre=novel_genre,
                novel_style=novel_style,
                novel_dir=novel_dir,
                total_chapters=total_chapters
            )

            # 响应消息
            self.send_message(
                message.get("sender"),
                {
                    "action": "initialize_response",
                    "status": "success",
                    "message": f"已初始化{novel_title}的大纲生成器"
                }
            )
        elif action == "get_next_chapter":
            # 获取下一章大纲
            if not self.outline_generator:
                self.send_message(
                    message.get("sender"),
                    {
                        "action": "get_next_chapter_response",
                        "status": "error",
                        "message": "大纲生成器未初始化"
                    }
                )
                return

            chapter = self.outline_generator.get_next_chapter_outline()
            chapter_text = self.outline_generator.convert_outline_to_text(chapter)

            self.send_message(
                message.get("sender"),
                {
                    "action": "get_next_chapter_response",
                    "status": "success",
                    "chapter": chapter.model_dump(),
                    "chapter_text": chapter_text
                }
            )
        elif action == "advance_chapter":
            # 前进到下一章
            if not self.outline_generator:
                self.send_message(
                    message.get("sender"),
                    {
                        "action": "advance_chapter_response",
                        "status": "error",
                        "message": "大纲生成器未初始化"
                    }
                )
                return

            chapter = self.outline_generator.advance_chapter()
            chapter_text = self.outline_generator.convert_outline_to_text(chapter)

            self.send_message(
                message.get("sender"),
                {
                    "action": "advance_chapter_response",
                    "status": "success",
                    "chapter": chapter.model_dump(),
                    "chapter_text": chapter_text
                }
            )
        elif action == "get_master_outline":
            # 获取总纲
            if not self.outline_generator:
                self.send_message(
                    message.get("sender"),
                    {
                        "action": "get_master_outline_response",
                        "status": "error",
                        "message": "大纲生成器未初始化"
                    }
                )
                return

            master_outline = self.outline_generator.get_master_outline()

            self.send_message(
                message.get("sender"),
                {
                    "action": "get_master_outline_response",
                    "status": "success",
                    "master_outline": master_outline
                }
            )
        elif action == "check_outline_status":
            # 检查大纲状态
            if not self.outline_generator:
                # 直接返回结果，不发送消息
                logger.info(f"[大纲生成器] 大纲生成器未初始化，返回默认状态")
                # 如果是从 writing_pipeline 发来的消息，不尝试发送响应
                # 因为 writing_pipeline 不是一个注册的智能体
                if message.get("sender") != "writing_pipeline":
                    self.send_message(
                        message.get("sender"),
                        {
                            "action": "check_outline_status_response",
                            "status": "error",
                            "message": "大纲生成器未初始化",
                            "need_new_outline": True
                        }
                    )
                return

            chapter_num = message.get("chapter_num", 1)
            status_info = self.outline_generator.check_outline_status(chapter_num)

            # 如果是从 writing_pipeline 发来的消息，不尝试发送响应
            # 因为 writing_pipeline 不是一个注册的智能体
            if message.get("sender") != "writing_pipeline":
                self.send_message(
                    message.get("sender"),
                    {
                        "action": "check_outline_status_response",
                        "status": "success",
                        "need_new_outline": status_info["need_new_outline"],
                        "current_phase": status_info["current_phase"],
                        "remaining_chapters": status_info["remaining_chapters"],
                        "message": status_info["message"]
                    }
                )

# ----------------- 工厂函数和帮助函数 -----------------

def register_outline_generator_agent():
    """注册大纲生成器智能体

    这个函数会创建并注册大纲生成器智能体

    返回:
        注册的智能体对象，如果注册失败则返回None
    """
    try:
        # 导入必要的模块
        from src.core.agent import AGENT_REGISTRY
        from src.core.model_config import AI_MODEL_CONFIG

        # 获取模型配置
        agent_models = AI_MODEL_CONFIG.get("agent_models", {})
        default_model = AI_MODEL_CONFIG.get("default_model", "glm-4")

        # 获取大纲生成器模型
        outline_model = agent_models.get("OutlineGeneratorAgent", default_model)

        # 检查是否已经注册
        existing_agent = AGENT_REGISTRY.get_agent("OutlineGeneratorAgent")
        if existing_agent:
            logger.info(f"大纲生成器智能体已经注册，使用模型: {existing_agent.model}")
            print(f"大纲生成器智能体已经注册，使用模型: {existing_agent.model}")
            return existing_agent

        # 注册大纲生成器智能体
        agent = OutlineGeneratorAgent("OutlineGeneratorAgent", outline_model)
        AGENT_REGISTRY.register(agent)

        logger.info(f"智能体 OutlineGeneratorAgent 已注册，使用模型: {outline_model}")
        print(f"智能体 OutlineGeneratorAgent 已注册，使用模型: {outline_model}")
        return agent
    except Exception as e:
        logger.error(f"注册大纲生成器智能体时出错: {e}")
        print(f"注册大纲生成器智能体时出错: {e}")
        return None

def create_outline_generator(novel_title: str, novel_genre: str, novel_style: str, novel_dir: str,
                            total_chapters: int = 30, llm_caller: Callable = None) -> OutlineGenerator:
    """创建大纲生成器

    参数:
        novel_title: 小说标题
        novel_genre: 小说类型
        novel_style: 写作风格
        novel_dir: 小说目录
        total_chapters: 总章节数
        llm_caller: 调用LLM的函数

    返回:
        大纲生成器对象
    """
    # 创建世界观设定
    world_setting = WorldSetting(
        title=novel_title,
        genre=novel_genre,
        style=novel_style,
        total_chapters=total_chapters
    )

    # 创建大纲生成器
    return OutlineGenerator(world_setting, novel_dir, llm_caller)


# 如果直接运行该文件，展示示例用法
if __name__ == "__main__":
    import sys
    from pathlib import Path

    # 如果没有提供小说目录，使用当前目录下的test_novel
    if len(sys.argv) > 1:
        novel_dir = sys.argv[1]
    else:
        novel_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_novel")

    # 确保目录存在
    os.makedirs(novel_dir, exist_ok=True)

    # 定义一个简单的LLM调用函数
    def simple_llm_caller(prompt: str) -> str:
        print("\n=== 模拟调用LLM ===\n")
        print(f"\n提示词: {prompt[:200]}...\n")

        # 模拟响应
        if "master_outline" in prompt.lower():
            return """{
                "title": "测试小说",
                "genre": "奇幻",
                "style": "轻松",
                "theme": "成长与冒险",
                "total_chapters": 10,
                "phases": [
                    {
                        "name": "序章",
                        "conflicts": ["主角发现神秘力量", "与初始敌人的冲突"],
                        "factions": ["神秘组织出现"],
                        "plots": ["神秘符文的来历", "失踪的导师"]
                    },
                    {
                        "name": "发展",
                        "conflicts": ["力量失控", "组织内部分裂"],
                        "factions": ["反派势力崛起"],
                        "plots": ["隐藏的真相", "身世之谜"]
                    },
                    {
                        "name": "高潮",
                        "conflicts": ["最终决战", "内心挣扎"],
                        "factions": ["古老种族回归"],
                        "plots": ["预言实现", "终极选择"]
                    }
                ],
                "cross_plots": ["贯穿全书的命运线索", "隐藏BOSS的真实身份", "主角的特殊血脉"]
            }"""
        elif "volume" in prompt.lower():
            return """{
                "chapters": [
                    {
                        "number": 1,
                        "title": "神秘的开始",
                        "type": "探索",
                        "summary": "主角在一次意外中发现了神秘符文，引发了一系列奇怪事件。",
                        "characters": ["主角", "神秘老人"],
                        "plot_actions": {
                            "神秘符文的来历": "引入",
                            "失踪的导师": "引入"
                        }
                    },
                    {
                        "number": 2,
                        "title": "初次觉醒",
                        "type": "战斗",
                        "summary": "主角的潜能被激发，首次展现出不凡的力量。",
                        "characters": ["主角", "初始敌人"],
                        "plot_actions": {
                            "神秘符文的来历": "发展",
                            "身世之谜": "引入"
                        }
                    }
                ]
            }"""
        elif "chapter_detail" in prompt.lower() or "章节大纲" in prompt:
            return """{
                "chapter_num": 1,
                "title": "神秘的开始",
                "summary": "主角林风在一次山洞探险中意外发现了一枚刻有奇怪符文的古老玉佩。当他触碰玉佩的瞬间，一股神秘的能量涌入体内，他昏迷不醒。醒来后，他发现自己似乎获得了某种奇怪的能力，同时也得知自己敬爱的导师已经失踪多日。",
                "key_scenes": [
                    {
                        "location": "古老山洞",
                        "participants": ["林风", "探险伙伴小李"],
                        "conflict": "发现神秘玉佩时触发的洞穴坍塌"
                    },
                    {
                        "location": "林风家中",
                        "participants": ["林风", "林母"],
                        "conflict": "得知导师失踪的消息与内心的震惊"
                    },
                    {
                        "location": "城市街道",
                        "participants": ["林风", "神秘老人"],
                        "conflict": "神秘老人的警告与林风的困惑"
                    }
                ],
                "plot_actions": {
                    "神秘符文的来历": "引入",
                    "失踪的导师": "引入"
                },
                "characters_involved": ["林风", "小李", "林母", "神秘老人"],
                "next_preview": "林风决定寻找失踪的导师，而神秘玉佩的能量似乎正在逐渐改变他的体质..."
            }"""
        else:
            return "{\"error\": \"未知的提示类型\"}"

    # 创建大纲生成器
    generator = create_outline_generator(
        novel_title="测试小说",
        novel_genre="奇幻",
        novel_style="轻松",
        novel_dir=novel_dir,
        total_chapters=10,
        llm_caller=simple_llm_caller
    )

    # 获取总纲
    master_outline = generator.get_master_outline()
    print("\n=== 总纲 ===\n")
    print(json.dumps(master_outline, ensure_ascii=False, indent=2))

    # 获取当前卷纲
    volume = generator.get_current_volume()
    print("\n=== 卷纲 ===\n")
    print(json.dumps(volume.model_dump(), ensure_ascii=False, indent=2))

    # 获取第一章大纲
    chapter = generator.get_next_chapter_outline()
    print("\n=== 第一章大纲 ===\n")
    print(generator.convert_outline_to_text(chapter))

    # 前进到下一章
    next_chapter = generator.advance_chapter()
    print("\n=== 第二章大纲 ===\n")
    print(generator.convert_outline_to_text(next_chapter))

    print("\n示例运行完成，大纲文件已保存到", novel_dir)

# 如果是主模块，注册大纲生成器智能体
if __name__ != "__main__":
    try:
        register_outline_generator_agent()
    except Exception as e:
        print(f"注册大纲生成器智能体时出错: {e}")

    # ----------------- 公共API方法 -----------------

    def get_next_chapter_outline(self) -> ChapterOutline:
        """获取下一章的大纲

        返回:
            章节大纲对象
        """
        # 如果缓冲池为空，填充缓冲池
        if self.buffer.is_empty():
            self._fill_buffer()

        # 返回缓冲池中的第一个章节大纲
        return self.buffer.peek()

    def advance_chapter(self) -> ChapterOutline:
        """前进到下一章

        返回:
            下一章的大纲对象
        """
        # 如果缓冲池为空，填充缓冲池
        if self.buffer.is_empty():
            self._fill_buffer()

        # 获取并移除第一个章节大纲
        chapter = self.buffer.pop()

        # 确保缓冲池中始终有足够的章节
        self._fill_buffer()

        return chapter

    def get_master_outline(self) -> Dict:
        """获取总纲

        返回:
            总纲字典
        """
        return self.master_outline

    def get_current_volume(self) -> VolumeOutline:
        """获取当前卷纲

        返回:
            当前卷纲对象
        """
        return self.current_volume

    def get_chapter_outline_by_number(self, chapter_num: int) -> Optional[ChapterOutline]:
        """根据章节编号获取章节大纲

        参数:
            chapter_num: 章节编号

        返回:
            章节大纲对象，如果不存在则返回None
        """
        # 检查缓冲池中是否有该章节
        for chapter in self.buffer.buffer:
            if chapter.chapter_num == chapter_num:
                return chapter

        # 如果缓冲池中没有，尝试从文件中加载
        chapter_file = os.path.join(self.novel_dir, "outlines", f"chapter_{chapter_num}.json")
        if os.path.exists(chapter_file):
            try:
                with open(chapter_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    return ChapterOutline(**data)
            except Exception as e:
                logger.error(f"加载章节大纲时出错: {e}")

        # 如果文件不存在，尝试生成该章节的大纲
        # 首先需要确定该章节属于哪个卷
        for volume in self.volumes:
            if volume.start_chapter <= chapter_num <= volume.end_chapter:
                # 找到对应的章节框架
                chapter_idx = chapter_num - volume.start_chapter
                if 0 <= chapter_idx < len(volume.chapters):
                    framework = volume.chapters[chapter_idx]
                    # 生成章节大纲
                    chapter = self._generate_chapter_detail(framework)
                    # 保存到文件
                    try:
                        with open(chapter_file, "w", encoding="utf-8") as f:
                            json.dump(chapter.dict(), f, ensure_ascii=False, indent=2)
                    except Exception as e:
                        logger.error(f"保存章节大纲时出错: {e}")
                    return chapter

        # 如果所有方法都失败，返回None
        return None

    def save_chapter_outline(self, chapter: ChapterOutline) -> bool:
        """保存章节大纲

        参数:
            chapter: 章节大纲对象

        返回:
            是否保存成功
        """
        chapter_file = os.path.join(self.novel_dir, "outlines", f"chapter_{chapter.chapter_num}.json")
        try:
            with open(chapter_file, "w", encoding="utf-8") as f:
                json.dump(chapter.model_dump(), f, ensure_ascii=False, indent=2)
            logger.info(f"已保存章节大纲到{chapter_file}")
            return True
        except Exception as e:
            logger.error(f"保存章节大纲时出错: {e}")
            return False

    def get_due_plots(self, chapter_num: int) -> List[Dict]:
        """获取当前章节需要处理的伏笔

        参数:
            chapter_num: 章节编号

        返回:
            需要处理的伏笔列表
        """
        return self.plot_mgr.get_due_plots(chapter_num)

    def get_active_plots(self) -> List[Dict]:
        """获取所有活跃的伏笔

        返回:
            活跃的伏笔列表
        """
        return self.plot_mgr.get_active_plots()

    def check_outline_status(self, chapter_num: int) -> Dict[str, Any]:
        """检查大纲状态，判断是否需要生成新的章节大纲

        参数:
            chapter_num: 当前章节编号

        返回:
            状态信息字典，包含以下字段：
            - need_new_outline: 是否需要生成新的大纲
            - current_phase: 当前阶段
            - remaining_chapters: 当前阶段剩余章节数
            - message: 状态描述信息
        """
        result = {
            "need_new_outline": False,
            "current_phase": "",
            "remaining_chapters": 0,
            "message": ""
        }

        # 如果大纲生成器未初始化，需要生成新的大纲
        if not hasattr(self, "master_outline") or not self.master_outline:
            result["need_new_outline"] = True
            result["message"] = "大纲生成器未初始化，需要生成新的大纲"
            return result

        # 获取当前阶段
        if self.current_phase_idx < len(self.master_outline["phases"]):
            current_phase = self.master_outline["phases"][self.current_phase_idx]
            result["current_phase"] = current_phase.get("name", f"阶段{self.current_phase_idx+1}")
        else:
            result["current_phase"] = "未知阶段"

        # 检查当前卷纲是否存在
        if not hasattr(self, "current_volume") or not self.current_volume:
            result["need_new_outline"] = True
            result["message"] = "当前卷纲不存在，需要生成新的卷纲"
            return result

        # 检查当前章节是否超出当前卷纲范围
        if chapter_num > self.current_volume.end_chapter:
            result["need_new_outline"] = True
            result["message"] = f"当前章节({chapter_num})超出当前卷纲范围({self.current_volume.start_chapter}-{self.current_volume.end_chapter})，需要生成新的卷纲"
            return result

        # 计算当前阶段剩余章节数
        remaining_chapters = self.current_volume.end_chapter - chapter_num
        result["remaining_chapters"] = remaining_chapters

        # 如果剩余章节数小于等于5，提前警告
        if remaining_chapters <= 5:
            result["message"] = f"当前阶段剩余{remaining_chapters}章，即将需要生成新的卷纲"
        else:
            result["message"] = f"当前阶段还有{remaining_chapters}章，无需生成新的卷纲"

        return result

    def convert_outline_to_text(self, chapter: ChapterOutline) -> str:
        """将章节大纲转换为文本格式

        参数:
            chapter: 章节大纲对象

        返回:
            文本格式的章节大纲
        """
        result = []
        result.append(f"## 第{chapter.chapter_num}章：{chapter.title}\n")

        result.append("### 章节摘要")
        result.append(chapter.summary)
        result.append("")

        result.append("### 关键场景")
        for i, scene in enumerate(chapter.key_scenes):
            result.append(f"#### 场景{i+1}：{scene.get('location', '未指定地点')}")
            result.append(f"**参与角色**: {', '.join(scene.get('participants', []))}")
            result.append(f"**冲突点**: {scene.get('conflict', '未指定冲突')}")
            result.append("")

        result.append("### 伏笔操作")
        for plot, action in chapter.plot_actions.items():
            result.append(f"- {plot}: {action}")
        result.append("")

        result.append("### 相关角色")
        for character in chapter.characters_involved:
            result.append(f"- {character}")
        result.append("")

        if chapter.next_preview:
            result.append("### 下一章预告")
            result.append(chapter.next_preview)

        return "\n".join(result)
class WorldSetting(BaseModel):
    """世界观基础设定"""
    title: str = Field(..., description="小说标题")
    genre: str = Field(..., description="小说类型，如奇幻、科幻、现代")
    style: str = Field(..., description="写作风格，如硬核、轻松、严肃")
    phases: List[str] = Field(default_factory=list, description="总纲阶段列表，如'序章'、'崛起'、'危机'等")
    total_chapters: int = Field(default=30, description="计划的总章节数")
    scene: str = Field(default="", description="主要场景设定")
    characters: str = Field(default="", description="主要角色设定")
    tech_rules: List[str] = Field(default_factory=list, description="技术/魔法规则")
    character_roster: Dict[str, Dict] = Field(default_factory=dict, description="角色名->角色档案")

    @validator('phases')
    def validate_phases(cls, v):
        if not v:
            return ["序章", "发展", "高潮", "结局"]
        return v

class VolumeOutline(BaseModel):
    """卷纲数据结构"""
    volume_id: int = Field(..., description="卷ID")
    phase: str = Field(..., description="所属阶段")
    chapters: List[Dict] = Field(..., description="章节框架列表")
    start_chapter: int = Field(..., description="起始章节编号")
    end_chapter: int = Field(..., description="结束章节编号")
    unresolved_plots: List[str] = Field(default_factory=list, description="未解决的伏笔")
    main_conflicts: List[str] = Field(default_factory=list, description="主要冲突")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="创建时间")

class ChapterOutline(BaseModel):
    """章节详细大纲"""
    chapter_num: int = Field(..., description="章节编号")
    title: str = Field(..., description="章节标题")
    summary: str = Field(..., description="章节摘要")
    key_scenes: List[Dict] = Field(default_factory=list, description="关键场景")
    plot_actions: Dict[str, str] = Field(default_factory=dict, description="伏笔处理动作")
    characters_involved: List[str] = Field(default_factory=list, description="涉及角色")
    next_preview: str = Field(default="", description="下一章预告")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="创建时间")

class MasterOutline(BaseModel):
    """总纲数据结构"""
    title: str = Field(..., description="小说标题")
    genre: str = Field(..., description="小说类型")
    style: str = Field(..., description="写作风格")
    phases: List[Dict] = Field(..., description="阶段列表")
    cross_plots: List[str] = Field(default_factory=list, description="跨阶段伏笔")
    total_chapters: int = Field(..., description="计划总章节数")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="创建时间")
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat(), description="更新时间")

class PlotItem(BaseModel):
    """伏笔项目"""
    id: str = Field(..., description="伏笔ID")
    description: str = Field(..., description="伏笔描述")
    intro_chapter: int = Field(..., description="引入章节")
    due_range: Tuple[int, int] = Field(..., description="预计解决范围(起始章节,结束章节)")
    status: str = Field(default="pending", description="状态: pending/active/resolved")
    resolution: str = Field(default="", description="解决方式")
    related_characters: List[str] = Field(default_factory=list, description="相关角色")
    importance: int = Field(default=5, description="重要性(1-10)")
