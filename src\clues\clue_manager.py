"""
Clue Management Module

This module provides functions to manage clues in the novel,
including tracking clues, ensuring they are used, and generating
new clues when needed.
"""

import os
import json
import re
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from datetime import datetime

class ClueManager:
    """Clue manager for novel content"""

    def __init__(self, novel_dir: str):
        """
        Initialize clue manager

        Args:
            novel_dir: Novel directory
        """
        self.novel_dir = novel_dir
        self.clues_dir = os.path.join(novel_dir, "clues")
        os.makedirs(self.clues_dir, exist_ok=True)

        self.clues = {}
        self.load_clues()

    def load_clues(self) -> bool:
        """
        Load clues from file

        Returns:
            Success flag
        """
        clues_file = os.path.join(self.clues_dir, "clues.json")
        if os.path.exists(clues_file):
            try:
                with open(clues_file, "r", encoding="utf-8") as f:
                    self.clues = json.load(f)
                return True
            except Exception as e:
                print(f"Error loading clues: {e}")
        return False

    def save_clues(self) -> bool:
        """
        Save clues to file

        Returns:
            Success flag
        """
        try:
            clues_file = os.path.join(self.clues_dir, "clues.json")
            with open(clues_file, "w", encoding="utf-8") as f:
                json.dump(self.clues, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"Error saving clues: {e}")
            return False

    def add_clue(self, clue_id: str, description: str, related_entities: List[str] = None, status: str = "active") -> bool:
        """
        Add a clue

        Args:
            clue_id: Clue identifier
            description: Clue description
            related_entities: Related entity names
            status: Clue status (active, resolved, abandoned)

        Returns:
            Success flag
        """
        if related_entities is None:
            related_entities = []

        self.clues[clue_id] = {
            "description": description,
            "related_entities": related_entities,
            "status": status,
            "mentions": 0,
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "last_mentioned_at": None
        }

        return self.save_clues()

    def update_clue_status(self, clue_id: str, status: str) -> bool:
        """
        Update clue status

        Args:
            clue_id: Clue identifier
            status: New status

        Returns:
            Success flag
        """
        if clue_id in self.clues:
            self.clues[clue_id]["status"] = status
            return self.save_clues()
        return False

    def increment_clue_mention(self, clue_id: str) -> bool:
        """
        Increment clue mention count

        Args:
            clue_id: Clue identifier

        Returns:
            Success flag
        """
        if clue_id in self.clues:
            self.clues[clue_id]["mentions"] += 1
            self.clues[clue_id]["last_mentioned_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return self.save_clues()
        return False

    def get_active_clues(self) -> List[Dict[str, Any]]:
        """
        Get active clues

        Returns:
            List of active clues
        """
        return [{"id": clue_id, **clue} for clue_id, clue in self.clues.items() if clue["status"] == "active"]

    def get_unused_clues(self, threshold: int = 3) -> List[Dict[str, Any]]:
        """
        Get unused clues

        Args:
            threshold: Mention threshold

        Returns:
            List of unused clues
        """
        return [{"id": clue_id, **clue} for clue_id, clue in self.clues.items()
                if clue["status"] == "active" and clue["mentions"] < threshold]

    def extract_clues_from_text(self, text: str, llm_caller: Callable = None) -> List[Dict[str, Any]]:
        """
        Extract clues from text

        Args:
            text: Text to analyze
            llm_caller: Function to call LLM

        Returns:
            List of extracted clues
        """
        if not llm_caller:
            return []

        # Create clue extraction prompt
        prompt = f"""
You are a clue extractor for a novel. Please analyze the following content and extract any clues or hints that might be important for the story.

Content to analyze:
{text[:3000]}...

Please extract any clues or hints that might be important for the story. Consider:
1. Objects mentioned that might be significant
2. Information revealed about characters
3. Mysterious events or unexplained phenomena
4. Foreshadowing or hints about future events

Format your response as JSON:
{{
  "clues": [
    {{
      "description": "Description of the clue",
      "related_entities": ["Entity1", "Entity2"],
      "importance": 1-10 (where 10 is extremely important)
    }}
  ]
}}
"""

        try:
            response = llm_caller(prompt)

            # Try to parse JSON response
            try:
                data = json.loads(response)
                return data.get("clues", [])
            except json.JSONDecodeError:
                print(f"Error parsing clue extraction response: {response[:100]}...")
                return []
        except Exception as e:
            print(f"Error in clue extraction: {e}")
            return []

    def update_clues_from_text(self, text: str, llm_caller: Callable = None) -> int:
        """
        Update clues based on text

        Args:
            text: Text to analyze
            llm_caller: Function to call LLM

        Returns:
            Number of clues updated
        """
        # Extract clues from text
        extracted_clues = self.extract_clues_from_text(text, llm_caller)

        # Update existing clues
        updated_count = 0
        for clue in extracted_clues:
            # Check if this clue matches any existing clues
            matched = False
            for clue_id, existing_clue in self.clues.items():
                # Simple similarity check - if descriptions are similar
                if self._is_similar(clue["description"], existing_clue["description"]):
                    # Update mention count
                    self.increment_clue_mention(clue_id)
                    matched = True
                    updated_count += 1
                    break

            # If no match, add as new clue
            if not matched and clue.get("importance", 0) >= 5:  # Only add important clues
                clue_id = f"clue_{len(self.clues) + 1}"
                self.add_clue(
                    clue_id=clue_id,
                    description=clue["description"],
                    related_entities=clue.get("related_entities", []),
                    status="active"
                )
                updated_count += 1

        return updated_count

    def _is_similar(self, text1: str, text2: str) -> bool:
        """
        Check if two texts are similar

        Args:
            text1: First text
            text2: Second text

        Returns:
            True if similar, False otherwise
        """
        # Simple similarity check - if one is a substring of the other
        text1 = text1.lower()
        text2 = text2.lower()

        return text1 in text2 or text2 in text1

    def generate_clue_integration_suggestions(self, unused_clues: List[Dict[str, Any]], chapter_outline: str, llm_caller: Callable = None) -> str:
        """
        Generate suggestions for integrating unused clues

        Args:
            unused_clues: Unused clues
            chapter_outline: Chapter outline
            llm_caller: Function to call LLM

        Returns:
            Suggestions text
        """
        if not unused_clues or not llm_caller:
            return ""

        # Create clue integration prompt
        clues_text = "\n".join([f"- {clue['description']}" for clue in unused_clues])

        prompt = f"""
You are a story consultant helping a novelist integrate unused clues into their story. Please suggest ways to integrate the following unused clues into the upcoming chapter.

Unused clues:
{clues_text}

Chapter outline:
{chapter_outline}

Please suggest specific ways to integrate each clue into the chapter. Your suggestions should:
1. Be natural and not forced
2. Fit with the chapter outline
3. Advance the plot or character development
4. Not reveal too much too soon

Format your response as a list of suggestions, one for each clue.
"""

        try:
            return llm_caller(prompt)
        except Exception as e:
            print(f"Error generating clue integration suggestions: {e}")
            return ""

# Global clue manager instance
_clue_manager = None

def get_clue_manager(novel_dir: str) -> ClueManager:
    """
    Get or create a clue manager for the given novel directory

    Args:
        novel_dir: Novel directory

    Returns:
        Clue manager instance
    """
    global _clue_manager

    if _clue_manager is None or _clue_manager.novel_dir != novel_dir:
        _clue_manager = ClueManager(novel_dir)
        print(f"Created new clue manager for {novel_dir}")

    return _clue_manager

def update_clues_from_text(text: str, novel_dir: str, llm_caller: Callable = None) -> int:
    """
    Update clues based on text

    Args:
        text: Text to analyze
        novel_dir: Novel directory
        llm_caller: Function to call LLM

    Returns:
        Number of clues updated
    """
    manager = get_clue_manager(novel_dir)
    return manager.update_clues_from_text(text, llm_caller)

def get_unused_clues(novel_dir: str, threshold: int = 3) -> List[Dict[str, Any]]:
    """
    Get unused clues

    Args:
        novel_dir: Novel directory
        threshold: Mention threshold

    Returns:
        List of unused clues
    """
    manager = get_clue_manager(novel_dir)
    return manager.get_unused_clues(threshold)

def generate_clue_integration_suggestions(chapter_outline: str, novel_dir: str, llm_caller: Callable = None) -> str:
    """
    Generate suggestions for integrating unused clues

    Args:
        chapter_outline: Chapter outline
        novel_dir: Novel directory
        llm_caller: Function to call LLM

    Returns:
        Suggestions text
    """
    manager = get_clue_manager(novel_dir)
    unused_clues = manager.get_unused_clues()
    return manager.generate_clue_integration_suggestions(unused_clues, chapter_outline, llm_caller)

def register_clue(clue_id: str, description: str, novel_dir: str, related_entities: List[str] = None, status: str = "active") -> bool:
    """
    Register a new clue

    Args:
        clue_id: Clue identifier
        description: Clue description
        novel_dir: Novel directory
        related_entities: Related entity names
        status: Clue status (active, resolved, abandoned)

    Returns:
        Success flag
    """
    manager = get_clue_manager(novel_dir)
    return manager.add_clue(clue_id, description, related_entities, status)

def update_clue_status(clue_id: str, status: str, novel_dir: str) -> bool:
    """
    Update the status of a clue

    Args:
        clue_id: Clue identifier
        status: New status (active, resolved, abandoned)
        novel_dir: Novel directory

    Returns:
        Success flag
    """
    manager = get_clue_manager(novel_dir)
    return manager.update_clue_status(clue_id, status)

def get_active_clues(novel_dir: str) -> List[Dict[str, Any]]:
    """
    Get all active clues for a novel

    Args:
        novel_dir: Novel directory

    Returns:
        List of active clues
    """
    manager = get_clue_manager(novel_dir)
    return manager.get_active_clues()
