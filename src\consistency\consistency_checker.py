"""
Consistency Checker Module

This module provides functions to check the consistency of novel content,
including character consistency, plot consistency, and logical consistency.
"""

import os
import json
import re
from typing import Dict, List, Any, Optional, Union, Tuple, Callable

class ConsistencyChecker:
    """Consistency checker for novel content"""

    def __init__(self, novel_dir: str):
        """
        Initialize consistency checker

        Args:
            novel_dir: Novel directory
        """
        self.novel_dir = novel_dir
        self.entity_graph = {}
        self.consistency_log = []

    def check_character_consistency(self, content: str, llm_caller: Callable = None) -> Tuple[bool, List[str]]:
        """
        Check character consistency

        Args:
            content: Content to check
            llm_caller: Function to call LLM

        Returns:
            (is_consistent, issues)
        """
        if not llm_caller:
            return True, []

        # Extract character names from content
        character_pattern = r'\b[A-Z][a-z]+\b'
        potential_characters = set(re.findall(character_pattern, content))

        # Load character information from previous chapters
        character_file = os.path.join(self.novel_dir, "memory", "characters.json")
        known_characters = {}

        if os.path.exists(character_file):
            try:
                with open(character_file, "r", encoding="utf-8") as f:
                    known_characters = json.load(f)
            except Exception as e:
                print(f"Error loading character file: {e}")

        # Check for new characters
        new_characters = [char for char in potential_characters if char not in known_characters]

        # If no new characters, assume consistent
        if not new_characters:
            return True, []

        # Create consistency check prompt
        prompt = f"""
You are a consistency checker for a novel. Please analyze the following content and determine if the new characters introduced are consistent with the story.

Known characters:
{json.dumps(known_characters, indent=2)}

Content to check:
{content[:2000]}...

New potential characters detected:
{', '.join(new_characters)}

Please check if these new characters are consistent with the story. Consider:
1. Are they properly introduced?
2. Do they fit with the existing characters and setting?
3. Are there any contradictions with previously established facts?

Format your response as JSON:
{{
  "is_consistent": true/false,
  "issues": [
    {{
      "character": "Character name",
      "description": "Description of the inconsistency",
      "severity": "high/medium/low"
    }}
  ]
}}
"""

        try:
            response = llm_caller(prompt)

            # Try to parse JSON response
            try:
                # 处理响应可能已经是Python对象的情况
                if isinstance(response, dict):
                    data = response
                else:
                    data = json.loads(response)

                is_consistent = data.get("is_consistent", True)
                issues = [f"{issue['character']}: {issue['description']} (Severity: {issue['severity']})" for issue in data.get("issues", [])]
                return is_consistent, issues
            except (json.JSONDecodeError, TypeError) as e:
                # 如果JSON解析失败，记录错误并假设一致
                print(f"Error in character consistency check: {e}")
                # 保存原始响应以便调试
                debug_file = os.path.join(self.novel_dir, "consistency", "character_response_error.txt")
                os.makedirs(os.path.dirname(debug_file), exist_ok=True)
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(str(response))
                return True, []
        except Exception as e:
            print(f"Error in character consistency check: {e}")
            return True, []

    def check_plot_consistency(self, content: str, chapter_num: int, llm_caller: Callable = None) -> Tuple[bool, List[str]]:
        """
        Check plot consistency

        Args:
            content: Content to check
            chapter_num: Chapter number
            llm_caller: Function to call LLM

        Returns:
            (is_consistent, issues)
        """
        if not llm_caller or chapter_num <= 1:
            return True, []

        # Load plot summary
        plot_file = os.path.join(self.novel_dir, "memory", "plot_summary.json")
        plot_summary = {}

        if os.path.exists(plot_file):
            try:
                with open(plot_file, "r", encoding="utf-8") as f:
                    plot_summary = json.load(f)
            except Exception as e:
                print(f"Error loading plot summary: {e}")

        # If no plot summary, assume consistent
        if not plot_summary:
            return True, []

        # Get previous chapter summaries
        chapter_summaries = plot_summary.get("chapter_summaries", {})
        previous_summaries = {int(k): v for k, v in chapter_summaries.items() if int(k) < chapter_num}

        if not previous_summaries:
            return True, []

        # Create consistency check prompt
        previous_content = "\n\n".join([f"Chapter {k}: {v}" for k, v in sorted(previous_summaries.items())])

        prompt = f"""
You are a plot consistency checker for a novel. Please analyze the following content and determine if it is consistent with the previous chapters.

Previous chapter summaries:
{previous_content}

Current chapter content:
{content[:2000]}...

Please check for the following types of plot inconsistencies:
1. Contradictions with previous events
2. Unexplained changes in character motivations
3. Sudden changes in setting or time without explanation
4. Introduction of new plot elements that contradict established facts

Format your response as JSON:
{{
  "is_consistent": true/false,
  "issues": [
    {{
      "type": "contradiction/motivation/setting/plot",
      "description": "Description of the inconsistency",
      "severity": "high/medium/low"
    }}
  ]
}}
"""

        try:
            response = llm_caller(prompt)

            # Try to parse JSON response
            try:
                # 处理响应可能已经是Python对象的情况
                if isinstance(response, dict):
                    data = response
                else:
                    data = json.loads(response)

                is_consistent = data.get("is_consistent", True)
                issues = [f"{issue['type'].upper()}: {issue['description']} (Severity: {issue['severity']})" for issue in data.get("issues", [])]
                return is_consistent, issues
            except (json.JSONDecodeError, TypeError) as e:
                # 如果JSON解析失败，记录错误并假设一致
                print(f"Error in plot consistency check: {e}")
                # 保存原始响应以便调试
                debug_file = os.path.join(self.novel_dir, "consistency", "plot_response_error.txt")
                os.makedirs(os.path.dirname(debug_file), exist_ok=True)
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(str(response))
                return True, []
        except Exception as e:
            print(f"Error in plot consistency check: {e}")
            return True, []

    def check_logical_consistency(self, content: str, llm_caller: Callable = None) -> Tuple[bool, List[str]]:
        """
        Check logical consistency

        Args:
            content: Content to check
            llm_caller: Function to call LLM

        Returns:
            (is_consistent, issues)
        """
        if not llm_caller:
            return True, []

        # Create logical consistency check prompt
        prompt = f"""
You are a logical consistency checker for a novel. Please analyze the following content and determine if it contains any logical inconsistencies.

Content to check:
{content[:3000]}...

Please check for the following types of logical inconsistencies:
1. Impossible events given the story's rules
2. Characters knowing things they couldn't know
3. Time paradoxes or timeline issues
4. Physical impossibilities not explained by the story's genre

Format your response as JSON:
{{
  "is_consistent": true/false,
  "issues": [
    {{
      "type": "impossible_event/knowledge/time/physical",
      "description": "Description of the inconsistency",
      "severity": "high/medium/low"
    }}
  ]
}}
"""

        try:
            response = llm_caller(prompt)

            # Try to parse JSON response
            try:
                # 处理响应可能已经是Python对象的情况
                if isinstance(response, dict):
                    data = response
                else:
                    data = json.loads(response)

                is_consistent = data.get("is_consistent", True)
                issues = [f"{issue['type'].upper()}: {issue['description']} (Severity: {issue['severity']})" for issue in data.get("issues", [])]
                return is_consistent, issues
            except (json.JSONDecodeError, TypeError) as e:
                # 如果JSON解析失败，记录错误并假设一致
                print(f"Error in logical consistency check: {e}")
                # 保存原始响应以便调试
                debug_file = os.path.join(self.novel_dir, "consistency", "logical_response_error.txt")
                os.makedirs(os.path.dirname(debug_file), exist_ok=True)
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(str(response))
                return True, []
        except Exception as e:
            print(f"Error in logical consistency check: {e}")
            return True, []

    def check_consistency(self, content: str, chapter_num: int, llm_caller: Callable = None) -> Tuple[bool, str]:
        """
        Check overall consistency

        Args:
            content: Content to check
            chapter_num: Chapter number
            llm_caller: Function to call LLM

        Returns:
            (is_consistent, consistency_report)
        """
        all_issues = []
        is_consistent = True

        # Check character consistency
        char_consistent, char_issues = self.check_character_consistency(content, llm_caller)
        if not char_consistent:
            is_consistent = False
        all_issues.extend(char_issues)

        # Check plot consistency
        plot_consistent, plot_issues = self.check_plot_consistency(content, chapter_num, llm_caller)
        if not plot_consistent:
            is_consistent = False
        all_issues.extend(plot_issues)

        # Check logical consistency
        logical_consistent, logical_issues = self.check_logical_consistency(content, llm_caller)
        if not logical_consistent:
            is_consistent = False
        all_issues.extend(logical_issues)

        # Create consistency report
        if not all_issues:
            report = "No consistency issues found."
        else:
            report = "Consistency issues found:\n" + "\n".join([f"- {issue}" for issue in all_issues])

        # Log consistency check
        self.consistency_log.append({
            "chapter": chapter_num,
            "is_consistent": is_consistent,
            "issues": all_issues
        })

        return is_consistent, report

    def save_consistency_log(self) -> bool:
        """
        Save consistency log

        Returns:
            Success flag
        """
        try:
            log_dir = os.path.join(self.novel_dir, "consistency")
            os.makedirs(log_dir, exist_ok=True)

            log_file = os.path.join(log_dir, "consistency_log.json")
            with open(log_file, "w", encoding="utf-8") as f:
                json.dump(self.consistency_log, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            print(f"Error saving consistency log: {e}")
            return False

# Global consistency checker instance
_consistency_checker = None

def get_consistency_checker(novel_dir: str) -> ConsistencyChecker:
    """
    Get or create a consistency checker for the given novel directory

    Args:
        novel_dir: Novel directory

    Returns:
        Consistency checker instance
    """
    global _consistency_checker

    if _consistency_checker is None or _consistency_checker.novel_dir != novel_dir:
        _consistency_checker = ConsistencyChecker(novel_dir)
        print(f"Created new consistency checker for {novel_dir}")

    return _consistency_checker

def check_consistency(content: str, chapter_num: int, novel_dir: str, llm_caller: Callable = None) -> Tuple[bool, str]:
    """
    Check consistency of content

    Args:
        content: Content to check
        chapter_num: Chapter number
        novel_dir: Novel directory
        llm_caller: Function to call LLM

    Returns:
        (is_consistent, consistency_report)
    """
    checker = get_consistency_checker(novel_dir)
    return checker.check_consistency(content, chapter_num, llm_caller)

def save_consistency_log(novel_dir: str) -> bool:
    """
    Save consistency log

    Args:
        novel_dir: Novel directory

    Returns:
        Success flag
    """
    checker = get_consistency_checker(novel_dir)
    return checker.save_consistency_log()
