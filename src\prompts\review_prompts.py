"""
审核相关提示词管理模块
集中管理所有与审核相关的提示词
"""

class ReviewPrompts:
    """审核相关提示词"""
    
    @staticmethod
    def review_content_prompt(content):
        """生成内容审核提示词"""
        return f"""
请对以下小说章节内容进行全面审核，检查是否存在问题，并给出改进建议。

内容：
{content[:2000]}...（内容较长，仅展示部分）

审核要求：
1. 检查情节是否连贯、合理
2. 检查人物形象是否一致、立体
3. 检查语言表达是否生动、准确
4. 检查是否存在逻辑矛盾或情节漏洞
5. 检查是否有不必要的重复或冗余内容
6. 检查是否有不恰当的内容或表达

请给出详细的审核意见，包括发现的问题和具体的改进建议。
"""

    @staticmethod
    def optimize_content_prompt(content):
        """生成内容优化提示词"""
        return f"""
请对以下小说章节内容进行优化，提升整体质量，但不要改变原有的核心情节。

内容：
{content[:2000]}...（内容较长，仅展示部分）

优化要求：
1. 提升情节的紧凑性和吸引力
2. 增强人物形象的鲜明度和立体感
3. 优化语言表达，使其更加生动、优美
4. 修正可能存在的逻辑矛盾或情节漏洞
5. 删减不必要的重复或冗余内容
6. 调整段落结构，提高阅读流畅度
7. 保持原有的核心情节和人物设定不变

请直接给出优化后的完整内容，不需要解释或标注修改之处。
"""
