"""
模型配置模块，定义各种AI模型的配置信息
"""

import os
import json
import warnings
from typing import Dict, List, Any, cast

# 修复导入路径
try:
    from src.utils.env_manager import env_manager
except ImportError:
    try:
        from utils.env_manager import env_manager
    except ImportError:
        # 创建一个简单的环境管理器替代品
        class SimpleEnvManager:
            def get_config_list(self):
                return []
        env_manager = SimpleEnvManager()

# 从环境变量获取API密钥
GLM_API_KEY = os.getenv("GLM_API_KEY", "")  # 用于所有GLM模型和CogView模型
SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY", "")  # 用于所有硅基流动模型
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "")
# 注意: 已移除STABILITY_API_KEY

# 默认模型名称配置
DEFAULT_MODEL_NAMES = {
    "glm": "glm-4-flash",  # 智谱AI的默认模型
    "siliconflow": "internlm/internlm2_5-7b-chat",  # 硅基流动的默认模型
    "cogview": "cogview-3-flash",  # 图像生成默认模型
    "ollama": "gemma2:2b",  # Ollama本地模型
    "openrouter": "deepseek/deepseek-chat:free"  # OpenRouter的默认模型
    # 注意: 已移除Stability AI的默认模型
}

# AI模型配置
AI_MODEL_CONFIG: Dict[str, Any] = {
    # 默认模型
    "default_model": DEFAULT_MODEL_NAMES["glm"],

    # 各智能体使用的模型
    "agent_models": {
        "WritingAgent": DEFAULT_MODEL_NAMES["glm"],  # 使用GLM-4-flash
        "PolishingAgent": DEFAULT_MODEL_NAMES["glm"],  # 使用GLM-4-flash
        "ExpansionAgent": DEFAULT_MODEL_NAMES["siliconflow"],  # 使用InternLM2
        "ReviewAgent": DEFAULT_MODEL_NAMES["glm"],  # 使用GLM-4-flash
        "OptimizationAgent": DEFAULT_MODEL_NAMES["siliconflow"],  # 使用InternLM2
        "IllustrationAgent": DEFAULT_MODEL_NAMES["cogview"],  # 使用CogView-3生成插图
        "OutlineGeneratorAgent": DEFAULT_MODEL_NAMES["glm"]  # 使用GLM-4-flash生成大纲
    },

    # 模型详细配置
    "models": {
        # 智谱AI模型
        DEFAULT_MODEL_NAMES["glm"]: {
            "api_key": GLM_API_KEY,
            "base_url": "https://open.bigmodel.cn/api/paas/v4",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "zhipu",
            "description": "GLM-4 Flash，智谱AI的高速模型，适合中文创作"
        },
        "glm-z1-flash": {
            "api_key": GLM_API_KEY,
            "base_url": "https://open.bigmodel.cn/api/paas/v4",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "zhipu",
            "description": "GLM-Z1 Flash，智谱AI的高速模型，适合中文创作"
        },
        "glm-4-flash-250414": {
            "api_key": GLM_API_KEY,
            "base_url": "https://open.bigmodel.cn/api/paas/v4",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "zhipu",
            "description": "GLM-4 Flash 250414，智谱AI的高速模型，适合中文创作"
        },
        # 硅基流动模型
        DEFAULT_MODEL_NAMES["siliconflow"]: {
            "api_key": SILICONFLOW_API_KEY,
            "base_url": "https://api.siliconflow.cn/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "siliconflow",
            "description": "InternLM2 5.7B，硅基流动的高性能模型，适合中文创作"
        },
        # 其他硅基流动模型
        "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {
            "api_key": SILICONFLOW_API_KEY,
            "base_url": "https://api.siliconflow.cn/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "siliconflow",
            "description": "DeepSeek R1 Distill Qwen 7B，硅基流动提供的高性能模型"
        },
        "Qwen/Qwen2.5-7B-Instruct": {
            "api_key": SILICONFLOW_API_KEY,
            "base_url": "https://api.siliconflow.cn/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "siliconflow",
            "description": "Qwen 2.5 7B Instruct，阿里云通义千问的高性能模型"
        },
        "Qwen/Qwen2-7B-Instruct": {
            "api_key": SILICONFLOW_API_KEY,
            "base_url": "https://api.siliconflow.cn/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "siliconflow",
            "description": "Qwen 2 7B Instruct，阿里云通义千问的高性能模型"
        },
        "THUDM/glm-4-9b-chat": {
            "api_key": SILICONFLOW_API_KEY,
            "base_url": "https://api.siliconflow.cn/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "siliconflow",
            "description": "GLM-4 9B Chat，清华大学开源的高性能模型"
        },
        "deepseek-ai/DeepSeek-V2.5": {
            "api_key": SILICONFLOW_API_KEY,
            "base_url": "https://api.siliconflow.cn/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "siliconflow",
            "description": "DeepSeek V2.5，深度求索的高性能模型"
        },
        "deepseek-ai/DeepSeek-V3": {
            "api_key": SILICONFLOW_API_KEY,
            "base_url": "https://api.siliconflow.cn/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "siliconflow",
            "description": "InternLM 2.5 7B Chat，硅基流动的高性能模型"
        },
        "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": {
            "api_key": SILICONFLOW_API_KEY,
            "base_url": "https://api.siliconflow.cn/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "siliconflow",
            "description": "DeepSeek R1 Distill Qwen 7B，硅基流动提供的高性能模型"
        },
        "Kwai-Kolors/Kolors": {
            "api_key": SILICONFLOW_API_KEY,
            "base_url": "https://api.siliconflow.cn/v1/images/generations",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "siliconflow",
            "model_type": "kolors",  # Add specific model type for Kolors
            "description": "Kolors 1 14B Instruct，硅基流动提供的高性能模型",
            "supports_image": True  # Add flag for image generation support
        },
        # CogView-3模型
        DEFAULT_MODEL_NAMES["cogview"]: {
            "api_key": GLM_API_KEY,  # 使用GLM API密钥
            "base_url": "https://open.bigmodel.cn/api/paas/v4",
            "temperature": 0.7,
            "max_tokens": 1024,
            "api_type": "cogview",
            "model_type": "cogview",  # 指定模型类型
            "description": "CogView-3 Flash，智谱AI的图像生成模型，适合生成小说插图",
            "image_sizes": ["512x512", "768x768", "1024x1024", "1440x720"],
            "supports_image": True  # 添加图像生成支持标志
        },
        # Ollama本地模型
        DEFAULT_MODEL_NAMES["ollama"]: {
            "base_url": "http://localhost:11434/api",
            "temperature": 0.7,
            "max_tokens": 2048,
            "api_type": "ollama",
            "description": "Gemma 2B，Google开源的高效语言模型"
        },
        "llama3:8b": {
            "base_url": "http://localhost:11434/api",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "ollama",
            "description": "Llama 3 8B，Meta开源的大语言模型"
        },

        # OpenRouter模型
        DEFAULT_MODEL_NAMES["openrouter"]: {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "DeepSeek Chat，OpenRouter提供的免费模型"
        },
        "deepseek/deepseek-v3-base:free": {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "DeepSeek V3 Base，OpenRouter提供的免费模型"
        },
        "google/gemini-2.5-pro-exp-03-25:free": {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "Google Gemini 2.5 Pro，OpenRouter提供的免费模型"
        },
        "deepseek/deepseek-r1-zero:free": {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "DeepSeek R1 Zero，OpenRouter提供的免费模型"
        },
        "nvidia/llama-3.1-nemotron-ultra-253b-v1:free": {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "NVIDIA Llama 3.1 Nemotron Ultra 253B，OpenRouter提供的免费模型"
        },
        "meta-llama/llama-3.3-70b-instruct:free": {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "Meta Llama 3.3 70B Instruct，OpenRouter提供的免费模型"
        },
        "qwen/qwen-2.5-72b-instruct:free": {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "Qwen 2.5 72B Instruct，OpenRouter提供的免费模型"
        },
        "qwen/qwq-32b-preview:free": {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "Qwen QWQ 32B Preview，OpenRouter提供的免费模型"
        },
        "deepseek/deepseek-chat:free": {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "DeepSeek Chat，OpenRouter提供的免费模型"
        },
        "qwen/qwq-32b:free": {
            "api_key": OPENROUTER_API_KEY,
            "base_url": "https://openrouter.ai/api/v1",
            "temperature": 0.7,
            "max_tokens": 4096,
            "api_type": "openrouter",
            "description": "Qwen QWQ 32B，OpenRouter提供的免费模型"
        },

        # 注意: 已移除Stability AI模型配置
    },

    # 预设模型选项
    "preset_models": {
        "glm_models": ["glm-4-flash", "glm-z1-flash", "glm-4-flash-250414"],
        "siliconflow_models": [
            "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B",
            "Qwen/Qwen2.5-7B-Instruct",
            "internlm/internlm2_5-7b-chat",
            "Qwen/Qwen2-7B-Instruct",
            "THUDM/glm-4-9b-chat",
            "deepseek-ai/DeepSeek-V2.5",
            "deepseek-ai/DeepSeek-V3",
            "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B"
        ],
        "openrouter_models": [
            "deepseek/deepseek-chat:free",
            "deepseek/deepseek-v3-base:free",
            "google/gemini-2.5-pro-exp-03-25:free",
            "deepseek/deepseek-r1-zero:free",
            "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
            "meta-llama/llama-3.3-70b-instruct:free",
            "qwen/qwen-2.5-72b-instruct:free",
            "qwen/qwq-32b-preview:free",
            "qwen/qwq-32b:free"
        ],
        "image_models": ["cogview-3-flash", "Kwai-Kolors/Kolors"]
    },

    # 模型类型配置
    "model_types": {
        "zhipu": {
            "name": "智谱AI",
            "base_url": "https://open.bigmodel.cn/api/paas/v4",
            "requires_key": True,
            "supports_chat": True
        },
        "siliconflow": {
            "name": "硅基流动",
            "base_url": "https://api.siliconflow.cn/v1",
            "requires_key": True,
            "supports_chat": True
        },
        "ollama": {
            "name": "Ollama本地",
            "base_url": "http://localhost:11434/api",
            "requires_key": False,
            "supports_chat": True
        },
        "cogview": {
            "name": "智谱AI图像",
            "base_url": "https://open.bigmodel.cn/api/paas/v4",
            "requires_key": True,
            "supports_chat": False,
            "supports_image": True
        },
        "kolors": {
            "name": "硅基流动图像",
            "base_url": "https://api.siliconflow.cn/v1/images/generations",
            "requires_key": True,
            "supports_chat": False,
            "supports_image": True
        },
        "openrouter": {
            "name": "OpenRouter",
            "base_url": "https://openrouter.ai/api/v1",
            "requires_key": True,
            "supports_chat": True
        },
        # 注意: 已移除Stability AI模型类型配置
    },

    # 自定义模型列表
    "custom_models": [],

    # 预设选项
    "presets": {
        "genres": [
            "现代小说",
            "科幻小说",
            "奇幻小说",
            "历史小说",
            "武侠小说",
            "悬疑推理",
            "言情小说",
            "都市小说"
        ],
        "styles": [
            "写实主义",
            "浪漫主义",
            "魔幻现实主义",
            "意识流",
            "黑色幽默",
            "简约风格",
            "抒情风格",
            "讽刺风格"
        ],
        "scenes": [
            "城市",
            "乡村",
            "山林",
            "海边",
            "太空",
            "异世界",
            "古代",
            "未来世界"
        ],
        "characters": [
            "一位年轻人",
            "一对恋人",
            "一个家庭",
            "一群朋友",
            "一位老者",
            "一位侦探",
            "一位科学家",
            "一位艺术家"
        ]
    }
}

# 自定义模型配置文件路径
CUSTOM_MODELS_FILE = "custom_models.json"

def get_config_list():
    """
    获取模型配置列表，用于API调用

    返回:
        模型配置列表
    """
    # 使用环境变量管理器获取配置列表
    return env_manager.get_config_list()

def load_custom_models():
    """
    从文件加载自定义模型配置
    """
    try:
        if os.path.exists(CUSTOM_MODELS_FILE):
            with open(CUSTOM_MODELS_FILE, 'r', encoding='utf-8') as f:
                custom_models = json.load(f)

                # 更新自定义模型列表
                models_dict = cast(Dict[str, Any], AI_MODEL_CONFIG["models"])
                custom_models_list = cast(List[Dict[str, Any]], AI_MODEL_CONFIG["custom_models"])

                # 清空当前自定义模型列表
                custom_models_list.clear()

                # 添加加载的自定义模型
                for model in custom_models:
                    model_name = model.get("name", "")
                    if model_name:
                        # 添加到模型配置
                        models_dict[model_name] = {
                            "api_key": model.get("api_key", ""),
                            "base_url": model.get("base_url", ""),
                            "temperature": model.get("temperature", 0.7),
                            "max_tokens": model.get("max_tokens", 4096),
                            "api_type": model.get("api_type", ""),
                            "description": model.get("description", "自定义模型")
                        }

                        # 添加到自定义模型列表
                        custom_models_list.append(model)

                print(f"已加载 {len(custom_models)} 个自定义模型")
    except Exception as e:
        print(f"加载自定义模型时出错: {e}")

def save_custom_models():
    """
    保存自定义模型配置到文件
    """
    try:
        custom_models_list = cast(List[Dict[str, Any]], AI_MODEL_CONFIG["custom_models"])
        with open(CUSTOM_MODELS_FILE, 'w', encoding='utf-8') as f:
            json.dump(custom_models_list, f, ensure_ascii=False, indent=2)
        print(f"已保存 {len(custom_models_list)} 个自定义模型")
    except Exception as e:
        print(f"保存自定义模型时出错: {e}")

def add_custom_model(model_name: str, model_type: str, base_url: str = "", api_key: str = "",
                    temperature: float = 0.7, max_tokens: int = 4096, description: str = "") -> bool:
    """
    添加自定义模型配置

    参数:
        model_name: 模型名称
        model_type: 模型类型 (zhipu/siliconflow/ollama/cogview)
        base_url: API基础URL
        api_key: API密钥
        temperature: 温度参数
        max_tokens: 最大生成token数
        description: 模型描述

    返回:
        是否添加成功
    """
    try:
        # 获取模型类型配置
        model_types = cast(Dict[str, Any], AI_MODEL_CONFIG["model_types"])
        type_config = model_types.get(model_type)
        if not type_config:
            print(f"错误: 不支持的模型类型 {model_type}")
            return False

        # 如果是Ollama模型，使用默认base_url
        if model_type == "ollama" and not base_url:
            base_url = type_config["base_url"]

        # 检查必要参数
        if type_config["requires_key"] and not api_key:
            print(f"错误: {type_config['name']}模型需要API密钥")
            return False

        if not base_url:
            base_url = type_config["base_url"]

        # 如果没有提供描述，使用默认描述
        if not description:
            description = f"自定义{type_config['name']}模型"

        # 添加模型配置
        models_dict = cast(Dict[str, Any], AI_MODEL_CONFIG["models"])
        models_dict[model_name] = {
            "api_key": api_key,
            "base_url": base_url,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "api_type": model_type,
            "description": description
        }

        # 添加到自定义模型列表
        custom_model = {
            "name": model_name,
            "api_type": model_type,
            "api_key": api_key,
            "base_url": base_url,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "description": description
        }

        custom_models_list = cast(List[Dict[str, Any]], AI_MODEL_CONFIG["custom_models"])

        # 检查是否已存在同名模型，如果存在则更新
        for i, model in enumerate(custom_models_list):
            if model.get("name") == model_name:
                custom_models_list[i] = custom_model
                save_custom_models()
                return True

        # 如果不存在，则添加新模型
        custom_models_list.append(custom_model)
        save_custom_models()
        return True
    except Exception as e:
        print(f"添加自定义模型时出错: {e}")
        return False

def get_custom_models() -> List[Dict[str, Any]]:
    """
    获取自定义模型列表

    返回:
        自定义模型列表
    """
    return cast(List[Dict[str, Any]], AI_MODEL_CONFIG["custom_models"])

def update_agent_model(agent_type: str, model_name: str) -> bool:
    """
    更新智能体使用的模型

    参数:
        agent_type: 智能体类型
        model_name: 模型名称

    返回:
        是否更新成功
    """
    try:
        agent_models = cast(Dict[str, str], AI_MODEL_CONFIG["agent_models"])
        models = cast(Dict[str, Any], AI_MODEL_CONFIG["models"])

        if agent_type not in agent_models:
            print(f"错误: 未知的智能体类型 {agent_type}")
            return False

        # 检查模型是否存在
        if model_name not in models:
            # 检查是否是自定义模型
            custom_models = get_custom_models()
            custom_model_names = [model.get("name") for model in custom_models]

            if model_name not in custom_model_names:
                print(f"错误: 未知的模型 {model_name}")
                return False

            # 如果是自定义模型，确保它已经添加到models字典中
            for model in custom_models:
                if model.get("name") == model_name:
                    # 添加到models字典
                    models[model_name] = {
                        "api_key": model.get("api_key", ""),
                        "base_url": model.get("base_url", ""),
                        "temperature": model.get("temperature", 0.7),
                        "max_tokens": model.get("max_tokens", 4096),
                        "api_type": model.get("api_type", ""),
                        "description": model.get("description", "自定义模型")
                    }
                    break

        # 保存之前的模型名称，用于日志
        previous_model = agent_models.get(agent_type, "")

        # 更新智能体使用的模型
        agent_models[agent_type] = model_name

        print(f"已更新智能体 {agent_type} 的模型: {previous_model} -> {model_name}")
        return True
    except Exception as e:
        print(f"更新智能体模型时出错: {e}")
        return False

def delete_custom_model(model_name: str) -> bool:
    """
    删除自定义模型

    参数:
        model_name: 模型名称

    返回:
        是否删除成功
    """
    try:
        custom_models_list = cast(List[Dict[str, Any]], AI_MODEL_CONFIG["custom_models"])
        models_dict = cast(Dict[str, Any], AI_MODEL_CONFIG["models"])

        # 从自定义模型列表中删除
        for i, model in enumerate(custom_models_list):
            if model.get("name") == model_name:
                custom_models_list.pop(i)

                # 从模型配置中删除
                if model_name in models_dict:
                    del models_dict[model_name]

                save_custom_models()
                return True

        print(f"错误: 未找到自定义模型 {model_name}")
        return False
    except Exception as e:
        print(f"删除自定义模型时出错: {e}")
        return False

def get_ollama_models():
    """
    获取本地Ollama模型列表

    返回:
        可用的Ollama模型列表
    """
    import requests
    try:
        # 获取Ollama模型列表
        response = requests.get("http://localhost:11434/api/tags", timeout=2)
        if response.status_code == 200:
            models_data = response.json()
            # 提取模型名称
            models = [model["name"] for model in models_data.get("models", [])]
            return models
        else:
            print(f"获取Ollama模型列表失败: HTTP {response.status_code}")
            return []
    except Exception as e:
        print(f"获取Ollama模型列表时出错: {e}")
        return []

# 加载自定义模型
load_custom_models()

# MCP协议配置
MCP_CONFIG = {
    "enabled": True,
    "timeout": 30,  # seconds
    "retry_count": 3,
    "retry_delay": 2,  # seconds
    "log_requests": True,
    "log_responses": True,
    "log_directory": "logs/mcp",

    # 智能体协作配置
    "collaboration": {
        "enabled": True,
        "max_rounds": 5,
        "timeout_per_round": 60,  # seconds
        "voting_threshold": 0.7
    }
}

# LLM配置
LLM_CONFIG = {
    "config_list": get_config_list(),
    "temperature": 0.7,
    "timeout": 60,
    "cache_seed": None
}

# --- Embedding Configuration ---
EMBEDDING_PROVIDER = os.getenv("EMBEDDING_PROVIDER", "siliconflow").lower()
EMBEDDING_MODEL_NAME = None
EMBEDDING_API_BASE = None
EMBEDDING_API_KEY = None

if EMBEDDING_PROVIDER == "siliconflow":
    EMBEDDING_MODEL_NAME = os.getenv("SILICONFLOW_EMBEDDING_MODEL", "BAAI/bge-m3")
    EMBEDDING_API_BASE = os.getenv("SILICONFLOW_EMBEDDING_API_BASE", "https://api.siliconflow.cn/v1/embeddings")
    EMBEDDING_API_KEY = SILICONFLOW_API_KEY
elif EMBEDDING_PROVIDER == "openai":
    EMBEDDING_MODEL_NAME = os.getenv("OPENAI_EMBEDDING_MODEL", "text-embedding-3-small")
    EMBEDDING_API_BASE = None # Use default openai base
    EMBEDDING_API_KEY = OPENAI_API_KEY
else:
    warnings.warn(f"Unsupported EMBEDDING_PROVIDER '{EMBEDDING_PROVIDER}'. Embedding features might be disabled.")

# --- Reranker Configuration ---
RERANK_PROVIDER = os.getenv("RERANK_PROVIDER", "siliconflow").lower()
RERANK_MODEL_NAME = None
RERANK_API_BASE = None
RERANK_API_KEY = None

if RERANK_PROVIDER == "siliconflow":
    RERANK_MODEL_NAME = os.getenv("SILICONFLOW_RERANK_MODEL", "BAAI/bge-reranker-v2-m3")
    RERANK_API_BASE = os.getenv("SILICONFLOW_RERANK_API_BASE", "https://api.siliconflow.cn/v1/rerank")
    RERANK_API_KEY = SILICONFLOW_API_KEY