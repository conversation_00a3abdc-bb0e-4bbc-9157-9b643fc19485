"""
环境变量管理模块，用于加载和管理API密钥
"""

import os
from typing import Dict, Any, Optional, List

# 尝试导入dotenv，如果不可用则设置标志
DOTENV_AVAILABLE = False
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    print("警告: python-dotenv 未安装，将使用默认API密钥")

# 尝试加载.env文件
if DOTENV_AVAILABLE:
    load_dotenv()  # type: ignore

class EnvManager:
    """环境变量管理器，负责加载和管理API密钥"""

    def __init__(self, env_file: str = ".env"):
        """
        初始化环境变量管理器

        参数:
            env_file: 环境变量文件路径
        """
        self.env_file = env_file
        self.api_keys = self._load_api_keys()

        # 如果没有找到API密钥，使用默认值
        if not self.api_keys:
            self._set_default_keys()

    def _load_api_keys(self) -> Dict[str, str]:
        """
        从环境变量加载API密钥

        返回:
            API密钥字典
        """
        api_keys = {}

        # 从环境变量中获取API密钥
        for key in os.environ:
            if key.endswith('_API_KEY'):
                api_keys[key] = os.environ[key]

        # 如果环境变量中没有API密钥，尝试从.env文件加载
        if not api_keys and DOTENV_AVAILABLE and os.path.exists(self.env_file):
            try:
                with open(self.env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if '=' in line and not line.startswith('#'):
                            key, value = line.strip().split('=', 1)
                            if key.endswith('_API_KEY'):
                                api_keys[key] = value
            except Exception as e:
                print(f"加载环境变量文件时出错: {e}")

        return api_keys

    def _set_default_keys(self):
        """设置默认API密钥"""
        self.api_keys = {
            # 智谱AI GLM API密钥 (包括CogView)
            "GLM_API_KEY": os.getenv("GLM_API_KEY", ""),

            # 硅基流动 API密钥 (包括DeepSeek, InternLM, Qwen和Kolors)
            "SILICONFLOW_API_KEY": os.getenv("SILICONFLOW_API_KEY", ""),

            # OpenRouter API密钥
            "OPENROUTER_API_KEY": os.getenv("OPENROUTER_API_KEY", ""),

            # 其他模型的API密钥
            "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
            "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY", "")
        }

        # 兼容旧版本的环境变量
        # 如果GLM_API_KEY为空，则尝试使用GLM_4_FLASH_API_KEY
        if not self.api_keys["GLM_API_KEY"]:
            self.api_keys["GLM_API_KEY"] = os.getenv("GLM_4_FLASH_API_KEY", "")

        # 保存默认密钥到.env文件
        self._save_api_keys()

    def _save_api_keys(self):
        """保存API密钥到.env文件"""
        try:
            with open(self.env_file, 'w', encoding='utf-8') as f:
                for key, value in self.api_keys.items():
                    f.write(f"{key}={value}\n")
            print(f"API密钥已保存到 {self.env_file}")
        except Exception as e:
            print(f"保存API密钥时出错: {e}")

    def get_api_key(self, key_name: str) -> Optional[str]:
        """
        获取指定名称的API密钥

        参数:
            key_name: API密钥名称或模型名称

        返回:
            API密钥，如果不存在则返回None
        """
        # 如果直接提供了密钥名称，则直接返回
        if key_name in self.api_keys:
            return self.api_keys.get(key_name)

        # 否则根据模型名称判断应该使用哪个密钥
        model_key = key_name.lower()

        if "glm" in model_key:
            return self.api_keys.get("GLM_API_KEY")
        elif "cogview" in model_key:
            return self.api_keys.get("GLM_API_KEY")  # CogView使用同一个GLM API密钥
        elif "kolors" in model_key or "deepseek" in model_key or "internlm" in model_key or "qwen" in model_key:
            return self.api_keys.get("SILICONFLOW_API_KEY")
        elif "openrouter" in model_key or "free" in model_key or "/" in model_key:
            return self.api_keys.get("OPENROUTER_API_KEY")

        # 如果没有匹配到，尝试将模型名称转换为环境变量名称
        env_key = f"{key_name.upper().replace('-', '_').replace('/', '_')}_API_KEY"
        return self.api_keys.get(env_key)

    def set_api_key(self, model_name: str, api_key: str):
        """
        设置指定模型的API密钥

        参数:
            model_name: 模型名称
            api_key: API密钥
        """
        # 转换模型名称为环境变量名称
        env_key = f"{model_name.upper().replace('-', '_').replace('/', '_')}_API_KEY"

        # 设置API密钥
        self.api_keys[env_key] = api_key

        # 保存API密钥到.env文件
        self._save_api_keys()

    def get_model_config(self, model_name: str) -> Dict[str, Any]:
        """
        获取指定模型的配置

        参数:
            model_name: 模型名称

        返回:
            模型配置字典
        """
        # 根据模型名称返回不同的配置
        if "glm" in model_name.lower():
            return {
                "model": model_name,
                "base_url": "https://open.bigmodel.cn/api/paas/v4/",
                "api_key": self.get_api_key("GLM_API_KEY") or "",
            }
        elif "cogview" in model_name.lower():
            return {
                "model": model_name,
                "base_url": "https://open.bigmodel.cn/api/paas/v4/",
                "api_key": self.get_api_key("GLM_API_KEY") or "",  # 使用同一个GLM API密钥
                "image_size": "1440x720"  # 默认图像尺寸
            }
        elif "kolors" in model_name.lower():
            return {
                "model": model_name,
                "base_url": "https://api.siliconflow.cn/v1/images/generations",
                "api_key": self.get_api_key("SILICONFLOW_API_KEY") or "",
                "model_type": "kolors"
            }
        elif "deepseek" in model_name.lower() or "internlm" in model_name.lower() or "qwen" in model_name.lower():
            return {
                "model": model_name,
                "base_url": "https://api.siliconflow.cn/v1",
                "api_key": self.get_api_key("SILICONFLOW_API_KEY") or "",
            }
        elif ":free" in model_name.lower() or "openrouter" in model_name.lower() or "/" in model_name.lower():
            # 对于所有OpenRouter模型，添加extra_headers
            return {
                "model": model_name,
                "base_url": "https://openrouter.ai/api/v1",
                "api_key": self.get_api_key("OPENROUTER_API_KEY") or "",
                "extra_headers": {
                    "HTTP-Referer": "https://storyteller.ai",
                    "X-Title": "Storyteller AI"
                }
            }
        else:
            # 其他模型使用标准配置
            return {
                "model": model_name,
                "api_key": self.get_api_key(model_name) or "",
            }

    def get_config_list(self) -> List[Dict[str, Any]]:
        """
        获取所有模型的配置列表

        返回:
            模型配置列表
        """
        config_list = []

        # 添加GLM模型配置
        glm_models = [
            "glm-4-flash",
            "glm-z1-flash",
            "glm-4-flash-250414"
        ]

        for model_name in glm_models:
            model_config = self.get_model_config(model_name)
            if model_config["api_key"]:
                config_list.append({
                    "model": model_config["model"],
                    "base_url": model_config["base_url"],
                    "api_key": model_config["api_key"],
                    "price": [0.01, 0.02]
                })

        # 添加CogView-3模型配置
        cogview_config = self.get_model_config("cogview-3-flash")
        if cogview_config["api_key"]:
            config_list.append({
                "model": "cogview-3-flash",
                "base_url": cogview_config["base_url"],
                "api_key": cogview_config["api_key"],
                "image_size": cogview_config["image_size"]
            })

        # 添加Kolors模型配置
        kolors_config = self.get_model_config("Kwai-Kolors/Kolors")
        if kolors_config["api_key"]:
            config_list.append({
                "model": "Kwai-Kolors/Kolors",
                "base_url": kolors_config["base_url"],
                "api_key": kolors_config["api_key"],
                "model_type": kolors_config["model_type"]
            })

        # 添加硅基流动模型配置
        siliconflow_models = [
            "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B",
            "internlm/internlm2_5-7b-chat",
            "Qwen/Qwen2.5-7B-Instruct"
        ]

        for model_name in siliconflow_models:
            model_config = self.get_model_config(model_name)
            if model_config["api_key"]:
                config_list.append({
                    "model": model_name,
                    "base_url": model_config["base_url"],
                    "api_key": model_config["api_key"],
                    "price": [0.01, 0.02]
                })

        # 添加OpenRouter模型配置
        openrouter_models = [
            "deepseek/deepseek-chat:free",
            "deepseek/deepseek-v3-base:free",
            "google/gemini-2.5-pro-exp-03-25:free",
            "deepseek/deepseek-r1-zero:free",
            "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
            "meta-llama/llama-3.3-70b-instruct:free",
            "qwen/qwen-2.5-72b-instruct:free",
            "qwen/qwq-32b-preview:free",
            "qwen/qwq-32b:free"
        ]

        # 检查是否有OpenRouter API密钥
        openrouter_api_key = self.api_keys.get("OPENROUTER_API_KEY")
        if openrouter_api_key:
            # 遍历所有OpenRouter模型
            for model_name in openrouter_models:
                openrouter_config = self.get_model_config(model_name)
                # 确保配置中包含所有必要的字段
                model_config = {
                    "model": model_name,
                    "base_url": openrouter_config.get("base_url", "https://openrouter.ai/api/v1"),
                    "api_key": openrouter_api_key,
                    "price": [0.0, 0.0]  # 免费模型
                }

                # 如果有extra_headers，则添加到配置中
                if "extra_headers" in openrouter_config:
                    model_config["extra_headers"] = openrouter_config["extra_headers"]
                else:
                    # 添加默认的extra_headers
                    model_config["extra_headers"] = {
                        "HTTP-Referer": "https://storyteller.ai",
                        "X-Title": "Storyteller AI"
                    }

                config_list.append(model_config)
            print(f"已添加 {len(openrouter_models)} 个OpenRouter模型到配置列表")

        return config_list

# 创建全局环境变量管理器实例
env_manager = EnvManager()