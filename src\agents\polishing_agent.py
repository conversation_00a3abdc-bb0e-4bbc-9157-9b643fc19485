"""
润色智能体模块
负责润色和完善小说内容
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, cast

try:
    from src.core.agent import Agent, AGENT_REGISTRY
except ImportError:
    # 如果导入失败，创建简单的Agent类
    class Agent:
        def __init__(self, name, model=None):
            self.name = name
            self.model = model

    # 创建简单的AGENT_REGISTRY类
    class AgentRegistry:
        def __init__(self):
            self.agents = {}

        def register(self, agent):
            self.agents[agent.name] = agent
            return agent

        def get_agent(self, name):
            return self.agents.get(name)

    AGENT_REGISTRY = AgentRegistry()

try:
    from src.core.prompt_manager import PromptManager
except ImportError:
    # 如果导入失败，创建简单的PromptManager类
    class PromptManager:
        def get_prompt(self, prompt_name, **kwargs):
            return f"提示词 {prompt_name} 示例内容"

try:
    from src.core.model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES
except ImportError:
    # 如果导入失败，创建简单的配置
    AI_MODEL_CONFIG = {
        "agent_models": {},
        "default_model": "glm-4-flash"
    }
    DEFAULT_MODEL_NAMES = {"glm": "glm-4-flash"}

# 初始化提示词管理器
prompt_manager = PromptManager()

class PolishingAgent(Agent):
    """
    润色智能体，负责润色和完善小说内容
    """

    def __init__(self, name: str, model: Optional[str] = None):
        """初始化润色智能体"""
        # 确保AI_MODEL_CONFIG是字典类型
        agent_models = cast(Dict[str, Any], AI_MODEL_CONFIG.get("agent_models", {}))
        default_model = cast(str, AI_MODEL_CONFIG.get("default_model", ""))

        # 获取模型名称
        model = model or agent_models.get("PolishingAgent", default_model)
        super().__init__(name, model)
        self.prompt_manager = prompt_manager

    def handle_message(self, msg):
        """处理接收到的消息"""
        if msg['type'] == 'polish':
            print(f"润色智能体接收到内容...")
            polished_content = self.polish_content(msg['content'])

            # 发送结果给下一个智能体（如果指定了）
            if 'next_agent' in msg:
                self.send_message(
                    msg['next_agent'],
                    {
                        'type': 'content',
                        'content': polished_content,
                        'metadata': msg.get('metadata', {})
                    }
                )

            return polished_content

    def polish_content(self, original_text: str) -> str:
        """
        润色小说内容

        参数:
            original_text: 原始文本

        返回:
            润色后的内容
        """
        print("正在润色内容...")
        
        # 检查原始内容长度
        original_length = len(original_text)
        print(f"原始内容长度: {original_length} 字符")
        
        # 如果原始内容太短，直接返回
        if original_length < 100:
            print("警告: 原始内容过短，跳过润色阶段")
            return original_text
            
        # 构建自定义润色提示词，确保不会大幅减少内容长度
        polish_prompt = f"""
你是一位专业的中文文学编辑，擅长对小说内容进行润色和优化。请对以下小说内容进行全面润色：

原文：
{original_text}

润色要求：
1. 语言表达：提升语言的优美度和流畅性，修正不自然或生硬的表达。
2. 词汇选择：使用更加精准、生动的词汇，避免重复用词，增强表现力。
3. 句式多样：调整句式结构，使长短句搭配合理，增加语言的节奏感和韵律感。
4. 修辞手法：适当增加比喻、拟人、排比等修辞手法，使文字更加生动形象。
5. 语法修正：纠正语法错误，确保文本在语法上的准确性。
6. 标点使用：规范标点符号的使用，使标点符号能够准确表达语气和停顿。

非常重要：
1. 必须保持原文的所有情节和内容，不得删减任何情节或场景
2. 润色后的内容长度必须至少与原文相同，最好略有增加
3. 不要添加任何提示词或解释，直接输出润色后的内容
4. 如果你无法完成润色，请原样返回原文
5. 不要改变原文的段落结构和分隔符

请直接给出润色后的内容，不需要解释修改原因。
"""

        # 调用LLM润色内容
        try:
            polished_content = self.call_llm(polish_prompt)
            
            # 检查润色后的内容长度
            polished_length = len(polished_content)
            print(f"润色后内容长度: {polished_length} 字符")
            
            # 如果润色后内容长度减少超过10%，或者内容看起来像错误信息，则使用原始内容
            if polished_length < original_length * 0.9 or "错误" in polished_content[:100] or "抱歉" in polished_content[:100]:
                print("警告: 润色后内容长度显著减少或内容异常，使用原始内容")
                return original_text
                
            print(f"内容润色完成，从 {original_length} 字符变为 {polished_length} 字符")
            return polished_content
            
        except Exception as e:
            print(f"润色过程中出错: {e}，使用原始内容")
            return original_text
