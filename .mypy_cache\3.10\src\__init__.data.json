{".class": "MypyFile", "_fullname": "src", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AI_MODEL_CONFIG": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.AI_MODEL_CONFIG", "kind": "Gdef"}, "APIClient": {".class": "SymbolTableNode", "cross_ref": "src.utils.api_client.APIClient", "kind": "Gdef"}, "Agent": {".class": "SymbolTableNode", "cross_ref": "src.core.agent.Agent", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Chapter": {".class": "SymbolTableNode", "cross_ref": "src.core.novel_core.Chapter", "kind": "Gdef"}, "ChapterGenerator": {".class": "SymbolTableNode", "cross_ref": "src.agents.chapter_generator.ChapterGenerator", "kind": "Gdef"}, "ChapterTransition": {".class": "SymbolTableNode", "cross_ref": "src.agents.chapter_transition.ChapterTransition", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "src.utils.config_class.Config", "kind": "Gdef"}, "DEFAULT_MODEL_NAMES": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.DEFAULT_MODEL_NAMES", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocumentGenerator": {".class": "SymbolTableNode", "cross_ref": "src.utils.document_generator.DocumentGenerator", "kind": "Gdef"}, "EnvManager": {".class": "SymbolTableNode", "cross_ref": "src.utils.env_manager.EnvManager", "kind": "Gdef"}, "ExpansionAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.expansion_agent.ExpansionAgent", "kind": "Gdef"}, "IllustrationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.illustration_agent.IllustrationAgent", "kind": "Gdef"}, "IllustrationGenerator": {".class": "SymbolTableNode", "cross_ref": "src.agents.illustration_generator.IllustrationGenerator", "kind": "Gdef"}, "IllustrationPrompts": {".class": "SymbolTableNode", "cross_ref": "src.prompts.prompts.IllustrationPrompts", "kind": "Gdef"}, "LLMInterface": {".class": "SymbolTableNode", "cross_ref": "src.core.novel_core.LLMInterface", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NEW_SYSTEM_AVAILABLE": {".class": "SymbolTableNode", "cross_ref": "src.novel_system.NEW_SYSTEM_AVAILABLE", "kind": "Gdef"}, "NovelManager": {".class": "SymbolTableNode", "cross_ref": "src.agents.novel_manager.NovelManager", "kind": "Gdef"}, "NovelState": {".class": "SymbolTableNode", "cross_ref": "src.core.novel_core.NovelState", "kind": "Gdef"}, "OLD_SYSTEM_AVAILABLE": {".class": "SymbolTableNode", "cross_ref": "src.novel_system.OLD_SYSTEM_AVAILABLE", "kind": "Gdef"}, "OptimizationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.optimization_agent.OptimizationAgent", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OutlineGeneratorAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.outline_generator.OutlineGeneratorAgent", "kind": "Gdef"}, "OutlineManager": {".class": "SymbolTableNode", "cross_ref": "src.core.outline_manager.OutlineManager", "kind": "Gdef"}, "OutlinePrompts": {".class": "SymbolTableNode", "cross_ref": "src.prompts.prompts.OutlinePrompts", "kind": "Gdef"}, "PolishingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.polishing_agent.PolishingAgent", "kind": "Gdef"}, "PolishingPrompts": {".class": "SymbolTableNode", "cross_ref": "src.prompts.prompts.PolishingPrompts", "kind": "Gdef"}, "ProgressCallback": {".class": "SymbolTableNode", "cross_ref": "src.core.novel_core.ProgressCallback", "kind": "Gdef"}, "ReviewAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.review_agent.ReviewAgent", "kind": "Gdef"}, "ReviewPrompts": {".class": "SymbolTableNode", "cross_ref": "src.prompts.prompts.ReviewPrompts", "kind": "Gdef"}, "SiliconFlowClient": {".class": "SymbolTableNode", "cross_ref": "src.utils.api_client.SiliconFlowClient", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WritingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_agent.WritingAgent", "kind": "Gdef"}, "WritingPrompts": {".class": "SymbolTableNode", "cross_ref": "src.prompts.prompts.WritingPrompts", "kind": "Gdef"}, "ZhipuAIClient": {".class": "SymbolTableNode", "cross_ref": "src.utils.api_client.ZhipuAIClient", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.__version__", "name": "__version__", "type": "builtins.str"}}, "add_custom_model": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.add_custom_model", "kind": "Gdef"}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "config": {".class": "SymbolTableNode", "cross_ref": "src.utils.config_class.config", "kind": "Gdef"}, "continue_novel": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.continue_novel", "kind": "Gdef"}, "create_timestamp_dir": {".class": "SymbolTableNode", "cross_ref": "src.core.novel_core.create_timestamp_dir", "kind": "Gdef"}, "create_writing_pipeline": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.create_writing_pipeline", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "delete_custom_model": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.delete_custom_model", "kind": "Gdef"}, "ensure_outline_has_basics": {".class": "SymbolTableNode", "cross_ref": "src.core.novel_core.ensure_outline_has_basics", "kind": "Gdef"}, "find_novel_files": {".class": "SymbolTableNode", "cross_ref": "src.core.novel_core.find_novel_files", "kind": "Gdef"}, "generate_novel": {".class": "SymbolTableNode", "cross_ref": "src.novel_system.generate_novel", "kind": "Gdef"}, "generate_novel_new": {".class": "SymbolTableNode", "cross_ref": "novel_integration.generate_novel", "kind": "Gdef"}, "get_api_client": {".class": "SymbolTableNode", "cross_ref": "src.utils.api_client.get_api_client", "kind": "Gdef"}, "get_custom_models": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.get_custom_models", "kind": "Gdef"}, "load_custom_models": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.load_custom_models", "kind": "Gdef"}, "log_chapter_generation": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.log_chapter_generation", "kind": "Gdef"}, "log_error": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.log_error", "kind": "Gdef"}, "log_model_usage": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.log_model_usage", "kind": "Gdef"}, "log_process_end": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.log_process_end", "kind": "Gdef"}, "log_process_start": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.log_process_start", "kind": "Gdef"}, "log_process_step": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.log_process_step", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.logger", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "cross_ref": "src.novel_system.main", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_arguments": {".class": "SymbolTableNode", "cross_ref": "src.novel_system.parse_arguments", "kind": "Gdef"}, "print_progress": {".class": "SymbolTableNode", "cross_ref": "src.novel_system.print_progress", "kind": "Gdef"}, "process_logger": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.process_logger", "kind": "Gdef"}, "register_outline_generator_agent": {".class": "SymbolTableNode", "cross_ref": "src.agents.outline_generator.register_outline_generator_agent", "kind": "Gdef"}, "register_writing_agents": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.register_writing_agents", "kind": "Gdef"}, "run_pipeline": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.run_pipeline", "kind": "Gdef"}, "save_custom_models": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.save_custom_models", "kind": "Gdef"}, "setup_chapter_logger": {".class": "SymbolTableNode", "cross_ref": "src.utils.logger.setup_chapter_logger", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "update_agent_model": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.update_agent_model", "kind": "Gdef"}}, "path": "E:\\storymc\\src\\__init__.py"}