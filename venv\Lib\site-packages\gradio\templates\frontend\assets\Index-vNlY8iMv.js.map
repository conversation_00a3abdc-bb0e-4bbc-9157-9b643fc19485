{"version": 3, "mappings": ";ojBACA,IAAIA,GAAe,IAAM,CACvB,MAAMC,EAAkB,SAAS,cAAc,MAAM,EACrDA,EAAgB,KAAO,0JACvBA,EAAgB,IAAM,aACtB,MAAMC,EAAW,SAAS,cAAc,MAAM,EAC9CA,EAAS,KAAO,uFAChBA,EAAS,IAAM,aACf,SAAS,KAAK,YAAYD,CAAe,EACzC,SAAS,KAAK,YAAYC,CAAQ,CACpC,EAGIC,GAAM,IAAM,CACd,MAAMC,EAAM,SAAS,cAAc,KAAK,EACxC,OAAAA,EAAI,MAAM,gBAAkB,0CAC5BA,EAAI,MAAM,OAAS,oBACnBA,EAAI,MAAM,aAAe,UACzBA,EAAI,MAAM,UAAY,8BACtBA,EAAI,MAAM,MAAQ,UAClBA,EAAI,MAAM,QAAU,OACpBA,EAAI,MAAM,cAAgB,MAC1BA,EAAI,MAAM,WAAa,SACvBA,EAAI,MAAM,OAAS,OACnBA,EAAI,MAAM,eAAiB,gBAC3BA,EAAI,MAAM,SAAW,SACrBA,EAAI,MAAM,SAAW,QACrBA,EAAI,MAAM,MAAQ,SAClBA,EAAI,MAAM,IAAM,SAChBA,EAAI,MAAM,MAAQ,OAClBA,EAAI,MAAM,OAAS,KACnBA,EAAI,MAAM,YAAc,OACxBA,EAAI,aAAa,KAAM,0BAA0B,EACjD,OAAO,WAAW,oBAAoB,EAAE,iBAAiB,SAAW,GAAM,CACpE,EAAE,QACJA,EAAI,MAAM,QAAU,OAEpBA,EAAI,MAAM,QAAU,MAE1B,CAAG,EACMA,CACT,EAGIC,GAAgB,IAAM,CACxB,MAAMC,EAAQ,SAAS,gBAAgB,6BAA8B,KAAK,EAC1EA,EAAM,aAAa,QAAS,4BAA4B,EACxDA,EAAM,aAAa,aAAc,8BAA8B,EAC/DA,EAAM,aAAa,cAAe,MAAM,EACxCA,EAAM,aAAa,YAAa,OAAO,EACvCA,EAAM,aAAa,OAAQ,KAAK,EAChCA,EAAM,aAAa,QAAS,KAAK,EACjCA,EAAM,aAAa,SAAU,KAAK,EAClCA,EAAM,aAAa,sBAAuB,eAAe,EACzDA,EAAM,aAAa,UAAW,WAAW,EACzCA,EAAM,aAAa,OAAQ,cAAc,EACzC,MAAMC,EAAO,SAAS,gBAAgB,6BAA8B,MAAM,EAC1E,OAAAA,EAAK,aACH,IACA,gwCACJ,EACED,EAAM,YAAYC,CAAI,EACfD,CACT,EAGIE,GAAW,CAACC,EAAOC,IAAa,CAClC,MAAMN,EAAM,SAAS,cAAc,KAAK,EACxC,OAAAA,EAAI,aAAa,KAAM,wBAAwB,EAC/CA,EAAI,MAAM,QAAU,OACpBA,EAAI,MAAM,cAAgB,MAC1BA,EAAI,MAAM,WAAa,SACvBA,EAAI,MAAM,eAAiB,SAC3BA,EAAI,MAAM,SAAW,OACrBA,EAAI,MAAM,YAAc,OACxBA,EAAI,MAAM,aAAe,OACzBA,EAAI,MAAM,OAAS,OACnBA,EAAI,MAAM,OAAS,UACnBA,EAAI,MAAM,MAAQ,UAClBA,EAAI,MAAM,mBAAqB,OAC/BA,EAAI,MAAM,mBAAqB,MAC/BA,EAAI,MAAM,yBAA2B,cACrCA,EAAI,YAAYC,GAAa,CAAE,EAC/BD,EAAI,iBAAiB,QAAUO,GAAM,CACnCA,EAAE,eAAc,EAChBA,EAAE,gBAAe,EACjBD,GACJ,CAAG,EACDN,EAAI,iBAAiB,aAAc,IAAM,CACvCA,EAAI,MAAM,MAAQ,SACtB,CAAG,EACDA,EAAI,iBAAiB,aAAc,IAAM,CACvCA,EAAI,MAAM,MAAQ,SACtB,CAAG,EACMA,CACT,EAGIQ,GAASC,GAAU,CACrB,MAAMC,EAAO,SAAS,cAAc,GAAG,EACvC,OAAAA,EAAK,MAAM,OAAS,IACpBA,EAAK,MAAM,QAAU,IACrBA,EAAK,MAAM,MAAQ,UACnBA,EAAK,MAAM,SAAW,OACtBA,EAAK,MAAM,WAAa,8BACxBA,EAAK,MAAM,QAAU,UACrBA,EAAK,MAAM,WAAa,oBACxBA,EAAK,MAAM,WAAa,MACxBA,EAAK,aAAeD,GAAwB,GAAG,WACxCC,CACT,EAGIC,GAAQ,IAAM,CAChB,MAAMC,EAAQ,SAAS,gBAAgB,6BAA8B,KAAK,EAC1EA,EAAM,aAAa,QAAS,4BAA4B,EACxDA,EAAM,aAAa,aAAc,8BAA8B,EAC/DA,EAAM,aAAa,cAAe,MAAM,EACxCA,EAAM,aAAa,YAAa,OAAO,EACvCA,EAAM,aAAa,OAAQ,KAAK,EAChCA,EAAM,aAAa,QAAS,KAAK,EACjCA,EAAM,aAAa,SAAU,KAAK,EAClCA,EAAM,aAAa,sBAAuB,eAAe,EACzDA,EAAM,aAAa,UAAW,WAAW,EACzCA,EAAM,aAAa,OAAQ,SAAS,EACpC,MAAMT,EAAO,SAAS,gBAAgB,6BAA8B,MAAM,EAC1E,OAAAA,EAAK,aACH,IACA,kUACJ,EACES,EAAM,YAAYT,CAAI,EACfS,CACT,EAGIC,GAAQR,GAAU,CACpB,MAAML,EAAM,SAAS,cAAc,GAAG,EACtC,OAAAA,EAAI,aAAa,OAAQ,iCAAiCK,EAAM,EAAE,EAAE,EACpEL,EAAI,aAAa,MAAO,qBAAqB,EAC7CA,EAAI,aAAa,SAAU,QAAQ,EACnCA,EAAI,MAAM,OAAS,oBACnBA,EAAI,MAAM,aAAe,MACzBA,EAAI,MAAM,QAAU,OACpBA,EAAI,MAAM,cAAgB,MAC1BA,EAAI,MAAM,WAAa,SACvBA,EAAI,MAAM,OAAS,aACnBA,EAAI,MAAM,SAAW,OACrBA,EAAI,MAAM,YAAc,MACxBA,EAAI,MAAM,eAAiB,OAC3BA,EAAI,YAAYW,GAAK,CAAE,EACvBX,EAAI,YAAYQ,GAAMH,EAAM,KAAK,CAAC,EAC3BL,CACT,EAGIc,GAAUC,GAAa,CACzB,MAAMC,EAAU,SAAS,cAAc,KAAK,EAC5C,OAAAA,EAAQ,IAAM,oCAAoCD,CAAQ,UAC1DC,EAAQ,MAAM,MAAQ,WACtBA,EAAQ,MAAM,OAAS,WACvBA,EAAQ,MAAM,aAAe,MAC7BA,EAAQ,MAAM,KAAO,OACrBA,EAAQ,MAAM,YAAc,WACrBA,CACT,EAGIC,GAAaC,GAAO,CACtB,KAAM,CAACC,EAAGC,CAAS,EAAIF,EAAG,MAAM,GAAG,EAC7BF,EAAU,SAAS,cAAc,GAAG,EAC1C,OAAAA,EAAQ,aAAa,OAAQ,iCAAiCE,CAAE,EAAE,EAClEF,EAAQ,aAAa,MAAO,qBAAqB,EACjDA,EAAQ,aAAa,SAAU,QAAQ,EACvCA,EAAQ,MAAM,MAAQ,UACtBA,EAAQ,MAAM,eAAiB,OAC/BA,EAAQ,MAAM,WAAa,MAC3BA,EAAQ,MAAM,SAAW,OACzBA,EAAQ,MAAM,WAAa,OAC3BA,EAAQ,MAAM,KAAO,OACrBA,EAAQ,MAAM,WAAa,4BAC3BA,EAAQ,iBAAiB,YAAa,IAAM,CAC1CA,EAAQ,MAAM,MAAQ,SAC1B,CAAG,EACDA,EAAQ,iBAAiB,WAAY,IAAM,CACzCA,EAAQ,MAAM,MAAQ,SAC1B,CAAG,EACDA,EAAQ,YAAcI,EACfJ,CACT,EAGIK,GAAa,IAAM,CACrB,MAAMC,EAAa,SAAS,cAAc,KAAK,EAC/C,OAAAA,EAAW,MAAM,WAAa,UAC9BA,EAAW,MAAM,YAAc,UAC/BA,EAAW,MAAM,MAAQ,UACzBA,EAAW,YAAc,IAClBA,CACT,EAGIC,GAAYR,GAAa,CAC3B,MAAMC,EAAU,SAAS,cAAc,GAAG,EAC1C,OAAAA,EAAQ,aAAa,OAAQ,0BAA0BD,CAAQ,EAAE,EACjEC,EAAQ,aAAa,MAAO,qBAAqB,EACjDA,EAAQ,aAAa,SAAU,QAAQ,EACvCA,EAAQ,MAAM,MAAQ,qBACtBA,EAAQ,MAAM,eAAiB,OAC/BA,EAAQ,MAAM,WAAa,MAC3BA,EAAQ,MAAM,SAAW,OACzBA,EAAQ,MAAM,WAAa,OAC3BA,EAAQ,MAAM,KAAO,OACrBA,EAAQ,MAAM,WAAa,8BAC3BA,EAAQ,iBAAiB,YAAa,IAAM,CAC1CA,EAAQ,MAAM,MAAQ,SAC1B,CAAG,EACDA,EAAQ,iBAAiB,WAAY,IAAM,CACzCA,EAAQ,MAAM,MAAQ,oBAC1B,CAAG,EACDA,EAAQ,YAAcD,EACfC,CACT,EAGIQ,GAAWnB,GAAU,CACvB,MAAMoB,EAAU,SAAS,cAAc,KAAK,EAC5C,OAAAA,EAAQ,MAAM,QAAU,OACxBA,EAAQ,MAAM,cAAgB,MAC9BA,EAAQ,MAAM,WAAa,SAC3BA,EAAQ,MAAM,eAAiB,SAC/BA,EAAQ,MAAM,YAAc,oBAC5BA,EAAQ,MAAM,aAAe,OAC7BA,EAAQ,MAAM,OAAS,OACvBA,EAAQ,YAAYX,GAAOT,EAAM,MAAM,CAAC,EACxCoB,EAAQ,YAAYF,GAASlB,EAAM,MAAM,CAAC,EAC1CoB,EAAQ,YAAYJ,GAAU,CAAE,EAChCI,EAAQ,YAAYR,GAAUZ,EAAM,EAAE,CAAC,EACvCoB,EAAQ,YAAYZ,GAAKR,CAAK,CAAC,EACxBoB,CACT,EAGIC,GAAUrB,GAAU,CACtB,MAAML,EAAMD,KACN4B,EAAiB,IAAM3B,EAAI,MAAM,QAAU,OACjD,OAAAA,EAAI,YAAYwB,GAAQnB,CAAK,CAAC,EAC9BL,EAAI,YAAYI,GAASC,EAAOsB,CAAc,CAAC,EACxC3B,CACT,EAGI4B,GAAY,MAAOC,GAAa,CAClC,GAAI,CAGF,OADa,MADI,MAAM,MAAM,qCAAqCA,CAAQ,EAAE,GAChD,MAE7B,MAAe,CACd,OAAO,IACR,CACH,EAGIC,GAAS,CAACd,EAASe,IAAY,CAQjC,GAAI,SAAS,OAAS,KACpB,OAAO,QAAQ,MAAM,uBAAuB,EAE9C,SAAS,KAAK,YAAYf,CAAO,CACnC,EAGA,eAAegB,GAAKC,EAAcF,EAAS,CACzC,IAAIG,EAAIC,EACR,GAAI,SAAW,OAAQ,OAAO,QAAQ,MAAM,iDAAiD,EAS7F,GARiC,OAAO,QACrCA,GAAMD,EAAK,OAAO,WAAa,KAAO,OAASA,EAAG,kBAAoB,KAAOC,EAAK,CACjF,EAAG,OAAO,SAAS,QACpB,CACL,EAAI,KAAMC,GAAW,CACjB,IAAIC,EACJ,QAASA,EAAM,IAAI,IAAID,CAAM,IAAM,KAAO,OAASC,EAAI,UAAY,wBACvE,CAAG,EAC6B,OAC9BzC,KACA,IAAIS,EACJ,GAAI,OAAO4B,GAAiB,UAE1B,GADA5B,EAAQ,MAAMuB,GAAUK,CAAY,EAChC5B,IAAU,KAAM,OAAO,QAAQ,MAAM,iBAAiB,OAE1DA,EAAQ4B,EAEV,MAAMK,EAAsBZ,GAAOrB,CAAK,EACxC,OAAAyB,GAAOQ,CAA4B,EAC5B,CACL,QAASA,CACb,CACA,CACA,IAAIC,GAAO,CAAClC,EAAO0B,IAAYC,GAAK3B,CAAc,8aC9NxC,SAAAmC,GAAA,sBAAAC,iBAAiD,2FAyZ7CC,EAAQ,UACXA,EAAa,UACd,kBACS,gBACJ,iBACC,2BAEPA,EAAE,gLAPGA,EAAQ,2BACXA,EAAa,gEAMfA,EAAE,sdAWHC,EAAAC,EAAAC,EAAAC,CAAA,uCAPCC,EAAAL,QAAoB,OAAKM,GAAA,wGAS1BL,EAAAC,EAAAK,EAAAH,CAAA,oEAkBCI,EAAAR,MAAG,4BAA4B,sEAAKC,EAAAC,EAAAC,EAAAC,CAAA,iBAApCK,EAAA,aAAAD,OAAAR,MAAG,4BAA4B,OAAAU,GAAAC,EAAAH,CAAA,yEAbjC,SACM,eAOP,iCACgC,MAC/B,oBACF,EATuCI,EAAAC,EAAA,OAAAC,EAAA,iCAAAd,+BAA8BA,EAAkB,IAAC,MACrFA,EAAQ,6BACOA,EAAkB,IAAC,YAClCA,OAAQ,OACR,SAAS,yEAKTC,EAAAC,EAAAC,EAAAC,CAAA,SAVKW,EASNZ,EAAAU,CAAA,wBARqCJ,EAAA,SAAAK,OAAA,iCAAAd,+BAA8BA,EAAkB,IAAC,MACrFA,EAAQ,6BACOA,EAAkB,IAAC,YAClCA,OAAQ,OACR,SAAS,oEARDA,EAAM,KAAE,SAAW,IAAE,uBAC3B,OAAAA,EAAO,aAAW,eAAiBA,EAAO,aAAW,WAAaA,MAAO,oBAAmBgB,mLAgB9Ff,EAAAC,EAAAK,EAAAH,CAAA,EAjBJW,EAA8CR,EAAAJ,CAAA,EAA3CY,EAAwCZ,EAAAc,CAAA,uDAA/BjB,EAAM,KAAE,SAAW,IAAE,KAAAU,GAAAQ,EAAAC,CAAA,0IA8B9BnB,EAAM,kBACIA,EAAQ,IAAIA,EAAM,IAAC,yBACrBA,EAAiB,uCAErBA,EAAO,oCAKDA,EAAQ,+CAGVA,EAAM,IAAC,YAAc,KAClB,cAAAA,MAAO,+BACN,MAAS,GACN,kCAAgB,OAAO,SAAS,MAAM,4MAPnCA,EAAc,iCAAdA,EAAc,sPARhCA,EAAM,8BACIA,EAAQ,IAAIA,EAAM,IAAC,uCACrBA,EAAiB,0DAErBA,EAAO,qDAKDA,EAAQ,0EAGVA,EAAM,IAAC,YAAc,gBAClB,cAAAA,MAAO,oCACN,MAAS,QACN,kCAAgB,OAAO,SAAS,MAAM,6JAPnCA,EAAc,6JAhBtB,aAAAA,MAAO,aACf,KAAAA,MAAO,cACHA,EAAK,mFAFDS,EAAA,UAAAW,EAAA,aAAApB,MAAO,cACfS,EAAA,UAAAW,EAAA,KAAApB,MAAO,4BACHA,EAAK,wJAjDXA,EAAa,MAAK,WAAaA,EAAa,MAAK,UAAO,EAAOA,EAAM,KAAIA,EAAM,KAAE,gBAAaqB,GAAArB,CAAA,8CA6C/FA,EAAM,KAAE,eAAiBA,EAAK,MAOzBA,EAAM,KAAIA,EAAM,KAAIA,EAAS,oJApDjCA,EAAa,MAAK,WAAaA,EAAa,MAAK,UAAO,EAAOA,EAAM,KAAIA,EAAM,KAAE,ubAd9E,QAAAA,MAAaA,EAAQ,iBAEtB,OAAAA,MAASA,EAAI,+CAIb,OAAAA,QAAkB,sBACdA,EAAM,KAAE,YAAc,0PAPzBS,EAAA,QAAAa,EAAA,QAAAtB,MAAaA,EAAQ,+BAEtBS,EAAA,SAAAa,EAAA,OAAAtB,MAASA,EAAI,sFAIbS,EAAA,YAAAa,EAAA,OAAAtB,QAAkB,qCACdA,EAAM,KAAE,YAAc,sSAhb9BxB,GAAK,GAEA,SAAA+C,IAAA,CAIFC,QAAeC,GAAA,IAEfC,MAAU,IAEVC,EAAA,IAAe,qBAAsBC,GAAA,CAC1CA,EAAQ,QAASC,GAAA,IACZA,EAAM,oBACLC,EAA0BJ,EAAI,IAAIG,EAAM,MAAwB,EAChEC,IAAA,QACUC,EAAA,WAAoB,IAAAC,EAAA,CAAIF,CAAa,EAAG,WAKhD,SAAAG,EAASC,EAAaC,EAAA,CAC1BT,EAAA,IAAIS,EAAID,CAAG,EACfP,EAAS,QAAQQ,CAAE,SAGX,SAAAF,EAAU,UAAWT,EAAa,iBAGtCA,GAAeD,GAAA,iBAsGNa,GACdC,EAAA,CAEI,GAAAA,EAAA,CACG,MAAAC,EAAa,cACbC,EAAmB,MAAM,KAC9BD,EAAO,gBAAgBD,EAAa,WAAW,EAAE,KAAK,UAGnD,GAAAE,UACMC,KAAgBD,EAAA,KACpBE,EAAa,SAAS,cAAcD,EAAa,OAAO,EAO3D,GAND,MAAM,KAAKA,EAAa,UAAU,EAAE,QAAS5B,GAAA,CAC5C6B,EAAW,aAAa7B,EAAK,KAAMA,EAAK,KAAK,IAE9C6B,EAAW,YAAcD,EAAa,YAGrCC,EAAW,SAAW,QACtBA,EAAW,aAAa,UAAU,GAK5B,MAAAC,EAHc,MAAM,KACzB,SAAS,KAAK,qBAAqB,MAAM,OAEd,KAAMP,GAEhCA,EAAG,aAAa,UAAU,GACzBM,EAAW,aAAa,UAAU,IAClCN,EAAG,YAAYM,CAAU,GAGxB,GAAAC,EAAA,CACM,cAAK,aAAaD,EAAYC,CAAO,YAKvC,cAAK,YAAYD,CAAU,2EA5HxCE,WAEMC,EAAW7C,KAEN,eAAA8C,CAAA,EAAAC,EACA,SAAAC,CAAA,EAAAD,EACA,gBAAAE,CAAA,EAAAF,EACA,UAAAG,CAAA,EAAAH,EACA,UAAAI,CAAA,EAAAJ,GACA,WAAAK,EAA+B,UAAAL,EAC/B,oBAAAM,CAAA,EAAAN,EACA,WAAAO,CAAA,EAAAP,EACA,MAAAQ,CAAA,EAAAR,EACA,OAAAS,CAAA,EAAAT,EACPU,EACAC,EAAA,GACAC,GACAC,cAGOC,EAAsCC,EAAA,EAAAf,EACtC,QAAAgB,CAAA,EAAAhB,GACA,aAAAiB,EAAwC,QAAAjB,EAC/CiB,IACHC,GAAsBD,CAAY,EAErBA,EAAA,iBAAiB,kBAAoBE,GAAA,CACjDC,EAAA,GAAAC,GAAgBF,EAAsB,OAAS,UAG7C,IAAAG,GAAUL,IAAiB,OAEpB,OAAApG,CAAA,EAAAmF,EACA,KAAAuB,CAAA,EAAAvB,EAEPZ,GAAM1D,KAEN8F,GACH,UAEGC,EACAC,EAAQ,GACRC,EAAkB,GAClBC,EACAP,GAAeQ,EAAG,gBAAgB,EAAI,MACtCC,GACAC,EAMAC,EAA+C,oBACpCC,GAAiBC,EAAA,CAC3BA,IACEF,IACkBA,EAAA,SAAS,cAAc,OAAO,EAC3C,cAAK,YAAYA,CAAmB,GAE9CA,EAAoB,YAAcG,GACjCD,EACAjC,EACA+B,CAAA,GAGI,MAAAlB,EACLc,EAAO,KAAO,gBAAkBA,EAAO,WACvC,SAAS,MAELA,EAAO,mBAEN,QAAQ,IACbA,EAAO,YAAY,IAAKQ,GAEtBA,EAAW,WAAW,OAAO,GAAKA,EAAW,WAAW,QAAQ,EAEzDtB,EAAUsB,EAAY,SAAS,IAAI,EAGpC,MAAMR,EAAO,KAAO,IAAMQ,CAAU,EACzC,KAAMC,GAAaA,EAAS,QAC5B,KAAMH,IACNC,GAAWD,EAAYjC,CAAO,eAgD1BqC,GAAkBlF,EAAA,CACpB,MAAAmF,EAAc,OAAO,kBAAoB,UAE3C,IAAAC,EACA,GAAAD,EACcC,EAAA,mBAGXC,EADU,QAAI,OAAO,SAAS,YACS,aAAa,IACzD,WAEDD,EAAiBnC,GAAcoC,GAAkB,SAG9C,OAAAD,IAAmB,QAAUA,IAAmB,QACnDE,GAAYtF,EAAQoF,CAAc,EAElCA,EAAiBG,GAAkBvF,CAAM,EAEnCoF,WAGCG,GAAkBvF,EAAA,OACpBwF,EAAQC,IACd,QACG,WAAW,8BAA8B,GACzC,iBAAiB,SAAUA,CAAa,EAElC,SAAAA,GAAA,KACJC,EAA2B,QAAQ,aACtC,8BACC,UACC,OACA,QAEH,OAAAJ,GAAYtF,EAAQ0F,CAAM,EACnBA,EAED,OAAAF,EAGC,SAAAF,GAAYtF,EAAwBwF,EAAA,OACtCG,EAAqB3C,EAAWhD,EAAO,cAAiB,SAAS,KACjE4F,EAAa5C,EAAWhD,EAASA,EAAO,cAC9C4F,EAAW,MAAM,WAAa,8BAC1BJ,IAAU,OACMG,EAAA,UAAU,IAAI,MAAM,EAEpBA,EAAA,UAAU,OAAO,MAAM,EAIxC,IAAAE,EAAA,CACH,QAAS,GACT,YAAa,UACb,OAAQ,WACR,OAAQ,YAGLC,EACAC,EAAY,YACPC,GAAcC,EAAA,MACtBJ,EAASI,CAAA,EAGJ,MAAAC,GAAkB,OAAO,eAE/BtG,GAAA,UACCoE,EAAA,GAAAU,GAAoBQ,GAAkBb,CAAO,GAGvC,MAAA8B,EAAc,OAAO,2BAGFxB,EAAAuB,KAAoB,iCAElCC,GAAgB,SAAWA,EAAc,IACjD,GACC1I,GAAS0G,GAAO,SAAS,OAE7BH,EAAA,GAAA8B,EAAA,MAAYlC,EAAO,QAAQe,EAAA,CAC1B,gBAAiBqB,GACjB,gBAAiB,GACjB,OAAS,QAAQ,MAAO,SAAU,QAAQ,KAEpC,wBAAiB,oBACvBF,EAAI,WAGAA,EAAI,OACE,gBAAM,8BAA8B,EAG/C9B,EAAA,GAAAQ,EAASsB,EAAI,kBACb,OAAO,iBAAmBtB,EAAO,SAEjCR,EAAA,GAAA6B,EAAA,CACC,QAAS,GACT,YAAa,WACb,OAAQ,UACR,OAAQ,YAGH,MAAAhB,GAAiBL,EAAO,GAAG,EAC3B,MAAAtC,GAAqBsC,EAAO,IAAI,OACtCuB,EAAY,IACZ,OAAO,aAAevB,EAAO,eAEvB4B,EAA2B,2BAC1B,wBAAiB,UAAYrC,GAAA,CAC/BA,EAAM,OAASqC,IAClB,OAAO,yBAA2B,MAG9B,MAAAC,EAAW,OAAO,SAAS,SAC3B7G,EAAS6G,EAAS,SAAS,OAAO,kBACrBA,EAAS,MAAM,GAAG,EAAE,CAAC,yDAEjC,cAAO,YAAYD,EAA0B5G,CAAM,EAE1DkD,EAAS,QAAQ,EAEjBsB,EAAA,GAAAT,EAAQiB,EAAO,OACfR,EAAA,GAAAR,GAAegB,EAAO,cACtBR,EAAA,GAAAP,GAAOe,EAAO,MAEVA,EAAO,UACV,gBACS,WAAA8B,CAAA,MAAa,IAAI3B,CAAO,EAC5B,IAAA4B,GAAA,IAAU,IAAc,UAAAD,CAAI,GAAGR,EAAI,UAAU,eACjDxC,EAAA,IAAa,YAAYiD,EAAG,EACrBjD,EAAA,iBAAiB,QAAgB,MAAA3F,IAAA,CACxB6I,EAAA,QAAS,sBAAuB,OAAO,EAEtD,QAAQ,MAAM,KAAK,MAAM7I,GAAE,IAAI,KAEzB2F,EAAA,iBAAiB,SAAiB,MAAAS,IAAA,IACxC+B,EAAI,QACJ9B,EAAA,GAAA8B,EAAA,MAAYlC,EAAO,QAAQe,EAAA,CAC1B,gBAAiBqB,GACjB,gBAAiB,GACjB,OAAS,QAAQ,MAAO,SAAU,QAAQ,MAGtCF,EAAI,OACE,gBAAM,8BAA8B,EAG/C9B,EAAA,GAAAQ,EAASsB,EAAI,kBACb,OAAO,iBAAmBtB,EAAO,SAC3B,MAAAK,GAAiBL,EAAO,GAAG,EAC3B,MAAAtC,GAAqBsC,EAAO,IAAI,OACtCuB,EAAY,IACZ,OAAO,aAAevB,EAAO,SAC7B9B,EAAS,QAAQ,KAEhB,OAaD,IAAA+D,GAEAC,GAEW,eAAAC,IAAA,CACd3C,EAAA,GAAAyC,IAAA,MAAAG,GAAA,WAAuB,sBAAqB,gIAAG,SAEjC,eAAAC,IAAA,CACd7C,EAAA,GAAA0C,IAAA,MAAAE,GAAA,WAAsB,qBAAoB,gJAAG,SAGrC,SAAAE,IAAA,CACJtC,EAAO,cACNqC,KAAAF,KAWA,MAAAI,GAAA,CACL,gBACC,YAAatC,EAAG,oBAAoB,EACpC,aAAcA,EAAG,qBAAqB,EACtC,YAAaA,EAAG,oBAAoB,EACpC,cAAeA,EAAG,sBAAsB,EACxC,OAAQA,EAAG,qBAAqB,GAEjC,MAAMuC,EAAA,CACE,0BAAmBvC,EAAG,0BAA0B,IAExD,YAAYuC,EAAoBC,EAAA,CACxB;AAAA;AAAA;AAAA;AAAA,oEAEL,KAAK,eAAeD,CAAK,GAAK,UAC/B;AAAA;AAAA,6FAAmGC,CAAI;AAAA;AAAA,YAKtG,IAAAT,EAEJ5G,GAAA,UACc0B,GAAA,SAASU,GAAKqC,CAAO,IAc/B,IAAA6C,EAEW,eAAAC,GACdlI,EACA+D,MAEI/D,GAAa+D,IAAY,OAAO,OAAS,OAAO,KAC/CkE,IACHA,EAAY,SACEA,EAAA,QAET,MAAAE,EAAA,MAAezH,GAAKV,CAAQ,EAC9BmI,IAAQF,EAAcE,EAAO,UAInCC,GAAA,KACCH,GAAa,gFAkFwBV,EAAAc,inBA3Z/B9C,GAAQ,QACLA,EAAO,2BAiPdR,EAAA,GAAAI,GAAA,CACDE,GAASuB,EAAO,cAAgB,QAC9B,UACC,CAAAvB,GAASuB,EAAO,cAAgB,QAChC,QACAA,EAAO,wDAETrB,IAAWnB,GAASkE,EAAcvF,EAAG,IAAM8E,GAAA,sBAoDvCvC,GACNF,EAAQ,kBACH,YAAY,UACf,QAAS,GACT,WAAY,GACZ,SAAU,2BAKVyB,GAAK,QAAUqB,GAAmBrB,GAAK,QAAQ,SAAU9C,CAAQ", "names": ["inject_fonts", "source_sans_pro", "ibm_mono", "Box", "box", "ArrowCollapse", "arrow", "path", "Collapse", "space", "callback", "e", "Count", "count", "text", "Heart", "heart", "Like", "Avatar", "username", "element", "Namespace", "id", "_", "spaceName", "Separation", "separation", "Username", "Content", "content", "create", "handleCollapse", "get_space", "space_id", "inject", "options", "main", "initialSpace", "_a", "_b", "origin", "_a2", "mini_header_element", "init", "onMount", "createEventDispatcher", "ctx", "insert", "target", "p", "anchor", "if_block", "create_if_block_4", "div", "t_value", "dirty", "set_data", "t", "attr", "a", "a_href_value", "append", "create_if_block_3", "strong", "t0", "t0_value", "login_changes", "create_if_block_2", "embed_changes", "create_intersection_store", "intersecting", "writable", "els", "observer", "entries", "entry", "_el", "intersecting2", "s", "register", "_id", "el", "add_custom_html_head", "head_string", "parser", "parsed_head_html", "head_element", "newElement", "matched", "setupi18n", "dispatch", "autoscroll", "$$props", "version", "initial_height", "app_mode", "is_embed", "theme_mode", "control_page_title", "container", "info", "eager", "stream", "pages", "current_page", "root", "mount_css", "default_mount_css", "Client", "worker_proxy", "setWorkerProxyContext", "event", "$$invalidate", "loading_text", "is_lite", "src", "loader_status", "wrapper", "ready", "render_complete", "config", "$_", "active_theme_mode", "api_url", "css_text_stylesheet", "mount_custom_css", "css_string", "prefix_css", "stylesheet", "response", "handle_theme_mode", "force_light", "new_theme_mode", "url_color_mode", "apply_theme", "sync_system_theme", "theme", "update_scheme", "_theme", "dark_class_element", "bg_element", "status", "app", "css_ready", "handle_status", "_status", "gradio_dev_mode", "server_port", "supports_zerogpu_headers", "hostname", "host", "url", "new_message_fn", "Blocks", "<PERSON><PERSON>", "get_blocks", "__vitePreload", "get_login", "load_demo", "discussion_message", "error", "site", "spaceheader", "mount_space_header", "header", "onDestroy", "value", "$intersecting"], "ignoreList": [0], "sources": ["../../../../node_modules/.pnpm/@huggingface+space-header@1.0.3/node_modules/@huggingface/space-header/dist/browser/index.mjs", "../../../../js/spa/src/Index.svelte"], "sourcesContent": ["// src/inject_fonts.ts\nvar inject_fonts = () => {\n  const source_sans_pro = document.createElement(\"link\");\n  source_sans_pro.href = \"https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap\";\n  source_sans_pro.rel = \"stylesheet\";\n  const ibm_mono = document.createElement(\"link\");\n  ibm_mono.href = \"https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap\";\n  ibm_mono.rel = \"stylesheet\";\n  document.head.appendChild(source_sans_pro);\n  document.head.appendChild(ibm_mono);\n};\n\n// src/header/components/box.ts\nvar Box = () => {\n  const box = document.createElement(\"div\");\n  box.style.backgroundImage = \"linear-gradient(to top, #f9fafb, white)\";\n  box.style.border = \"1px solid #e5e7eb\";\n  box.style.borderRadius = \"0.75rem\";\n  box.style.boxShadow = \"0 0 10px rgba(0, 0, 0, 0.1)\";\n  box.style.color = \"#374151\";\n  box.style.display = \"flex\";\n  box.style.flexDirection = \"row\";\n  box.style.alignItems = \"center\";\n  box.style.height = \"40px\";\n  box.style.justifyContent = \"space-between\";\n  box.style.overflow = \"hidden\";\n  box.style.position = \"fixed\";\n  box.style.right = \".75rem\";\n  box.style.top = \".75rem\";\n  box.style.width = \"auto\";\n  box.style.zIndex = \"20\";\n  box.style.paddingLeft = \"1rem\";\n  box.setAttribute(\"id\", \"huggingface-space-header\");\n  window.matchMedia(\"(max-width: 768px)\").addEventListener(\"change\", (e) => {\n    if (e.matches) {\n      box.style.display = \"none\";\n    } else {\n      box.style.display = \"flex\";\n    }\n  });\n  return box;\n};\n\n// src/header/components/collapse/arrow.ts\nvar ArrowCollapse = () => {\n  const arrow = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n  arrow.setAttribute(\"xmlns\", \"http://www.w3.org/2000/svg\");\n  arrow.setAttribute(\"xmlns:link\", \"http://www.w3.org/1999/xlink\");\n  arrow.setAttribute(\"aria-hidden\", \"true\");\n  arrow.setAttribute(\"focusable\", \"false\");\n  arrow.setAttribute(\"role\", \"img\");\n  arrow.setAttribute(\"width\", \"1em\");\n  arrow.setAttribute(\"height\", \"1em\");\n  arrow.setAttribute(\"preserveAspectRatio\", \"xMidYMid meet\");\n  arrow.setAttribute(\"viewBox\", \"0 0 12 12\");\n  arrow.setAttribute(\"fill\", \"currentColor\");\n  const path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n  path.setAttribute(\n    \"d\",\n    \"M0.375001 10.3828L0.375 1.61719C0.375 1.104 0.816001 0.687501 1.35938 0.687501L10.6406 0.6875C10.9017 0.6875 11.1521 0.785449 11.3367 0.959797C11.5213 1.13415 11.625 1.37062 11.625 1.61719V10.3828C11.625 10.6294 11.5213 10.8659 11.3367 11.0402C11.1521 11.2145 10.9017 11.3125 10.6406 11.3125H1.35938C0.816001 11.3125 0.375001 10.896 0.375001 10.3828ZM1.35938 10.5156H10.6406C10.7183 10.5156 10.7813 10.4561 10.7813 10.3828V4.40625H1.21875V10.3828C1.21875 10.418 1.23356 10.4518 1.25994 10.4767C1.28631 10.5017 1.32208 10.5156 1.35938 10.5156ZM4.61052 6.38251L5.9999 7.69472L7.38927 6.38251C7.44083 6.33007 7.50645 6.29173 7.57913 6.27153C7.6518 6.25134 7.72898 6.25003 7.8024 6.26776C7.87583 6.28549 7.9428 6.3216 7.99628 6.37227C8.04983 6.42295 8.08785 6.48631 8.10645 6.5557C8.12528 6.62497 8.12393 6.69773 8.10263 6.76635C8.0814 6.83497 8.0409 6.8969 7.98555 6.94564L6.29802 8.53936C6.21892 8.61399 6.11169 8.65592 5.9999 8.65592C5.8881 8.65592 5.78087 8.61399 5.70177 8.53936L4.01427 6.94564C3.95874 6.89694 3.91814 6.835 3.89676 6.76633C3.87538 6.69766 3.874 6.62483 3.89277 6.55549C3.91154 6.48615 3.94977 6.42287 4.00343 6.37233C4.05708 6.32179 4.12418 6.28585 4.19765 6.2683C4.27098 6.25054 4.34803 6.25178 4.42068 6.27188C4.49334 6.29198 4.55891 6.3302 4.61052 6.38251Z\"\n  );\n  arrow.appendChild(path);\n  return arrow;\n};\n\n// src/header/components/collapse/index.ts\nvar Collapse = (space, callback) => {\n  const box = document.createElement(\"div\");\n  box.setAttribute(\"id\", \"space-header__collapse\");\n  box.style.display = \"flex\";\n  box.style.flexDirection = \"row\";\n  box.style.alignItems = \"center\";\n  box.style.justifyContent = \"center\";\n  box.style.fontSize = \"16px\";\n  box.style.paddingLeft = \"10px\";\n  box.style.paddingRight = \"10px\";\n  box.style.height = \"40px\";\n  box.style.cursor = \"pointer\";\n  box.style.color = \"#40546e\";\n  box.style.transitionDuration = \"0.1s\";\n  box.style.transitionProperty = \"all\";\n  box.style.transitionTimingFunction = \"ease-in-out\";\n  box.appendChild(ArrowCollapse());\n  box.addEventListener(\"click\", (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    callback();\n  });\n  box.addEventListener(\"mouseenter\", () => {\n    box.style.color = \"#213551\";\n  });\n  box.addEventListener(\"mouseleave\", () => {\n    box.style.color = \"#40546e\";\n  });\n  return box;\n};\n\n// src/header/components/like/count.ts\nvar Count = (count) => {\n  const text = document.createElement(\"p\");\n  text.style.margin = \"0\";\n  text.style.padding = \"0\";\n  text.style.color = \"#9ca3af\";\n  text.style.fontSize = \"14px\";\n  text.style.fontFamily = \"Source Sans Pro, sans-serif\";\n  text.style.padding = \"0px 6px\";\n  text.style.borderLeft = \"1px solid #e5e7eb\";\n  text.style.marginLeft = \"4px\";\n  text.textContent = (count != null ? count : 0).toString();\n  return text;\n};\n\n// src/header/components/like/heart.ts\nvar Heart = () => {\n  const heart = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n  heart.setAttribute(\"xmlns\", \"http://www.w3.org/2000/svg\");\n  heart.setAttribute(\"xmlns:link\", \"http://www.w3.org/1999/xlink\");\n  heart.setAttribute(\"aria-hidden\", \"true\");\n  heart.setAttribute(\"focusable\", \"false\");\n  heart.setAttribute(\"role\", \"img\");\n  heart.setAttribute(\"width\", \"1em\");\n  heart.setAttribute(\"height\", \"1em\");\n  heart.setAttribute(\"preserveAspectRatio\", \"xMidYMid meet\");\n  heart.setAttribute(\"viewBox\", \"0 0 32 32\");\n  heart.setAttribute(\"fill\", \"#6b7280\");\n  const path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n  path.setAttribute(\n    \"d\",\n    \"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"\n  );\n  heart.appendChild(path);\n  return heart;\n};\n\n// src/header/components/like/index.ts\nvar Like = (space) => {\n  const box = document.createElement(\"a\");\n  box.setAttribute(\"href\", `https://huggingface.co/spaces/${space.id}`);\n  box.setAttribute(\"rel\", \"noopener noreferrer\");\n  box.setAttribute(\"target\", \"_blank\");\n  box.style.border = \"1px solid #e5e7eb\";\n  box.style.borderRadius = \"6px\";\n  box.style.display = \"flex\";\n  box.style.flexDirection = \"row\";\n  box.style.alignItems = \"center\";\n  box.style.margin = \"0 0 0 12px\";\n  box.style.fontSize = \"14px\";\n  box.style.paddingLeft = \"4px\";\n  box.style.textDecoration = \"none\";\n  box.appendChild(Heart());\n  box.appendChild(Count(space.likes));\n  return box;\n};\n\n// src/header/components/content/avatar.ts\nvar Avatar = (username) => {\n  const element = document.createElement(\"img\");\n  element.src = `https://huggingface.co/api/users/${username}/avatar`;\n  element.style.width = \"0.875rem\";\n  element.style.height = \"0.875rem\";\n  element.style.borderRadius = \"50%\";\n  element.style.flex = \"none\";\n  element.style.marginRight = \"0.375rem\";\n  return element;\n};\n\n// src/header/components/content/namespace.ts\nvar Namespace = (id) => {\n  const [_, spaceName] = id.split(\"/\");\n  const element = document.createElement(\"a\");\n  element.setAttribute(\"href\", `https://huggingface.co/spaces/${id}`);\n  element.setAttribute(\"rel\", \"noopener noreferrer\");\n  element.setAttribute(\"target\", \"_blank\");\n  element.style.color = \"#1f2937\";\n  element.style.textDecoration = \"none\";\n  element.style.fontWeight = \"600\";\n  element.style.fontSize = \"15px\";\n  element.style.lineHeight = \"24px\";\n  element.style.flex = \"none\";\n  element.style.fontFamily = \"IBM Plex Mono, sans-serif\";\n  element.addEventListener(\"mouseover\", () => {\n    element.style.color = \"#2563eb\";\n  });\n  element.addEventListener(\"mouseout\", () => {\n    element.style.color = \"#1f2937\";\n  });\n  element.textContent = spaceName;\n  return element;\n};\n\n// src/header/components/content/separation.ts\nvar Separation = () => {\n  const separation = document.createElement(\"div\");\n  separation.style.marginLeft = \".125rem\";\n  separation.style.marginRight = \".125rem\";\n  separation.style.color = \"#d1d5db\";\n  separation.textContent = \"/\";\n  return separation;\n};\n\n// src/header/components/content/username.ts\nvar Username = (username) => {\n  const element = document.createElement(\"a\");\n  element.setAttribute(\"href\", `https://huggingface.co/${username}`);\n  element.setAttribute(\"rel\", \"noopener noreferrer\");\n  element.setAttribute(\"target\", \"_blank\");\n  element.style.color = \"rgb(107, 114, 128)\";\n  element.style.textDecoration = \"none\";\n  element.style.fontWeight = \"400\";\n  element.style.fontSize = \"16px\";\n  element.style.lineHeight = \"24px\";\n  element.style.flex = \"none\";\n  element.style.fontFamily = \"Source Sans Pro, sans-serif\";\n  element.addEventListener(\"mouseover\", () => {\n    element.style.color = \"#2563eb\";\n  });\n  element.addEventListener(\"mouseout\", () => {\n    element.style.color = \"rgb(107, 114, 128)\";\n  });\n  element.textContent = username;\n  return element;\n};\n\n// src/header/components/content/index.ts\nvar Content = (space) => {\n  const content = document.createElement(\"div\");\n  content.style.display = \"flex\";\n  content.style.flexDirection = \"row\";\n  content.style.alignItems = \"center\";\n  content.style.justifyContent = \"center\";\n  content.style.borderRight = \"1px solid #e5e7eb\";\n  content.style.paddingRight = \"12px\";\n  content.style.height = \"40px\";\n  content.appendChild(Avatar(space.author));\n  content.appendChild(Username(space.author));\n  content.appendChild(Separation());\n  content.appendChild(Namespace(space.id));\n  content.appendChild(Like(space));\n  return content;\n};\n\n// src/header/create.ts\nvar create = (space) => {\n  const box = Box();\n  const handleCollapse = () => box.style.display = \"none\";\n  box.appendChild(Content(space));\n  box.appendChild(Collapse(space, handleCollapse));\n  return box;\n};\n\n// src/get_space.ts\nvar get_space = async (space_id) => {\n  try {\n    const response = await fetch(`https://huggingface.co/api/spaces/${space_id}`);\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return null;\n  }\n};\n\n// src/inject.ts\nvar inject = (element, options) => {\n  if (options == null ? void 0 : options.target) {\n    if (options.target.appendChild) {\n      options.target.appendChild(element);\n      return;\n    }\n    return console.error(\"the target element does not have an appendChild method\");\n  }\n  if (document.body === null) {\n    return console.error(\"document.body is null\");\n  }\n  document.body.appendChild(element);\n};\n\n// src/index.ts\nasync function main(initialSpace, options) {\n  var _a, _b;\n  if (window === void 0) return console.error(\"Please run this script in a browser environment\");\n  const has_huggingface_ancestor = Object.values(\n    (_b = (_a = window.location) == null ? void 0 : _a.ancestorOrigins) != null ? _b : {\n      0: window.document.referrer\n    }\n  ).some((origin) => {\n    var _a2;\n    return ((_a2 = new URL(origin)) == null ? void 0 : _a2.origin) === \"https://huggingface.co\";\n  });\n  if (has_huggingface_ancestor) return;\n  inject_fonts();\n  let space;\n  if (typeof initialSpace === \"string\") {\n    space = await get_space(initialSpace);\n    if (space === null) return console.error(\"Space not found\");\n  } else {\n    space = initialSpace;\n  }\n  const mini_header_element = create(space);\n  inject(mini_header_element, options);\n  return {\n    element: mini_header_element\n  };\n}\nvar init = (space, options) => main(space, options);\nexport {\n  init\n};\n", "<script context=\"module\" lang=\"ts\">\n\timport { writable } from \"svelte/store\";\n\timport { mount_css as default_mount_css, prefix_css } from \"@gradio/core\";\n\n\timport type { Client as ClientType } from \"@gradio/client\";\n\n\timport type { ComponentMeta, Dependency, LayoutNode } from \"@gradio/core\";\n\n\tdeclare let BUILD_MODE: string;\n\tinterface Config {\n\t\tauth_required?: true;\n\t\tauth_message: string;\n\t\tcomponents: ComponentMeta[];\n\t\tcss: string | null;\n\t\tjs: string | null;\n\t\thead: string | null;\n\t\tdependencies: Dependency[];\n\t\tdev_mode: boolean;\n\t\tenable_queue: boolean;\n\t\tlayout: LayoutNode;\n\t\tmode: \"blocks\" | \"interface\";\n\t\troot: string;\n\t\ttheme: string;\n\t\ttitle: string;\n\t\tversion: string;\n\t\tspace_id: string | null;\n\t\tis_colab: boolean;\n\t\tshow_api: boolean;\n\t\tstylesheets?: string[];\n\t\tpath: string;\n\t\tapp_id?: string;\n\t\tfill_height?: boolean;\n\t\tfill_width?: boolean;\n\t\ttheme_hash?: number;\n\t\tusername: string | null;\n\t\tapi_prefix?: string;\n\t\tmax_file_size?: number;\n\t\tpages: [string, string][];\n\t\tcurrent_page: string;\n\t\tpage: Record<\n\t\t\tstring,\n\t\t\t{\n\t\t\t\tcomponents: number[];\n\t\t\t\tdependencies: number[];\n\t\t\t\tlayout: any;\n\t\t\t}\n\t\t>;\n\t}\n\n\tlet id = -1;\n\n\tfunction create_intersection_store(): {\n\t\tregister: (n: number, el: HTMLDivElement) => void;\n\t\tsubscribe: (typeof intersecting)[\"subscribe\"];\n\t} {\n\t\tconst intersecting = writable<Record<string, boolean>>({});\n\n\t\tconst els = new Map<HTMLDivElement, number>();\n\n\t\tconst observer = new IntersectionObserver((entries) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting) {\n\t\t\t\t\tlet _el: number | undefined = els.get(entry.target as HTMLDivElement);\n\t\t\t\t\tif (_el !== undefined)\n\t\t\t\t\t\tintersecting.update((s) => ({ ...s, [_el as number]: true }));\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tfunction register(_id: number, el: HTMLDivElement): void {\n\t\t\tels.set(el, _id);\n\t\t\tobserver.observe(el);\n\t\t}\n\n\t\treturn { register, subscribe: intersecting.subscribe };\n\t}\n\n\tconst intersecting = create_intersection_store();\n</script>\n\n<script lang=\"ts\">\n\timport { onMount, createEventDispatcher, onDestroy } from \"svelte\";\n\timport type { SpaceStatus } from \"@gradio/client\";\n\timport { Embed } from \"@gradio/core\";\n\timport type { ThemeMode } from \"@gradio/core\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { _ } from \"svelte-i18n\";\n\timport { setupi18n } from \"@gradio/core\";\n\timport type { WorkerProxy } from \"@gradio/wasm\";\n\timport { setWorkerProxyContext } from \"@gradio/wasm/svelte\";\n\timport { init } from \"@huggingface/space-header\";\n\n\tsetupi18n();\n\n\tconst dispatch = createEventDispatcher();\n\n\texport let autoscroll: boolean;\n\texport let version: string;\n\texport let initial_height: string;\n\texport let app_mode: boolean;\n\texport let is_embed: boolean;\n\texport let theme_mode: ThemeMode | null = \"system\";\n\texport let control_page_title: boolean;\n\texport let container: boolean;\n\texport let info: boolean;\n\texport let eager: boolean;\n\tlet stream: EventSource;\n\tlet pages: [string, string][] = [];\n\tlet current_page: string;\n\tlet root: string;\n\n\t// These utilities are exported to be injectable for the Wasm version.\n\texport let mount_css: typeof default_mount_css = default_mount_css;\n\texport let Client: typeof ClientType;\n\texport let worker_proxy: WorkerProxy | undefined = undefined;\n\tif (worker_proxy) {\n\t\tsetWorkerProxyContext(worker_proxy);\n\n\t\tworker_proxy.addEventListener(\"progress-update\", (event) => {\n\t\t\tloading_text = (event as CustomEvent).detail + \"...\";\n\t\t});\n\t}\n\tlet is_lite = worker_proxy !== undefined;\n\n\texport let space: string | null;\n\texport let src: string | null;\n\n\tlet _id = id++;\n\n\tlet loader_status: \"pending\" | \"error\" | \"complete\" | \"generating\" =\n\t\t\"pending\";\n\tlet app_id: string | null = null;\n\tlet wrapper: HTMLDivElement;\n\tlet ready = false;\n\tlet render_complete = false;\n\tlet config: Config;\n\tlet loading_text = $_(\"common.loading\") + \"...\";\n\tlet active_theme_mode: ThemeMode;\n\tlet api_url: string;\n\n\t$: if (config?.app_id) {\n\t\tapp_id = config.app_id;\n\t}\n\n\tlet css_text_stylesheet: HTMLStyleElement | null = null;\n\tasync function mount_custom_css(css_string: string | null): Promise<void> {\n\t\tif (css_string) {\n\t\t\tif (!css_text_stylesheet) {\n\t\t\t\tcss_text_stylesheet = document.createElement(\"style\");\n\t\t\t\tdocument.head.appendChild(css_text_stylesheet);\n\t\t\t}\n\t\t\tcss_text_stylesheet.textContent = prefix_css(\n\t\t\t\tcss_string,\n\t\t\t\tversion,\n\t\t\t\tcss_text_stylesheet\n\t\t\t);\n\t\t}\n\t\tawait mount_css(\n\t\t\tconfig.root + \"/theme.css?v=\" + config.theme_hash,\n\t\t\tdocument.head\n\t\t);\n\t\tif (!config.stylesheets) return;\n\n\t\tawait Promise.all(\n\t\t\tconfig.stylesheets.map((stylesheet) => {\n\t\t\t\tlet absolute_link =\n\t\t\t\t\tstylesheet.startsWith(\"http:\") || stylesheet.startsWith(\"https:\");\n\t\t\t\tif (absolute_link) {\n\t\t\t\t\treturn mount_css(stylesheet, document.head);\n\t\t\t\t}\n\n\t\t\t\treturn fetch(config.root + \"/\" + stylesheet)\n\t\t\t\t\t.then((response) => response.text())\n\t\t\t\t\t.then((css_string) => {\n\t\t\t\t\t\tprefix_css(css_string, version);\n\t\t\t\t\t});\n\t\t\t})\n\t\t);\n\t}\n\tasync function add_custom_html_head(\n\t\thead_string: string | null\n\t): Promise<void> {\n\t\tif (head_string) {\n\t\t\tconst parser = new DOMParser();\n\t\t\tconst parsed_head_html = Array.from(\n\t\t\t\tparser.parseFromString(head_string, \"text/html\").head.children\n\t\t\t);\n\n\t\t\tif (parsed_head_html) {\n\t\t\t\tfor (let head_element of parsed_head_html) {\n\t\t\t\t\tlet newElement = document.createElement(head_element.tagName);\n\t\t\t\t\tArray.from(head_element.attributes).forEach((attr) => {\n\t\t\t\t\t\tnewElement.setAttribute(attr.name, attr.value);\n\t\t\t\t\t});\n\t\t\t\t\tnewElement.textContent = head_element.textContent;\n\n\t\t\t\t\tif (\n\t\t\t\t\t\tnewElement.tagName == \"META\" &&\n\t\t\t\t\t\tnewElement.getAttribute(\"property\")\n\t\t\t\t\t) {\n\t\t\t\t\t\tconst domMetaList = Array.from(\n\t\t\t\t\t\t\tdocument.head.getElementsByTagName(\"meta\") ?? []\n\t\t\t\t\t\t);\n\t\t\t\t\t\tconst matched = domMetaList.find((el) => {\n\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\tel.getAttribute(\"property\") ==\n\t\t\t\t\t\t\t\t\tnewElement.getAttribute(\"property\") &&\n\t\t\t\t\t\t\t\t!el.isEqualNode(newElement)\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (matched) {\n\t\t\t\t\t\t\tdocument.head.replaceChild(newElement, matched);\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tdocument.head.appendChild(newElement);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction handle_theme_mode(target: HTMLDivElement): \"light\" | \"dark\" {\n\t\tconst force_light = window.__gradio_mode__ === \"website\";\n\n\t\tlet new_theme_mode: ThemeMode;\n\t\tif (force_light) {\n\t\t\tnew_theme_mode = \"light\";\n\t\t} else {\n\t\t\tconst url = new URL(window.location.toString());\n\t\t\tconst url_color_mode: ThemeMode | null = url.searchParams.get(\n\t\t\t\t\"__theme\"\n\t\t\t) as ThemeMode | null;\n\t\t\tnew_theme_mode = theme_mode || url_color_mode || \"system\";\n\t\t}\n\n\t\tif (new_theme_mode === \"dark\" || new_theme_mode === \"light\") {\n\t\t\tapply_theme(target, new_theme_mode);\n\t\t} else {\n\t\t\tnew_theme_mode = sync_system_theme(target);\n\t\t}\n\t\treturn new_theme_mode;\n\t}\n\n\tfunction sync_system_theme(target: HTMLDivElement): \"light\" | \"dark\" {\n\t\tconst theme = update_scheme();\n\t\twindow\n\t\t\t?.matchMedia(\"(prefers-color-scheme: dark)\")\n\t\t\t?.addEventListener(\"change\", update_scheme);\n\n\t\tfunction update_scheme(): \"light\" | \"dark\" {\n\t\t\tlet _theme: \"light\" | \"dark\" = window?.matchMedia?.(\n\t\t\t\t\"(prefers-color-scheme: dark)\"\n\t\t\t).matches\n\t\t\t\t? \"dark\"\n\t\t\t\t: \"light\";\n\n\t\t\tapply_theme(target, _theme);\n\t\t\treturn _theme;\n\t\t}\n\t\treturn theme;\n\t}\n\n\tfunction apply_theme(target: HTMLDivElement, theme: \"dark\" | \"light\"): void {\n\t\tconst dark_class_element = is_embed ? target.parentElement! : document.body;\n\t\tconst bg_element = is_embed ? target : target.parentElement!;\n\t\tbg_element.style.background = \"var(--body-background-fill)\";\n\t\tif (theme === \"dark\") {\n\t\t\tdark_class_element.classList.add(\"dark\");\n\t\t} else {\n\t\t\tdark_class_element.classList.remove(\"dark\");\n\t\t}\n\t}\n\n\tlet status: SpaceStatus = {\n\t\tmessage: \"\",\n\t\tload_status: \"pending\",\n\t\tstatus: \"sleeping\",\n\t\tdetail: \"SLEEPING\"\n\t};\n\n\tlet app: ClientType;\n\tlet css_ready = false;\n\tfunction handle_status(_status: SpaceStatus): void {\n\t\tstatus = _status;\n\t}\n\t//@ts-ignore\n\tconst gradio_dev_mode = window.__GRADIO_DEV__;\n\n\tonMount(async () => {\n\t\tactive_theme_mode = handle_theme_mode(wrapper);\n\n\t\t//@ts-ignore\n\t\tconst server_port = window.__GRADIO__SERVER_PORT__;\n\n\t\tapi_url =\n\t\t\tBUILD_MODE === \"dev\" || gradio_dev_mode === \"dev\"\n\t\t\t\t? `http://localhost:${\n\t\t\t\t\t\ttypeof server_port === \"number\" ? server_port : 7860\n\t\t\t\t\t}`\n\t\t\t\t: space || src || location.origin;\n\n\t\tapp = await Client.connect(api_url, {\n\t\t\tstatus_callback: handle_status,\n\t\t\twith_null_state: true,\n\t\t\tevents: [\"data\", \"log\", \"status\", \"render\"]\n\t\t});\n\t\twindow.addEventListener(\"beforeunload\", () => {\n\t\t\tapp.close();\n\t\t});\n\n\t\tif (!app.config) {\n\t\t\tthrow new Error(\"Could not resolve app config\");\n\t\t}\n\n\t\tconfig = app.get_url_config();\n\t\twindow.__gradio_space__ = config.space_id;\n\n\t\tstatus = {\n\t\t\tmessage: \"\",\n\t\t\tload_status: \"complete\",\n\t\t\tstatus: \"running\",\n\t\t\tdetail: \"RUNNING\"\n\t\t};\n\n\t\tawait mount_custom_css(config.css);\n\t\tawait add_custom_html_head(config.head);\n\t\tcss_ready = true;\n\t\twindow.__is_colab__ = config.is_colab;\n\n\t\tconst supports_zerogpu_headers = \"supports-zerogpu-headers\";\n\t\twindow.addEventListener(\"message\", (event) => {\n\t\t\tif (event.data === supports_zerogpu_headers) {\n\t\t\t\twindow.supports_zerogpu_headers = true;\n\t\t\t}\n\t\t});\n\t\tconst hostname = window.location.hostname;\n\t\tconst origin = hostname.includes(\".dev.\")\n\t\t\t? `https://moon-${hostname.split(\".\")[1]}.dev.spaces.huggingface.tech`\n\t\t\t: `https://huggingface.co`;\n\t\twindow.parent.postMessage(supports_zerogpu_headers, origin);\n\n\t\tdispatch(\"loaded\");\n\n\t\tpages = config.pages;\n\t\tcurrent_page = config.current_page;\n\t\troot = config.root;\n\n\t\tif (config.dev_mode) {\n\t\t\tsetTimeout(() => {\n\t\t\t\tconst { host } = new URL(api_url);\n\t\t\t\tlet url = new URL(`http://${host}${app.api_prefix}/dev/reload`);\n\t\t\t\tstream = new EventSource(url);\n\t\t\t\tstream.addEventListener(\"error\", async (e) => {\n\t\t\t\t\tnew_message_fn(\"Error\", \"Error reloading app\", \"error\");\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tconsole.error(JSON.parse(e.data));\n\t\t\t\t});\n\t\t\t\tstream.addEventListener(\"reload\", async (event) => {\n\t\t\t\t\tapp.close();\n\t\t\t\t\tapp = await Client.connect(api_url, {\n\t\t\t\t\t\tstatus_callback: handle_status,\n\t\t\t\t\t\twith_null_state: true,\n\t\t\t\t\t\tevents: [\"data\", \"log\", \"status\", \"render\"]\n\t\t\t\t\t});\n\n\t\t\t\t\tif (!app.config) {\n\t\t\t\t\t\tthrow new Error(\"Could not resolve app config\");\n\t\t\t\t\t}\n\n\t\t\t\t\tconfig = app.get_url_config();\n\t\t\t\t\twindow.__gradio_space__ = config.space_id;\n\t\t\t\t\tawait mount_custom_css(config.css);\n\t\t\t\t\tawait add_custom_html_head(config.head);\n\t\t\t\t\tcss_ready = true;\n\t\t\t\t\twindow.__is_colab__ = config.is_colab;\n\t\t\t\t\tdispatch(\"loaded\");\n\t\t\t\t});\n\t\t\t}, 200);\n\t\t}\n\t});\n\n\t$: loader_status =\n\t\t!ready && status.load_status !== \"error\"\n\t\t\t? \"pending\"\n\t\t\t: !ready && status.load_status === \"error\"\n\t\t\t\t? \"error\"\n\t\t\t\t: status.load_status;\n\n\t$: config && (eager || $intersecting[_id]) && load_demo();\n\n\tlet Blocks: typeof import(\"@gradio/core/blocks\").default;\n\n\tlet Login: typeof import(\"@gradio/core/login\").default;\n\n\tasync function get_blocks(): Promise<void> {\n\t\tBlocks = (await import(\"@gradio/core/blocks\")).default;\n\t}\n\tasync function get_login(): Promise<void> {\n\t\tLogin = (await import(\"@gradio/core/login\")).default;\n\t}\n\n\tfunction load_demo(): void {\n\t\tif (config.auth_required) get_login();\n\t\telse get_blocks();\n\t}\n\n\ttype error_types =\n\t\t| \"NO_APP_FILE\"\n\t\t| \"CONFIG_ERROR\"\n\t\t| \"BUILD_ERROR\"\n\t\t| \"RUNTIME_ERROR\"\n\t\t| \"PAUSED\";\n\n\t// todo @hannahblair: translate these messages\n\tconst discussion_message = {\n\t\treadable_error: {\n\t\t\tNO_APP_FILE: $_(\"errors.no_app_file\"),\n\t\t\tCONFIG_ERROR: $_(\"errors.config_error\"),\n\t\t\tBUILD_ERROR: $_(\"errors.build_error\"),\n\t\t\tRUNTIME_ERROR: $_(\"errors.runtime_error\"),\n\t\t\tPAUSED: $_(\"errors.space_paused\")\n\t\t} as const,\n\t\ttitle(error: error_types): string {\n\t\t\treturn encodeURIComponent($_(\"errors.space_not_working\"));\n\t\t},\n\t\tdescription(error: error_types, site: string): string {\n\t\t\treturn encodeURIComponent(\n\t\t\t\t`Hello,\\n\\nFirstly, thanks for creating this space!\\n\\nI noticed that the space isn't working correctly because there is ${\n\t\t\t\t\tthis.readable_error[error] || \"an error\"\n\t\t\t\t}.\\n\\nIt would be great if you could take a look at this because this space is being embedded on ${site}.\\n\\nThanks!`\n\t\t\t);\n\t\t}\n\t};\n\n\tlet new_message_fn: (title: string, message: string, type: string) => void;\n\n\tonMount(async () => {\n\t\tintersecting.register(_id, wrapper);\n\t});\n\n\t$: if (render_complete) {\n\t\twrapper.dispatchEvent(\n\t\t\tnew CustomEvent(\"render\", {\n\t\t\t\tbubbles: true,\n\t\t\t\tcancelable: false,\n\t\t\t\tcomposed: true\n\t\t\t})\n\t\t);\n\t}\n\n\t$: app?.config && mount_space_header(app?.config?.space_id, is_embed);\n\tlet spaceheader: HTMLElement | undefined;\n\n\tasync function mount_space_header(\n\t\tspace_id: string | null | undefined,\n\t\tis_embed: boolean\n\t): Promise<void> {\n\t\tif (space_id && !is_embed && window.self === window.top) {\n\t\t\tif (spaceheader) {\n\t\t\t\tspaceheader.remove();\n\t\t\t\tspaceheader = undefined;\n\t\t\t}\n\t\t\tconst header = await init(space_id);\n\t\t\tif (header) spaceheader = header.element;\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tspaceheader?.remove();\n\t});\n</script>\n\n<Embed\n\tdisplay={container && is_embed}\n\t{is_embed}\n\tinfo={!!space && info}\n\t{version}\n\t{initial_height}\n\t{space}\n\tloaded={loader_status === \"complete\"}\n\tfill_width={config?.fill_width || false}\n\t{pages}\n\t{current_page}\n\t{root}\n\t{is_lite}\n\tbind:wrapper\n>\n\t{#if (loader_status === \"pending\" || loader_status === \"error\") && !(config && config?.auth_required)}\n\t\t<StatusTracker\n\t\t\tabsolute={!is_embed}\n\t\t\tstatus={loader_status}\n\t\t\ttimer={false}\n\t\t\tqueue_position={null}\n\t\t\tqueue_size={null}\n\t\t\ttranslucent={true}\n\t\t\t{loading_text}\n\t\t\ti18n={$_}\n\t\t\t{autoscroll}\n\t\t>\n\t\t\t<div class=\"load-text\" slot=\"additional-loading-text\">\n\t\t\t\t{#if gradio_dev_mode === \"dev\"}\n\t\t\t\t\t<p>\n\t\t\t\t\t\tIf your custom component never loads, consult the troubleshooting <a\n\t\t\t\t\t\t\tstyle=\"color: blue;\"\n\t\t\t\t\t\t\thref=\"https://www.gradio.app/guides/frequently-asked-questions#the-development-server-didnt-work-for-me\"\n\t\t\t\t\t\t\t>guide</a\n\t\t\t\t\t\t>.\n\t\t\t\t\t</p>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t\t<!-- todo: translate message text -->\n\t\t\t<div class=\"error\" slot=\"error\">\n\t\t\t\t<p><strong>{status?.message || \"\"}</strong></p>\n\t\t\t\t{#if (status.status === \"space_error\" || status.status === \"paused\") && status.discussions_enabled}\n\t\t\t\t\t<p>\n\t\t\t\t\t\tPlease <a\n\t\t\t\t\t\t\thref=\"https://huggingface.co/spaces/{space}/discussions/new?title={discussion_message.title(\n\t\t\t\t\t\t\t\tstatus?.detail\n\t\t\t\t\t\t\t)}&description={discussion_message.description(\n\t\t\t\t\t\t\t\tstatus?.detail,\n\t\t\t\t\t\t\t\tlocation.origin\n\t\t\t\t\t\t\t)}\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tcontact the author of the space</a\n\t\t\t\t\t\t> to let them know.\n\t\t\t\t\t</p>\n\t\t\t\t{:else}\n\t\t\t\t\t<p>{$_(\"errors.contact_page_author\")}</p>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</StatusTracker>\n\t{/if}\n\t{#if config?.auth_required && Login}\n\t\t<Login\n\t\t\tauth_message={config.auth_message}\n\t\t\troot={config.root}\n\t\t\tspace_id={space}\n\t\t\t{app_mode}\n\t\t/>\n\t{:else if config && Blocks && css_ready}\n\t\t<Blocks\n\t\t\t{app}\n\t\t\t{...config}\n\t\t\tfill_height={!is_embed && config.fill_height}\n\t\t\ttheme_mode={active_theme_mode}\n\t\t\t{control_page_title}\n\t\t\ttarget={wrapper}\n\t\t\t{autoscroll}\n\t\t\tbind:ready\n\t\t\tbind:render_complete\n\t\t\tbind:add_new_message={new_message_fn}\n\t\t\tshow_footer={!is_embed}\n\t\t\t{app_mode}\n\t\t\t{version}\n\t\t\tapi_prefix={config.api_prefix || \"\"}\n\t\t\tmax_file_size={config.max_file_size}\n\t\t\tinitial_layout={undefined}\n\t\t\tsearch_params={new URLSearchParams(window.location.search)}\n\t\t/>\n\t{/if}\n</Embed>\n\n<style>\n\t.error {\n\t\tposition: relative;\n\t\tpadding: var(--size-4);\n\t\tcolor: var(--body-text-color);\n\t\ttext-align: center;\n\t}\n\n\t.error > * {\n\t\tmargin-top: var(--size-4);\n\t}\n\n\ta {\n\t\tcolor: var(--link-text-color);\n\t}\n\n\ta:hover {\n\t\tcolor: var(--link-text-color-hover);\n\t\ttext-decoration: underline;\n\t}\n\n\ta:visited {\n\t\tcolor: var(--link-text-color-visited);\n\t}\n\n\ta:active {\n\t\tcolor: var(--link-text-color-active);\n\t}\n</style>\n"], "file": "assets/Index-vNlY8iMv.js"}