#!/usr/bin/env python3
"""
清理脚本
用于清理项目中的无用文件
"""

import os
import re
import shutil
import argparse

def find_backup_outline_files(directory):
    """查找备份的大纲文件"""
    backup_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if re.match(r'novel_outline_original_\d+\.txt', file):
                backup_files.append(os.path.join(root, file))
    return backup_files

def find_duplicate_modules(directory):
    """查找重复的模块文件"""
    # 已移动到src目录的模块
    moved_modules = [
        'novel_core.py',
        'outline_manager.py',
        'model_config.py',
        'prompts.py',
        'prompt_manager.py',
        'writing_agents.py',
        'chapter_generator.py',
        'illustration_generator.py',
        'chapter_transition.py',
        'config.py',
        'env_manager.py',
        'document_generator.py',
        'run.py',
        'main.py',
        'novel_system.py',
        'generate_novel.py'
    ]
    
    # 过渡文件
    transition_files = [
        'transition_generator.py',
        'generate_transition.py',
        'enhanced_prompt_context.txt',
        'writing_prompt.txt'
    ]
    
    duplicate_files = []
    for file in moved_modules + transition_files:
        file_path = os.path.join(directory, file)
        if os.path.exists(file_path):
            duplicate_files.append(file_path)
    
    return duplicate_files

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="清理项目中的无用文件")
    parser.add_argument("--dry-run", action="store_true", help="只显示要删除的文件，不实际删除")
    parser.add_argument("--directory", default=".", help="要清理的目录")
    args = parser.parse_args()
    
    directory = args.directory
    dry_run = args.dry_run
    
    # 查找备份的大纲文件
    backup_outline_files = find_backup_outline_files(directory)
    print(f"找到 {len(backup_outline_files)} 个备份大纲文件:")
    for file in backup_outline_files:
        print(f"  - {file}")
    
    # 查找重复的模块文件
    duplicate_modules = find_duplicate_modules(directory)
    print(f"找到 {len(duplicate_modules)} 个重复模块文件:")
    for file in duplicate_modules:
        print(f"  - {file}")
    
    # 删除文件
    if not dry_run:
        print("\n开始删除文件...")
        
        # 删除备份的大纲文件
        for file in backup_outline_files:
            try:
                os.remove(file)
                print(f"已删除: {file}")
            except Exception as e:
                print(f"删除失败: {file}, 错误: {e}")
        
        # 删除重复的模块文件
        for file in duplicate_modules:
            try:
                os.remove(file)
                print(f"已删除: {file}")
            except Exception as e:
                print(f"删除失败: {file}, 错误: {e}")
        
        print("清理完成!")
    else:
        print("\n这是一次演习，没有文件被删除。使用 --dry-run=false 来实际删除文件。")

if __name__ == "__main__":
    main()
