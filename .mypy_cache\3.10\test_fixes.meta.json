{"data_mtime": **********, "dep_lines": [61, 62, 63, 92, 143, 144, 178, 6, 7, 8, 129, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 20, 20, 20, 20, 20, 20, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.core.model_config", "src.core.agent", "src.utils.mcp_client", "src.agents.writing_agent", "src.core.specialized_prompts", "src.core.novel_types", "src.core.medical_report_generator", "os", "sys", "json", "shutil", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "src", "src.agents", "src.core", "typing"], "hash": "9824af664c1188fc8728225ad796b00ef4f60f27", "id": "test_fixes", "ignore_all": false, "interface_hash": "673565261d6bce18bc1eeab8eaf03724494964ec", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\storymc\\test_fixes.py", "plugin_data": null, "size": 8459, "suppressed": [], "version_id": "1.15.0"}