"""
审核智能体模块
负责审核和优化小说内容
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, cast

try:
    from src.core.agent import Agent, AGENT_REGISTRY
except ImportError:
    # 如果导入失败，创建简单的Agent类
    class Agent:
        def __init__(self, name, model=None):
            self.name = name
            self.model = model

    # 创建简单的AGENT_REGISTRY类
    class AgentRegistry:
        def __init__(self):
            self.agents = {}

        def register(self, agent):
            self.agents[agent.name] = agent
            return agent

        def get_agent(self, name):
            return self.agents.get(name)

    AGENT_REGISTRY = AgentRegistry()

try:
    from src.core.prompt_manager import PromptManager
except ImportError:
    # 如果导入失败，创建简单的PromptManager类
    class PromptManager:
        def get_prompt(self, prompt_name, **kwargs):
            return f"提示词 {prompt_name} 示例内容"

try:
    from src.core.model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES
except ImportError:
    # 如果导入失败，创建简单的配置
    AI_MODEL_CONFIG = {
        "agent_models": {},
        "default_model": "glm-4-flash"
    }
    DEFAULT_MODEL_NAMES = {"glm": "glm-4-flash"}

# 初始化提示词管理器
prompt_manager = PromptManager()

class ReviewAgent(Agent):
    """
    审核智能体，负责审核和优化小说内容
    """

    def __init__(self, name: str, model: Optional[str] = None):
        """初始化审核智能体"""
        # 确保AI_MODEL_CONFIG是字典类型
        agent_models = cast(Dict[str, Any], AI_MODEL_CONFIG.get("agent_models", {}))
        default_model = cast(str, AI_MODEL_CONFIG.get("default_model", ""))

        # 获取模型名称
        model = model or agent_models.get("ReviewAgent", default_model)
        super().__init__(name, model)
        self.prompt_manager = prompt_manager

    def handle_message(self, msg):
        """处理接收到的消息"""
        if msg['type'] == 'review':
            print(f"审核智能体接收到内容...")
            reviewed_content = self.review_content(msg['content'])

            # 发送结果给下一个智能体（如果指定了）
            if 'next_agent' in msg:
                self.send_message(
                    msg['next_agent'],
                    {
                        'type': 'optimize',
                        'content': reviewed_content,
                        'metadata': msg.get('metadata', {})
                    }
                )

            return reviewed_content

    def review_content(self, original_text: str) -> str:
        """
        审核小说内容

        参数:
            original_text: 原始文本

        返回:
            审核后的内容
        """
        print("正在优化内容...")

        # 检查原始内容长度
        original_length = len(original_text)

        # 如果原始内容太短，直接返回
        if original_length < 500:
            print("警告: 原始内容过短，跳过审核阶段")
            return original_text

        # 检查内容是否包含错误信息或提示词
        if "错误" in original_text[:100] or "抱歉" in original_text[:100]:
            print("检测到内容可能是错误信息或提示词，跳过审核阶段")
            return original_text

        # 获取简化版的审核提示词，优先删除重复内容
        prompt = f"""
请对以下小说内容进行审核，主要删除所有重复内容：

要删除的重复内容包括：
- 完全相同的段落
- 内容高度相似的段落
- 表达相同意思的重复句子

要求：
- 不要重写内容，只删除重复部分
- 不要添加新内容
- 不要删除非重复的内容

内容：

{original_text}

直接返回处理后的完整内容。
"""

        # 调用LLM审核内容
        try:
            reviewed_content = self.call_llm(prompt)
            reviewed_length = len(reviewed_content)

            # 检查审核后的内容长度
            if reviewed_length < original_length * 0.9:
                print(f"警告：优化后内容减少了超过10%的字数，从 {original_length} 减少到 {reviewed_length} 字符")

                # 尝试重新优化，使用更简单的提示词
                retry_prompt = f"""
请再次审核以下小说内容，只删除最明显的重复段落：

{original_text}

要求：
- 只删除完全相同或非常相似的段落
- 保留大部分原始内容
- 不要添加新内容

直接返回处理后的完整内容。
"""
                reviewed_content = self.call_llm(retry_prompt)
                new_length = len(reviewed_content)
                print(f"重新优化后的内容长度为 {new_length} 字符")

                # 如果重试后内容仍然减少超过30%，则使用原始内容
                if new_length < original_length * 0.7:
                    print(f"警告：即使重试后内容仍然减少超过30%，使用原始内容")
                    return original_text

            # 检查内容是否过短或看起来像错误信息
            if reviewed_length < 500 or "错误" in reviewed_content[:100] or "抱歉" in reviewed_content[:100]:
                print(f"警告：审核后内容异常，使用原始内容")
                return original_text

            print(f"内容优化完成，共计 {len(reviewed_content)} 字符")
            return reviewed_content

        except Exception as e:
            print(f"审核过程中出错: {e}，使用原始内容")
            return original_text
