{".class": "MypyFile", "_fullname": "src.ui", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AGENT_REGISTRY": {".class": "SymbolTableNode", "cross_ref": "src.main.AGENT_REGISTRY", "kind": "Gdef", "module_public": false}, "AI_MODEL_CONFIG": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.AI_MODEL_CONFIG", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "DEFAULT_MODEL_NAMES": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.DEFAULT_MODEL_NAMES", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "ExpansionAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.expansion_agent.ExpansionAgent", "kind": "Gdef", "module_public": false}, "IllustrationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.illustration_agent.IllustrationAgent", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "NEW_SYSTEM_AVAILABLE": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.NEW_SYSTEM_AVAILABLE", "kind": "Gdef", "module_public": false}, "NOVEL_CREATION_STATE": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.NOVEL_CREATION_STATE", "kind": "Gdef", "module_public": false}, "NovelManager": {".class": "SymbolTableNode", "cross_ref": "src.agents.novel_manager.NovelManager", "kind": "Gdef", "module_public": false}, "OptimizationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.optimization_agent.OptimizationAgent", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PolishingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.polishing_agent.PolishingAgent", "kind": "Gdef", "module_public": false}, "ReviewAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.review_agent.ReviewAgent", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WritingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_agent.WritingAgent", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.ui.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_custom_model": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.add_custom_model", "kind": "Gdef", "module_public": false}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "continue_novel": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.continue_novel", "kind": "Gdef"}, "create_web_ui": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.create_web_ui", "kind": "Gdef", "module_public": false}, "create_writing_pipeline": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.create_writing_pipeline", "kind": "Gdef", "module_public": false}, "current_dir": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.current_dir", "kind": "Gdef", "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_public": false}, "delete_custom_model": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.delete_custom_model", "kind": "Gdef", "module_public": false}, "get_agents": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.get_agents", "kind": "Gdef", "module_public": false}, "get_custom_models": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.get_custom_models", "kind": "Gdef", "module_public": false}, "gr": {".class": "SymbolTableNode", "cross_ref": "gradio", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "launch_simple_reader": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.launch_simple_reader", "kind": "Gdef", "module_public": false}, "load_custom_models": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.load_custom_models", "kind": "Gdef", "module_public": false}, "main": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.main", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "parse_arguments": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.parse_arguments", "kind": "Gdef", "module_public": false}, "register_writing_agents": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.register_writing_agents", "kind": "Gdef", "module_public": false}, "root_dir": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.root_dir", "kind": "Gdef", "module_public": false}, "run_pipeline": {".class": "SymbolTableNode", "cross_ref": "src.ui.run.run_pipeline", "kind": "Gdef"}, "save_custom_models": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.save_custom_models", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef", "module_public": false}, "update_agent_model": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.update_agent_model", "kind": "Gdef", "module_public": false}, "webbrowser": {".class": "SymbolTableNode", "cross_ref": "webbrowser", "kind": "Gdef", "module_public": false}}, "path": "E:\\storymc\\src\\ui\\__init__.py"}