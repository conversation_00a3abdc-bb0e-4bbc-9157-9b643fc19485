{".class": "MypyFile", "_fullname": "src.agents.writing_agents", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AGENT_REGISTRY": {".class": "SymbolTableNode", "cross_ref": "src.core.agent.AGENT_REGISTRY", "kind": "Gdef"}, "AI_MODEL_CONFIG": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.AI_MODEL_CONFIG", "kind": "Gdef"}, "Agent": {".class": "SymbolTableNode", "cross_ref": "src.core.agent.Agent", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Chapter": {".class": "SymbolTableNode", "cross_ref": "src.agents.novel_manager.Chapter", "kind": "Gdef"}, "DEFAULT_MODEL_NAMES": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.DEFAULT_MODEL_NAMES", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExpansionAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.expansion_agent.ExpansionAgent", "kind": "Gdef"}, "IllustrationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.illustration_agent.IllustrationAgent", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Novel": {".class": "SymbolTableNode", "cross_ref": "src.agents.novel_manager.Novel", "kind": "Gdef"}, "NovelManager": {".class": "SymbolTableNode", "cross_ref": "src.agents.novel_manager.NovelManager", "kind": "Gdef"}, "OptimizationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.optimization_agent.OptimizationAgent", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PolishingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.polishing_agent.PolishingAgent", "kind": "Gdef"}, "PromptManager": {".class": "SymbolTableNode", "cross_ref": "src.core.prompt_manager.PromptManager", "kind": "Gdef"}, "ReviewAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.review_agent.ReviewAgent", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WritingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_agent.WritingAgent", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agents.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agents.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agents.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agents.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agents.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agents.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "check_chapter_exists": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.check_chapter_exists", "kind": "Gdef"}, "create_writing_pipeline": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.create_writing_pipeline", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "register_writing_agents": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.register_writing_agents", "kind": "Gdef"}, "run_pipeline": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.run_pipeline", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}}, "path": "E:\\storymc\\src\\agents\\writing_agents.py"}