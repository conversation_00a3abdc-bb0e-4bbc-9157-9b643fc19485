const __vite__fileDeps=["./Blocks-DB4cNihJ.js","./index-V1UhiEW8.js","./index-Bq9js3go.css","./Button-DBmw6N-D.js","./Image-CnqB5dbD.js","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Image-B8dFOee4.css","./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js","./IconButtonWrapper-BULHeAAS.css","./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js","./prism-python-DO0H4w6Q.js","./MarkdownCode-CMJLBV0e.css","./Button-DTh9AgeE.css","./ImagePreview-DJhr8Mfv.css","./index-CWG4El0O.js","./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js","./StreamingBar-DOagx4HU.css","./IconButton-DbC-jsk_.js","./Clear-By3xiIwg.js","./Block-CMfAaXj9.js","./Dropdown-lPfdMq6j.js","./index-CQUs-3jT.js","./BlockTitle-BijYJada.js","./Info-BDZmU2Ku.js","./MarkdownCode-Cd8Z5cIb.js","./DropdownArrow-DuA1Ifms.js","./Dropdown-CWxB-qJp.css","./Toast-C4cLYcV8.js","./utils-BsGrhMNe.js","./Blocks-B5wxaDIo.css","./Example-D7K5RtQ2.css","./Login-phx6DJWM.js","./Index-DE1Sah7F.js","./Index-12OnbRhk.css","./Textbox-BAD5XOM4.js","./Check-BiRlaMNo.js","./Copy-CxQ9EyK2.js","./Send-DyoOovnk.js","./Square-oAGqOwsh.js","./Textbox-jWD3sCxr.css","./Index-CyFoS1ud.js","./Index-CptIZeFZ.css","./Login-BCwzjozv.css","./Example-ClKJOMGh.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{$ as qe,s as Te,w as je,m as Be,p as fe,_ as pe}from"./index-V1UhiEW8.js";import{E as Fe}from"./Embed-DkWAkqUg.js";import{S as He}from"./index-CWG4El0O.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js";import"./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js";import{s as Ve}from"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./svelte/svelte.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js";import"./prism-python-DO0H4w6Q.js";import"./IconButton-DbC-jsk_.js";import"./Clear-By3xiIwg.js";var Ge=()=>{const t=document.createElement("link");t.href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap",t.rel="stylesheet";const e=document.createElement("link");e.href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap",e.rel="stylesheet",document.head.appendChild(t),document.head.appendChild(e)},We=()=>{const t=document.createElement("div");return t.style.backgroundImage="linear-gradient(to top, #f9fafb, white)",t.style.border="1px solid #e5e7eb",t.style.borderRadius="0.75rem",t.style.boxShadow="0 0 10px rgba(0, 0, 0, 0.1)",t.style.color="#374151",t.style.display="flex",t.style.flexDirection="row",t.style.alignItems="center",t.style.height="40px",t.style.justifyContent="space-between",t.style.overflow="hidden",t.style.position="fixed",t.style.right=".75rem",t.style.top=".75rem",t.style.width="auto",t.style.zIndex="20",t.style.paddingLeft="1rem",t.setAttribute("id","huggingface-space-header"),window.matchMedia("(max-width: 768px)").addEventListener("change",e=>{e.matches?t.style.display="none":t.style.display="flex"}),t},Ze=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 12 12"),t.setAttribute("fill","currentColor");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M0.375001 10.3828L0.375 1.61719C0.375 1.104 0.816001 0.687501 1.35938 0.687501L10.6406 0.6875C10.9017 0.6875 11.1521 0.785449 11.3367 0.959797C11.5213 1.13415 11.625 1.37062 11.625 1.61719V10.3828C11.625 10.6294 11.5213 10.8659 11.3367 11.0402C11.1521 11.2145 10.9017 11.3125 10.6406 11.3125H1.35938C0.816001 11.3125 0.375001 10.896 0.375001 10.3828ZM1.35938 10.5156H10.6406C10.7183 10.5156 10.7813 10.4561 10.7813 10.3828V4.40625H1.21875V10.3828C1.21875 10.418 1.23356 10.4518 1.25994 10.4767C1.28631 10.5017 1.32208 10.5156 1.35938 10.5156ZM4.61052 6.38251L5.9999 7.69472L7.38927 6.38251C7.44083 6.33007 7.50645 6.29173 7.57913 6.27153C7.6518 6.25134 7.72898 6.25003 7.8024 6.26776C7.87583 6.28549 7.9428 6.3216 7.99628 6.37227C8.04983 6.42295 8.08785 6.48631 8.10645 6.5557C8.12528 6.62497 8.12393 6.69773 8.10263 6.76635C8.0814 6.83497 8.0409 6.8969 7.98555 6.94564L6.29802 8.53936C6.21892 8.61399 6.11169 8.65592 5.9999 8.65592C5.8881 8.65592 5.78087 8.61399 5.70177 8.53936L4.01427 6.94564C3.95874 6.89694 3.91814 6.835 3.89676 6.76633C3.87538 6.69766 3.874 6.62483 3.89277 6.55549C3.91154 6.48615 3.94977 6.42287 4.00343 6.37233C4.05708 6.32179 4.12418 6.28585 4.19765 6.2683C4.27098 6.25054 4.34803 6.25178 4.42068 6.27188C4.49334 6.29198 4.55891 6.3302 4.61052 6.38251Z"),t.appendChild(e),t},xe=(t,e)=>{const n=document.createElement("div");return n.setAttribute("id","space-header__collapse"),n.style.display="flex",n.style.flexDirection="row",n.style.alignItems="center",n.style.justifyContent="center",n.style.fontSize="16px",n.style.paddingLeft="10px",n.style.paddingRight="10px",n.style.height="40px",n.style.cursor="pointer",n.style.color="#40546e",n.style.transitionDuration="0.1s",n.style.transitionProperty="all",n.style.transitionTimingFunction="ease-in-out",n.appendChild(Ze()),n.addEventListener("click",r=>{r.preventDefault(),r.stopPropagation(),e()}),n.addEventListener("mouseenter",()=>{n.style.color="#213551"}),n.addEventListener("mouseleave",()=>{n.style.color="#40546e"}),n},Ye=t=>{const e=document.createElement("p");return e.style.margin="0",e.style.padding="0",e.style.color="#9ca3af",e.style.fontSize="14px",e.style.fontFamily="Source Sans Pro, sans-serif",e.style.padding="0px 6px",e.style.borderLeft="1px solid #e5e7eb",e.style.marginLeft="4px",e.textContent=(t??0).toString(),e},Je=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 32 32"),t.setAttribute("fill","#6b7280");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z"),t.appendChild(e),t},Ke=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/spaces/${t.id}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.border="1px solid #e5e7eb",e.style.borderRadius="6px",e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.margin="0 0 0 12px",e.style.fontSize="14px",e.style.paddingLeft="4px",e.style.textDecoration="none",e.appendChild(Je()),e.appendChild(Ye(t.likes)),e},Qe=t=>{const e=document.createElement("img");return e.src=`https://huggingface.co/api/users/${t}/avatar`,e.style.width="0.875rem",e.style.height="0.875rem",e.style.borderRadius="50%",e.style.flex="none",e.style.marginRight="0.375rem",e},Xe=t=>{const[e,n]=t.split("/"),r=document.createElement("a");return r.setAttribute("href",`https://huggingface.co/spaces/${t}`),r.setAttribute("rel","noopener noreferrer"),r.setAttribute("target","_blank"),r.style.color="#1f2937",r.style.textDecoration="none",r.style.fontWeight="600",r.style.fontSize="15px",r.style.lineHeight="24px",r.style.flex="none",r.style.fontFamily="IBM Plex Mono, sans-serif",r.addEventListener("mouseover",()=>{r.style.color="#2563eb"}),r.addEventListener("mouseout",()=>{r.style.color="#1f2937"}),r.textContent=n,r},$e=()=>{const t=document.createElement("div");return t.style.marginLeft=".125rem",t.style.marginRight=".125rem",t.style.color="#d1d5db",t.textContent="/",t},et=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/${t}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.color="rgb(107, 114, 128)",e.style.textDecoration="none",e.style.fontWeight="400",e.style.fontSize="16px",e.style.lineHeight="24px",e.style.flex="none",e.style.fontFamily="Source Sans Pro, sans-serif",e.addEventListener("mouseover",()=>{e.style.color="#2563eb"}),e.addEventListener("mouseout",()=>{e.style.color="rgb(107, 114, 128)"}),e.textContent=t,e},tt=t=>{const e=document.createElement("div");return e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.justifyContent="center",e.style.borderRight="1px solid #e5e7eb",e.style.paddingRight="12px",e.style.height="40px",e.appendChild(Qe(t.author)),e.appendChild(et(t.author)),e.appendChild($e()),e.appendChild(Xe(t.id)),e.appendChild(Ke(t)),e},nt=t=>{const e=We(),n=()=>e.style.display="none";return e.appendChild(tt(t)),e.appendChild(xe(t,n)),e},rt=async t=>{try{return await(await fetch(`https://huggingface.co/api/spaces/${t}`)).json()}catch{return null}},st=(t,e)=>{if(document.body===null)return console.error("document.body is null");document.body.appendChild(t)};async function ot(t,e){var n,r;if(window===void 0)return console.error("Please run this script in a browser environment");if(Object.values((r=(n=window.location)==null?void 0:n.ancestorOrigins)!=null?r:{0:window.document.referrer}).some(d=>{var u;return((u=new URL(d))==null?void 0:u.origin)==="https://huggingface.co"}))return;Ge();let a;if(typeof t=="string"){if(a=await rt(t),a===null)return console.error("Space not found")}else a=t;const o=nt(a);return st(o),{element:o}}var it=(t,e)=>ot(t);const{SvelteComponent:at,add_flush_callback:F,append:k,assign:lt,attr:y,bind:H,binding_callbacks:V,check_outros:me,component_subscribe:he,create_component:G,destroy_component:W,detach:L,element:E,empty:ct,flush:g,get_spread_object:dt,get_spread_update:ut,group_outros:ge,init:_t,insert:R,mount_component:Z,noop:ft,safe_not_equal:pt,set_data:ke,space:Ce,text:z,transition_in:v,transition_out:C}=window.__gradio__svelte__internal,{onMount:be,createEventDispatcher:mt,onDestroy:ht}=window.__gradio__svelte__internal;function we(t){let e,n;return e=new He({props:{absolute:!t[4],status:t[18],timer:!1,queue_position:null,queue_size:null,translucent:!0,loading_text:t[19],i18n:t[25],autoscroll:t[0],$$slots:{error:[vt],"additional-loading-text":[bt]},$$scope:{ctx:t}}}),{c(){G(e.$$.fragment)},m(r,i){Z(e,r,i),n=!0},p(r,i){const a={};i[0]&16&&(a.absolute=!r[4]),i[0]&262144&&(a.status=r[18]),i[0]&524288&&(a.loading_text=r[19]),i[0]&33554432&&(a.i18n=r[25]),i[0]&1&&(a.autoscroll=r[0]),i[0]&33562880|i[1]&33554432&&(a.$$scope={dirty:i,ctx:r}),e.$set(a)},i(r){n||(v(e.$$.fragment,r),n=!0)},o(r){C(e.$$.fragment,r),n=!1},d(r){W(e,r)}}}function gt(t){let e;return{c(){e=E("p"),e.innerHTML='If your custom component never loads, consult the troubleshooting <a style="color: blue;" href="https://www.gradio.app/guides/frequently-asked-questions#the-development-server-didnt-work-for-me" class="svelte-y6l4b">guide</a>.'},m(n,r){R(n,e,r)},d(n){n&&L(e)}}}function bt(t){let e,n=t[27]==="dev"&&gt();return{c(){e=E("div"),n&&n.c(),y(e,"class","load-text"),y(e,"slot","additional-loading-text")},m(r,i){R(r,e,i),n&&n.m(e,null)},p:ft,d(r){r&&L(e),n&&n.d()}}}function wt(t){let e,n=t[25]("errors.contact_page_author")+"",r;return{c(){e=E("p"),r=z(n),y(e,"class","svelte-y6l4b")},m(i,a){R(i,e,a),k(e,r)},p(i,a){a[0]&33554432&&n!==(n=i[25]("errors.contact_page_author")+"")&&ke(r,n)},d(i){i&&L(e)}}}function yt(t){let e,n,r,i,a,o;return{c(){e=E("p"),n=z("Please "),r=E("a"),i=z("contact the author of the space"),o=z(" to let them know."),y(r,"href",a="https://huggingface.co/spaces/"+t[8]+"/discussions/new?title="+t[28].title(t[13]?.detail)+"&description="+t[28].description(t[13]?.detail,location.origin)),y(r,"class","svelte-y6l4b"),y(e,"class","svelte-y6l4b")},m(d,u){R(d,e,u),k(e,n),k(e,r),k(r,i),k(e,o)},p(d,u){u[0]&8448&&a!==(a="https://huggingface.co/spaces/"+d[8]+"/discussions/new?title="+d[28].title(d[13]?.detail)+"&description="+d[28].description(d[13]?.detail,location.origin))&&y(r,"href",a)},d(d){d&&L(e)}}}function vt(t){let e,n,r,i=(t[13]?.message||"")+"",a,o;function d(c,l){return(c[13].status==="space_error"||c[13].status==="paused")&&c[13].discussions_enabled?yt:wt}let u=d(t),m=u(t);return{c(){e=E("div"),n=E("p"),r=E("strong"),a=z(i),o=Ce(),m.c(),y(n,"class","svelte-y6l4b"),y(e,"class","error svelte-y6l4b"),y(e,"slot","error")},m(c,l){R(c,e,l),k(e,n),k(n,r),k(r,a),k(e,o),m.m(e,null)},p(c,l){l[0]&8192&&i!==(i=(c[13]?.message||"")+"")&&ke(a,i),u===(u=d(c))&&m?m.p(c,l):(m.d(1),m=u(c),m&&(m.c(),m.m(e,null)))},d(c){c&&L(e),m.d()}}}function kt(t){let e,n,r,i,a;const o=[{app:t[14]},t[12],{fill_height:!t[4]&&t[12].fill_height},{theme_mode:t[20]},{control_page_title:t[5]},{target:t[9]},{autoscroll:t[0]},{show_footer:!t[4]},{app_mode:t[3]},{version:t[1]},{api_prefix:t[12].api_prefix||""},{max_file_size:t[12].max_file_size},{initial_layout:void 0},{search_params:new URLSearchParams(window.location.search)}];function d(l){t[36](l)}function u(l){t[37](l)}function m(l){t[38](l)}let c={};for(let l=0;l<o.length;l+=1)c=lt(c,o[l]);return t[10]!==void 0&&(c.ready=t[10]),t[11]!==void 0&&(c.render_complete=t[11]),t[24]!==void 0&&(c.add_new_message=t[24]),e=new t[22]({props:c}),V.push(()=>H(e,"ready",d)),V.push(()=>H(e,"render_complete",u)),V.push(()=>H(e,"add_new_message",m)),{c(){G(e.$$.fragment)},m(l,_){Z(e,l,_),a=!0},p(l,_){const A=_[0]&1069627?ut(o,[_[0]&16384&&{app:l[14]},_[0]&4096&&dt(l[12]),_[0]&4112&&{fill_height:!l[4]&&l[12].fill_height},_[0]&1048576&&{theme_mode:l[20]},_[0]&32&&{control_page_title:l[5]},_[0]&512&&{target:l[9]},_[0]&1&&{autoscroll:l[0]},_[0]&16&&{show_footer:!l[4]},_[0]&8&&{app_mode:l[3]},_[0]&2&&{version:l[1]},_[0]&4096&&{api_prefix:l[12].api_prefix||""},_[0]&4096&&{max_file_size:l[12].max_file_size},_&0&&{initial_layout:void 0},_&0&&{search_params:new URLSearchParams(window.location.search)}]):{};!n&&_[0]&1024&&(n=!0,A.ready=l[10],F(()=>n=!1)),!r&&_[0]&2048&&(r=!0,A.render_complete=l[11],F(()=>r=!1)),!i&&_[0]&16777216&&(i=!0,A.add_new_message=l[24],F(()=>i=!1)),e.$set(A)},i(l){a||(v(e.$$.fragment,l),a=!0)},o(l){C(e.$$.fragment,l),a=!1},d(l){W(e,l)}}}function Ct(t){let e,n;return e=new t[23]({props:{auth_message:t[12].auth_message,root:t[12].root,space_id:t[8],app_mode:t[3]}}),{c(){G(e.$$.fragment)},m(r,i){Z(e,r,i),n=!0},p(r,i){const a={};i[0]&4096&&(a.auth_message=r[12].auth_message),i[0]&4096&&(a.root=r[12].root),i[0]&256&&(a.space_id=r[8]),i[0]&8&&(a.app_mode=r[3]),e.$set(a)},i(r){n||(v(e.$$.fragment,r),n=!0)},o(r){C(e.$$.fragment,r),n=!1},d(r){W(e,r)}}}function Et(t){let e,n,r,i,a,o=(t[18]==="pending"||t[18]==="error")&&!(t[12]&&t[12]?.auth_required)&&we(t);const d=[Ct,kt],u=[];function m(c,l){return c[12]?.auth_required&&c[23]?0:c[12]&&c[22]&&c[21]?1:-1}return~(n=m(t))&&(r=u[n]=d[n](t)),{c(){o&&o.c(),e=Ce(),r&&r.c(),i=ct()},m(c,l){o&&o.m(c,l),R(c,e,l),~n&&u[n].m(c,l),R(c,i,l),a=!0},p(c,l){(c[18]==="pending"||c[18]==="error")&&!(c[12]&&c[12]?.auth_required)?o?(o.p(c,l),l[0]&266240&&v(o,1)):(o=we(c),o.c(),v(o,1),o.m(e.parentNode,e)):o&&(ge(),C(o,1,1,()=>{o=null}),me());let _=n;n=m(c),n===_?~n&&u[n].p(c,l):(r&&(ge(),C(u[_],1,1,()=>{u[_]=null}),me()),~n?(r=u[n],r?r.p(c,l):(r=u[n]=d[n](c),r.c()),v(r,1),r.m(i.parentNode,i)):r=null)},i(c){a||(v(o),v(r),a=!0)},o(c){C(o),C(r),a=!1},d(c){c&&(L(e),L(i)),o&&o.d(c),~n&&u[n].d(c)}}}function At(t){let e,n,r;function i(o){t[39](o)}let a={display:t[6]&&t[4],is_embed:t[4],info:!!t[8]&&t[7],version:t[1],initial_height:t[2],space:t[8],loaded:t[18]==="complete",fill_width:t[12]?.fill_width||!1,pages:t[15],current_page:t[16],root:t[17],is_lite:t[26],$$slots:{default:[Et]},$$scope:{ctx:t}};return t[9]!==void 0&&(a.wrapper=t[9]),e=new Fe({props:a}),V.push(()=>H(e,"wrapper",i)),{c(){G(e.$$.fragment)},m(o,d){Z(e,o,d),r=!0},p(o,d){const u={};d[0]&80&&(u.display=o[6]&&o[4]),d[0]&16&&(u.is_embed=o[4]),d[0]&384&&(u.info=!!o[8]&&o[7]),d[0]&2&&(u.version=o[1]),d[0]&4&&(u.initial_height=o[2]),d[0]&256&&(u.space=o[8]),d[0]&262144&&(u.loaded=o[18]==="complete"),d[0]&4096&&(u.fill_width=o[12]?.fill_width||!1),d[0]&32768&&(u.pages=o[15]),d[0]&65536&&(u.current_page=o[16]),d[0]&131072&&(u.root=o[17]),d[0]&66879291|d[1]&33554432&&(u.$$scope={dirty:d,ctx:o}),!n&&d[0]&512&&(n=!0,u.wrapper=o[9],F(()=>n=!1)),e.$set(u)},i(o){r||(v(e.$$.fragment,o),r=!0)},o(o){C(e.$$.fragment,o),r=!1},d(o){W(e,o)}}}let Lt=-1;function Rt(){const t=je({}),e=new Map,n=new IntersectionObserver(i=>{i.forEach(a=>{if(a.isIntersecting){let o=e.get(a.target);o!==void 0&&t.update(d=>({...d,[o]:!0}))}})});function r(i,a){e.set(a,i),n.observe(a)}return{register:r,subscribe:t.subscribe}}const ye=Rt();async function ve(t){if(t){const e=new DOMParser,n=Array.from(e.parseFromString(t,"text/html").head.children);if(n)for(let r of n){let i=document.createElement(r.tagName);if(Array.from(r.attributes).forEach(a=>{i.setAttribute(a.name,a.value)}),i.textContent=r.textContent,i.tagName=="META"&&i.getAttribute("property")){const o=Array.from(document.head.getElementsByTagName("meta")??[]).find(d=>d.getAttribute("property")==i.getAttribute("property")&&!d.isEqualNode(i));if(o){document.head.replaceChild(i,o);continue}}document.head.appendChild(i)}}}function St(t,e,n){let r,i;he(t,qe,s=>n(25,r=s)),he(t,ye,s=>n(35,i=s)),Te();const a=mt();let{autoscroll:o}=e,{version:d}=e,{initial_height:u}=e,{app_mode:m}=e,{is_embed:c}=e,{theme_mode:l="system"}=e,{control_page_title:_}=e,{container:A}=e,{info:X}=e,{eager:x}=e,Y,$=[],ee,te,{mount_css:U=Be}=e,{Client:q}=e,{worker_proxy:S=void 0}=e;S&&(Ve(S),S.addEventListener("progress-update",s=>{n(19,se=s.detail+"...")}));let Ee=S!==void 0,{space:J}=e,{src:K}=e,ne=Lt++,re="pending",I,P=!1,T=!1,f,se=r("common.loading")+"...",oe,j,N=null;async function ie(s){s&&(N||(N=document.createElement("style"),document.head.appendChild(N)),N.textContent=fe(s,d,N)),await U(f.root+"/theme.css?v="+f.theme_hash,document.head),f.stylesheets&&await Promise.all(f.stylesheets.map(h=>h.startsWith("http:")||h.startsWith("https:")?U(h,document.head):fetch(f.root+"/"+h).then(w=>w.text()).then(w=>{fe(w,d)})))}function Ae(s){const h=window.__gradio_mode__==="website";let p;if(h)p="light";else{const O=new URL(window.location.toString()).searchParams.get("__theme");p=l||O||"system"}return p==="dark"||p==="light"?ae(s,p):p=Le(s),p}function Le(s){const h=p();window?.matchMedia("(prefers-color-scheme: dark)")?.addEventListener("change",p);function p(){let w=window?.matchMedia?.("(prefers-color-scheme: dark)").matches?"dark":"light";return ae(s,w),w}return h}function ae(s,h){const p=c?s.parentElement:document.body,w=c?s:s.parentElement;w.style.background="var(--body-background-fill)",h==="dark"?p.classList.add("dark"):p.classList.remove("dark")}let M={message:"",load_status:"pending",status:"sleeping",detail:"SLEEPING"},b,Q=!1;function le(s){n(13,M=s)}const ce=window.__GRADIO_DEV__;be(async()=>{n(20,oe=Ae(I));const s=window.__GRADIO__SERVER_PORT__;if(j=ce==="dev"?`http://localhost:${typeof s=="number"?s:7860}`:J||K||location.origin,n(14,b=await q.connect(j,{status_callback:le,with_null_state:!0,events:["data","log","status","render"]})),window.addEventListener("beforeunload",()=>{b.close()}),!b.config)throw new Error("Could not resolve app config");n(12,f=b.get_url_config()),window.__gradio_space__=f.space_id,n(13,M={message:"",load_status:"complete",status:"running",detail:"RUNNING"}),await ie(f.css),await ve(f.head),n(21,Q=!0),window.__is_colab__=f.is_colab;const h="supports-zerogpu-headers";window.addEventListener("message",O=>{O.data===h&&(window.supports_zerogpu_headers=!0)});const p=window.location.hostname,w=p.includes(".dev.")?`https://moon-${p.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co";window.parent.postMessage(h,w),a("loaded"),n(15,$=f.pages),n(16,ee=f.current_page),n(17,te=f.root),f.dev_mode&&setTimeout(()=>{const{host:O}=new URL(j);let Ue=new URL(`http://${O}${b.api_prefix}/dev/reload`);Y=new EventSource(Ue),Y.addEventListener("error",async _e=>{B("Error","Error reloading app","error"),console.error(JSON.parse(_e.data))}),Y.addEventListener("reload",async _e=>{if(b.close(),n(14,b=await q.connect(j,{status_callback:le,with_null_state:!0,events:["data","log","status","render"]})),!b.config)throw new Error("Could not resolve app config");n(12,f=b.get_url_config()),window.__gradio_space__=f.space_id,await ie(f.css),await ve(f.head),n(21,Q=!0),window.__is_colab__=f.is_colab,a("loaded")})},200)});let de,ue;async function Re(){n(22,de=(await pe(()=>import("./Blocks-DB4cNihJ.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32]),import.meta.url)).default)}async function Se(){n(23,ue=(await pe(()=>import("./Login-phx6DJWM.js"),__vite__mapDeps([33,34,35,36,9,10,24,25,26,11,12,1,2,13,37,38,39,40,23,41,21,17,18,3,4,5,6,7,8,14,15,42,16,19,20,43,44,45]),import.meta.url)).default)}function Ie(){f.auth_required?Se():Re()}const Me={readable_error:{NO_APP_FILE:r("errors.no_app_file"),CONFIG_ERROR:r("errors.config_error"),BUILD_ERROR:r("errors.build_error"),RUNTIME_ERROR:r("errors.runtime_error"),PAUSED:r("errors.space_paused")},title(s){return encodeURIComponent(r("errors.space_not_working"))},description(s,h){return encodeURIComponent(`Hello,

Firstly, thanks for creating this space!

I noticed that the space isn't working correctly because there is ${this.readable_error[s]||"an error"}.

It would be great if you could take a look at this because this space is being embedded on ${h}.

Thanks!`)}};let B;be(async()=>{ye.register(ne,I)});let D;async function Pe(s,h){if(s&&!h&&window.self===window.top){D&&(D.remove(),D=void 0);const p=await it(s);p&&(D=p.element)}}ht(()=>{D?.remove()});function Ne(s){P=s,n(10,P)}function De(s){T=s,n(11,T)}function Oe(s){B=s,n(24,B)}function ze(s){I=s,n(9,I)}return t.$$set=s=>{"autoscroll"in s&&n(0,o=s.autoscroll),"version"in s&&n(1,d=s.version),"initial_height"in s&&n(2,u=s.initial_height),"app_mode"in s&&n(3,m=s.app_mode),"is_embed"in s&&n(4,c=s.is_embed),"theme_mode"in s&&n(29,l=s.theme_mode),"control_page_title"in s&&n(5,_=s.control_page_title),"container"in s&&n(6,A=s.container),"info"in s&&n(7,X=s.info),"eager"in s&&n(30,x=s.eager),"mount_css"in s&&n(31,U=s.mount_css),"Client"in s&&n(32,q=s.Client),"worker_proxy"in s&&n(33,S=s.worker_proxy),"space"in s&&n(8,J=s.space),"src"in s&&n(34,K=s.src)},t.$$.update=()=>{t.$$.dirty[0]&4096&&f?.app_id&&f.app_id,t.$$.dirty[0]&9216&&n(18,re=!P&&M.load_status!=="error"?"pending":!P&&M.load_status==="error"?"error":M.load_status),t.$$.dirty[0]&1073745920|t.$$.dirty[1]&16&&f&&(x||i[ne])&&Ie(),t.$$.dirty[0]&2560&&T&&I.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0})),t.$$.dirty[0]&16400&&b?.config&&Pe(b?.config?.space_id,c)},[o,d,u,m,c,_,A,X,J,I,P,T,f,M,b,$,ee,te,re,se,oe,Q,de,ue,B,r,Ee,ce,Me,l,x,U,q,S,K,i,Ne,De,Oe,ze]}class Bt extends at{constructor(e){super(),_t(this,e,St,At,pt,{autoscroll:0,version:1,initial_height:2,app_mode:3,is_embed:4,theme_mode:29,control_page_title:5,container:6,info:7,eager:30,mount_css:31,Client:32,worker_proxy:33,space:8,src:34},null,[-1,-1])}get autoscroll(){return this.$$.ctx[0]}set autoscroll(e){this.$$set({autoscroll:e}),g()}get version(){return this.$$.ctx[1]}set version(e){this.$$set({version:e}),g()}get initial_height(){return this.$$.ctx[2]}set initial_height(e){this.$$set({initial_height:e}),g()}get app_mode(){return this.$$.ctx[3]}set app_mode(e){this.$$set({app_mode:e}),g()}get is_embed(){return this.$$.ctx[4]}set is_embed(e){this.$$set({is_embed:e}),g()}get theme_mode(){return this.$$.ctx[29]}set theme_mode(e){this.$$set({theme_mode:e}),g()}get control_page_title(){return this.$$.ctx[5]}set control_page_title(e){this.$$set({control_page_title:e}),g()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),g()}get info(){return this.$$.ctx[7]}set info(e){this.$$set({info:e}),g()}get eager(){return this.$$.ctx[30]}set eager(e){this.$$set({eager:e}),g()}get mount_css(){return this.$$.ctx[31]}set mount_css(e){this.$$set({mount_css:e}),g()}get Client(){return this.$$.ctx[32]}set Client(e){this.$$set({Client:e}),g()}get worker_proxy(){return this.$$.ctx[33]}set worker_proxy(e){this.$$set({worker_proxy:e}),g()}get space(){return this.$$.ctx[8]}set space(e){this.$$set({space:e}),g()}get src(){return this.$$.ctx[34]}set src(e){this.$$set({src:e}),g()}}export{Bt as default};
//# sourceMappingURL=Index-vNlY8iMv.js.map
