import"./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js";import{B as Ie}from"./BlockTitle-BijYJada.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js";import{C as Je}from"./Check-BiRlaMNo.js";import{C as Oe}from"./Copy-CxQ9EyK2.js";import{S as Pe}from"./Send-DyoOovnk.js";import{S as Qe}from"./Square-oAGqOwsh.js";import{f as Re}from"./index-CQUs-3jT.js";/* empty css                                              */const{SvelteComponent:Ve,action_destroyer:We,add_render_callback:Xe,append:j,attr:f,binding_callbacks:Q,bubble:K,check_outros:U,create_component:A,create_in_transition:Ze,destroy_component:G,detach:H,element:q,empty:be,flush:w,group_outros:Y,init:xe,insert:S,is_function:$e,listen:b,mount_component:I,noop:L,run_all:V,safe_not_equal:et,set_data:le,set_input_value:M,space:R,text:ne,toggle_class:B,transition_in:y,transition_out:T}=window.__gradio__svelte__internal,{beforeUpdate:tt,afterUpdate:lt,createEventDispatcher:nt,tick:ae}=window.__gradio__svelte__internal;function _e(l){let e,t,n,o;const s=[ot,it],u=[];function a(r,c){return r[19]?0:1}return e=a(l),t=u[e]=s[e](l),{c(){t.c(),n=be()},m(r,c){u[e].m(r,c),S(r,n,c),o=!0},p(r,c){let _=e;e=a(r),e===_?u[e].p(r,c):(Y(),T(u[_],1,1,()=>{u[_]=null}),U(),t=u[e],t?t.p(r,c):(t=u[e]=s[e](r),t.c()),y(t,1),t.m(n.parentNode,n))},i(r){o||(y(t),o=!0)},o(r){T(t),o=!1},d(r){r&&H(n),u[e].d(r)}}}function it(l){let e,t,n,o,s;return t=new Oe({}),{c(){e=q("button"),A(t.$$.fragment),f(e,"class","copy-button svelte-173056l"),f(e,"aria-label","Copy"),f(e,"aria-roledescription","Copy text")},m(u,a){S(u,e,a),I(t,e,null),n=!0,o||(s=b(e,"click",l[21]),o=!0)},p:L,i(u){n||(y(t.$$.fragment,u),n=!0)},o(u){T(t.$$.fragment,u),n=!1},d(u){u&&H(e),G(t),o=!1,s()}}}function ot(l){let e,t,n,o;return t=new Je({}),{c(){e=q("button"),A(t.$$.fragment),f(e,"class","copy-button svelte-173056l"),f(e,"aria-label","Copied"),f(e,"aria-roledescription","Text copied")},m(s,u){S(s,e,u),I(t,e,null),o=!0},p:L,i(s){o||(y(t.$$.fragment,s),s&&(n||Xe(()=>{n=Ze(e,Re,{duration:300}),n.start()})),o=!0)},o(s){T(t.$$.fragment,s),o=!1},d(s){s&&H(e),G(t)}}}function st(l){let e;return{c(){e=ne(l[3])},m(t,n){S(t,e,n)},p(t,n){n[0]&8&&le(e,t[3])},d(t){t&&H(e)}}}function ut(l){let e,t,n,o,s,u;return{c(){e=q("textarea"),f(e,"data-testid","textbox"),f(e,"class","scroll-hide svelte-173056l"),f(e,"dir",t=l[12]?"rtl":"ltr"),f(e,"placeholder",l[2]),f(e,"rows",l[1]),e.disabled=l[5],e.autofocus=l[13],f(e,"maxlength",l[15]),f(e,"style",n=l[14]?"text-align: "+l[14]:""),B(e,"no-label",!l[6]&&(l[10]||l[11]))},m(a,r){S(a,e,r),M(e,l[0]),l[46](e),l[13]&&e.focus(),s||(u=[We(o=l[27].call(null,e,l[0])),b(e,"input",l[45]),b(e,"keypress",l[23]),b(e,"blur",l[37]),b(e,"select",l[22]),b(e,"focus",l[38]),b(e,"scroll",l[24])],s=!0)},p(a,r){r[0]&4096&&t!==(t=a[12]?"rtl":"ltr")&&f(e,"dir",t),r[0]&4&&f(e,"placeholder",a[2]),r[0]&2&&f(e,"rows",a[1]),r[0]&32&&(e.disabled=a[5]),r[0]&8192&&(e.autofocus=a[13]),r[0]&32768&&f(e,"maxlength",a[15]),r[0]&16384&&n!==(n=a[14]?"text-align: "+a[14]:"")&&f(e,"style",n),o&&$e(o.update)&&r[0]&1&&o.update.call(null,a[0]),r[0]&1&&M(e,a[0]),r[0]&3136&&B(e,"no-label",!a[6]&&(a[10]||a[11]))},d(a){a&&H(e),l[46](null),s=!1,V(u)}}}function rt(l){let e;function t(s,u){if(s[8]==="text")return _t;if(s[8]==="password")return at;if(s[8]==="email")return ft}let n=t(l),o=n&&n(l);return{c(){o&&o.c(),e=be()},m(s,u){o&&o.m(s,u),S(s,e,u)},p(s,u){n===(n=t(s))&&o?o.p(s,u):(o&&o.d(1),o=n&&n(s),o&&(o.c(),o.m(e.parentNode,e)))},d(s){s&&H(e),o&&o.d(s)}}}function ft(l){let e,t,n;return{c(){e=q("input"),f(e,"data-testid","textbox"),f(e,"type","email"),f(e,"class","scroll-hide svelte-173056l"),f(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[13],f(e,"maxlength",l[15]),f(e,"autocomplete","email")},m(o,s){S(o,e,s),M(e,l[0]),l[44](e),l[13]&&e.focus(),t||(n=[b(e,"input",l[43]),b(e,"keypress",l[23]),b(e,"blur",l[35]),b(e,"select",l[22]),b(e,"focus",l[36])],t=!0)},p(o,s){s[0]&4&&f(e,"placeholder",o[2]),s[0]&32&&(e.disabled=o[5]),s[0]&8192&&(e.autofocus=o[13]),s[0]&32768&&f(e,"maxlength",o[15]),s[0]&1&&e.value!==o[0]&&M(e,o[0])},d(o){o&&H(e),l[44](null),t=!1,V(n)}}}function at(l){let e,t,n;return{c(){e=q("input"),f(e,"data-testid","password"),f(e,"type","password"),f(e,"class","scroll-hide svelte-173056l"),f(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[13],f(e,"maxlength",l[15]),f(e,"autocomplete","")},m(o,s){S(o,e,s),M(e,l[0]),l[42](e),l[13]&&e.focus(),t||(n=[b(e,"input",l[41]),b(e,"keypress",l[23]),b(e,"blur",l[33]),b(e,"select",l[22]),b(e,"focus",l[34])],t=!0)},p(o,s){s[0]&4&&f(e,"placeholder",o[2]),s[0]&32&&(e.disabled=o[5]),s[0]&8192&&(e.autofocus=o[13]),s[0]&32768&&f(e,"maxlength",o[15]),s[0]&1&&e.value!==o[0]&&M(e,o[0])},d(o){o&&H(e),l[42](null),t=!1,V(n)}}}function _t(l){let e,t,n,o,s;return{c(){e=q("input"),f(e,"data-testid","textbox"),f(e,"type","text"),f(e,"class","scroll-hide svelte-173056l"),f(e,"dir",t=l[12]?"rtl":"ltr"),f(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[13],f(e,"maxlength",l[15]),f(e,"style",n=l[14]?"text-align: "+l[14]:"")},m(u,a){S(u,e,a),M(e,l[0]),l[40](e),l[13]&&e.focus(),o||(s=[b(e,"input",l[39]),b(e,"keypress",l[23]),b(e,"blur",l[31]),b(e,"select",l[22]),b(e,"focus",l[32])],o=!0)},p(u,a){a[0]&4096&&t!==(t=u[12]?"rtl":"ltr")&&f(e,"dir",t),a[0]&4&&f(e,"placeholder",u[2]),a[0]&32&&(e.disabled=u[5]),a[0]&8192&&(e.autofocus=u[13]),a[0]&32768&&f(e,"maxlength",u[15]),a[0]&16384&&n!==(n=u[14]?"text-align: "+u[14]:"")&&f(e,"style",n),a[0]&1&&e.value!==u[0]&&M(e,u[0])},d(u){u&&H(e),l[40](null),o=!1,V(s)}}}function ce(l){let e,t,n,o,s,u;const a=[ht,ct],r=[];function c(_,g){return _[10]===!0?0:1}return t=c(l),n=r[t]=a[t](l),{c(){e=q("button"),n.c(),f(e,"class","submit-button svelte-173056l"),B(e,"padded-button",l[10]!==!0)},m(_,g){S(_,e,g),r[t].m(e,null),o=!0,s||(u=b(e,"click",l[26]),s=!0)},p(_,g){let d=t;t=c(_),t===d?r[t].p(_,g):(Y(),T(r[d],1,1,()=>{r[d]=null}),U(),n=r[t],n?n.p(_,g):(n=r[t]=a[t](_),n.c()),y(n,1),n.m(e,null)),(!o||g[0]&1024)&&B(e,"padded-button",_[10]!==!0)},i(_){o||(y(n),o=!0)},o(_){T(n),o=!1},d(_){_&&H(e),r[t].d(),s=!1,u()}}}function ct(l){let e;return{c(){e=ne(l[10])},m(t,n){S(t,e,n)},p(t,n){n[0]&1024&&le(e,t[10])},i:L,o:L,d(t){t&&H(e)}}}function ht(l){let e,t;return e=new Pe({}),{c(){A(e.$$.fragment)},m(n,o){I(e,n,o),t=!0},p:L,i(n){t||(y(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function he(l){let e,t,n,o,s,u;const a=[dt,bt],r=[];function c(_,g){return _[11]===!0?0:1}return t=c(l),n=r[t]=a[t](l),{c(){e=q("button"),n.c(),f(e,"class","stop-button svelte-173056l"),B(e,"padded-button",l[11]!==!0)},m(_,g){S(_,e,g),r[t].m(e,null),o=!0,s||(u=b(e,"click",l[25]),s=!0)},p(_,g){let d=t;t=c(_),t===d?r[t].p(_,g):(Y(),T(r[d],1,1,()=>{r[d]=null}),U(),n=r[t],n?n.p(_,g):(n=r[t]=a[t](_),n.c()),y(n,1),n.m(e,null)),(!o||g[0]&2048)&&B(e,"padded-button",_[11]!==!0)},i(_){o||(y(n),o=!0)},o(_){T(n),o=!1},d(_){_&&H(e),r[t].d(),s=!1,u()}}}function bt(l){let e;return{c(){e=ne(l[11])},m(t,n){S(t,e,n)},p(t,n){n[0]&2048&&le(e,t[11])},i:L,o:L,d(t){t&&H(e)}}}function dt(l){let e,t;return e=new Qe({props:{fill:"none",stroke_width:2.5}}),{c(){A(e.$$.fragment)},m(n,o){I(e,n,o),t=!0},p:L,i(n){t||(y(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function mt(l){let e,t,n,o,s,u,a,r,c=l[6]&&l[9]&&_e(l);n=new Ie({props:{root:l[16],show_label:l[6],info:l[4],$$slots:{default:[st]},$$scope:{ctx:l}}});function _(h,k){return h[1]===1&&h[18]===1?rt:ut}let g=_(l),d=g(l),m=l[10]&&ce(l),p=l[11]&&he(l);return{c(){e=q("label"),c&&c.c(),t=R(),A(n.$$.fragment),o=R(),s=q("div"),d.c(),u=R(),m&&m.c(),a=R(),p&&p.c(),f(s,"class","input-container svelte-173056l"),f(e,"class","svelte-173056l"),B(e,"container",l[7]),B(e,"show_textbox_border",l[20])},m(h,k){S(h,e,k),c&&c.m(e,null),j(e,t),I(n,e,null),j(e,o),j(e,s),d.m(s,null),j(s,u),m&&m.m(s,null),j(s,a),p&&p.m(s,null),r=!0},p(h,k){h[6]&&h[9]?c?(c.p(h,k),k[0]&576&&y(c,1)):(c=_e(h),c.c(),y(c,1),c.m(e,t)):c&&(Y(),T(c,1,1,()=>{c=null}),U());const D={};k[0]&65536&&(D.root=h[16]),k[0]&64&&(D.show_label=h[6]),k[0]&16&&(D.info=h[4]),k[0]&8|k[1]&33554432&&(D.$$scope={dirty:k,ctx:h}),n.$set(D),g===(g=_(h))&&d?d.p(h,k):(d.d(1),d=g(h),d&&(d.c(),d.m(s,u))),h[10]?m?(m.p(h,k),k[0]&1024&&y(m,1)):(m=ce(h),m.c(),y(m,1),m.m(s,a)):m&&(Y(),T(m,1,1,()=>{m=null}),U()),h[11]?p?(p.p(h,k),k[0]&2048&&y(p,1)):(p=he(h),p.c(),y(p,1),p.m(s,null)):p&&(Y(),T(p,1,1,()=>{p=null}),U()),(!r||k[0]&128)&&B(e,"container",h[7])},i(h){r||(y(c),y(n.$$.fragment,h),y(m),y(p),r=!0)},o(h){T(c),T(n.$$.fragment,h),T(m),T(p),r=!1},d(h){h&&H(e),c&&c.d(),G(n),d.d(),m&&m.d(),p&&p.d()}}}function gt(l,e,t){let{value:n=""}=e,{value_is_output:o=!1}=e,{lines:s=1}=e,{placeholder:u="Type here..."}=e,{label:a}=e,{info:r=void 0}=e,{disabled:c=!1}=e,{show_label:_=!0}=e,{container:g=!0}=e,{max_lines:d=void 0}=e,{type:m="text"}=e,{show_copy_button:p=!1}=e,{submit_btn:h=null}=e,{stop_btn:k=null}=e,{rtl:D=!1}=e,{autofocus:W=!1}=e,{text_align:ie=void 0}=e,{autoscroll:J=!0}=e,{max_length:oe=void 0}=e,{root:se}=e,v,X=!1,Z,x,ue=0,$=!1,E;const de=!h,F=nt();tt(()=>{x=v&&v.offsetHeight+v.scrollTop>v.scrollHeight-100});const me=()=>{x&&J&&!$&&v.scrollTo(0,v.scrollHeight)};function ge(){F("change",n),o||F("input")}lt(()=>{W&&v.focus(),x&&J&&me(),t(28,o=!1)});async function pe(){"clipboard"in navigator&&(await navigator.clipboard.writeText(n),F("copy",{value:n}),ke())}function ke(){t(19,X=!0),Z&&clearTimeout(Z),Z=setTimeout(()=>{t(19,X=!1)},1e3)}function we(i){const C=i.target,z=C.value,N=[C.selectionStart,C.selectionEnd];F("select",{value:z.substring(...N),index:N})}async function ye(i){await ae(),(i.key==="Enter"&&i.shiftKey&&s>1||i.key==="Enter"&&!i.shiftKey&&s===1&&E>=1)&&(i.preventDefault(),F("submit"))}function ve(i){const C=i.target,z=C.scrollTop;z<ue&&($=!0),ue=z;const N=C.scrollHeight-C.clientHeight;z>=N&&($=!1)}function Te(){F("stop")}function Ce(){F("submit")}async function O(i){if(await ae(),s===E)return;const C=i.target,z=window.getComputedStyle(C),N=parseFloat(z.paddingTop),ee=parseFloat(z.paddingBottom),re=parseFloat(z.lineHeight);let te=E===void 0?!1:N+ee+re*E,fe=N+ee+s*re;C.style.height="1px";let P;te&&C.scrollHeight>te?P=te:C.scrollHeight<fe?P=fe:P=C.scrollHeight,C.style.height=`${P}px`}function He(i,C){if(s!==E&&(i.style.overflowY="scroll",i.addEventListener("input",O),!!C.trim()))return O({target:i}),{destroy:()=>i.removeEventListener("input",O)}}function Se(i){K.call(this,l,i)}function Ee(i){K.call(this,l,i)}function qe(i){K.call(this,l,i)}function ze(i){K.call(this,l,i)}function Be(i){K.call(this,l,i)}function De(i){K.call(this,l,i)}function Fe(i){K.call(this,l,i)}function Ke(i){K.call(this,l,i)}function Le(){n=this.value,t(0,n)}function Me(i){Q[i?"unshift":"push"](()=>{v=i,t(17,v)})}function Ne(){n=this.value,t(0,n)}function Ue(i){Q[i?"unshift":"push"](()=>{v=i,t(17,v)})}function Ye(){n=this.value,t(0,n)}function je(i){Q[i?"unshift":"push"](()=>{v=i,t(17,v)})}function Ae(){n=this.value,t(0,n)}function Ge(i){Q[i?"unshift":"push"](()=>{v=i,t(17,v)})}return l.$$set=i=>{"value"in i&&t(0,n=i.value),"value_is_output"in i&&t(28,o=i.value_is_output),"lines"in i&&t(1,s=i.lines),"placeholder"in i&&t(2,u=i.placeholder),"label"in i&&t(3,a=i.label),"info"in i&&t(4,r=i.info),"disabled"in i&&t(5,c=i.disabled),"show_label"in i&&t(6,_=i.show_label),"container"in i&&t(7,g=i.container),"max_lines"in i&&t(29,d=i.max_lines),"type"in i&&t(8,m=i.type),"show_copy_button"in i&&t(9,p=i.show_copy_button),"submit_btn"in i&&t(10,h=i.submit_btn),"stop_btn"in i&&t(11,k=i.stop_btn),"rtl"in i&&t(12,D=i.rtl),"autofocus"in i&&t(13,W=i.autofocus),"text_align"in i&&t(14,ie=i.text_align),"autoscroll"in i&&t(30,J=i.autoscroll),"max_length"in i&&t(15,oe=i.max_length),"root"in i&&t(16,se=i.root)},l.$$.update=()=>{l.$$.dirty[0]&536871170&&(d===void 0?m==="text"?t(18,E=Math.max(s,20)):t(18,E=1):t(18,E=Math.max(d,s))),l.$$.dirty[0]&1&&n===null&&t(0,n=""),l.$$.dirty[0]&393219&&v&&s!==E&&O({target:v}),l.$$.dirty[0]&1&&ge()},[n,s,u,a,r,c,_,g,m,p,h,k,D,W,ie,oe,se,v,E,X,de,pe,we,ye,ve,Te,Ce,He,o,d,J,Se,Ee,qe,ze,Be,De,Fe,Ke,Le,Me,Ne,Ue,Ye,je,Ae,Ge]}class Et extends Ve{constructor(e){super(),xe(this,e,gt,mt,et,{value:0,value_is_output:28,lines:1,placeholder:2,label:3,info:4,disabled:5,show_label:6,container:7,max_lines:29,type:8,show_copy_button:9,submit_btn:10,stop_btn:11,rtl:12,autofocus:13,text_align:14,autoscroll:30,max_length:15,root:16},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),w()}get value_is_output(){return this.$$.ctx[28]}set value_is_output(e){this.$$set({value_is_output:e}),w()}get lines(){return this.$$.ctx[1]}set lines(e){this.$$set({lines:e}),w()}get placeholder(){return this.$$.ctx[2]}set placeholder(e){this.$$set({placeholder:e}),w()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),w()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),w()}get disabled(){return this.$$.ctx[5]}set disabled(e){this.$$set({disabled:e}),w()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),w()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),w()}get max_lines(){return this.$$.ctx[29]}set max_lines(e){this.$$set({max_lines:e}),w()}get type(){return this.$$.ctx[8]}set type(e){this.$$set({type:e}),w()}get show_copy_button(){return this.$$.ctx[9]}set show_copy_button(e){this.$$set({show_copy_button:e}),w()}get submit_btn(){return this.$$.ctx[10]}set submit_btn(e){this.$$set({submit_btn:e}),w()}get stop_btn(){return this.$$.ctx[11]}set stop_btn(e){this.$$set({stop_btn:e}),w()}get rtl(){return this.$$.ctx[12]}set rtl(e){this.$$set({rtl:e}),w()}get autofocus(){return this.$$.ctx[13]}set autofocus(e){this.$$set({autofocus:e}),w()}get text_align(){return this.$$.ctx[14]}set text_align(e){this.$$set({text_align:e}),w()}get autoscroll(){return this.$$.ctx[30]}set autoscroll(e){this.$$set({autoscroll:e}),w()}get max_length(){return this.$$.ctx[15]}set max_length(e){this.$$set({max_length:e}),w()}get root(){return this.$$.ctx[16]}set root(e){this.$$set({root:e}),w()}}export{Et as T};
//# sourceMappingURL=Textbox-BAD5XOM4.js.map
