"""
插图生成模块
用于生成小说插图
"""

import os
import json
import base64
import requests
from typing import Dict, Any, Optional, <PERSON><PERSON>

def generate_illustration(prompt: str, negative_prompt: str = "",
                         output_path: Optional[str] = None,
                         api_key: Optional[str] = None,
                         model_name: Optional[str] = None) -> Tuple[bool, str]:
    """
    使用Kwai-Kolors/Kolors模型生成插图

    参数:
        prompt: 提示词
        negative_prompt: 负面提示词
        output_path: 输出路径，如果为None则返回base64编码的图像数据
        api_key: API密钥，如果为None则尝试从环境变量获取

    返回:
        (成功标志, 结果信息)
    """
    # 如果没有提供API密钥，尝试从环境变量获取
    if not api_key:
        import os
        api_key = os.environ.get("SILICONFLOW_API_KEY", "")
        if not api_key:
            return False, "未提供API密钥，请设置SILICONFLOW_API_KEY环境变量或直接提供api_key参数"

    # 构建请求URL
    url = "https://api.siliconflow.cn/v1/images/generations"

    # 构建请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # 使用指定的模型或默认模型
    model = model_name or "Kwai-Kolors/Kolors"

    # 构建请求体
    payload = {
        "model": model,
        "prompt": prompt,
        "negative_prompt": negative_prompt,
        "image_size": "1024x1024",
        "batch_size": 1,
        "seed": 4999999999,
        "num_inference_steps": 20,
        "guidance_scale": 7.5
    }

    try:
        # 发送请求
        response = requests.post(url, json=payload, headers=headers, timeout=60)

        # 检查响应状态码
        if response.status_code != 200:
            return False, f"API请求失败: {response.status_code} - {response.text}"

        # 解析响应
        result = response.json()

        # 检查是否有图像URL
        if "images" in result and len(result["images"]) > 0 and "url" in result["images"][0]:
            image_url = result["images"][0]["url"]

            # 如果指定了输出路径，下载图像并保存
            if output_path:
                # 下载图像
                image_response = requests.get(image_url, timeout=30)
                if image_response.status_code != 200:
                    return False, f"下载图像失败: {image_response.status_code}"

                # 确保输出目录存在
                os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

                # 保存图像
                with open(output_path, "wb") as f:
                    f.write(image_response.content)

                return True, output_path
            else:
                # 返回图像URL
                return True, image_url
        else:
            return False, f"API响应中没有图像URL: {result}"
    except Exception as e:
        return False, f"生成插图时出错: {e}"

def generate_novel_illustration(title: str, scene: str, style: str = "写实风格",
                               output_path: Optional[str] = None,
                               api_key: Optional[str] = None,
                               model_name: Optional[str] = None) -> Tuple[bool, str]:
    """
    生成小说插图的便捷函数

    参数:
        title: 小说标题
        scene: 场景描述
        style: 风格描述
        output_path: 输出路径
        api_key: API密钥
        model_name: 模型名称

    返回:
        (成功标志, 结果信息)
    """
    # 构建提示词 - 增强版本，更强调风景插图的要求
    prompt = f"中国风插画，{style}，《{title}》中的风景场景：{scene}，高清细节，壮丽的自然风光，精美的风景描绘，无人物，无人类元素，纯风景插图，山水画卷风格，高品质插图"

    # 负面提示词，强化禁止生成人物
    negative_prompt = "人物, 人脸, 人像, 人体, 人类, 手, 脚, 手指, 人形, 人影, 人群, 人物特写, 人物肖像, 人物表情, 人物动作, 人物姿态, 人物身体部位, 人物衣物, 人物面容, 人物头部, 人物脸部, 人物身体, 人物转头, 人物肩膀, 人物手臂, 人物脚部, 人物身体特写"

    # 生成插图
    return generate_illustration(prompt, negative_prompt, output_path, api_key, model_name)

# 测试代码
if __name__ == "__main__":
    # 测试生成插图
    success, result = generate_novel_illustration(
        "山海经",
        "一座古老的山间寺庙，晨雾缭绕，阳光透过云层洒下金色光芒",
        "水墨国画风格",
        "test_illustration.png"
    )

    if success:
        print(f"插图生成成功: {result}")
    else:
        print(f"插图生成失败: {result}")
