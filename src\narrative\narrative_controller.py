"""
Narrative Controller Module

This module provides functions to control the narrative flow,
including generating branching options and selecting the optimal path.
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from datetime import datetime

class NarrativeController:
    """Controller for narrative flow"""

    def __init__(self, novel_dir: str):
        """
        Initialize narrative controller

        Args:
            novel_dir: Novel directory
        """
        self.novel_dir = novel_dir
        self.narrative_dir = os.path.join(novel_dir, "narrative")
        os.makedirs(self.narrative_dir, exist_ok=True)

        self.branches = {}
        self.selected_paths = {}
        self.load_branches()

    def load_branches(self) -> bool:
        """
        Load branches from file

        Returns:
            Success flag
        """
        branches_file = os.path.join(self.narrative_dir, "branches.json")
        if os.path.exists(branches_file):
            try:
                with open(branches_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.branches = data.get("branches", {})
                    self.selected_paths = data.get("selected_paths", {})
                return True
            except Exception as e:
                print(f"Error loading branches: {e}")
        return False

    def save_branches(self) -> bool:
        """
        Save branches to file

        Returns:
            Success flag
        """
        try:
            branches_file = os.path.join(self.narrative_dir, "branches.json")
            with open(branches_file, "w", encoding="utf-8") as f:
                json.dump({
                    "branches": self.branches,
                    "selected_paths": self.selected_paths
                }, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"Error saving branches: {e}")
            return False

    def generate_branch_options(self, chapter_num: int, chapter_content: str, llm_caller: Callable = None) -> List[Dict[str, Any]]:
        """
        Generate branch options for the next chapter

        Args:
            chapter_num: Current chapter number
            chapter_content: Current chapter content
            llm_caller: Function to call LLM

        Returns:
            List of branch options
        """
        if not llm_caller:
            return []

        # Create branch generation prompt
        prompt = f"""
You are a narrative designer for a novel. Based on the current chapter, please generate three potential branching options for the next chapter.

Current chapter content:
{chapter_content[-2000:]}

Please generate three distinct branching options for the next chapter. Each option should:
1. Be a natural continuation of the current chapter
2. Offer a different direction for the story
3. Be interesting and create narrative tension

Format your response as JSON:
{{
  "branches": [
    {{
      "title": "Brief title for this branch",
      "description": "Detailed description of this branch option",
      "key_events": ["Event 1", "Event 2"],
      "character_focus": ["Character 1", "Character 2"],
      "emotional_tone": "The emotional tone of this branch",
      "tension_level": 1-10 (where 10 is highest tension)
    }},
    ...
  ]
}}
"""

        try:
            response = llm_caller(prompt)

            # Try to parse JSON response
            try:
                # 尝试提取JSON部分，处理可能的前缀和后缀文本
                json_str = response

                # 查找JSON开始的位置（通常是第一个{）
                start_idx = json_str.find('{')
                if start_idx == -1:
                    print("No JSON object found in response")
                    return []

                # 查找JSON结束的位置（最后一个}）
                end_idx = json_str.rfind('}')
                if end_idx == -1 or end_idx < start_idx:
                    print("Invalid JSON structure in response")
                    return []

                # 提取JSON部分
                json_str = json_str[start_idx:end_idx+1]

                # 解析JSON
                data = json.loads(json_str)
                branches = data.get("branches", [])

                # 如果没有分支，尝试使用更宽松的解析方法
                if not branches:
                    print("No branches found in JSON, trying alternative parsing...")
                    # 保存原始响应以便调试
                    debug_file = os.path.join(self.narrative_dir, f"branch_response_{chapter_num}.txt")
                    with open(debug_file, "w", encoding="utf-8") as f:
                        f.write(response)

                    # 创建一个基本的分支作为后备
                    branches = [{
                        "title": "Story Continuation",
                        "description": "A natural continuation of the current narrative",
                        "key_events": ["Continuation of current plot"],
                        "character_focus": ["Main characters"],
                        "emotional_tone": "Consistent with current tone",
                        "tension_level": 5
                    }]

                # Add metadata
                for i, branch in enumerate(branches):
                    branch["id"] = f"branch_{chapter_num}_{i+1}"
                    branch["chapter_num"] = chapter_num
                    branch["created_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # Save branches
                self.branches[str(chapter_num)] = branches
                self.save_branches()

                return branches
            except json.JSONDecodeError as e:
                print(f"Error parsing branch generation response: {e}")
                # 保存原始响应以便调试
                debug_file = os.path.join(self.narrative_dir, f"branch_response_error_{chapter_num}.txt")
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(response)

                # 创建一个基本的分支作为后备
                fallback_branch = [{
                    "title": "Story Continuation",
                    "description": "A natural continuation of the current narrative",
                    "key_events": ["Continuation of current plot"],
                    "character_focus": ["Main characters"],
                    "emotional_tone": "Consistent with current tone",
                    "tension_level": 5,
                    "id": f"branch_{chapter_num}_1",
                    "chapter_num": chapter_num,
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }]

                # Save fallback branch
                self.branches[str(chapter_num)] = fallback_branch
                self.save_branches()

                return fallback_branch
        except Exception as e:
            print(f"Error in branch generation: {e}")
            return []

    def evaluate_branch_options(self, chapter_num: int, novel_outline: str, llm_caller: Callable = None) -> Dict[str, float]:
        """
        Evaluate branch options using a value network

        Args:
            chapter_num: Chapter number
            novel_outline: Novel outline
            llm_caller: Function to call LLM

        Returns:
            Dictionary mapping branch IDs to scores
        """
        if not llm_caller or str(chapter_num) not in self.branches:
            return {}

        branches = self.branches[str(chapter_num)]

        # Create branch evaluation prompt
        branches_text = "\n\n".join([
            f"Branch {i+1}: {branch['title']}\n{branch['description']}"
            for i, branch in enumerate(branches)
        ])

        prompt = f"""
You are a narrative evaluator for a novel. Please evaluate the following branching options for the next chapter based on the novel outline.

Novel outline:
{novel_outline[:1000]}...

Branching options:
{branches_text}

Please evaluate each branch based on the following criteria:
1. Consistency with the novel outline
2. Potential for character development
3. Narrative tension and reader engagement
4. Originality and unexpectedness
5. Potential for future plot development

Format your response as JSON:
{{
  "evaluations": [
    {{
      "branch": 1,
      "score": 0-10 (where 10 is best),
      "reasoning": "Explanation for this score"
    }},
    ...
  ]
}}
"""

        try:
            response = llm_caller(prompt)

            # Try to parse JSON response
            try:
                # 尝试提取JSON部分，处理可能的前缀和后缀文本
                json_str = response

                # 查找JSON开始的位置（通常是第一个{）
                start_idx = json_str.find('{')
                if start_idx == -1:
                    print("No JSON object found in evaluation response")
                    return {}

                # 查找JSON结束的位置（最后一个}）
                end_idx = json_str.rfind('}')
                if end_idx == -1 or end_idx < start_idx:
                    print("Invalid JSON structure in evaluation response")
                    return {}

                # 提取JSON部分
                json_str = json_str[start_idx:end_idx+1]

                # 解析JSON
                data = json.loads(json_str)
                evaluations = data.get("evaluations", [])

                # 如果没有评估结果，创建默认评估
                if not evaluations:
                    print("No evaluations found in JSON, creating default evaluations...")
                    # 保存原始响应以便调试
                    debug_file = os.path.join(self.narrative_dir, f"evaluation_response_{chapter_num}.txt")
                    with open(debug_file, "w", encoding="utf-8") as f:
                        f.write(response)

                    # 创建默认评估，给每个分支相同的分数
                    evaluations = []
                    for i in range(len(branches)):
                        evaluations.append({
                            "branch": i+1,
                            "score": 7,  # 默认给个中等偏高的分数
                            "reasoning": "Default evaluation"
                        })

                # Convert to dictionary mapping branch IDs to scores
                scores = {}
                for i, evaluation in enumerate(evaluations):
                    branch_num = evaluation.get("branch", i+1)
                    if 0 < branch_num <= len(branches):
                        branch_id = branches[branch_num-1]["id"]
                        scores[branch_id] = evaluation.get("score", 5)

                return scores
            except json.JSONDecodeError as e:
                print(f"Error parsing branch evaluation response: {e}")
                # 保存原始响应以便调试
                debug_file = os.path.join(self.narrative_dir, f"evaluation_response_error_{chapter_num}.txt")
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(response)

                # 创建默认评估
                scores = {}
                for i, branch in enumerate(branches):
                    scores[branch["id"]] = 7  # 默认给个中等偏高的分数

                return scores
        except Exception as e:
            print(f"Error in branch evaluation: {e}")
            return {}

    def select_optimal_path(self, chapter_num: int, novel_outline: str, llm_caller: Callable = None) -> Optional[Dict[str, Any]]:
        """
        Select the optimal path from branch options

        Args:
            chapter_num: Chapter number
            novel_outline: Novel outline
            llm_caller: Function to call LLM

        Returns:
            Selected branch or None
        """
        if str(chapter_num) not in self.branches:
            return None

        branches = self.branches[str(chapter_num)]
        if not branches:
            return None

        # Evaluate branches
        scores = self.evaluate_branch_options(chapter_num, novel_outline, llm_caller)

        # If no scores, select the first branch
        if not scores:
            selected_branch = branches[0]
        else:
            # Select the branch with the highest score
            max_score = -1
            selected_branch = None
            for branch in branches:
                score = scores.get(branch["id"], 0)
                if score > max_score:
                    max_score = score
                    selected_branch = branch

            # If still no selected branch, use the first one
            if selected_branch is None:
                selected_branch = branches[0]

        # Save selected path
        self.selected_paths[str(chapter_num)] = selected_branch["id"]
        self.save_branches()

        return selected_branch

    def get_selected_path(self, chapter_num: int) -> Optional[Dict[str, Any]]:
        """
        Get the selected path for a chapter

        Args:
            chapter_num: Chapter number

        Returns:
            Selected branch or None
        """
        if str(chapter_num) not in self.branches or str(chapter_num) not in self.selected_paths:
            return None

        branch_id = self.selected_paths[str(chapter_num)]
        branches = self.branches[str(chapter_num)]

        for branch in branches:
            if branch["id"] == branch_id:
                return branch

        return None

    def generate_chapter_outline_from_branch(self, branch: Dict[str, Any], llm_caller: Callable = None) -> str:
        """
        Generate chapter outline from branch

        Args:
            branch: Branch data
            llm_caller: Function to call LLM

        Returns:
            Chapter outline
        """
        if not llm_caller:
            return branch.get("description", "")

        # Create outline generation prompt
        key_events = "\n".join([f"- {event}" for event in branch.get("key_events", [])])
        character_focus = ", ".join(branch.get("character_focus", []))

        prompt = f"""
You are a novel outline writer. Please create a detailed chapter outline based on the following branch information.

Branch title: {branch.get("title", "")}
Branch description: {branch.get("description", "")}
Key events:
{key_events}
Character focus: {character_focus}
Emotional tone: {branch.get("emotional_tone", "")}
Tension level: {branch.get("tension_level", 5)}/10

Please create a detailed chapter outline that includes:
1. A chapter title
2. The main plot points in sequence
3. Character development moments
4. Setting descriptions
5. Any important dialogue or revelations

The outline should be detailed enough to guide the writing of a full chapter.
"""

        try:
            return llm_caller(prompt)
        except Exception as e:
            print(f"Error generating chapter outline: {e}")
            return branch.get("description", "")

# Global narrative controller instance
_narrative_controller = None

def get_narrative_controller(novel_dir: str) -> NarrativeController:
    """
    Get or create a narrative controller for the given novel directory

    Args:
        novel_dir: Novel directory

    Returns:
        Narrative controller instance
    """
    global _narrative_controller

    if _narrative_controller is None or _narrative_controller.novel_dir != novel_dir:
        _narrative_controller = NarrativeController(novel_dir)
        print(f"Created new narrative controller for {novel_dir}")

    return _narrative_controller

def generate_branch_options(chapter_num: int, chapter_content: str, novel_dir: str, llm_caller: Callable = None) -> List[Dict[str, Any]]:
    """
    Generate branch options for the next chapter

    Args:
        chapter_num: Current chapter number
        chapter_content: Current chapter content
        novel_dir: Novel directory
        llm_caller: Function to call LLM

    Returns:
        List of branch options
    """
    controller = get_narrative_controller(novel_dir)
    return controller.generate_branch_options(chapter_num, chapter_content, llm_caller)

def select_optimal_path(chapter_num: int, novel_outline: str, novel_dir: str, llm_caller: Callable = None) -> Optional[Dict[str, Any]]:
    """
    Select the optimal path from branch options

    Args:
        chapter_num: Chapter number
        novel_outline: Novel outline
        novel_dir: Novel directory
        llm_caller: Function to call LLM

    Returns:
        Selected branch or None
    """
    controller = get_narrative_controller(novel_dir)
    return controller.select_optimal_path(chapter_num, novel_outline, llm_caller)

def generate_next_chapter_outline(chapter_num: int, novel_outline: str, novel_dir: str, llm_caller: Callable = None) -> str:
    """
    Generate outline for the next chapter

    Args:
        chapter_num: Current chapter number
        novel_outline: Novel outline
        novel_dir: Novel directory
        llm_caller: Function to call LLM

    Returns:
        Chapter outline
    """
    controller = get_narrative_controller(novel_dir)

    # Generate branch options if not already generated
    if str(chapter_num) not in controller.branches:
        # We need chapter content to generate branches
        chapter_file = os.path.join(novel_dir, "chapters", f"chapter_{chapter_num}_final.txt")
        if os.path.exists(chapter_file):
            with open(chapter_file, "r", encoding="utf-8") as f:
                chapter_content = f.read()
            controller.generate_branch_options(chapter_num, chapter_content, llm_caller)

    # Select optimal path
    branch = controller.select_optimal_path(chapter_num, novel_outline, llm_caller)

    # Generate chapter outline from branch
    if branch:
        return controller.generate_chapter_outline_from_branch(branch, llm_caller)

    # Fallback: generate generic outline
    return f"Chapter {chapter_num + 1} outline - continuation of the story"
