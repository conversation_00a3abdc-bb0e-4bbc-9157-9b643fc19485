"""
API Client Module

This module provides a unified interface for calling different AI APIs.
"""

import os
import json
import requests
from typing import Dict, List, Any, Optional, Union, Tuple

class APIClient:
    """Base API client class"""

    def __init__(self, api_key: str = None, model: str = None):
        """
        Initialize API client

        Args:
            api_key: API key
            model: Model name
        """
        self.api_key = api_key or os.environ.get("API_KEY", "")
        self.model = model

    def call(self, prompt: str, **kwargs) -> str:
        """
        Call API

        Args:
            prompt: Prompt text
            **kwargs: Additional parameters

        Returns:
            Response text
        """
        raise NotImplementedError("Subclasses must implement this method")

class ZhipuAIClient(APIClient):
    """ZhipuAI API client"""

    def __init__(self, api_key: str = None, model: str = "glm-4-flash"):
        """
        Initialize ZhipuAI client

        Args:
            api_key: API key
            model: Model name
        """
        super().__init__(api_key, model)
        self.api_key = api_key or os.environ.get("GLM_API_KEY", "") or os.environ.get("ZHIPUAI_API_KEY", "")
        print(f"ZhipuAI API Key: {self.api_key[:5]}...")
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"

    def call(self, prompt: str, **kwargs) -> str:
        """
        Call ZhipuAI API

        Args:
            prompt: Prompt text
            **kwargs: Additional parameters

        Returns:
            Response text
        """
        try:
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Prepare data
            data = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 0.7),
                "max_tokens": kwargs.get("max_tokens", 2000)
            }

            # Make request
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()

            # Parse response
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"Error calling ZhipuAI API: {e}")
            return f"Error: {e}"

    def generate_content(self, prompt: str, **kwargs) -> str:
        """
        Generate content using ZhipuAI API (alias for call method)

        Args:
            prompt: Prompt text
            **kwargs: Additional parameters

        Returns:
            Generated content
        """
        # This is an alias for the call method to maintain compatibility
        return self.call(prompt, **kwargs)

    def chat_completion(self, model: str = None, messages: list = None, **kwargs) -> dict:
        """
        Call ZhipuAI chat completion API

        Args:
            model: Model name (overrides the instance model if provided)
            messages: List of message objects with role and content
            **kwargs: Additional parameters

        Returns:
            Response dictionary
        """
        try:
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Use provided model or instance model
            model_name = model or self.model

            # Prepare data
            data = {
                "model": model_name,
                "messages": messages or [{"role": "user", "content": "Hello"}],
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 0.7),
                "max_tokens": kwargs.get("max_tokens", 2000)
            }

            # Make request
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()

            # Return full response
            return response.json()
        except Exception as e:
            print(f"Error calling ZhipuAI chat completion API: {e}")
            return {"error": str(e), "choices": [{"message": {"content": f"Error: {e}"}}]}

class SiliconFlowClient(APIClient):
    """SiliconFlow API client"""

    def __init__(self, api_key: str = None, model: str = "internlm/internlm2_5-7b-chat"):
        """
        Initialize SiliconFlow client

        Args:
            api_key: API key
            model: Model name
        """
        super().__init__(api_key, model)
        self.api_key = api_key or os.environ.get("SILICONFLOW_API_KEY", "")
        print(f"SiliconFlow API Key: {self.api_key[:5]}...")
        self.base_url = "https://api.siliconflow.cn/v1/chat/completions"

    def call(self, prompt: str, **kwargs) -> str:
        """
        Call SiliconFlow API

        Args:
            prompt: Prompt text
            **kwargs: Additional parameters

        Returns:
            Response text
        """
        try:
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Prepare data
            data = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 0.7),
                "max_tokens": kwargs.get("max_tokens", 2000)
            }

            # Make request
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()

            # Parse response
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"Error calling SiliconFlow API: {e}")
            return f"Error: {e}"

    def generate_content(self, prompt: str, **kwargs) -> str:
        """
        Generate content using SiliconFlow API (alias for call method)

        Args:
            prompt: Prompt text
            **kwargs: Additional parameters

        Returns:
            Generated content
        """
        # This is an alias for the call method to maintain compatibility
        return self.call(prompt, **kwargs)

    def chat_completion(self, model: str = None, messages: list = None, **kwargs) -> dict:
        """
        Call SiliconFlow chat completion API

        Args:
            model: Model name (overrides the instance model if provided)
            messages: List of message objects with role and content
            **kwargs: Additional parameters

        Returns:
            Response dictionary
        """
        try:
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # Use provided model or instance model
            model_name = model or self.model

            # Prepare data
            data = {
                "model": model_name,
                "messages": messages or [{"role": "user", "content": "Hello"}],
                "temperature": kwargs.get("temperature", 0.7),
                "top_p": kwargs.get("top_p", 0.7),
                "max_tokens": kwargs.get("max_tokens", 2000)
            }

            # Make request
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()

            # Return full response
            return response.json()
        except Exception as e:
            print(f"Error calling SiliconFlow chat completion API: {e}")
            return {"error": str(e), "choices": [{"message": {"content": f"Error: {e}"}}]}

# Factory function to get appropriate client
def get_api_client(model: str = None, api_key: str = None) -> APIClient:
    """
    Get API client for the specified model

    Args:
        model: Model name
        api_key: API key

    Returns:
        API client instance
    """
    # 获取环境变量中的API密钥
    glm_api_key = os.environ.get("GLM_API_KEY", "")
    siliconflow_api_key = os.environ.get("SILICONFLOW_API_KEY", "")

    # 如果没有提供API密钥，使用环境变量中的密钥
    if not api_key:
        if model and model.startswith("glm-"):
            api_key = glm_api_key
        elif model and (model.startswith("internlm/") or model.startswith("BAAI/")):
            api_key = siliconflow_api_key

    print(f"Getting API client for model: {model}, API key provided: {bool(api_key)}")

    if not model:
        # Default to ZhipuAI
        return ZhipuAIClient(api_key)

    # Check model prefix
    if model.startswith("glm-"):
        return ZhipuAIClient(api_key, model)
    elif model.startswith("internlm/") or model.startswith("BAAI/"):
        return SiliconFlowClient(api_key, model)
    else:
        # Default to ZhipuAI
        return ZhipuAIClient(api_key, "glm-4-flash")
