"""
章节生成模块
用于根据大纲生成章节内容，确保内容严格按照大纲要求，并且章节之间自然衔接
"""

import re
import random
from typing import Dict, List, Optional, Tuple, Any

# 导入过渡处理模块
try:
    from src.transition_handler import create_transition, ensure_chapter_connection, create_chapter_ending
except ImportError:
    # 如果导入失败，创建简单的替代函数
    def create_transition(prev_chapter_content, next_chapter_outline):
        return ""

    def ensure_chapter_connection(prev_chapter, next_chapter):
        return prev_chapter, next_chapter

    def create_chapter_ending(chapter_content, next_chapter_outline=None):
        return chapter_content

# 导入中国文化元素模块
try:
    from src.chinese_cultural_elements import enrich_novel_with_chinese_elements, ChineseCulturalElements
except ImportError:
    # 如果导入失败，创建简单的替代函数
    def enrich_novel_with_chinese_elements(chapter_content, genre, chapter_num):
        return chapter_content

    class ChineseCulturalElements:
        def generate_chinese_style_description(self, scene, season=None):
            return f"{scene}的景色十分美丽。"

        def generate_chinese_style_emotion(self, emotion):
            return f"他感到非常{emotion}。"

class ChapterGenerator:
    """章节生成器，根据大纲生成章节内容"""

    def __init__(self):
        self.cultural_elements = ChineseCulturalElements()

    def extract_outline_elements(self, chapter_outline: str) -> Dict[str, str]:
        """
        从章节大纲中提取各个元素

        参数:
            chapter_outline: 章节大纲

        返回:
            包含章节标题、核心事件等元素的字典
        """
        elements = {
            "title": "",
            "core_events": "",
            "characters": "",
            "story_relation": "",
            "emotional_tone": ""
        }

        # 提取章节标题
        title_patterns = [
            r'1\. \*\*章节标题\*\*：(.+?)(?:\n|$)',
            r'####\s*\*\*第\d+章[：:](.+?)\*\*',
            r'第\d+章[：:](.+?)(?:\n|$)'
        ]

        for pattern in title_patterns:
            title_match = re.search(pattern, chapter_outline)
            if title_match:
                elements["title"] = title_match.group(1).strip()
                break

        # 提取核心事件
        core_events_match = re.search(r'2\. \*\*本章核心事件\*\*：(.+?)(?:\n|$)', chapter_outline)
        if core_events_match:
            elements["core_events"] = core_events_match.group(1).strip()

        # 提取出场角色
        characters_match = re.search(r'3\. \*\*本章出场角色\*\*：(.+?)(?:\n|$)', chapter_outline)
        if characters_match:
            elements["characters"] = characters_match.group(1).strip()

        # 提取与整体故事的关系
        story_relation_match = re.search(r'4\. \*\*本章与整体故事的关系\*\*：(.+?)(?:\n|$)', chapter_outline)
        if story_relation_match:
            elements["story_relation"] = story_relation_match.group(1).strip()

        # 提取情感基调
        emotional_tone_match = re.search(r'5\. \*\*本章情感基调\*\*：(.+?)(?:\n|$)', chapter_outline)
        if emotional_tone_match:
            elements["emotional_tone"] = emotional_tone_match.group(1).strip()

        return elements

    def generate_chapter_title(self, chapter_num: int, elements: Dict[str, str]) -> str:
        """
        生成章节标题

        参数:
            chapter_num: 章节号
            elements: 章节元素字典

        返回:
            章节标题
        """
        if elements["title"]:
            return f"第{chapter_num}章：{elements['title']}"
        else:
            return f"第{chapter_num}章"

    def generate_opening_paragraphs(self, elements: Dict[str, str], chapter_num: int, prev_chapter_content: Optional[str] = None) -> List[str]:
        """
        生成章节开头段落

        参数:
            elements: 章节元素字典
            chapter_num: 章节号
            prev_chapter_content: 前一章内容

        返回:
            开头段落列表
        """
        paragraphs = []

        # 提取角色
        characters_str = elements.get("characters", "")
        character_list = []

        # 处理不同的分隔符
        if "、" in characters_str:
            character_list = [c.strip() for c in characters_str.split("、") if c.strip()]
        elif "，" in characters_str:
            character_list = [c.strip() for c in characters_str.split("，") if c.strip()]
        elif "," in characters_str:
            character_list = [c.strip() for c in characters_str.split(",") if c.strip()]
        else:
            character_list = [characters_str.strip()] if characters_str.strip() else []

        # 确保至少有一个角色
        if not character_list:
            character_list = ["主角"]

        # 获取主角
        main_character = character_list[0]

        # 处理通用角色名称
        if main_character == "一位年轻人" or main_character == "主角":
            # 尝试从前一章内容中提取具体的人名
            if prev_chapter_content and chapter_num > 1:
                # 使用正则表达式查找可能的中文人名（2-3个字符）
                name_matches = re.findall(r'([\u4e00-\u9fa5]{2,3})(?=[说道想着回答])', prev_chapter_content)
                if name_matches:
                    # 使用出现频率最高的名字作为主角名称
                    from collections import Counter
                    name_counter = Counter(name_matches)
                    main_character = name_counter.most_common(1)[0][0]
                else:
                    # 如果没有找到，使用一个随机的中文名字
                    chinese_names = ["李明", "张伟", "王强", "赵华", "陈刚", "杨光", "周鹏"]
                    main_character = random.choice(chinese_names)
            else:
                # 如果没有前一章内容，使用一个随机的中文名字
                chinese_names = ["李明", "张伟", "王强", "赵华", "陈刚", "杨光", "周鹏"]
                main_character = random.choice(chinese_names)

        # 如果有前一章内容，创建过渡段落
        if prev_chapter_content and chapter_num > 1:
            transition = create_transition(prev_chapter_content, elements.get("core_events", ""))
            if transition:
                paragraphs.append(transition)

        # 根据情感基调设置开场氛围
        emotional_tone = elements.get("emotional_tone", "")
        if emotional_tone:
            emotions = re.findall(r'[，,、\s]?([^\s，,、]+)', emotional_tone)
            if emotions:
                main_emotion = emotions[0]
                emotion_description = self.cultural_elements.generate_chinese_style_emotion(main_emotion)
                paragraphs.append(emotion_description)

        # 根据章节类型生成不同的开场
        if chapter_num == 1:
            # 第一章通常是引入和背景介绍
            season = "春"  # 第一章用春天象征开始
            scene_description = self.cultural_elements.generate_chinese_style_description(elements.get("scene", "城市"), season)
            paragraphs.append(scene_description)

            paragraphs.append(f"这是{elements.get('title', '故事')}的开始，也是{main_character}人生的转折点。")

            # 介绍主要角色
            if len(character_list) > 1:
                character_intro = f"{main_character}站在故事的起点，身边是{', '.join(character_list[1:])}，一段不平凡的旅程即将开始。"
            else:
                character_intro = f"{main_character}站在故事的起点，一段不平凡的旅程即将开始。"
            paragraphs.append(character_intro)
        else:
            # 非第一章，根据核心事件设置开场
            core_events = elements.get("core_events", "")
            if core_events:
                # 根据章节的奇偶性设置不同的开场
                if chapter_num % 2 == 0:
                    # 偶数章通常是冲突和行动
                    season = "夏"  # 夏天象征热情和行动
                    scene_description = self.cultural_elements.generate_chinese_style_description(elements.get("scene", "城市"), season)
                    paragraphs.append(scene_description)

                    # 提取核心事件的关键动作
                    actions = re.findall(r'([^，。,]+?)[了过]', core_events)
                    if actions:
                        paragraphs.append(f"新的挑战已经来临，{main_character}知道{actions[0]}的时刻到了。")
                    else:
                        paragraphs.append(f"新的挑战已经来临，{main_character}必须做出决断。")
                else:
                    # 奇数章通常是探索和发现
                    season = "秋"  # 秋天象征收获和反思
                    scene_description = self.cultural_elements.generate_chinese_style_description(elements.get("scene", "城市"), season)
                    paragraphs.append(scene_description)

                    # 提取核心事件的关键信息
                    discoveries = re.findall(r'发现了([^，。,]+)', core_events)
                    if discoveries:
                        paragraphs.append(f"{main_character}即将发现{discoveries[0]}，这个发现将改变一切。")
                    else:
                        paragraphs.append(f"故事继续展开，{main_character}的旅程进入了新的篇章。")
            else:
                paragraphs.append(f"故事继续展开，{main_character}的旅程进入了新的篇章。")

        return paragraphs

    def generate_main_content_paragraphs(self, elements: Dict[str, str], chapter_num: int, genre: str, scene: str) -> List[str]:
        """
        生成章节主要内容段落

        参数:
            elements: 章节元素字典
            chapter_num: 章节号
            genre: 小说类型
            scene: 场景

        返回:
            主要内容段落列表
        """
        paragraphs = []

        # 提取核心事件
        core_events = elements.get("core_events", "")
        if not core_events:
            return paragraphs

        # 将核心事件分解为多个小事件
        events = re.split(r'[，。,]', core_events)
        events = [event.strip() for event in events if event.strip()]

        # 为每个小事件创建内容
        for event in events:
            # 场景描述
            scene_description = self.cultural_elements.generate_chinese_style_description(scene)
            paragraphs.append(scene_description)

            # 事件描述
            paragraphs.append(f"{event}。这一刻，仿佛时间都静止了。")

            # 角色反应
            characters = elements.get("characters", "").split("、")
            if characters:
                character = random.choice(characters)
                paragraphs.append(f"{character}深吸一口气，知道这将是一个重要的转折点。")

                # 添加对话
                if len(characters) > 1:
                    other_character = random.choice([c for c in characters if c != character])
                    paragraphs.append(f""{你确定要这么做吗？"" {other_character}问道。")
                    paragraphs.append(f""{我别无选择，"" {character}回答，"这是唯一的方法。"")

        # 添加与整体故事的关系
        story_relation = elements.get("story_relation", "")
        if story_relation:
            paragraphs.append(f"这一切的发生，{story_relation}。故事的脉络逐渐清晰。")

        return paragraphs

    def generate_ending_paragraphs(self, elements: Dict[str, str], chapter_num: int, next_chapter_outline: Optional[str] = None) -> List[str]:
        """
        生成章节结尾段落

        参数:
            elements: 章节元素字典
            chapter_num: 章节号
            next_chapter_outline: 下一章大纲

        返回:
            结尾段落列表
        """
        paragraphs = []

        # 根据情感基调设置结尾氛围
        emotional_tone = elements.get("emotional_tone", "")
        if emotional_tone:
            emotions = re.findall(r'[，,、\s]?([^\s，,、]+)', emotional_tone)
            if emotions:
                last_emotion = emotions[-1]
                emotion_description = self.cultural_elements.generate_chinese_style_emotion(last_emotion)
                paragraphs.append(emotion_description)

        # 添加章节总结
        title = elements.get("title", "")
        if title:
            paragraphs.append(f"{title}的篇章暂告一段落，但故事远未结束。")

        # 如果有下一章大纲，添加悬念
        if next_chapter_outline:
            next_elements = self.extract_outline_elements(next_chapter_outline)
            next_core_events = next_elements.get("core_events", "")
            if next_core_events:
                # 提取下一章核心事件的关键信息
                next_events = re.split(r'[，。,]', next_core_events)
                next_events = [event.strip() for event in next_events if event.strip()]
                if next_events:
                    hint = next_events[0][:10] + "..."
                    paragraphs.append(f"而接下来，{hint} 这将会是怎样的发展？")

        return paragraphs

    def generate_chapter_content(self, chapter_num: int, chapter_outline: str,
                                prev_chapter_content: Optional[str] = None,
                                next_chapter_outline: Optional[str] = None,
                                novel_info: Optional[Dict[str, Any]] = None) -> str:
        """
        生成章节内容

        参数:
            chapter_num: 章节号
            chapter_outline: 章节大纲
            prev_chapter_content: 前一章内容
            next_chapter_outline: 下一章大纲
            novel_info: 小说信息

        返回:
            生成的章节内容
        """
        # 提取小说信息
        genre = novel_info.get("genre", "现代小说") if novel_info else "现代小说"
        style = novel_info.get("style", "写实主义") if novel_info else "写实主义"
        scene = novel_info.get("scene", "城市") if novel_info else "城市"

        # 提取章节元素
        elements = self.extract_outline_elements(chapter_outline)

        # 生成章节标题
        chapter_title = self.generate_chapter_title(chapter_num, elements)

        # 生成开头段落
        opening_paragraphs = self.generate_opening_paragraphs(elements, chapter_num, prev_chapter_content)

        # 生成主要内容段落
        main_content_paragraphs = self.generate_main_content_paragraphs(elements, chapter_num, genre, scene)

        # 生成结尾段落
        ending_paragraphs = self.generate_ending_paragraphs(elements, chapter_num, next_chapter_outline)

        # 组合所有段落
        all_paragraphs = [chapter_title, ""] + opening_paragraphs + main_content_paragraphs + ending_paragraphs

        # 合并段落
        chapter_content = "\n\n".join(all_paragraphs)

        # 使用中国文化元素丰富内容
        enriched_content = enrich_novel_with_chinese_elements(chapter_content, genre, chapter_num)

        # 添加章节结尾
        final_content = create_chapter_ending(enriched_content, next_chapter_outline)

        return final_content

    def ensure_chapters_connection(self, chapters: List[str]) -> List[str]:
        """
        确保章节之间的连接自然流畅

        参数:
            chapters: 章节内容列表

        返回:
            修改后的章节内容列表
        """
        if len(chapters) <= 1:
            return chapters

        connected_chapters = [chapters[0]]

        for i in range(1, len(chapters)):
            prev_chapter = connected_chapters[i-1]
            current_chapter = chapters[i]

            # 确保章节之间的连接
            _, modified_current = ensure_chapter_connection(prev_chapter, current_chapter)
            connected_chapters.append(modified_current)

        return connected_chapters

def generate_chapter(chapter_num: int, chapter_outline: str,
                    prev_chapter_content: Optional[str] = None,
                    next_chapter_outline: Optional[str] = None,
                    novel_info: Optional[Dict[str, Any]] = None) -> str:
    """
    生成章节内容的便捷函数

    参数:
        chapter_num: 章节号
        chapter_outline: 章节大纲
        prev_chapter_content: 前一章内容
        next_chapter_outline: 下一章大纲
        novel_info: 小说信息

    返回:
        生成的章节内容
    """
    generator = ChapterGenerator()
    return generator.generate_chapter_content(
        chapter_num, chapter_outline, prev_chapter_content, next_chapter_outline, novel_info
    )

if __name__ == "__main__":
    # 测试代码
    chapter_outline = """#### **第1章：开篇章节**
1. **章节标题**：开篇章节
2. **本章核心事件**：介绍了李明的背景和城市的环境。主角李明面临着一个重要的选择。
3. **本章出场角色**：李明、张华、王芳
4. **本章与整体故事的关系**：建立故事背景和主角形象，引入故事的主要冲突。
5. **本章情感基调**：好奇、期待、稳定中带有一丝紧张。
"""

    next_chapter_outline = """#### **第2章：冲突与行动**
1. **章节标题**：冲突与行动
2. **本章核心事件**：李明遇到了一个重大的障碍。场景从城市转移到了一个新的地点。冲突开始升级，紧张感增加。
3. **本章出场角色**：李明、张华、赵强
4. **本章与整体故事的关系**：推动情节发展，加深故事冲突，展示主角的成长。
5. **本章情感基调**：紧张、激烈、决然。
"""

    novel_info = {
        "title": "城市之光",
        "genre": "现代都市",
        "style": "写实主义",
        "scene": "繁华都市",
        "characters": "李明"
    }

    generator = ChapterGenerator()
    chapter_content = generator.generate_chapter_content(
        1, chapter_outline, None, next_chapter_outline, novel_info
    )

    print("生成的章节内容:")
    print(chapter_content)
