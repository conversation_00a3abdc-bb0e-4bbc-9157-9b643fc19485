"""
硅基流动API调用模块
用于调用硅基流动的API
"""

import os
import json
import time
import requests
from typing import Dict, Any, Optional, List, Union

# 获取API密钥
SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY", "")

def call_siliconflow_api(model: str,
                        prompt: str,
                        system_message: Optional[str] = None,
                        temperature: float = 0.7,
                        max_tokens: int = 4096) -> str:
    """
    调用硅基流动的API

    参数:
        model: 模型名称
        prompt: 提示词
        system_message: 系统消息
        temperature: 温度参数
        max_tokens: 最大生成token数

    返回:
        模型生成的内容
    """
    # 检查API密钥
    api_key = SILICONFLOW_API_KEY
    if not api_key:
        return "错误: 未提供硅基流动API密钥，请设置SILICONFLOW_API_KEY环境变量"

    # 构建请求URL
    url = "https://api.siliconflow.cn/v1/chat/completions"

    # 构建请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # 构建消息
    messages = []
    if system_message:
        messages.append({"role": "system", "content": system_message})
    messages.append({"role": "user", "content": prompt})

    # 构建请求体
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "stream": False
    }

    # 设置重试参数
    max_retries = 3
    retry_delay = 5  # 秒
    timeout = 120  # 增加超时时间到120秒

    for retry in range(max_retries):
        try:
            # 发送请求
            print(f"调用硅基流动API，模型: {model}，尝试次数: {retry + 1}/{max_retries}")
            response = requests.post(url, json=payload, headers=headers, timeout=timeout)

            # 检查响应状态码
            if response.status_code != 200:
                error_msg = f"API请求失败: {response.status_code} - {response.text}"
                print(error_msg)

                # 如果是最后一次重试，返回错误
                if retry == max_retries - 1:
                    return "API请求失败，请稍后再试"

                # 否则等待后重试
                time.sleep(retry_delay * (retry + 1))  # 每次重试增加等待时间
                continue

            # 解析响应
            result = response.json()

            # 提取生成的内容
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                return content
            else:
                error_msg = f"API响应中没有生成内容"
                print(error_msg)

                # 如果是最后一次重试，返回错误
                if retry == max_retries - 1:
                    return "生成内容失败，请稍后再试"

                # 否则等待后重试
                time.sleep(retry_delay * (retry + 1))

        except requests.exceptions.Timeout:
            # 超时处理
            print(f"调用硅基流动API超时，模型: {model}，尝试次数: {retry + 1}/{max_retries}")

            # 如果是最后一次重试，返回错误
            if retry == max_retries - 1:
                return "调用API超时，请稍后再试"

            # 否则等待后重试
            time.sleep(retry_delay * (retry + 1))

        except Exception as e:
            # 其他异常处理
            error_msg = f"调用硅基流动API时出错: {e}"
            print(error_msg)

            # 如果是最后一次重试，返回错误
            if retry == max_retries - 1:
                return "调用API出错，请稍后再试"

            # 否则等待后重试
            time.sleep(retry_delay * (retry + 1))

    # 如果所有重试都失败，返回错误
    return f"调用硅基流动API失败，已重试{max_retries}次"
