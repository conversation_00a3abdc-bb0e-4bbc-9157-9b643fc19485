const __vite__fileDeps=["./index-TAdSoOLO.js","./index-DiDhehyb.js","./index-V1UhiEW8.js","./index-Bq9js3go.css","./Check-BiRlaMNo.js","./Copy-CxQ9EyK2.js","./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js","./IconButtonWrapper-BULHeAAS.css","./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js","./prism-python-DO0H4w6Q.js","./MarkdownCode-CMJLBV0e.css","./IconButton-DbC-jsk_.js","./Download-DVtk-Jv3.js","./DownloadLink-QIttOhoR.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./file-url-DoxvUUVV.js","./IconButtonWrapper-DrWC4NJv.js","./index-CWG4El0O.js","./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js","./StreamingBar-DOagx4HU.css","./Clear-By3xiIwg.js","./Code-DGNrTu_I.js","./Block-CMfAaXj9.js","./BlockLabel-JbMBN4MZ.js","./Empty-BgOmVBFg.js","./Example-Wp-_4AVX.js","./Example-oomIF0ca.css","./index-Bz6aSYMe.js","./index-HByxFVxO.js","./index-BMz6jcp6.js","./index-CnmEBB3i.js","./frontmatter-CCCjReic.js","./yaml-DsCXHVTH.js","./index-DlT7JNkm.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as lt}from"./index-V1UhiEW8.js";import{C as zn}from"./Check-BiRlaMNo.js";import{C as Ro}from"./Copy-CxQ9EyK2.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js";import{I as qa}from"./IconButton-DbC-jsk_.js";import{D as _o}from"./Download-DVtk-Jv3.js";import{D as Yf}from"./DownloadLink-QIttOhoR.js";import{I as Jf}from"./IconButtonWrapper-DrWC4NJv.js";import{S as Xf}from"./index-CWG4El0O.js";import{C as ja}from"./Code-DGNrTu_I.js";import"./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js";import{B as Qf}from"./Block-CMfAaXj9.js";import{B as Zf}from"./BlockLabel-JbMBN4MZ.js";import{E as tu}from"./Empty-BgOmVBFg.js";import eu from"./Example-Wp-_4AVX.js";let Gs=[],Ka=[];(()=>{let s="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(t=>t?parseInt(t,36):1);for(let t=0,e=0;t<s.length;t++)(t%2?Ka:Gs).push(e=e+s[t])})();function iu(s){if(s<768)return!1;for(let t=0,e=Gs.length;;){let i=t+e>>1;if(s<Gs[i])e=i;else if(s>=Ka[i])t=i+1;else return!0;if(t==e)return!1}}function Io(s){return s>=127462&&s<=127487}const No=8205;function nu(s,t,e=!0,i=!0){return(e?Ua:su)(s,t,i)}function Ua(s,t,e){if(t==s.length)return t;t&&Ga(s.charCodeAt(t))&&Ya(s.charCodeAt(t-1))&&t--;let i=bs(s,t);for(t+=Fo(i);t<s.length;){let n=bs(s,t);if(i==No||n==No||e&&iu(n))t+=Fo(n),i=n;else if(Io(n)){let r=0,o=t-2;for(;o>=0&&Io(bs(s,o));)r++,o-=2;if(r%2==0)break;t+=2}else break}return t}function su(s,t,e){for(;t>0;){let i=Ua(s,t-2,e);if(i<t)return i;t--}return 0}function bs(s,t){let e=s.charCodeAt(t);if(!Ya(e)||t+1==s.length)return e;let i=s.charCodeAt(t+1);return Ga(i)?(e-55296<<10)+(i-56320)+65536:e}function Ga(s){return s>=56320&&s<57344}function Ya(s){return s>=55296&&s<56320}function Fo(s){return s<65536?1:2}class F{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,i){[t,e]=li(this,t,e);let n=[];return this.decompose(0,t,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(e,this.length,n,1),ie.from(n,this.length-(e-t)+i.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){[t,e]=li(this,t,e);let i=[];return this.decompose(t,e,i,0),ie.from(i,e-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let e=this.scanIdentical(t,1),i=this.length-this.scanIdentical(t,-1),n=new Pi(this),r=new Pi(t);for(let o=e,l=e;;){if(n.next(o),r.next(o),o=0,n.lineBreak!=r.lineBreak||n.done!=r.done||n.value!=r.value)return!1;if(l+=n.value.length,n.done||l>=i)return!0}}iter(t=1){return new Pi(this,t)}iterRange(t,e=this.length){return new Ja(this,t,e)}iterLines(t,e){let i;if(t==null)i=this.iter();else{e==null&&(e=this.lines+1);let n=this.line(t).from;i=this.iterRange(n,Math.max(n,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to))}return new Xa(i)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(t.length==0)throw new RangeError("A document must have at least one line");return t.length==1&&!t[0]?F.empty:t.length<=32?new Q(t):ie.from(Q.split(t,[]))}}class Q extends F{constructor(t,e=ru(t)){super(),this.text=t,this.length=e}get lines(){return this.text.length}get children(){return null}lineInner(t,e,i,n){for(let r=0;;r++){let o=this.text[r],l=n+o.length;if((e?i:l)>=t)return new ou(n,l,i,o);n=l+1,i++}}decompose(t,e,i,n){let r=t<=0&&e>=this.length?this:new Q(Ho(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(n&1){let o=i.pop(),l=Pn(r.text,o.text.slice(),0,r.length);if(l.length<=32)i.push(new Q(l,o.length+r.length));else{let a=l.length>>1;i.push(new Q(l.slice(0,a)),new Q(l.slice(a)))}}else i.push(r)}replace(t,e,i){if(!(i instanceof Q))return super.replace(t,e,i);[t,e]=li(this,t,e);let n=Pn(this.text,Pn(i.text,Ho(this.text,0,t)),e),r=this.length+i.length-(e-t);return n.length<=32?new Q(n,r):ie.from(Q.split(n,[]),r)}sliceString(t,e=this.length,i=`
`){[t,e]=li(this,t,e);let n="";for(let r=0,o=0;r<=e&&o<this.text.length;o++){let l=this.text[o],a=r+l.length;r>t&&o&&(n+=i),t<a&&e>r&&(n+=l.slice(Math.max(0,t-r),e-r)),r=a+1}return n}flatten(t){for(let e of this.text)t.push(e)}scanIdentical(){return 0}static split(t,e){let i=[],n=-1;for(let r of t)i.push(r),n+=r.length+1,i.length==32&&(e.push(new Q(i,n)),i=[],n=-1);return n>-1&&e.push(new Q(i,n)),e}}class ie extends F{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let i of t)this.lines+=i.lines}lineInner(t,e,i,n){for(let r=0;;r++){let o=this.children[r],l=n+o.length,a=i+o.lines-1;if((e?a:l)>=t)return o.lineInner(t,e,i,n);n=l+1,i=a+1}}decompose(t,e,i,n){for(let r=0,o=0;o<=e&&r<this.children.length;r++){let l=this.children[r],a=o+l.length;if(t<=a&&e>=o){let h=n&((o<=t?1:0)|(a>=e?2:0));o>=t&&a<=e&&!h?i.push(l):l.decompose(t-o,e-o,i,h)}o=a+1}}replace(t,e,i){if([t,e]=li(this,t,e),i.lines<this.lines)for(let n=0,r=0;n<this.children.length;n++){let o=this.children[n],l=r+o.length;if(t>=r&&e<=l){let a=o.replace(t-r,e-r,i),h=this.lines-o.lines+a.lines;if(a.lines<h>>4&&a.lines>h>>6){let c=this.children.slice();return c[n]=a,new ie(c,this.length-(e-t)+i.length)}return super.replace(r,l,a)}r=l+1}return super.replace(t,e,i)}sliceString(t,e=this.length,i=`
`){[t,e]=li(this,t,e);let n="";for(let r=0,o=0;r<this.children.length&&o<=e;r++){let l=this.children[r],a=o+l.length;o>t&&r&&(n+=i),t<a&&e>o&&(n+=l.sliceString(t-o,e-o,i)),o=a+1}return n}flatten(t){for(let e of this.children)e.flatten(t)}scanIdentical(t,e){if(!(t instanceof ie))return 0;let i=0,[n,r,o,l]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;n+=e,r+=e){if(n==o||r==l)return i;let a=this.children[n],h=t.children[r];if(a!=h)return i+a.scanIdentical(h,e);i+=a.length+1}}static from(t,e=t.reduce((i,n)=>i+n.length+1,-1)){let i=0;for(let d of t)i+=d.lines;if(i<32){let d=[];for(let p of t)p.flatten(d);return new Q(d,e)}let n=Math.max(32,i>>5),r=n<<1,o=n>>1,l=[],a=0,h=-1,c=[];function f(d){let p;if(d.lines>r&&d instanceof ie)for(let m of d.children)f(m);else d.lines>o&&(a>o||!a)?(u(),l.push(d)):d instanceof Q&&a&&(p=c[c.length-1])instanceof Q&&d.lines+p.lines<=32?(a+=d.lines,h+=d.length+1,c[c.length-1]=new Q(p.text.concat(d.text),p.length+1+d.length)):(a+d.lines>n&&u(),a+=d.lines,h+=d.length+1,c.push(d))}function u(){a!=0&&(l.push(c.length==1?c[0]:ie.from(c,h)),h=-1,a=c.length=0)}for(let d of t)f(d);return u(),l.length==1?l[0]:new ie(l,e)}}F.empty=new Q([""],0);function ru(s){let t=-1;for(let e of s)t+=e.length+1;return t}function Pn(s,t,e=0,i=1e9){for(let n=0,r=0,o=!0;r<s.length&&n<=i;r++){let l=s[r],a=n+l.length;a>=e&&(a>i&&(l=l.slice(0,i-n)),n<e&&(l=l.slice(e-n)),o?(t[t.length-1]+=l,o=!1):t.push(l)),n=a+1}return t}function Ho(s,t,e){return Pn(s,[""],t,e)}class Pi{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof Q?t.text.length:t.children.length)<<1]}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],r=this.offsets[i],o=r>>1,l=n instanceof Q?n.text.length:n.children.length;if(o==(e>0?l:0)){if(i==0)return this.done=!0,this.value="",this;e>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((r&1)==(e>0?0:1)){if(this.offsets[i]+=e,t==0)return this.lineBreak=!0,this.value=`
`,this;t--}else if(n instanceof Q){let a=n.text[o+(e<0?-1:0)];if(this.offsets[i]+=e,a.length>Math.max(0,t))return this.value=t==0?a:e>0?a.slice(t):a.slice(0,a.length-t),this;t-=a.length}else{let a=n.children[o+(e<0?-1:0)];t>a.length?(t-=a.length,this.offsets[i]+=e):(e<0&&this.offsets[i]--,this.nodes.push(a),this.offsets.push(e>0?1:(a instanceof Q?a.text.length:a.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class Ja{constructor(t,e,i){this.value="",this.done=!1,this.cursor=new Pi(t,e>i?-1:1),this.pos=e>i?t.length:0,this.from=Math.min(e,i),this.to=Math.max(e,i)}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let i=e<0?this.pos-this.from:this.to-this.pos;t>i&&(t=i),i-=t;let{value:n}=this.cursor.next(t);return this.pos+=(n.length+t)*e,this.value=n.length<=i?n:e<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&this.value!=""}}class Xa{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:e,lineBreak:i,value:n}=this.inner.next(t);return e&&this.afterBreak?(this.value="",this.afterBreak=!1):e?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return!1}}typeof Symbol<"u"&&(F.prototype[Symbol.iterator]=function(){return this.iter()},Pi.prototype[Symbol.iterator]=Ja.prototype[Symbol.iterator]=Xa.prototype[Symbol.iterator]=function(){return this});class ou{constructor(t,e,i,n){this.from=t,this.to=e,this.number=i,this.text=n}get length(){return this.to-this.from}}function li(s,t,e){return t=Math.max(0,Math.min(s.length,t)),[t,Math.max(t,Math.min(s.length,e))]}function yt(s,t,e=!0,i=!0){return nu(s,t,e,i)}function lu(s){return s>=56320&&s<57344}function au(s){return s>=55296&&s<56320}function Pt(s,t){let e=s.charCodeAt(t);if(!au(e)||t+1==s.length)return e;let i=s.charCodeAt(t+1);return lu(i)?(e-55296<<10)+(i-56320)+65536:e}function Qa(s){return s<=65535?String.fromCharCode(s):(s-=65536,String.fromCharCode((s>>10)+55296,(s&1023)+56320))}function ue(s){return s<65536?1:2}const Ys=/\r\n?|\n/;var ct=function(s){return s[s.Simple=0]="Simple",s[s.TrackDel=1]="TrackDel",s[s.TrackBefore=2]="TrackBefore",s[s.TrackAfter=3]="TrackAfter",s}(ct||(ct={}));class le{constructor(t){this.sections=t}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e+1];t+=i<0?this.sections[e]:i}return t}get empty(){return this.sections.length==0||this.sections.length==2&&this.sections[1]<0}iterGaps(t){for(let e=0,i=0,n=0;e<this.sections.length;){let r=this.sections[e++],o=this.sections[e++];o<0?(t(i,n,r),n+=r):n+=o,i+=r}}iterChangedRanges(t,e=!1){Js(this,t,e)}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];n<0?t.push(i,n):t.push(n,i)}return new le(t)}composeDesc(t){return this.empty?t:t.empty?this:Za(this,t)}mapDesc(t,e=!1){return t.empty?this:Xs(this,t,e)}mapPos(t,e=-1,i=ct.Simple){let n=0,r=0;for(let o=0;o<this.sections.length;){let l=this.sections[o++],a=this.sections[o++],h=n+l;if(a<0){if(h>t)return r+(t-n);r+=l}else{if(i!=ct.Simple&&h>=t&&(i==ct.TrackDel&&n<t&&h>t||i==ct.TrackBefore&&n<t||i==ct.TrackAfter&&h>t))return null;if(h>t||h==t&&e<0&&!l)return t==n||e<0?r:r+a;r+=a}n=h}if(t>n)throw new RangeError(`Position ${t} is out of range for changeset of length ${n}`);return r}touchesRange(t,e=t){for(let i=0,n=0;i<this.sections.length&&n<=e;){let r=this.sections[i++],o=this.sections[i++],l=n+r;if(o>=0&&n<=e&&l>=t)return n<t&&l>e?"cover":!0;n=l}return!1}toString(){let t="";for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];t+=(t?" ":"")+i+(n>=0?":"+n:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some(e=>typeof e!="number"))throw new RangeError("Invalid JSON representation of ChangeDesc");return new le(t)}static create(t){return new le(t)}}class nt extends le{constructor(t,e){super(t),this.inserted=e}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return Js(this,(e,i,n,r,o)=>t=t.replace(n,n+(i-e),o),!1),t}mapDesc(t,e=!1){return Xs(this,t,e,!0)}invert(t){let e=this.sections.slice(),i=[];for(let n=0,r=0;n<e.length;n+=2){let o=e[n],l=e[n+1];if(l>=0){e[n]=l,e[n+1]=o;let a=n>>1;for(;i.length<a;)i.push(F.empty);i.push(o?t.slice(r,r+o):F.empty)}r+=o}return new nt(e,i)}compose(t){return this.empty?t:t.empty?this:Za(this,t,!0)}map(t,e=!1){return t.empty?this:Xs(this,t,e,!0)}iterChanges(t,e=!1){Js(this,t,e)}get desc(){return le.create(this.sections)}filter(t){let e=[],i=[],n=[],r=new Ni(this);t:for(let o=0,l=0;;){let a=o==t.length?1e9:t[o++];for(;l<a||l==a&&r.len==0;){if(r.done)break t;let c=Math.min(r.len,a-l);dt(n,c,-1);let f=r.ins==-1?-1:r.off==0?r.ins:0;dt(e,c,f),f>0&&ve(i,e,r.text),r.forward(c),l+=c}let h=t[o++];for(;l<h;){if(r.done)break t;let c=Math.min(r.len,h-l);dt(e,c,-1),dt(n,c,r.ins==-1?-1:r.off==0?r.ins:0),r.forward(c),l+=c}}return{changes:new nt(e,i),filtered:le.create(n)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e],n=this.sections[e+1];n<0?t.push(i):n==0?t.push([i]):t.push([i].concat(this.inserted[e>>1].toJSON()))}return t}static of(t,e,i){let n=[],r=[],o=0,l=null;function a(c=!1){if(!c&&!n.length)return;o<e&&dt(n,e-o,-1);let f=new nt(n,r);l=l?l.compose(f.map(l)):f,n=[],r=[],o=0}function h(c){if(Array.isArray(c))for(let f of c)h(f);else if(c instanceof nt){if(c.length!=e)throw new RangeError(`Mismatched change set length (got ${c.length}, expected ${e})`);a(),l=l?l.compose(c.map(l)):c}else{let{from:f,to:u=f,insert:d}=c;if(f>u||f<0||u>e)throw new RangeError(`Invalid change range ${f} to ${u} (in doc of length ${e})`);let p=d?typeof d=="string"?F.of(d.split(i||Ys)):d:F.empty,m=p.length;if(f==u&&m==0)return;f<o&&a(),f>o&&dt(n,f-o,-1),dt(n,u-f,m),ve(r,n,p),o=u}}return h(t),a(!l),l}static empty(t){return new nt(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],i=[];for(let n=0;n<t.length;n++){let r=t[n];if(typeof r=="number")e.push(r,-1);else{if(!Array.isArray(r)||typeof r[0]!="number"||r.some((o,l)=>l&&typeof o!="string"))throw new RangeError("Invalid JSON representation of ChangeSet");if(r.length==1)e.push(r[0],0);else{for(;i.length<n;)i.push(F.empty);i[n]=F.of(r.slice(1)),e.push(r[0],i[n].length)}}}return new nt(e,i)}static createSet(t,e){return new nt(t,e)}}function dt(s,t,e,i=!1){if(t==0&&e<=0)return;let n=s.length-2;n>=0&&e<=0&&e==s[n+1]?s[n]+=t:n>=0&&t==0&&s[n]==0?s[n+1]+=e:i?(s[n]+=t,s[n+1]+=e):s.push(t,e)}function ve(s,t,e){if(e.length==0)return;let i=t.length-2>>1;if(i<s.length)s[s.length-1]=s[s.length-1].append(e);else{for(;s.length<i;)s.push(F.empty);s.push(e)}}function Js(s,t,e){let i=s.inserted;for(let n=0,r=0,o=0;o<s.sections.length;){let l=s.sections[o++],a=s.sections[o++];if(a<0)n+=l,r+=l;else{let h=n,c=r,f=F.empty;for(;h+=l,c+=a,a&&i&&(f=f.append(i[o-2>>1])),!(e||o==s.sections.length||s.sections[o+1]<0);)l=s.sections[o++],a=s.sections[o++];t(n,h,r,c,f),n=h,r=c}}}function Xs(s,t,e,i=!1){let n=[],r=i?[]:null,o=new Ni(s),l=new Ni(t);for(let a=-1;;){if(o.done&&l.len||l.done&&o.len)throw new Error("Mismatched change set lengths");if(o.ins==-1&&l.ins==-1){let h=Math.min(o.len,l.len);dt(n,h,-1),o.forward(h),l.forward(h)}else if(l.ins>=0&&(o.ins<0||a==o.i||o.off==0&&(l.len<o.len||l.len==o.len&&!e))){let h=l.len;for(dt(n,l.ins,-1);h;){let c=Math.min(o.len,h);o.ins>=0&&a<o.i&&o.len<=c&&(dt(n,0,o.ins),r&&ve(r,n,o.text),a=o.i),o.forward(c),h-=c}l.next()}else if(o.ins>=0){let h=0,c=o.len;for(;c;)if(l.ins==-1){let f=Math.min(c,l.len);h+=f,c-=f,l.forward(f)}else if(l.ins==0&&l.len<c)c-=l.len,l.next();else break;dt(n,h,a<o.i?o.ins:0),r&&a<o.i&&ve(r,n,o.text),a=o.i,o.forward(o.len-c)}else{if(o.done&&l.done)return r?nt.createSet(n,r):le.create(n);throw new Error("Mismatched change set lengths")}}}function Za(s,t,e=!1){let i=[],n=e?[]:null,r=new Ni(s),o=new Ni(t);for(let l=!1;;){if(r.done&&o.done)return n?nt.createSet(i,n):le.create(i);if(r.ins==0)dt(i,r.len,0,l),r.next();else if(o.len==0&&!o.done)dt(i,0,o.ins,l),n&&ve(n,i,o.text),o.next();else{if(r.done||o.done)throw new Error("Mismatched change set lengths");{let a=Math.min(r.len2,o.len),h=i.length;if(r.ins==-1){let c=o.ins==-1?-1:o.off?0:o.ins;dt(i,a,c,l),n&&c&&ve(n,i,o.text)}else o.ins==-1?(dt(i,r.off?0:r.len,a,l),n&&ve(n,i,r.textBit(a))):(dt(i,r.off?0:r.len,o.off?0:o.ins,l),n&&!o.off&&ve(n,i,o.text));l=(r.ins>a||o.ins>=0&&o.len>a)&&(l||i.length>h),r.forward2(a),o.forward(a)}}}}class Ni{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return this.ins==-2}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?F.empty:t[e]}textBit(t){let{inserted:e}=this.set,i=this.i-2>>1;return i>=e.length&&!t?F.empty:e[i].slice(this.off,t==null?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){this.ins==-1?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class Ie{constructor(t,e,i){this.from=t,this.to=e,this.flags=i}get anchor(){return this.flags&32?this.to:this.from}get head(){return this.flags&32?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return this.flags&8?-1:this.flags&16?1:0}get bidiLevel(){let t=this.flags&7;return t==7?null:t}get goalColumn(){let t=this.flags>>6;return t==16777215?void 0:t}map(t,e=-1){let i,n;return this.empty?i=n=t.mapPos(this.from,e):(i=t.mapPos(this.from,1),n=t.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new Ie(i,n,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return v.range(t,e);let i=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return v.range(this.anchor,i)}eq(t,e=!1){return this.anchor==t.anchor&&this.head==t.head&&(!e||!this.empty||this.assoc==t.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid JSON representation for SelectionRange");return v.range(t.anchor,t.head)}static create(t,e,i){return new Ie(t,e,i)}}class v{constructor(t,e){this.ranges=t,this.mainIndex=e}map(t,e=-1){return t.empty?this:v.create(this.ranges.map(i=>i.map(t,e)),this.mainIndex)}eq(t,e=!1){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(t.ranges[i],e))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return this.ranges.length==1?this:new v([this.main],0)}addRange(t,e=!0){return v.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let i=this.ranges.slice();return i[e]=t,v.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(t=>t.toJSON()),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||typeof t.main!="number"||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new v(t.ranges.map(e=>Ie.fromJSON(e)),t.main)}static single(t,e=t){return new v([v.range(t,e)],0)}static create(t,e=0){if(t.length==0)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<t.length;n++){let r=t[n];if(r.empty?r.from<=i:r.from<i)return v.normalized(t.slice(),e);i=r.to}return new v(t,e)}static cursor(t,e=0,i,n){return Ie.create(t,t,(e==0?0:e<0?8:16)|(i==null?7:Math.min(6,i))|(n??16777215)<<6)}static range(t,e,i,n){let r=(i??16777215)<<6|(n==null?7:Math.min(6,n));return e<t?Ie.create(e,t,48|r):Ie.create(t,e,(e>t?8:0)|r)}static normalized(t,e=0){let i=t[e];t.sort((n,r)=>n.from-r.from),e=t.indexOf(i);for(let n=1;n<t.length;n++){let r=t[n],o=t[n-1];if(r.empty?r.from<=o.to:r.from<o.to){let l=o.from,a=Math.max(r.to,o.to);n<=e&&e--,t.splice(--n,2,r.anchor>r.head?v.range(a,l):v.range(l,a))}}return new v(t,e)}}function th(s,t){for(let e of s.ranges)if(e.to>t)throw new RangeError("Selection points outside of document")}let Jr=0;class O{constructor(t,e,i,n,r){this.combine=t,this.compareInput=e,this.compare=i,this.isStatic=n,this.id=Jr++,this.default=t([]),this.extensions=typeof r=="function"?r(this):r}get reader(){return this}static define(t={}){return new O(t.combine||(e=>e),t.compareInput||((e,i)=>e===i),t.compare||(t.combine?(e,i)=>e===i:Xr),!!t.static,t.enables)}of(t){return new En([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new En(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new En(t,this,2,e)}from(t,e){return e||(e=i=>i),this.compute([t],i=>e(i.field(t)))}}function Xr(s,t){return s==t||s.length==t.length&&s.every((e,i)=>e===t[i])}class En{constructor(t,e,i,n){this.dependencies=t,this.facet=e,this.type=i,this.value=n,this.id=Jr++}dynamicSlot(t){var e;let i=this.value,n=this.facet.compareInput,r=this.id,o=t[r]>>1,l=this.type==2,a=!1,h=!1,c=[];for(let f of this.dependencies)f=="doc"?a=!0:f=="selection"?h=!0:((e=t[f.id])!==null&&e!==void 0?e:1)&1||c.push(t[f.id]);return{create(f){return f.values[o]=i(f),1},update(f,u){if(a&&u.docChanged||h&&(u.docChanged||u.selection)||Qs(f,c)){let d=i(f);if(l?!Vo(d,f.values[o],n):!n(d,f.values[o]))return f.values[o]=d,1}return 0},reconfigure:(f,u)=>{let d,p=u.config.address[r];if(p!=null){let m=qn(u,p);if(this.dependencies.every(g=>g instanceof O?u.facet(g)===f.facet(g):g instanceof Tt?u.field(g,!1)==f.field(g,!1):!0)||(l?Vo(d=i(f),m,n):n(d=i(f),m)))return f.values[o]=m,0}else d=i(f);return f.values[o]=d,1}}}}function Vo(s,t,e){if(s.length!=t.length)return!1;for(let i=0;i<s.length;i++)if(!e(s[i],t[i]))return!1;return!0}function Qs(s,t){let e=!1;for(let i of t)Ei(s,i)&1&&(e=!0);return e}function hu(s,t,e){let i=e.map(a=>s[a.id]),n=e.map(a=>a.type),r=i.filter(a=>!(a&1)),o=s[t.id]>>1;function l(a){let h=[];for(let c=0;c<i.length;c++){let f=qn(a,i[c]);if(n[c]==2)for(let u of f)h.push(u);else h.push(f)}return t.combine(h)}return{create(a){for(let h of i)Ei(a,h);return a.values[o]=l(a),1},update(a,h){if(!Qs(a,r))return 0;let c=l(a);return t.compare(c,a.values[o])?0:(a.values[o]=c,1)},reconfigure(a,h){let c=Qs(a,i),f=h.config.facets[t.id],u=h.facet(t);if(f&&!c&&Xr(e,f))return a.values[o]=u,0;let d=l(a);return t.compare(d,u)?(a.values[o]=u,0):(a.values[o]=d,1)}}}const ln=O.define({static:!0});class Tt{constructor(t,e,i,n,r){this.id=t,this.createF=e,this.updateF=i,this.compareF=n,this.spec=r,this.provides=void 0}static define(t){let e=new Tt(Jr++,t.create,t.update,t.compare||((i,n)=>i===n),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(ln).find(i=>i.field==this);return(e?.create||this.createF)(t)}slot(t){let e=t[this.id]>>1;return{create:i=>(i.values[e]=this.create(i),1),update:(i,n)=>{let r=i.values[e],o=this.updateF(r,n);return this.compareF(r,o)?0:(i.values[e]=o,1)},reconfigure:(i,n)=>{let r=i.facet(ln),o=n.facet(ln),l;return(l=r.find(a=>a.field==this))&&l!=o.find(a=>a.field==this)?(i.values[e]=l.create(i),1):n.config.address[this.id]!=null?(i.values[e]=n.field(this),0):(i.values[e]=this.create(i),1)}}}init(t){return[this,ln.of({field:this,create:t})]}get extension(){return this}}const Re={lowest:4,low:3,default:2,high:1,highest:0};function wi(s){return t=>new eh(t,s)}const Ye={highest:wi(Re.highest),high:wi(Re.high),default:wi(Re.default),low:wi(Re.low),lowest:wi(Re.lowest)};class eh{constructor(t,e){this.inner=t,this.prec=e}}class rs{of(t){return new Zs(this,t)}reconfigure(t){return rs.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class Zs{constructor(t,e){this.compartment=t,this.inner=e}}class $n{constructor(t,e,i,n,r,o){for(this.base=t,this.compartments=e,this.dynamicSlots=i,this.address=n,this.staticValues=r,this.facets=o,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(t){let e=this.address[t.id];return e==null?t.default:this.staticValues[e>>1]}static resolve(t,e,i){let n=[],r=Object.create(null),o=new Map;for(let u of cu(t,e,o))u instanceof Tt?n.push(u):(r[u.facet.id]||(r[u.facet.id]=[])).push(u);let l=Object.create(null),a=[],h=[];for(let u of n)l[u.id]=h.length<<1,h.push(d=>u.slot(d));let c=i?.config.facets;for(let u in r){let d=r[u],p=d[0].facet,m=c&&c[u]||[];if(d.every(g=>g.type==0))if(l[p.id]=a.length<<1|1,Xr(m,d))a.push(i.facet(p));else{let g=p.combine(d.map(y=>y.value));a.push(i&&p.compare(g,i.facet(p))?i.facet(p):g)}else{for(let g of d)g.type==0?(l[g.id]=a.length<<1|1,a.push(g.value)):(l[g.id]=h.length<<1,h.push(y=>g.dynamicSlot(y)));l[p.id]=h.length<<1,h.push(g=>hu(g,p,d))}}let f=h.map(u=>u(l));return new $n(t,o,f,l,a,r)}}function cu(s,t,e){let i=[[],[],[],[],[]],n=new Map;function r(o,l){let a=n.get(o);if(a!=null){if(a<=l)return;let h=i[a].indexOf(o);h>-1&&i[a].splice(h,1),o instanceof Zs&&e.delete(o.compartment)}if(n.set(o,l),Array.isArray(o))for(let h of o)r(h,l);else if(o instanceof Zs){if(e.has(o.compartment))throw new RangeError("Duplicate use of compartment in extensions");let h=t.get(o.compartment)||o.inner;e.set(o.compartment,h),r(h,l)}else if(o instanceof eh)r(o.inner,o.prec);else if(o instanceof Tt)i[l].push(o),o.provides&&r(o.provides,l);else if(o instanceof En)i[l].push(o),o.facet.extensions&&r(o.facet.extensions,Re.default);else{let h=o.extension;if(!h)throw new Error(`Unrecognized extension value in extension set (${o}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);r(h,l)}}return r(s,Re.default),i.reduce((o,l)=>o.concat(l))}function Ei(s,t){if(t&1)return 2;let e=t>>1,i=s.status[e];if(i==4)throw new Error("Cyclic dependency between fields and/or facets");if(i&2)return i;s.status[e]=4;let n=s.computeSlot(s,s.config.dynamicSlots[e]);return s.status[e]=2|n}function qn(s,t){return t&1?s.config.staticValues[t>>1]:s.values[t>>1]}const ih=O.define(),tr=O.define({combine:s=>s.some(t=>t),static:!0}),nh=O.define({combine:s=>s.length?s[0]:void 0,static:!0}),sh=O.define(),rh=O.define(),oh=O.define(),lh=O.define({combine:s=>s.length?s[0]:!1});class be{constructor(t,e){this.type=t,this.value=e}static define(){return new fu}}class fu{of(t){return new be(this,t)}}class uu{constructor(t){this.map=t}of(t){return new _(this,t)}}class _{constructor(t,e){this.type=t,this.value=e}map(t){let e=this.type.map(this.value,t);return e===void 0?void 0:e==this.value?this:new _(this.type,e)}is(t){return this.type==t}static define(t={}){return new uu(t.map||(e=>e))}static mapEffects(t,e){if(!t.length)return t;let i=[];for(let n of t){let r=n.map(e);r&&i.push(r)}return i}}_.reconfigure=_.define();_.appendConfig=_.define();class et{constructor(t,e,i,n,r,o){this.startState=t,this.changes=e,this.selection=i,this.effects=n,this.annotations=r,this.scrollIntoView=o,this._doc=null,this._state=null,i&&th(i,e.newLength),r.some(l=>l.type==et.time)||(this.annotations=r.concat(et.time.of(Date.now())))}static create(t,e,i,n,r,o){return new et(t,e,i,n,r,o)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(et.userEvent);return!!(e&&(e==t||e.length>t.length&&e.slice(0,t.length)==t&&e[t.length]=="."))}}et.time=be.define();et.userEvent=be.define();et.addToHistory=be.define();et.remote=be.define();function du(s,t){let e=[];for(let i=0,n=0;;){let r,o;if(i<s.length&&(n==t.length||t[n]>=s[i]))r=s[i++],o=s[i++];else if(n<t.length)r=t[n++],o=t[n++];else return e;!e.length||e[e.length-1]<r?e.push(r,o):e[e.length-1]<o&&(e[e.length-1]=o)}}function ah(s,t,e){var i;let n,r,o;return e?(n=t.changes,r=nt.empty(t.changes.length),o=s.changes.compose(t.changes)):(n=t.changes.map(s.changes),r=s.changes.mapDesc(t.changes,!0),o=s.changes.compose(n)),{changes:o,selection:t.selection?t.selection.map(r):(i=s.selection)===null||i===void 0?void 0:i.map(n),effects:_.mapEffects(s.effects,n).concat(_.mapEffects(t.effects,r)),annotations:s.annotations.length?s.annotations.concat(t.annotations):t.annotations,scrollIntoView:s.scrollIntoView||t.scrollIntoView}}function er(s,t,e){let i=t.selection,n=ni(t.annotations);return t.userEvent&&(n=n.concat(et.userEvent.of(t.userEvent))),{changes:t.changes instanceof nt?t.changes:nt.of(t.changes||[],e,s.facet(nh)),selection:i&&(i instanceof v?i:v.single(i.anchor,i.head)),effects:ni(t.effects),annotations:n,scrollIntoView:!!t.scrollIntoView}}function hh(s,t,e){let i=er(s,t.length?t[0]:{},s.doc.length);t.length&&t[0].filter===!1&&(e=!1);for(let r=1;r<t.length;r++){t[r].filter===!1&&(e=!1);let o=!!t[r].sequential;i=ah(i,er(s,t[r],o?i.changes.newLength:s.doc.length),o)}let n=et.create(s,i.changes,i.selection,i.effects,i.annotations,i.scrollIntoView);return mu(e?pu(n):n)}function pu(s){let t=s.startState,e=!0;for(let n of t.facet(sh)){let r=n(s);if(r===!1){e=!1;break}Array.isArray(r)&&(e=e===!0?r:du(e,r))}if(e!==!0){let n,r;if(e===!1)r=s.changes.invertedDesc,n=nt.empty(t.doc.length);else{let o=s.changes.filter(e);n=o.changes,r=o.filtered.mapDesc(o.changes).invertedDesc}s=et.create(t,n,s.selection&&s.selection.map(r),_.mapEffects(s.effects,r),s.annotations,s.scrollIntoView)}let i=t.facet(rh);for(let n=i.length-1;n>=0;n--){let r=i[n](s);r instanceof et?s=r:Array.isArray(r)&&r.length==1&&r[0]instanceof et?s=r[0]:s=hh(t,ni(r),!1)}return s}function mu(s){let t=s.startState,e=t.facet(oh),i=s;for(let n=e.length-1;n>=0;n--){let r=e[n](s);r&&Object.keys(r).length&&(i=ah(i,er(t,r,s.changes.newLength),!0))}return i==s?s:et.create(t,s.changes,s.selection,i.effects,i.annotations,i.scrollIntoView)}const gu=[];function ni(s){return s==null?gu:Array.isArray(s)?s:[s]}var Nt=function(s){return s[s.Word=0]="Word",s[s.Space=1]="Space",s[s.Other=2]="Other",s}(Nt||(Nt={}));const bu=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let ir;try{ir=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch{}function yu(s){if(ir)return ir.test(s);for(let t=0;t<s.length;t++){let e=s[t];if(/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||bu.test(e)))return!0}return!1}function wu(s){return t=>{if(!/\S/.test(t))return Nt.Space;if(yu(t))return Nt.Word;for(let e=0;e<s.length;e++)if(t.indexOf(s[e])>-1)return Nt.Word;return Nt.Other}}class V{constructor(t,e,i,n,r,o){this.config=t,this.doc=e,this.selection=i,this.values=n,this.status=t.statusTemplate.slice(),this.computeSlot=r,o&&(o._state=this);for(let l=0;l<this.config.dynamicSlots.length;l++)Ei(this,l<<1);this.computeSlot=null}field(t,e=!0){let i=this.config.address[t.id];if(i==null){if(e)throw new RangeError("Field is not present in this state");return}return Ei(this,i),qn(this,i)}update(...t){return hh(this,t,!0)}applyTransaction(t){let e=this.config,{base:i,compartments:n}=e;for(let l of t.effects)l.is(rs.reconfigure)?(e&&(n=new Map,e.compartments.forEach((a,h)=>n.set(h,a)),e=null),n.set(l.value.compartment,l.value.extension)):l.is(_.reconfigure)?(e=null,i=l.value):l.is(_.appendConfig)&&(e=null,i=ni(i).concat(l.value));let r;e?r=t.startState.values.slice():(e=$n.resolve(i,n,this),r=new V(e,this.doc,this.selection,e.dynamicSlots.map(()=>null),(a,h)=>h.reconfigure(a,this),null).values);let o=t.startState.facet(tr)?t.newSelection:t.newSelection.asSingle();new V(e,t.newDoc,o,r,(l,a)=>a.update(l,t),t)}replaceSelection(t){return typeof t=="string"&&(t=this.toText(t)),this.changeByRange(e=>({changes:{from:e.from,to:e.to,insert:t},range:v.cursor(e.from+t.length)}))}changeByRange(t){let e=this.selection,i=t(e.ranges[0]),n=this.changes(i.changes),r=[i.range],o=ni(i.effects);for(let l=1;l<e.ranges.length;l++){let a=t(e.ranges[l]),h=this.changes(a.changes),c=h.map(n);for(let u=0;u<l;u++)r[u]=r[u].map(c);let f=n.mapDesc(h,!0);r.push(a.range.map(f)),n=n.compose(c),o=_.mapEffects(o,c).concat(_.mapEffects(ni(a.effects),f))}return{changes:n,selection:v.create(r,e.mainIndex),effects:o}}changes(t=[]){return t instanceof nt?t:nt.of(t,this.doc.length,this.facet(V.lineSeparator))}toText(t){return F.of(t.split(this.facet(V.lineSeparator)||Ys))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return e==null?t.default:(Ei(this,e),qn(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let i in t){let n=t[i];n instanceof Tt&&this.config.address[n.id]!=null&&(e[i]=n.spec.toJSON(this.field(t[i]),this))}return e}static fromJSON(t,e={},i){if(!t||typeof t.doc!="string")throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i){for(let r in i)if(Object.prototype.hasOwnProperty.call(t,r)){let o=i[r],l=t[r];n.push(o.init(a=>o.spec.fromJSON(l,a)))}}return V.create({doc:t.doc,selection:v.fromJSON(t.selection),extensions:e.extensions?n.concat([e.extensions]):n})}static create(t={}){let e=$n.resolve(t.extensions||[],new Map),i=t.doc instanceof F?t.doc:F.of((t.doc||"").split(e.staticFacet(V.lineSeparator)||Ys)),n=t.selection?t.selection instanceof v?t.selection:v.single(t.selection.anchor,t.selection.head):v.single(0);return th(n,i.length),e.staticFacet(tr)||(n=n.asSingle()),new V(e,i,n,e.dynamicSlots.map(()=>null),(r,o)=>o.create(r),null)}get tabSize(){return this.facet(V.tabSize)}get lineBreak(){return this.facet(V.lineSeparator)||`
`}get readOnly(){return this.facet(lh)}phrase(t,...e){for(let i of this.facet(V.phrases))if(Object.prototype.hasOwnProperty.call(i,t)){t=i[t];break}return e.length&&(t=t.replace(/\$(\$|\d*)/g,(i,n)=>{if(n=="$")return"$";let r=+(n||1);return!r||r>e.length?i:e[r-1]})),t}languageDataAt(t,e,i=-1){let n=[];for(let r of this.facet(ih))for(let o of r(this,e,i))Object.prototype.hasOwnProperty.call(o,t)&&n.push(o[t]);return n}charCategorizer(t){return wu(this.languageDataAt("wordChars",t).join(""))}wordAt(t){let{text:e,from:i,length:n}=this.doc.lineAt(t),r=this.charCategorizer(t),o=t-i,l=t-i;for(;o>0;){let a=yt(e,o,!1);if(r(e.slice(a,o))!=Nt.Word)break;o=a}for(;l<n;){let a=yt(e,l);if(r(e.slice(l,a))!=Nt.Word)break;l=a}return o==l?null:v.range(o+i,l+i)}}V.allowMultipleSelections=tr;V.tabSize=O.define({combine:s=>s.length?s[0]:4});V.lineSeparator=nh;V.readOnly=lh;V.phrases=O.define({compare(s,t){let e=Object.keys(s),i=Object.keys(t);return e.length==i.length&&e.every(n=>s[n]==t[n])}});V.languageData=ih;V.changeFilter=sh;V.transactionFilter=rh;V.transactionExtender=oh;rs.reconfigure=_.define();function Je(s,t,e={}){let i={};for(let n of s)for(let r of Object.keys(n)){let o=n[r],l=i[r];if(l===void 0)i[r]=o;else if(!(l===o||o===void 0))if(Object.hasOwnProperty.call(e,r))i[r]=e[r](l,o);else throw new Error("Config merge conflict for field "+r)}for(let n in t)i[n]===void 0&&(i[n]=t[n]);return i}class Ve{eq(t){return this==t}range(t,e=t){return nr.create(t,e,this)}}Ve.prototype.startSide=Ve.prototype.endSide=0;Ve.prototype.point=!1;Ve.prototype.mapMode=ct.TrackDel;let nr=class ch{constructor(t,e,i){this.from=t,this.to=e,this.value=i}static create(t,e,i){return new ch(t,e,i)}};function sr(s,t){return s.from-t.from||s.value.startSide-t.value.startSide}class Qr{constructor(t,e,i,n){this.from=t,this.to=e,this.value=i,this.maxPoint=n}get length(){return this.to[this.to.length-1]}findIndex(t,e,i,n=0){let r=i?this.to:this.from;for(let o=n,l=r.length;;){if(o==l)return o;let a=o+l>>1,h=r[a]-t||(i?this.value[a].endSide:this.value[a].startSide)-e;if(a==o)return h>=0?o:l;h>=0?l=a:o=a+1}}between(t,e,i,n){for(let r=this.findIndex(e,-1e9,!0),o=this.findIndex(i,1e9,!1,r);r<o;r++)if(n(this.from[r]+t,this.to[r]+t,this.value[r])===!1)return!1}map(t,e){let i=[],n=[],r=[],o=-1,l=-1;for(let a=0;a<this.value.length;a++){let h=this.value[a],c=this.from[a]+t,f=this.to[a]+t,u,d;if(c==f){let p=e.mapPos(c,h.startSide,h.mapMode);if(p==null||(u=d=p,h.startSide!=h.endSide&&(d=e.mapPos(c,h.endSide),d<u)))continue}else if(u=e.mapPos(c,h.startSide),d=e.mapPos(f,h.endSide),u>d||u==d&&h.startSide>0&&h.endSide<=0)continue;(d-u||h.endSide-h.startSide)<0||(o<0&&(o=u),h.point&&(l=Math.max(l,d-u)),i.push(h),n.push(u-o),r.push(d-o))}return{mapped:i.length?new Qr(n,r,i,l):null,pos:o}}}class z{constructor(t,e,i,n){this.chunkPos=t,this.chunk=e,this.nextLayer=i,this.maxPoint=n}static create(t,e,i,n){return new z(t,e,i,n)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:i=!1,filterFrom:n=0,filterTo:r=this.length}=t,o=t.filter;if(e.length==0&&!o)return this;if(i&&(e=e.slice().sort(sr)),this.isEmpty)return e.length?z.of(e):this;let l=new fh(this,null,-1).goto(0),a=0,h=[],c=new Ce;for(;l.value||a<e.length;)if(a<e.length&&(l.from-e[a].from||l.startSide-e[a].value.startSide)>=0){let f=e[a++];c.addInner(f.from,f.to,f.value)||h.push(f)}else l.rangeIndex==1&&l.chunkIndex<this.chunk.length&&(a==e.length||this.chunkEnd(l.chunkIndex)<e[a].from)&&(!o||n>this.chunkEnd(l.chunkIndex)||r<this.chunkPos[l.chunkIndex])&&c.addChunk(this.chunkPos[l.chunkIndex],this.chunk[l.chunkIndex])?l.nextChunk():((!o||n>l.to||r<l.from||o(l.from,l.to,l.value))&&(c.addInner(l.from,l.to,l.value)||h.push(nr.create(l.from,l.to,l.value))),l.next());return c.finishInner(this.nextLayer.isEmpty&&!h.length?z.empty:this.nextLayer.update({add:h,filter:o,filterFrom:n,filterTo:r}))}map(t){if(t.empty||this.isEmpty)return this;let e=[],i=[],n=-1;for(let o=0;o<this.chunk.length;o++){let l=this.chunkPos[o],a=this.chunk[o],h=t.touchesRange(l,l+a.length);if(h===!1)n=Math.max(n,a.maxPoint),e.push(a),i.push(t.mapPos(l));else if(h===!0){let{mapped:c,pos:f}=a.map(l,t);c&&(n=Math.max(n,c.maxPoint),e.push(c),i.push(f))}}let r=this.nextLayer.map(t);return e.length==0?r:new z(i,e,r||z.empty,n)}between(t,e,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let r=this.chunkPos[n],o=this.chunk[n];if(e>=r&&t<=r+o.length&&o.between(r,t-r,e-r,i)===!1)return}this.nextLayer.between(t,e,i)}}iter(t=0){return Fi.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return Fi.from(t).goto(e)}static compare(t,e,i,n,r=-1){let o=t.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),l=e.filter(f=>f.maxPoint>0||!f.isEmpty&&f.maxPoint>=r),a=Wo(o,l,i),h=new xi(o,a,r),c=new xi(l,a,r);i.iterGaps((f,u,d)=>zo(h,f,c,u,d,n)),i.empty&&i.length==0&&zo(h,0,c,0,0,n)}static eq(t,e,i=0,n){n==null&&(n=999999999);let r=t.filter(c=>!c.isEmpty&&e.indexOf(c)<0),o=e.filter(c=>!c.isEmpty&&t.indexOf(c)<0);if(r.length!=o.length)return!1;if(!r.length)return!0;let l=Wo(r,o),a=new xi(r,l,0).goto(i),h=new xi(o,l,0).goto(i);for(;;){if(a.to!=h.to||!rr(a.active,h.active)||a.point&&(!h.point||!a.point.eq(h.point)))return!1;if(a.to>n)return!0;a.next(),h.next()}}static spans(t,e,i,n,r=-1){let o=new xi(t,null,r).goto(e),l=e,a=o.openStart;for(;;){let h=Math.min(o.to,i);if(o.point){let c=o.activeForPoint(o.to),f=o.pointFrom<e?c.length+1:o.point.startSide<0?c.length:Math.min(c.length,a);n.point(l,h,o.point,c,f,o.pointRank),a=Math.min(o.openEnd(h),c.length)}else h>l&&(n.span(l,h,o.active,a),a=o.openEnd(h));if(o.to>i)return a+(o.point&&o.to>i?1:0);l=o.to,o.next()}}static of(t,e=!1){let i=new Ce;for(let n of t instanceof nr?[t]:e?xu(t):t)i.add(n.from,n.to,n.value);return i.finish()}static join(t){if(!t.length)return z.empty;let e=t[t.length-1];for(let i=t.length-2;i>=0;i--)for(let n=t[i];n!=z.empty;n=n.nextLayer)e=new z(n.chunkPos,n.chunk,e,Math.max(n.maxPoint,e.maxPoint));return e}}z.empty=new z([],[],null,-1);function xu(s){if(s.length>1)for(let t=s[0],e=1;e<s.length;e++){let i=s[e];if(sr(t,i)>0)return s.slice().sort(sr);t=i}return s}z.empty.nextLayer=z.empty;class Ce{finishChunk(t){this.chunks.push(new Qr(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(t,e,i){this.addInner(t,e,i)||(this.nextLayer||(this.nextLayer=new Ce)).add(t,e,i)}addInner(t,e,i){let n=t-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(t-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return n<0?!1:(this.from.length==250&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=i,this.lastFrom=t,this.lastTo=e,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),!0)}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let i=e.value.length-1;return this.last=e.value[i],this.lastFrom=e.from[i]+t,this.lastTo=e.to[i]+t,!0}finish(){return this.finishInner(z.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),this.chunks.length==0)return t;let e=z.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function Wo(s,t,e){let i=new Map;for(let r of s)for(let o=0;o<r.chunk.length;o++)r.chunk[o].maxPoint<=0&&i.set(r.chunk[o],r.chunkPos[o]);let n=new Set;for(let r of t)for(let o=0;o<r.chunk.length;o++){let l=i.get(r.chunk[o]);l!=null&&(e?e.mapPos(l):l)==r.chunkPos[o]&&!e?.touchesRange(l,l+r.chunk[o].length)&&n.add(r.chunk[o])}return n}class fh{constructor(t,e,i,n=0){this.layer=t,this.skip=e,this.minPoint=i,this.rank=n}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,i){for(;this.chunkIndex<this.layer.chunk.length;){let n=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(n)||this.layer.chunkEnd(this.chunkIndex)<t||n.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n)}this.next()}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0)}next(){for(;;)if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}else{let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],i=t+e.from[this.rangeIndex];if(this.from=i,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class Fi{constructor(t){this.heap=t}static from(t,e=null,i=-1){let n=[];for(let r=0;r<t.length;r++)for(let o=t[r];!o.isEmpty;o=o.nextLayer)o.maxPoint>=i&&n.push(new fh(o,e,i,r));return n.length==1?n[0]:new Fi(n)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let i of this.heap)i.goto(t,e);for(let i=this.heap.length>>1;i>=0;i--)ys(this.heap,i);return this.next(),this}forward(t,e){for(let i of this.heap)i.forward(t,e);for(let i=this.heap.length>>1;i>=0;i--)ys(this.heap,i);(this.to-t||this.value.endSide-e)<0&&this.next()}next(){if(this.heap.length==0)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),ys(this.heap,0)}}}function ys(s,t){for(let e=s[t];;){let i=(t<<1)+1;if(i>=s.length)break;let n=s[i];if(i+1<s.length&&n.compare(s[i+1])>=0&&(n=s[i+1],i++),e.compare(n)<0)break;s[i]=e,s[t]=n,t=i}}class xi{constructor(t,e,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Fi.from(t,e,i)}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e)}removeActive(t){an(this.active,t),an(this.activeTo,t),an(this.activeRank,t),this.minActive=$o(this.active,this.activeTo)}addActive(t){let e=0,{value:i,to:n,rank:r}=this.cursor;for(;e<this.activeRank.length&&(r-this.activeRank[e]||n-this.activeTo[e])>0;)e++;hn(this.active,e,i),hn(this.activeTo,e,n),hn(this.activeRank,e,r),t&&hn(t,e,this.cursor.from),this.minActive=$o(this.active,this.activeTo)}next(){let t=this.to,e=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let n=this.minActive;if(n>-1&&(this.activeTo[n]-this.cursor.from||this.active[n].endSide-this.cursor.startSide)<0){if(this.activeTo[n]>t){this.to=this.activeTo[n],this.endSide=this.active[n].endSide;break}this.removeActive(n),i&&an(i,n)}else if(this.cursor.value)if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}else{let r=this.cursor.value;if(!r.point)this.addActive(i),this.cursor.next();else if(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{this.point=r,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=r.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}}else{this.to=this.endSide=1e9;break}}if(i){this.openStart=0;for(let n=i.length-1;n>=0&&i[n]<t;n--)this.openStart++}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>t||this.activeTo[i]==t&&this.active[i].endSide>=this.point.endSide)&&e.push(this.active[i]);return e.reverse()}openEnd(t){let e=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>t;i--)e++;return e}}function zo(s,t,e,i,n,r){s.goto(t),e.goto(i);let o=i+n,l=i,a=i-t;for(;;){let h=s.to+a-e.to,c=h||s.endSide-e.endSide,f=c<0?s.to+a:e.to,u=Math.min(f,o);if(s.point||e.point?s.point&&e.point&&(s.point==e.point||s.point.eq(e.point))&&rr(s.activeForPoint(s.to),e.activeForPoint(e.to))||r.comparePoint(l,u,s.point,e.point):u>l&&!rr(s.active,e.active)&&r.compareRange(l,u,s.active,e.active),f>o)break;(h||s.openEnd!=e.openEnd)&&r.boundChange&&r.boundChange(f),l=f,c<=0&&s.next(),c>=0&&e.next()}}function rr(s,t){if(s.length!=t.length)return!1;for(let e=0;e<s.length;e++)if(s[e]!=t[e]&&!s[e].eq(t[e]))return!1;return!0}function an(s,t){for(let e=t,i=s.length-1;e<i;e++)s[e]=s[e+1];s.pop()}function hn(s,t,e){for(let i=s.length-1;i>=t;i--)s[i+1]=s[i];s[t]=e}function $o(s,t){let e=-1,i=1e9;for(let n=0;n<t.length;n++)(t[n]-i||s[n].endSide-s[e].endSide)<0&&(e=n,i=t[n]);return e}function mi(s,t,e=s.length){let i=0;for(let n=0;n<e&&n<s.length;)s.charCodeAt(n)==9?(i+=t-i%t,n++):(i++,n=yt(s,n));return i}function or(s,t,e,i){for(let n=0,r=0;;){if(r>=t)return n;if(n==s.length)break;r+=s.charCodeAt(n)==9?e-r%e:1,n=yt(s,n)}return i===!0?-1:s.length}const lr="ͼ",qo=typeof Symbol>"u"?"__"+lr:Symbol.for(lr),ar=typeof Symbol>"u"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet"),jo=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{};class Ae{constructor(t,e){this.rules=[];let{finish:i}=e||{};function n(o){return/^@/.test(o)?[o]:o.split(/,\s*/)}function r(o,l,a,h){let c=[],f=/^@(\w+)\b/.exec(o[0]),u=f&&f[1]=="keyframes";if(f&&l==null)return a.push(o[0]+";");for(let d in l){let p=l[d];if(/&/.test(d))r(d.split(/,\s*/).map(m=>o.map(g=>m.replace(/&/,g))).reduce((m,g)=>m.concat(g)),p,a);else if(p&&typeof p=="object"){if(!f)throw new RangeError("The value of a property ("+d+") should be a primitive value.");r(n(d),p,c,u)}else p!=null&&c.push(d.replace(/_.*/,"").replace(/[A-Z]/g,m=>"-"+m.toLowerCase())+": "+p+";")}(c.length||u)&&a.push((i&&!f&&!h?o.map(i):o).join(", ")+" {"+c.join(" ")+"}")}for(let o in t)r(n(o),t[o],this.rules)}getRules(){return this.rules.join(`
`)}static newName(){let t=jo[qo]||1;return jo[qo]=t+1,lr+t.toString(36)}static mount(t,e,i){let n=t[ar],r=i&&i.nonce;n?r&&n.setNonce(r):n=new vu(t,r),n.mount(Array.isArray(e)?e:[e])}}let Ko=new Map;class vu{constructor(t,e){let i=t.ownerDocument||t,n=i.defaultView;if(!t.head&&t.adoptedStyleSheets&&n.CSSStyleSheet){let r=Ko.get(i);if(r)return t.adoptedStyleSheets=[r.sheet,...t.adoptedStyleSheets],t[ar]=r;this.sheet=new n.CSSStyleSheet,t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets],Ko.set(i,this)}else{this.styleTag=i.createElement("style"),e&&this.styleTag.setAttribute("nonce",e);let r=t.head||t;r.insertBefore(this.styleTag,r.firstChild)}this.modules=[],t[ar]=this}mount(t){let e=this.sheet,i=0,n=0;for(let r=0;r<t.length;r++){let o=t[r],l=this.modules.indexOf(o);if(l<n&&l>-1&&(this.modules.splice(l,1),n--,l=-1),l==-1){if(this.modules.splice(n++,0,o),e)for(let a=0;a<o.rules.length;a++)e.insertRule(o.rules[a],i++)}else{for(;n<l;)i+=this.modules[n++].rules.length;i+=o.rules.length,n++}}if(!e){let r="";for(let o=0;o<this.modules.length;o++)r+=this.modules[o].getRules()+`
`;this.styleTag.textContent=r}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}var Me={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Hi={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},ku=typeof navigator<"u"&&/Mac/.test(navigator.platform),Su=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var ht=0;ht<10;ht++)Me[48+ht]=Me[96+ht]=String(ht);for(var ht=1;ht<=24;ht++)Me[ht+111]="F"+ht;for(var ht=65;ht<=90;ht++)Me[ht]=String.fromCharCode(ht+32),Hi[ht]=String.fromCharCode(ht);for(var ws in Me)Hi.hasOwnProperty(ws)||(Hi[ws]=Me[ws]);function Cu(s){var t=ku&&s.metaKey&&s.shiftKey&&!s.ctrlKey&&!s.altKey||Su&&s.shiftKey&&s.key&&s.key.length==1||s.key=="Unidentified",e=!t&&s.key||(s.shiftKey?Hi:Me)[s.keyCode]||s.key||"Unidentified";return e=="Esc"&&(e="Escape"),e=="Del"&&(e="Delete"),e=="Left"&&(e="ArrowLeft"),e=="Up"&&(e="ArrowUp"),e=="Right"&&(e="ArrowRight"),e=="Down"&&(e="ArrowDown"),e}function Vi(s){let t;return s.nodeType==11?t=s.getSelection?s:s.ownerDocument:t=s,t.getSelection()}function hr(s,t){return t?s==t||s.contains(t.nodeType!=1?t.parentNode:t):!1}function Ln(s,t){if(!t.anchorNode)return!1;try{return hr(s,t.anchorNode)}catch{return!1}}function ai(s){return s.nodeType==3?ze(s,0,s.nodeValue.length).getClientRects():s.nodeType==1?s.getClientRects():[]}function Li(s,t,e,i){return e?Uo(s,t,e,i,-1)||Uo(s,t,e,i,1):!1}function We(s){for(var t=0;;t++)if(s=s.previousSibling,!s)return t}function jn(s){return s.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(s.nodeName)}function Uo(s,t,e,i,n){for(;;){if(s==e&&t==i)return!0;if(t==(n<0?0:ae(s))){if(s.nodeName=="DIV")return!1;let r=s.parentNode;if(!r||r.nodeType!=1)return!1;t=We(s)+(n<0?0:1),s=r}else if(s.nodeType==1){if(s=s.childNodes[t+(n<0?-1:0)],s.nodeType==1&&s.contentEditable=="false")return!1;t=n<0?ae(s):0}else return!1}}function ae(s){return s.nodeType==3?s.nodeValue.length:s.childNodes.length}function Qi(s,t){let e=t?s.left:s.right;return{left:e,right:e,top:s.top,bottom:s.bottom}}function Au(s){let t=s.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:s.innerWidth,top:0,bottom:s.innerHeight}}function uh(s,t){let e=t.width/s.offsetWidth,i=t.height/s.offsetHeight;return(e>.995&&e<1.005||!isFinite(e)||Math.abs(t.width-s.offsetWidth)<1)&&(e=1),(i>.995&&i<1.005||!isFinite(i)||Math.abs(t.height-s.offsetHeight)<1)&&(i=1),{scaleX:e,scaleY:i}}function Mu(s,t,e,i,n,r,o,l){let a=s.ownerDocument,h=a.defaultView||window;for(let c=s,f=!1;c&&!f;)if(c.nodeType==1){let u,d=c==a.body,p=1,m=1;if(d)u=Au(h);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(f=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let w=c.getBoundingClientRect();({scaleX:p,scaleY:m}=uh(c,w)),u={left:w.left,right:w.left+c.clientWidth*p,top:w.top,bottom:w.top+c.clientHeight*m}}let g=0,y=0;if(n=="nearest")t.top<u.top?(y=t.top-(u.top+o),e>0&&t.bottom>u.bottom+y&&(y=t.bottom-u.bottom+o)):t.bottom>u.bottom&&(y=t.bottom-u.bottom+o,e<0&&t.top-y<u.top&&(y=t.top-(u.top+o)));else{let w=t.bottom-t.top,S=u.bottom-u.top;y=(n=="center"&&w<=S?t.top+w/2-S/2:n=="start"||n=="center"&&e<0?t.top-o:t.bottom-S+o)-u.top}if(i=="nearest"?t.left<u.left?(g=t.left-(u.left+r),e>0&&t.right>u.right+g&&(g=t.right-u.right+r)):t.right>u.right&&(g=t.right-u.right+r,e<0&&t.left<u.left+g&&(g=t.left-(u.left+r))):g=(i=="center"?t.left+(t.right-t.left)/2-(u.right-u.left)/2:i=="start"==l?t.left-r:t.right-(u.right-u.left)+r)-u.left,g||y)if(d)h.scrollBy(g,y);else{let w=0,S=0;if(y){let k=c.scrollTop;c.scrollTop+=y/m,S=(c.scrollTop-k)*m}if(g){let k=c.scrollLeft;c.scrollLeft+=g/p,w=(c.scrollLeft-k)*p}t={left:t.left-w,top:t.top-S,right:t.right-w,bottom:t.bottom-S},w&&Math.abs(w-g)<1&&(i="nearest"),S&&Math.abs(S-y)<1&&(n="nearest")}if(d)break;(t.top<u.top||t.bottom>u.bottom||t.left<u.left||t.right>u.right)&&(t={left:Math.max(t.left,u.left),right:Math.min(t.right,u.right),top:Math.max(t.top,u.top),bottom:Math.min(t.bottom,u.bottom)}),c=c.assignedSlot||c.parentNode}else if(c.nodeType==11)c=c.host;else break}function Du(s){let t=s.ownerDocument,e,i;for(let n=s.parentNode;n&&!(n==t.body||e&&i);)if(n.nodeType==1)!i&&n.scrollHeight>n.clientHeight&&(i=n),!e&&n.scrollWidth>n.clientWidth&&(e=n),n=n.assignedSlot||n.parentNode;else if(n.nodeType==11)n=n.host;else break;return{x:e,y:i}}class Tu{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?ae(e):0),i,Math.min(t.focusOffset,i?ae(i):0))}set(t,e,i,n){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=n}}let Qe=null;function dh(s){if(s.setActive)return s.setActive();if(Qe)return s.focus(Qe);let t=[];for(let e=s;e&&(t.push(e,e.scrollTop,e.scrollLeft),e!=e.ownerDocument);e=e.parentNode);if(s.focus(Qe==null?{get preventScroll(){return Qe={preventScroll:!0},!0}}:void 0),!Qe){Qe=!1;for(let e=0;e<t.length;){let i=t[e++],n=t[e++],r=t[e++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=r&&(i.scrollLeft=r)}}}let Go;function ze(s,t,e=t){let i=Go||(Go=document.createRange());return i.setEnd(s,e),i.setStart(s,t),i}function si(s,t,e,i){let n={key:t,code:t,keyCode:e,which:e,cancelable:!0};i&&({altKey:n.altKey,ctrlKey:n.ctrlKey,shiftKey:n.shiftKey,metaKey:n.metaKey}=i);let r=new KeyboardEvent("keydown",n);r.synthetic=!0,s.dispatchEvent(r);let o=new KeyboardEvent("keyup",n);return o.synthetic=!0,s.dispatchEvent(o),r.defaultPrevented||o.defaultPrevented}function Ou(s){for(;s;){if(s&&(s.nodeType==9||s.nodeType==11&&s.host))return s;s=s.assignedSlot||s.parentNode}return null}function ph(s){for(;s.attributes.length;)s.removeAttributeNode(s.attributes[0])}function Bu(s,t){let e=t.focusNode,i=t.focusOffset;if(!e||t.anchorNode!=e||t.anchorOffset!=i)return!1;for(i=Math.min(i,ae(e));;)if(i){if(e.nodeType!=1)return!1;let n=e.childNodes[i-1];n.contentEditable=="false"?i--:(e=n,i=ae(e))}else{if(e==s)return!0;i=We(e),e=e.parentNode}}function mh(s){return s.scrollTop>Math.max(1,s.scrollHeight-s.clientHeight-4)}function gh(s,t){for(let e=s,i=t;;){if(e.nodeType==3&&i>0)return{node:e,offset:i};if(e.nodeType==1&&i>0){if(e.contentEditable=="false")return null;e=e.childNodes[i-1],i=ae(e)}else if(e.parentNode&&!jn(e))i=We(e),e=e.parentNode;else return null}}function bh(s,t){for(let e=s,i=t;;){if(e.nodeType==3&&i<e.nodeValue.length)return{node:e,offset:i};if(e.nodeType==1&&i<e.childNodes.length){if(e.contentEditable=="false")return null;e=e.childNodes[i],i=0}else if(e.parentNode&&!jn(e))i=We(e)+1,e=e.parentNode;else return null}}class pt{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new pt(t.parentNode,We(t),e)}static after(t,e){return new pt(t.parentNode,We(t)+1,e)}}const Zr=[];class K{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(this.flags&2){let i=this.dom,n=null,r;for(let o of this.children){if(o.flags&7){if(!o.dom&&(r=n?n.nextSibling:i.firstChild)){let l=K.get(r);(!l||!l.parent&&l.canReuseDOM(o))&&o.reuseDOM(r)}o.sync(t,e),o.flags&=-8}if(r=n?n.nextSibling:i.firstChild,e&&!e.written&&e.node==i&&r!=o.dom&&(e.written=!0),o.dom.parentNode==i)for(;r&&r!=o.dom;)r=Yo(r);else i.insertBefore(o.dom,r);n=o.dom}for(r=n?n.nextSibling:i.firstChild,r&&e&&e.node==i&&(e.written=!0);r;)r=Yo(r)}else if(this.flags&1)for(let i of this.children)i.flags&7&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let n=ae(t)==0?0:e==0?-1:1;for(;;){let r=t.parentNode;if(r==this.dom)break;n==0&&r.firstChild!=r.lastChild&&(t==r.firstChild?n=-1:n=1),t=r}n<0?i=t:i=t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!K.get(i);)i=i.nextSibling;if(!i)return this.length;for(let n=0,r=0;;n++){let o=this.children[n];if(o.dom==i)return r;r+=o.length+o.breakAfter}}domBoundsAround(t,e,i=0){let n=-1,r=-1,o=-1,l=-1;for(let a=0,h=i,c=i;a<this.children.length;a++){let f=this.children[a],u=h+f.length;if(h<t&&u>e)return f.domBoundsAround(t,e,h);if(u>=t&&n==-1&&(n=a,r=h),h>e&&f.dom.parentNode==this.dom){o=a,l=c;break}c=u,h=u+f.breakAfter}return{from:r,to:l<0?i+this.length:l,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:o<this.children.length&&o>=0?this.children[o].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),e.flags&1)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,this.flags&7&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=Zr){this.markDirty();for(let n=t;n<e;n++){let r=this.children[n];r.parent==this&&i.indexOf(r)<0&&r.destroy()}i.length<250?this.children.splice(t,e-t,...i):this.children=[].concat(this.children.slice(0,t),i,this.children.slice(e));for(let n=0;n<i.length;n++)i[n].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new yh(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+(t=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,n,r,o){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!((this.flags|t.flags)&8)}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}K.prototype.breakAfter=0;function Yo(s){let t=s.nextSibling;return s.parentNode.removeChild(s),t}class yh{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||this.i==0||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function wh(s,t,e,i,n,r,o,l,a){let{children:h}=s,c=h.length?h[t]:null,f=r.length?r[r.length-1]:null,u=f?f.breakAfter:o;if(!(t==i&&c&&!o&&!u&&r.length<2&&c.merge(e,n,r.length?f:null,e==0,l,a))){if(i<h.length){let d=h[i];d&&(n<d.length||d.breakAfter&&f?.breakAfter)?(t==i&&(d=d.split(n),n=0),!u&&f&&d.merge(0,n,f,!0,0,a)?r[r.length-1]=d:((n||d.children.length&&!d.children[0].length)&&d.merge(0,n,null,!1,0,a),r.push(d))):d?.breakAfter&&(f?f.breakAfter=1:o=1),i++}for(c&&(c.breakAfter=o,e>0&&(!o&&r.length&&c.merge(e,c.length,r[0],!1,l,0)?c.breakAfter=r.shift().breakAfter:(e<c.length||c.children.length&&c.children[c.children.length-1].length==0)&&c.merge(e,c.length,null,!1,l,0),t++));t<i&&r.length;)if(h[i-1].become(r[r.length-1]))i--,r.pop(),a=r.length?0:l;else if(h[t].become(r[0]))t++,r.shift(),l=r.length?0:a;else break;!r.length&&t&&i<h.length&&!h[t-1].breakAfter&&h[i].merge(0,0,h[t-1],!1,l,a)&&t--,(t<i||r.length)&&s.replaceChildren(t,i,r)}}function xh(s,t,e,i,n,r){let o=s.childCursor(),{i:l,off:a}=o.findPos(e,1),{i:h,off:c}=o.findPos(t,-1),f=t-e;for(let u of i)f+=u.length;s.length+=f,wh(s,h,c,l,a,i,0,n,r)}let Ct=typeof navigator<"u"?navigator:{userAgent:"",vendor:"",platform:""},cr=typeof document<"u"?document:{documentElement:{style:{}}};const fr=/Edge\/(\d+)/.exec(Ct.userAgent),vh=/MSIE \d/.test(Ct.userAgent),ur=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Ct.userAgent),os=!!(vh||ur||fr),Jo=!os&&/gecko\/(\d+)/i.test(Ct.userAgent),xs=!os&&/Chrome\/(\d+)/.exec(Ct.userAgent),Xo="webkitFontSmoothing"in cr.documentElement.style,kh=!os&&/Apple Computer/.test(Ct.vendor),Qo=kh&&(/Mobile\/\w+/.test(Ct.userAgent)||Ct.maxTouchPoints>2);var T={mac:Qo||/Mac/.test(Ct.platform),windows:/Win/.test(Ct.platform),linux:/Linux|X11/.test(Ct.platform),ie:os,ie_version:vh?cr.documentMode||6:ur?+ur[1]:fr?+fr[1]:0,gecko:Jo,gecko_version:Jo?+(/Firefox\/(\d+)/.exec(Ct.userAgent)||[0,0])[1]:0,chrome:!!xs,chrome_version:xs?+xs[1]:0,ios:Qo,android:/Android\b/.test(Ct.userAgent),webkit:Xo,safari:kh,webkit_version:Xo?+(/\bAppleWebKit\/(\d+)/.exec(Ct.userAgent)||[0,0])[1]:0,tabSize:cr.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const Pu=256;class Gt extends K{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){t.nodeType==3&&this.createDOM(t)}merge(t,e,i){return this.flags&8||i&&(!(i instanceof Gt)||this.length-(e-t)+i.length>Pu||i.flags&8)?!1:(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new Gt(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=this.flags&8,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new pt(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return Eu(this.dom,t,e)}}class ge extends K{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let n of e)n.setParent(this)}setAttrs(t){if(ph(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!((this.flags|t.flags)&8)}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?this.flags&4&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,n,r,o){return i&&(!(i instanceof ge&&i.mark.eq(this.mark))||t&&r<=0||e<this.length&&o<=0)?!1:(xh(this,t,e,i?i.children.slice():[],r-1,o-1),this.markDirty(),!0)}split(t){let e=[],i=0,n=-1,r=0;for(let l of this.children){let a=i+l.length;a>t&&e.push(i<t?l.split(t-i):l),n<0&&i>=t&&(n=r),i=a,r++}let o=this.length-t;return this.length=t,n>-1&&(this.children.length=n,this.markDirty()),new ge(this.mark,e,o)}domAtPos(t){return Sh(this,t)}coordsAt(t,e){return Ah(this,t,e)}}function Eu(s,t,e){let i=s.nodeValue.length;t>i&&(t=i);let n=t,r=t,o=0;t==0&&e<0||t==i&&e>=0?T.chrome||T.gecko||(t?(n--,o=1):r<i&&(r++,o=-1)):e<0?n--:r<i&&r++;let l=ze(s,n,r).getClientRects();if(!l.length)return null;let a=l[(o?o<0:e>=0)?0:l.length-1];return T.safari&&!o&&a.width==0&&(a=Array.prototype.find.call(l,h=>h.width)||a),o?Qi(a,o<0):a||null}class ke extends K{static create(t,e,i){return new ke(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=ke.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,e,i,n,r,o){return i&&(!(i instanceof ke)||!this.widget.compare(i.widget)||t>0&&r<=0||e<this.length&&o<=0)?!1:(this.length=t+(i?i.length:0)+(this.length-e),!0)}become(t){return t instanceof ke&&t.side==this.side&&this.widget.constructor==t.widget.constructor?(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0):!1}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(this.length==0)return F.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,n=this.posAtStart;return i?i.slice(n,n+this.length):F.empty}domAtPos(t){return(this.length?t==0:this.side>0)?pt.before(this.dom):pt.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let n=this.dom.getClientRects(),r=null;if(!n.length)return null;let o=this.side?this.side<0:t>0;for(let l=o?n.length-1:0;r=n[l],!(t>0?l==0:l==n.length-1||r.top<r.bottom);l+=o?-1:1);return Qi(r,!o)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class hi extends K{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof hi&&t.side==this.side}split(){return new hi(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?pt.before(this.dom):pt.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return F.empty}get isHidden(){return!0}}Gt.prototype.children=ke.prototype.children=hi.prototype.children=Zr;function Sh(s,t){let e=s.dom,{children:i}=s,n=0;for(let r=0;n<i.length;n++){let o=i[n],l=r+o.length;if(!(l==r&&o.getSide()<=0)){if(t>r&&t<l&&o.dom.parentNode==e)return o.domAtPos(t-r);if(t<=r)break;r=l}}for(let r=n;r>0;r--){let o=i[r-1];if(o.dom.parentNode==e)return o.domAtPos(o.length)}for(let r=n;r<i.length;r++){let o=i[r];if(o.dom.parentNode==e)return o.domAtPos(0)}return new pt(e,0)}function Ch(s,t,e){let i,{children:n}=s;e>0&&t instanceof ge&&n.length&&(i=n[n.length-1])instanceof ge&&i.mark.eq(t.mark)?Ch(i,t.children[0],e-1):(n.push(t),t.setParent(s)),s.length+=t.length}function Ah(s,t,e){let i=null,n=-1,r=null,o=-1;function l(h,c){for(let f=0,u=0;f<h.children.length&&u<=c;f++){let d=h.children[f],p=u+d.length;p>=c&&(d.children.length?l(d,c-u):(!r||r.isHidden&&e>0)&&(p>c||u==p&&d.getSide()>0)?(r=d,o=c-u):(u<c||u==p&&d.getSide()<0&&!d.isHidden)&&(i=d,n=c-u)),u=p}}l(s,t);let a=(e<0?i:r)||i||r;return a?a.coordsAt(Math.max(0,a==i?n:o),e):Lu(s)}function Lu(s){let t=s.dom.lastChild;if(!t)return s.dom.getBoundingClientRect();let e=ai(t);return e[e.length-1]||null}function dr(s,t){for(let e in s)e=="class"&&t.class?t.class+=" "+s.class:e=="style"&&t.style?t.style+=";"+s.style:t[e]=s[e];return t}const Zo=Object.create(null);function Kn(s,t,e){if(s==t)return!0;s||(s=Zo),t||(t=Zo);let i=Object.keys(s),n=Object.keys(t);if(i.length-(e&&i.indexOf(e)>-1?1:0)!=n.length-(e&&n.indexOf(e)>-1?1:0))return!1;for(let r of i)if(r!=e&&(n.indexOf(r)==-1||s[r]!==t[r]))return!1;return!0}function pr(s,t,e){let i=!1;if(t)for(let n in t)e&&n in e||(i=!0,n=="style"?s.style.cssText="":s.removeAttribute(n));if(e)for(let n in e)t&&t[n]==e[n]||(i=!0,n=="style"?s.style.cssText=e[n]:s.setAttribute(n,e[n]));return i}function Ru(s){let t=Object.create(null);for(let e=0;e<s.attributes.length;e++){let i=s.attributes[e];t[i.name]=i.value}return t}class he{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var wt=function(s){return s[s.Text=0]="Text",s[s.WidgetBefore=1]="WidgetBefore",s[s.WidgetAfter=2]="WidgetAfter",s[s.WidgetRange=3]="WidgetRange",s}(wt||(wt={}));class I extends Ve{constructor(t,e,i,n){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=n}get heightRelevant(){return!1}static mark(t){return new Zi(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new De(t,e,e,i,t.widget||null,!1)}static replace(t){let e=!!t.block,i,n;if(t.isBlockGap)i=-5e8,n=4e8;else{let{start:r,end:o}=Mh(t,e);i=(r?e?-3e8:-1:5e8)-1,n=(o?e?2e8:1:-6e8)+1}return new De(t,i,n,e,t.widget||null,!0)}static line(t){return new tn(t)}static set(t,e=!1){return z.of(t,e)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:!1}}I.none=z.empty;class Zi extends I{constructor(t){let{start:e,end:i}=Mh(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof Zi&&this.tagName==t.tagName&&(this.class||((e=this.attrs)===null||e===void 0?void 0:e.class))==(t.class||((i=t.attrs)===null||i===void 0?void 0:i.class))&&Kn(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}Zi.prototype.point=!1;class tn extends I{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof tn&&this.spec.class==t.spec.class&&Kn(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}tn.prototype.mapMode=ct.TrackBefore;tn.prototype.point=!0;class De extends I{constructor(t,e,i,n,r,o){super(e,i,r,t),this.block=n,this.isReplace=o,this.mapMode=n?e<=0?ct.TrackBefore:ct.TrackAfter:ct.TrackDel}get type(){return this.startSide!=this.endSide?wt.WidgetRange:this.startSide<=0?wt.WidgetBefore:wt.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof De&&_u(this.widget,t.widget)&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}De.prototype.point=!0;function Mh(s,t=!1){let{inclusiveStart:e,inclusiveEnd:i}=s;return e==null&&(e=s.inclusive),i==null&&(i=s.inclusive),{start:e??t,end:i??t}}function _u(s,t){return s==t||!!(s&&t&&s.compare(t))}function Rn(s,t,e,i=0){let n=e.length-1;n>=0&&e[n]+i>=s?e[n]=Math.max(e[n],t):e.push(s,t)}class tt extends K{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,n,r,o){if(i){if(!(i instanceof tt))return!1;this.dom||i.transferDOM(this)}return n&&this.setDeco(i?i.attrs:null),xh(this,t,e,i?i.children.slice():[],r,o),!0}split(t){let e=new tt;if(e.breakAfter=this.breakAfter,this.length==0)return e;let{i,off:n}=this.childPos(t);n&&(e.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let r=i;r<this.children.length;r++)e.append(this.children[r],0);for(;i>0&&this.children[i-1].length==0;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=this.prevAttrs===void 0?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){Kn(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){Ch(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=dr(e,this.attrs||{})),i&&(this.attrs=dr({class:i},this.attrs||{}))}domAtPos(t){return Sh(this,t)}reuseDOM(t){t.nodeName=="DIV"&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?this.flags&4&&(ph(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),this.prevAttrs!==void 0&&(pr(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let n=this.dom.lastChild;for(;n&&K.get(n)instanceof ge;)n=n.lastChild;if(!n||!this.length||n.nodeName!="BR"&&((i=K.get(n))===null||i===void 0?void 0:i.isEditable)==!1&&(!T.ios||!this.children.some(r=>r instanceof Gt))){let r=document.createElement("BR");r.cmIgnore=!0,this.dom.appendChild(r)}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let t=0,e;for(let i of this.children){if(!(i instanceof Gt)||/[^ -~]/.test(i.text))return null;let n=ai(i.dom);if(n.length!=1)return null;t+=n[0].width,e=n[0].height}return t?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:t/this.length,textHeight:e}:null}coordsAt(t,e){let i=Ah(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:n}=this.parent.view.viewState,r=i.bottom-i.top;if(Math.abs(r-n.lineHeight)<2&&n.textHeight<r){let o=(r-n.textHeight)/2;return{top:i.top+o,bottom:i.bottom-o,left:i.left,right:i.left}}}return i}become(t){return t instanceof tt&&this.children.length==0&&t.children.length==0&&Kn(this.attrs,t.attrs)&&this.breakAfter==t.breakAfter}covers(){return!0}static find(t,e){for(let i=0,n=0;i<t.children.length;i++){let r=t.children[i],o=n+r.length;if(o>=e){if(r instanceof tt)return r;if(o>e)break}n=o+r.breakAfter}return null}}class pe extends K{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,n,r,o){return i&&(!(i instanceof pe)||!this.widget.compare(i.widget)||t>0&&r<=0||e<this.length&&o<=0)?!1:(this.length=t+(i?i.length:0)+(this.length-e),!0)}domAtPos(t){return t==0?pt.before(this.dom):pt.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new pe(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return Zr}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):F.empty}domBoundsAround(){return null}become(t){return t instanceof pe&&t.widget.constructor==this.widget.constructor?(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0):!1}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);return i||(this.widget instanceof mr?null:Qi(this.dom.getBoundingClientRect(),this.length?t==0:e<=0))}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e==i?!1:t<0?e<0:i>0}}class mr extends he{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}class Ri{constructor(t,e,i,n){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(this.content.length==0)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof pe&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new tt),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(cn(new hi(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,!this.posCovered()&&!(t&&this.content.length&&this.content[this.content.length-1]instanceof pe)&&this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:r,lineBreak:o,done:l}=this.cursor.next(this.skip);if(this.skip=0,l)throw new Error("Ran out of text content when drawing inline views");if(o){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}else this.text=r,this.textOff=0}let n=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(cn(new Gt(this.text.slice(this.textOff,this.textOff+n)),e),i),this.atCursorPos=!0,this.textOff+=n,t-=n,i=0}}span(t,e,i,n){this.buildText(e-t,i,n),this.pos=e,this.openStart<0&&(this.openStart=n)}point(t,e,i,n,r,o){if(this.disallowBlockEffectsFor[o]&&i instanceof De){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let l=e-t;if(i instanceof De)if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new pe(i.widget||ci.block,l,i));else{let a=ke.create(i.widget||ci.inline,l,l?0:i.startSide),h=this.atCursorPos&&!a.isEditable&&r<=n.length&&(t<e||i.startSide>0),c=!a.isEditable&&(t<e||r>n.length||i.startSide<=0),f=this.getLine();this.pendingBuffer==2&&!h&&!a.isEditable&&(this.pendingBuffer=0),this.flushBuffer(n),h&&(f.append(cn(new hi(1),n),r),r=n.length+Math.max(0,r-n.length)),f.append(cn(a,n),r),this.atCursorPos=c,this.pendingBuffer=c?t<e||r>n.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=n.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);l&&(this.textOff+l<=this.text.length?this.textOff+=l:(this.skip+=l-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=r)}static build(t,e,i,n,r){let o=new Ri(t,e,i,r);return o.openEnd=z.spans(n,e,i,o),o.openStart<0&&(o.openStart=o.openEnd),o.finish(o.openEnd),o}}function cn(s,t){for(let e of t)s=new ge(e,[s],s.length);return s}class ci extends he{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}ci.inline=new ci("span");ci.block=new ci("div");var Y=function(s){return s[s.LTR=0]="LTR",s[s.RTL=1]="RTL",s}(Y||(Y={}));const $e=Y.LTR,to=Y.RTL;function Dh(s){let t=[];for(let e=0;e<s.length;e++)t.push(1<<+s[e]);return t}const Iu=Dh("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),Nu=Dh("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),gr=Object.create(null),Xt=[];for(let s of["()","[]","{}"]){let t=s.charCodeAt(0),e=s.charCodeAt(1);gr[t]=e,gr[e]=-t}function Th(s){return s<=247?Iu[s]:1424<=s&&s<=1524?2:1536<=s&&s<=1785?Nu[s-1536]:1774<=s&&s<=2220?4:8192<=s&&s<=8204?256:64336<=s&&s<=65023?4:1}const Fu=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class Se{get dir(){return this.level%2?to:$e}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,n){let r=-1;for(let o=0;o<t.length;o++){let l=t[o];if(l.from<=e&&l.to>=e){if(l.level==i)return o;(r<0||(n!=0?n<0?l.from<e:l.to>e:t[r].level>l.level))&&(r=o)}}if(r<0)throw new RangeError("Index out of range");return r}}function Oh(s,t){if(s.length!=t.length)return!1;for(let e=0;e<s.length;e++){let i=s[e],n=t[e];if(i.from!=n.from||i.to!=n.to||i.direction!=n.direction||!Oh(i.inner,n.inner))return!1}return!0}const j=[];function Hu(s,t,e,i,n){for(let r=0;r<=i.length;r++){let o=r?i[r-1].to:t,l=r<i.length?i[r].from:e,a=r?256:n;for(let h=o,c=a,f=a;h<l;h++){let u=Th(s.charCodeAt(h));u==512?u=c:u==8&&f==4&&(u=16),j[h]=u==4?2:u,u&7&&(f=u),c=u}for(let h=o,c=a,f=a;h<l;h++){let u=j[h];if(u==128)h<l-1&&c==j[h+1]&&c&24?u=j[h]=c:j[h]=256;else if(u==64){let d=h+1;for(;d<l&&j[d]==64;)d++;let p=h&&c==8||d<e&&j[d]==8?f==1?1:8:256;for(let m=h;m<d;m++)j[m]=p;h=d-1}else u==8&&f==1&&(j[h]=1);c=u,u&7&&(f=u)}}}function Vu(s,t,e,i,n){let r=n==1?2:1;for(let o=0,l=0,a=0;o<=i.length;o++){let h=o?i[o-1].to:t,c=o<i.length?i[o].from:e;for(let f=h,u,d,p;f<c;f++)if(d=gr[u=s.charCodeAt(f)])if(d<0){for(let m=l-3;m>=0;m-=3)if(Xt[m+1]==-d){let g=Xt[m+2],y=g&2?n:g&4?g&1?r:n:0;y&&(j[f]=j[Xt[m]]=y),l=m;break}}else{if(Xt.length==189)break;Xt[l++]=f,Xt[l++]=u,Xt[l++]=a}else if((p=j[f])==2||p==1){let m=p==n;a=m?0:1;for(let g=l-3;g>=0;g-=3){let y=Xt[g+2];if(y&2)break;if(m)Xt[g+2]|=2;else{if(y&4)break;Xt[g+2]|=4}}}}}function Wu(s,t,e,i){for(let n=0,r=i;n<=e.length;n++){let o=n?e[n-1].to:s,l=n<e.length?e[n].from:t;for(let a=o;a<l;){let h=j[a];if(h==256){let c=a+1;for(;;)if(c==l){if(n==e.length)break;c=e[n++].to,l=n<e.length?e[n].from:t}else if(j[c]==256)c++;else break;let f=r==1,u=(c<t?j[c]:i)==1,d=f==u?f?1:2:i;for(let p=c,m=n,g=m?e[m-1].to:s;p>a;)p==g&&(p=e[--m].from,g=m?e[m-1].to:s),j[--p]=d;a=c}else r=h,a++}}}function br(s,t,e,i,n,r,o){let l=i%2?2:1;if(i%2==n%2)for(let a=t,h=0;a<e;){let c=!0,f=!1;if(h==r.length||a<r[h].from){let m=j[a];m!=l&&(c=!1,f=m==16)}let u=!c&&l==1?[]:null,d=c?i:i+1,p=a;t:for(;;)if(h<r.length&&p==r[h].from){if(f)break t;let m=r[h];if(!c)for(let g=m.to,y=h+1;;){if(g==e)break t;if(y<r.length&&r[y].from==g)g=r[y++].to;else{if(j[g]==l)break t;break}}if(h++,u)u.push(m);else{m.from>a&&o.push(new Se(a,m.from,d));let g=m.direction==$e!=!(d%2);yr(s,g?i+1:i,n,m.inner,m.from,m.to,o),a=m.to}p=m.to}else{if(p==e||(c?j[p]!=l:j[p]==l))break;p++}u?br(s,a,p,i+1,n,u,o):a<p&&o.push(new Se(a,p,d)),a=p}else for(let a=e,h=r.length;a>t;){let c=!0,f=!1;if(!h||a>r[h-1].to){let m=j[a-1];m!=l&&(c=!1,f=m==16)}let u=!c&&l==1?[]:null,d=c?i:i+1,p=a;t:for(;;)if(h&&p==r[h-1].to){if(f)break t;let m=r[--h];if(!c)for(let g=m.from,y=h;;){if(g==t)break t;if(y&&r[y-1].to==g)g=r[--y].from;else{if(j[g-1]==l)break t;break}}if(u)u.push(m);else{m.to<a&&o.push(new Se(m.to,a,d));let g=m.direction==$e!=!(d%2);yr(s,g?i+1:i,n,m.inner,m.from,m.to,o),a=m.from}p=m.from}else{if(p==t||(c?j[p-1]!=l:j[p-1]==l))break;p--}u?br(s,p,a,i+1,n,u,o):p<a&&o.push(new Se(p,a,d)),a=p}}function yr(s,t,e,i,n,r,o){let l=t%2?2:1;Hu(s,n,r,i,l),Vu(s,n,r,i,l),Wu(n,r,i,l),br(s,n,r,t,e,i,o)}function zu(s,t,e){if(!s)return[new Se(0,0,t==to?1:0)];if(t==$e&&!e.length&&!Fu.test(s))return Bh(s.length);if(e.length)for(;s.length>j.length;)j[j.length]=256;let i=[],n=t==$e?0:1;return yr(s,n,n,e,0,s.length,i),i}function Bh(s){return[new Se(0,s,0)]}let Ph="";function $u(s,t,e,i,n){var r;let o=i.head-s.from,l=Se.find(t,o,(r=i.bidiLevel)!==null&&r!==void 0?r:-1,i.assoc),a=t[l],h=a.side(n,e);if(o==h){let u=l+=n?1:-1;if(u<0||u>=t.length)return null;a=t[l=u],o=a.side(!n,e),h=a.side(n,e)}let c=yt(s.text,o,a.forward(n,e));(c<a.from||c>a.to)&&(c=h),Ph=s.text.slice(Math.min(o,c),Math.max(o,c));let f=l==(n?t.length-1:0)?null:t[l+(n?1:-1)];return f&&c==h&&f.level+(n?0:1)<a.level?v.cursor(f.side(!n,e)+s.from,f.forward(n,e)?1:-1,f.level):v.cursor(c+s.from,a.forward(n,e)?-1:1,a.level)}function qu(s,t,e){for(let i=t;i<e;i++){let n=Th(s.charCodeAt(i));if(n==1)return $e;if(n==2||n==4)return to}return $e}const Eh=O.define(),Lh=O.define(),Rh=O.define(),_h=O.define(),wr=O.define(),Ih=O.define(),Nh=O.define(),eo=O.define(),io=O.define(),Fh=O.define({combine:s=>s.some(t=>t)}),Hh=O.define({combine:s=>s.some(t=>t)}),Vh=O.define();class ri{constructor(t,e="nearest",i="nearest",n=5,r=5,o=!1){this.range=t,this.y=e,this.x=i,this.yMargin=n,this.xMargin=r,this.isSnapshot=o}map(t){return t.empty?this:new ri(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new ri(v.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const fn=_.define({map:(s,t)=>s.map(t)}),Wh=_.define();function Mt(s,t,e){let i=s.facet(_h);i.length?i[0](t):window.onerror?window.onerror(String(t),e,void 0,void 0,t):e?console.error(e+":",t):console.error(t)}const de=O.define({combine:s=>s.length?s[0]:!0});let ju=0;const Di=O.define();class ft{constructor(t,e,i,n,r){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=n,this.extension=r(this)}static define(t,e){const{eventHandlers:i,eventObservers:n,provide:r,decorations:o}=e||{};return new ft(ju++,t,i,n,l=>{let a=[Di.of(l)];return o&&a.push(Wi.of(h=>{let c=h.plugin(l);return c?o(c):I.none})),r&&a.push(r(l)),a})}static fromClass(t,e){return ft.define(i=>new t(i),e)}}class vs{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}update(t){if(this.value){if(this.mustUpdate){let e=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(e)}catch(i){if(Mt(e.state,i,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch{}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(t)}catch(e){Mt(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(!((e=this.value)===null||e===void 0)&&e.destroy)try{this.value.destroy()}catch(i){Mt(t.state,i,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const zh=O.define(),no=O.define(),Wi=O.define(),$h=O.define(),so=O.define(),qh=O.define();function tl(s,t){let e=s.state.facet(qh);if(!e.length)return e;let i=e.map(r=>r instanceof Function?r(s):r),n=[];return z.spans(i,t.from,t.to,{point(){},span(r,o,l,a){let h=r-t.from,c=o-t.from,f=n;for(let u=l.length-1;u>=0;u--,a--){let d=l[u].spec.bidiIsolate,p;if(d==null&&(d=qu(t.text,h,c)),a>0&&f.length&&(p=f[f.length-1]).to==h&&p.direction==d)p.to=c,f=p.inner;else{let m={from:h,to:c,direction:d,inner:[]};f.push(m),f=m.inner}}}}),n}const jh=O.define();function ro(s){let t=0,e=0,i=0,n=0;for(let r of s.state.facet(jh)){let o=r(s);o&&(o.left!=null&&(t=Math.max(t,o.left)),o.right!=null&&(e=Math.max(e,o.right)),o.top!=null&&(i=Math.max(i,o.top)),o.bottom!=null&&(n=Math.max(n,o.bottom)))}return{left:t,right:e,top:i,bottom:n}}const Ti=O.define();class zt{constructor(t,e,i,n){this.fromA=t,this.toA=e,this.fromB=i,this.toB=n}join(t){return new zt(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let n=t[e-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(e.length==0)return t;let i=[];for(let n=0,r=0,o=0,l=0;;n++){let a=n==t.length?null:t[n],h=o-l,c=a?a.fromB:1e9;for(;r<e.length&&e[r]<c;){let f=e[r],u=e[r+1],d=Math.max(l,f),p=Math.min(c,u);if(d<=p&&new zt(d+h,p+h,d,p).addToSet(i),u>c)break;r+=2}if(!a)return i;new zt(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),o=a.toA,l=a.toB}}}class Un{constructor(t,e,i){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=nt.empty(this.startState.doc.length);for(let r of i)this.changes=this.changes.compose(r.changes);let n=[];this.changes.iterChangedRanges((r,o,l,a)=>n.push(new zt(r,o,l,a))),this.changedRanges=n}static create(t,e,i){return new Un(t,e,i)}get viewportChanged(){return(this.flags&4)>0}get viewportMoved(){return(this.flags&8)>0}get heightChanged(){return(this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&18)>0}get focusChanged(){return(this.flags&1)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(t=>t.selection)}get empty(){return this.flags==0&&this.transactions.length==0}}class el extends K{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[!1],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.editContextFormatting=I.none,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new tt],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new zt(0,0,0,t.state.doc.length)],0,null)}update(t){var e;let i=t.changedRanges;this.minWidth>0&&i.length&&(i.every(({fromA:h,toA:c})=>c<this.minWidthFrom||h>this.minWidthTo)?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.updateEditContextFormatting(t);let n=-1;this.view.inputState.composing>=0&&!this.view.observer.editContext&&(!((e=this.domChanged)===null||e===void 0)&&e.newSel?n=this.domChanged.newSel.head:!Qu(t.changes,this.hasComposition)&&!t.selectionSet&&(n=t.state.selection.main.head));let r=n>-1?Uu(this.view,t.changes,n):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:h,to:c}=this.hasComposition;i=new zt(h,c,t.changes.mapPos(h,-1),t.changes.mapPos(c,1)).addToSet(i.slice())}this.hasComposition=r?{from:r.range.fromB,to:r.range.toB}:null,(T.ie||T.chrome)&&!r&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let o=this.decorations,l=this.updateDeco(),a=Ju(o,l,t.changes);return i=zt.extendWithRanges(i,a),!(this.flags&7)&&i.length==0?!1:(this.updateInner(i,t.startState.doc.length,r),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:n}=this.view;n.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let o=T.chrome||T.ios?{node:n.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,o),this.flags&=-8,o&&(o.written||n.selectionRange.focusNode!=o.node)&&(this.forceSelection=!0),this.dom.style.height=""}),this.markedForComposition.forEach(o=>o.flags&=-9);let r=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let o of this.children)o instanceof pe&&o.widget instanceof mr&&r.push(o.dom);n.updateGaps(r)}updateChildren(t,e,i){let n=i?i.range.addToSet(t.slice()):t,r=this.childCursor(e);for(let o=n.length-1;;o--){let l=o>=0?n[o]:null;if(!l)break;let{fromA:a,toA:h,fromB:c,toB:f}=l,u,d,p,m;if(i&&i.range.fromB<f&&i.range.toB>c){let k=Ri.build(this.view.state.doc,c,i.range.fromB,this.decorations,this.dynamicDecorationMap),x=Ri.build(this.view.state.doc,i.range.toB,f,this.decorations,this.dynamicDecorationMap);d=k.breakAtStart,p=k.openStart,m=x.openEnd;let C=this.compositionView(i);x.breakAtStart?C.breakAfter=1:x.content.length&&C.merge(C.length,C.length,x.content[0],!1,x.openStart,0)&&(C.breakAfter=x.content[0].breakAfter,x.content.shift()),k.content.length&&C.merge(0,0,k.content[k.content.length-1],!0,0,k.openEnd)&&k.content.pop(),u=k.content.concat(C).concat(x.content)}else({content:u,breakAtStart:d,openStart:p,openEnd:m}=Ri.build(this.view.state.doc,c,f,this.decorations,this.dynamicDecorationMap));let{i:g,off:y}=r.findPos(h,1),{i:w,off:S}=r.findPos(a,-1);wh(this,w,S,g,y,u,d,p,m)}i&&this.fixCompositionDOM(i)}updateEditContextFormatting(t){this.editContextFormatting=this.editContextFormatting.map(t.changes);for(let e of t.transactions)for(let i of e.effects)i.is(Wh)&&(this.editContextFormatting=i.value)}compositionView(t){let e=new Gt(t.text.nodeValue);e.flags|=8;for(let{deco:n}of t.marks)e=new ge(n,[e],e.length);let i=new tt;return i.append(e,0),i}fixCompositionDOM(t){let e=(r,o)=>{o.flags|=8|(o.children.some(a=>a.flags&7)?1:0),this.markedForComposition.add(o);let l=K.get(r);l&&l!=o&&(l.dom=null),o.setDOM(r)},i=this.childPos(t.range.fromB,1),n=this.children[i.i];e(t.line,n);for(let r=t.marks.length-1;r>=-1;r--)i=n.childPos(i.off,1),n=n.children[i.i],e(r>=0?t.marks[r].node:t.text,n)}updateSelection(t=!1,e=!1){(t||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange();let i=this.view.root.activeElement,n=i==this.dom,r=!n&&!(this.view.state.facet(de)||this.dom.tabIndex>-1)&&Ln(this.dom,this.view.observer.selectionRange)&&!(i&&this.dom.contains(i));if(!(n||e||r))return;let o=this.forceSelection;this.forceSelection=!1;let l=this.view.state.selection.main,a=this.moveToLine(this.domAtPos(l.anchor)),h=l.empty?a:this.moveToLine(this.domAtPos(l.head));if(T.gecko&&l.empty&&!this.hasComposition&&Ku(a)){let f=document.createTextNode("");this.view.observer.ignore(()=>a.node.insertBefore(f,a.node.childNodes[a.offset]||null)),a=h=new pt(f,0),o=!0}let c=this.view.observer.selectionRange;(o||!c.focusNode||(!Li(a.node,a.offset,c.anchorNode,c.anchorOffset)||!Li(h.node,h.offset,c.focusNode,c.focusOffset))&&!this.suppressWidgetCursorChange(c,l))&&(this.view.observer.ignore(()=>{T.android&&T.chrome&&this.dom.contains(c.focusNode)&&Xu(c.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let f=Vi(this.view.root);if(f)if(l.empty){if(T.gecko){let u=Gu(a.node,a.offset);if(u&&u!=3){let d=(u==1?gh:bh)(a.node,a.offset);d&&(a=new pt(d.node,d.offset))}}f.collapse(a.node,a.offset),l.bidiLevel!=null&&f.caretBidiLevel!==void 0&&(f.caretBidiLevel=l.bidiLevel)}else if(f.extend){f.collapse(a.node,a.offset);try{f.extend(h.node,h.offset)}catch{}}else{let u=document.createRange();l.anchor>l.head&&([a,h]=[h,a]),u.setEnd(h.node,h.offset),u.setStart(a.node,a.offset),f.removeAllRanges(),f.addRange(u)}r&&this.view.root.activeElement==this.dom&&(this.dom.blur(),i&&i.focus())}),this.view.observer.setSelectionRange(a,h)),this.impreciseAnchor=a.precise?null:new pt(c.anchorNode,c.anchorOffset),this.impreciseHead=h.precise?null:new pt(c.focusNode,c.focusOffset)}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&Li(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=Vi(t.root),{anchorNode:n,anchorOffset:r}=t.observer.selectionRange;if(!i||!e.empty||!e.assoc||!i.modify)return;let o=tt.find(this,e.head);if(!o)return;let l=o.posAtStart;if(e.head==l||e.head==l+o.length)return;let a=this.coordsAt(e.head,-1),h=this.coordsAt(e.head,1);if(!a||!h||a.bottom>h.top)return;let c=this.domAtPos(e.head+e.assoc);i.collapse(c.node,c.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let f=t.observer.selectionRange;t.docView.posFromDOM(f.anchorNode,f.anchorOffset)!=e.from&&i.collapse(n,r)}moveToLine(t){let e=this.dom,i;if(t.node!=e)return t;for(let n=t.offset;!i&&n<e.childNodes.length;n++){let r=K.get(e.childNodes[n]);r instanceof tt&&(i=r.domAtPos(0))}for(let n=t.offset-1;!i&&n>=0;n--){let r=K.get(e.childNodes[n]);r instanceof tt&&(i=r.domAtPos(r.length))}return i?new pt(i.node,i.offset,!0):t}nearest(t){for(let e=t;e;){let i=K.get(e);if(i&&i.rootView==this)return i;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let n=this.children[e];if(i<n.length||n instanceof tt)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,n=0;for(let r=this.length,o=this.children.length-1;o>=0;o--){let l=this.children[o],a=r-l.breakAfter,h=a-l.length;if(a<t)break;if(h<=t&&(h<t||l.covers(-1))&&(a>t||l.covers(1))&&(!i||l instanceof tt&&!(i instanceof tt&&e>=0)))i=l,n=h;else if(i&&h==t&&a==t&&l instanceof pe&&Math.abs(e)<2){if(l.deco.startSide<0)break;o&&(i=null)}r=h}return i?i.coordsAt(t-n,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),n=this.children[e];if(!(n instanceof tt))return null;for(;n.children.length;){let{i:l,off:a}=n.childPos(i,1);for(;;l++){if(l==n.children.length)return null;if((n=n.children[l]).length)break}i=a}if(!(n instanceof Gt))return null;let r=yt(n.text,i);if(r==i)return null;let o=ze(n.dom,i,r).getClientRects();for(let l=0;l<o.length;l++){let a=o[l];if(l==o.length-1||a.top<a.bottom&&a.left<a.right)return a}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:n}=t,r=this.view.contentDOM.clientWidth,o=r>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,l=-1,a=this.view.textDirection==Y.LTR;for(let h=0,c=0;c<this.children.length;c++){let f=this.children[c],u=h+f.length;if(u>n)break;if(h>=i){let d=f.dom.getBoundingClientRect();if(e.push(d.height),o){let p=f.dom.lastChild,m=p?ai(p):[];if(m.length){let g=m[m.length-1],y=a?g.right-d.left:d.right-g.left;y>l&&(l=y,this.minWidth=r,this.minWidthFrom=h,this.minWidthTo=u)}}}h=u+f.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return getComputedStyle(this.children[e].dom).direction=="rtl"?Y.RTL:Y.LTR}measureTextSize(){for(let r of this.children)if(r instanceof tt){let o=r.measureTextSize();if(o)return o}let t=document.createElement("div"),e,i,n;return t.className="cm-line",t.style.width="99999px",t.style.position="absolute",t.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(t);let r=ai(t.firstChild)[0];e=t.getBoundingClientRect().height,i=r?r.width/27:7,n=r?r.height:e,t.remove()}),{lineHeight:e,charWidth:i,textHeight:n}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new yh(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,n=0;;n++){let r=n==e.viewports.length?null:e.viewports[n],o=r?r.from-1:this.length;if(o>i){let l=(e.lineBlockAt(o).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(I.replace({widget:new mr(l),block:!0,inclusive:!0,isBlockGap:!0}).range(i,o))}if(!r)break;i=r.to+1}return I.set(t)}updateDeco(){let t=1,e=this.view.state.facet(Wi).map(r=>(this.dynamicDecorationMap[t++]=typeof r=="function")?r(this.view):r),i=!1,n=this.view.state.facet($h).map((r,o)=>{let l=typeof r=="function";return l&&(i=!0),l?r(this.view):r});for(n.length&&(this.dynamicDecorationMap[t++]=i,e.push(z.join(n))),this.decorations=[this.editContextFormatting,...e,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];t<this.decorations.length;)this.dynamicDecorationMap[t++]=!1;return this.decorations}scrollIntoView(t){if(t.isSnapshot){let h=this.view.viewState.lineBlockAt(t.range.head);this.view.scrollDOM.scrollTop=h.top-t.yMargin,this.view.scrollDOM.scrollLeft=t.xMargin;return}for(let h of this.view.state.facet(Vh))try{if(h(this.view,t.range,t))return!0}catch(c){Mt(this.view.state,c,"scroll handler")}let{range:e}=t,i=this.coordsAt(e.head,e.empty?e.assoc:e.head>e.anchor?-1:1),n;if(!i)return;!e.empty&&(n=this.coordsAt(e.anchor,e.anchor>e.head?-1:1))&&(i={left:Math.min(i.left,n.left),top:Math.min(i.top,n.top),right:Math.max(i.right,n.right),bottom:Math.max(i.bottom,n.bottom)});let r=ro(this.view),o={left:i.left-r.left,top:i.top-r.top,right:i.right+r.right,bottom:i.bottom+r.bottom},{offsetWidth:l,offsetHeight:a}=this.view.scrollDOM;Mu(this.view.scrollDOM,o,e.head<e.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,l),-l),Math.max(Math.min(t.yMargin,a),-a),this.view.textDirection==Y.LTR)}}function Ku(s){return s.node.nodeType==1&&s.node.firstChild&&(s.offset==0||s.node.childNodes[s.offset-1].contentEditable=="false")&&(s.offset==s.node.childNodes.length||s.node.childNodes[s.offset].contentEditable=="false")}function Kh(s,t){let e=s.observer.selectionRange;if(!e.focusNode)return null;let i=gh(e.focusNode,e.focusOffset),n=bh(e.focusNode,e.focusOffset),r=i||n;if(n&&i&&n.node!=i.node){let l=K.get(n.node);if(!l||l instanceof Gt&&l.text!=n.node.nodeValue)r=n;else if(s.docView.lastCompositionAfterCursor){let a=K.get(i.node);!a||a instanceof Gt&&a.text!=i.node.nodeValue||(r=n)}}if(s.docView.lastCompositionAfterCursor=r!=i,!r)return null;let o=t-r.offset;return{from:o,to:o+r.node.nodeValue.length,node:r.node}}function Uu(s,t,e){let i=Kh(s,e);if(!i)return null;let{node:n,from:r,to:o}=i,l=n.nodeValue;if(/[\n\r]/.test(l)||s.state.doc.sliceString(i.from,i.to)!=l)return null;let a=t.invertedDesc,h=new zt(a.mapPos(r),a.mapPos(o),r,o),c=[];for(let f=n.parentNode;;f=f.parentNode){let u=K.get(f);if(u instanceof ge)c.push({node:f,deco:u.mark});else{if(u instanceof tt||f.nodeName=="DIV"&&f.parentNode==s.contentDOM)return{range:h,text:n,marks:c,line:f};if(f!=s.contentDOM)c.push({node:f,deco:new Zi({inclusive:!0,attributes:Ru(f),tagName:f.tagName.toLowerCase()})});else return null}}}function Gu(s,t){return s.nodeType!=1?0:(t&&s.childNodes[t-1].contentEditable=="false"?1:0)|(t<s.childNodes.length&&s.childNodes[t].contentEditable=="false"?2:0)}let Yu=class{constructor(){this.changes=[]}compareRange(t,e){Rn(t,e,this.changes)}comparePoint(t,e){Rn(t,e,this.changes)}boundChange(t){Rn(t,t,this.changes)}};function Ju(s,t,e){let i=new Yu;return z.compare(s,t,e,i),i.changes}function Xu(s,t){for(let e=s;e&&e!=t;e=e.assignedSlot||e.parentNode)if(e.nodeType==1&&e.contentEditable=="false")return!0;return!1}function Qu(s,t){let e=!1;return t&&s.iterChangedRanges((i,n)=>{i<t.to&&n>t.from&&(e=!0)}),e}function Zu(s,t,e=1){let i=s.charCategorizer(t),n=s.doc.lineAt(t),r=t-n.from;if(n.length==0)return v.cursor(t);r==0?e=1:r==n.length&&(e=-1);let o=r,l=r;e<0?o=yt(n.text,r,!1):l=yt(n.text,r);let a=i(n.text.slice(o,l));for(;o>0;){let h=yt(n.text,o,!1);if(i(n.text.slice(h,o))!=a)break;o=h}for(;l<n.length;){let h=yt(n.text,l);if(i(n.text.slice(l,h))!=a)break;l=h}return v.range(o+n.from,l+n.from)}function td(s,t){return t.left>s?t.left-s:Math.max(0,s-t.right)}function ed(s,t){return t.top>s?t.top-s:Math.max(0,s-t.bottom)}function ks(s,t){return s.top<t.bottom-1&&s.bottom>t.top+1}function il(s,t){return t<s.top?{top:t,left:s.left,right:s.right,bottom:s.bottom}:s}function nl(s,t){return t>s.bottom?{top:s.top,left:s.left,right:s.right,bottom:t}:s}function xr(s,t,e){let i,n,r,o,l=!1,a,h,c,f;for(let p=s.firstChild;p;p=p.nextSibling){let m=ai(p);for(let g=0;g<m.length;g++){let y=m[g];n&&ks(n,y)&&(y=il(nl(y,n.bottom),n.top));let w=td(t,y),S=ed(e,y);if(w==0&&S==0)return p.nodeType==3?sl(p,t,e):xr(p,t,e);if(!i||o>S||o==S&&r>w){i=p,n=y,r=w,o=S;let k=S?e<y.top?-1:1:w?t<y.left?-1:1:0;l=!k||(k>0?g<m.length-1:g>0)}w==0?e>y.bottom&&(!c||c.bottom<y.bottom)?(a=p,c=y):e<y.top&&(!f||f.top>y.top)&&(h=p,f=y):c&&ks(c,y)?c=nl(c,y.bottom):f&&ks(f,y)&&(f=il(f,y.top))}}if(c&&c.bottom>=e?(i=a,n=c):f&&f.top<=e&&(i=h,n=f),!i)return{node:s,offset:0};let u=Math.max(n.left,Math.min(n.right,t));if(i.nodeType==3)return sl(i,u,e);if(l&&i.contentEditable!="false")return xr(i,u,e);let d=Array.prototype.indexOf.call(s.childNodes,i)+(t>=(n.left+n.right)/2?1:0);return{node:s,offset:d}}function sl(s,t,e){let i=s.nodeValue.length,n=-1,r=1e9,o=0;for(let l=0;l<i;l++){let a=ze(s,l,l+1).getClientRects();for(let h=0;h<a.length;h++){let c=a[h];if(c.top==c.bottom)continue;o||(o=t-c.left);let f=(c.top>e?c.top-e:e-c.bottom)-1;if(c.left-1<=t&&c.right+1>=t&&f<r){let u=t>=(c.left+c.right)/2,d=u;if((T.chrome||T.gecko)&&ze(s,l).getBoundingClientRect().left==c.right&&(d=!u),f<=0)return{node:s,offset:l+(d?1:0)};n=l+(d?1:0),r=f}}}return{node:s,offset:n>-1?n:o>0?s.nodeValue.length:0}}function Uh(s,t,e,i=-1){var n,r;let o=s.contentDOM.getBoundingClientRect(),l=o.top+s.viewState.paddingTop,a,{docHeight:h}=s.viewState,{x:c,y:f}=t,u=f-l;if(u<0)return 0;if(u>h)return s.state.doc.length;for(let k=s.viewState.heightOracle.textHeight/2,x=!1;a=s.elementAtHeight(u),a.type!=wt.Text;)for(;u=i>0?a.bottom+k:a.top-k,!(u>=0&&u<=h);){if(x)return e?null:0;x=!0,i=-i}f=l+u;let d=a.from;if(d<s.viewport.from)return s.viewport.from==0?0:e?null:rl(s,o,a,c,f);if(d>s.viewport.to)return s.viewport.to==s.state.doc.length?s.state.doc.length:e?null:rl(s,o,a,c,f);let p=s.dom.ownerDocument,m=s.root.elementFromPoint?s.root:p,g=m.elementFromPoint(c,f);g&&!s.contentDOM.contains(g)&&(g=null),g||(c=Math.max(o.left+1,Math.min(o.right-1,c)),g=m.elementFromPoint(c,f),g&&!s.contentDOM.contains(g)&&(g=null));let y,w=-1;if(g&&((n=s.docView.nearest(g))===null||n===void 0?void 0:n.isEditable)!=!1){if(p.caretPositionFromPoint){let k=p.caretPositionFromPoint(c,f);k&&({offsetNode:y,offset:w}=k)}else if(p.caretRangeFromPoint){let k=p.caretRangeFromPoint(c,f);k&&({startContainer:y,startOffset:w}=k,(!s.contentDOM.contains(y)||T.safari&&id(y,w,c)||T.chrome&&nd(y,w,c))&&(y=void 0))}y&&(w=Math.min(ae(y),w))}if(!y||!s.docView.dom.contains(y)){let k=tt.find(s.docView,d);if(!k)return u>a.top+a.height/2?a.to:a.from;({node:y,offset:w}=xr(k.dom,c,f))}let S=s.docView.nearest(y);if(!S)return null;if(S.isWidget&&((r=S.dom)===null||r===void 0?void 0:r.nodeType)==1){let k=S.dom.getBoundingClientRect();return t.y<k.top||t.y<=k.bottom&&t.x<=(k.left+k.right)/2?S.posAtStart:S.posAtEnd}else return S.localPosFromDOM(y,w)+S.posAtStart}function rl(s,t,e,i,n){let r=Math.round((i-t.left)*s.defaultCharacterWidth);if(s.lineWrapping&&e.height>s.defaultLineHeight*1.5){let l=s.viewState.heightOracle.textHeight,a=Math.floor((n-e.top-(s.defaultLineHeight-l)*.5)/l);r+=a*s.viewState.heightOracle.lineLength}let o=s.state.sliceDoc(e.from,e.to);return e.from+or(o,r,s.state.tabSize)}function id(s,t,e){let i;if(s.nodeType!=3||t!=(i=s.nodeValue.length))return!1;for(let n=s.nextSibling;n;n=n.nextSibling)if(n.nodeType!=1||n.nodeName!="BR")return!1;return ze(s,i-1,i).getBoundingClientRect().left>e}function nd(s,t,e){if(t!=0)return!1;for(let n=s;;){let r=n.parentNode;if(!r||r.nodeType!=1||r.firstChild!=n)return!1;if(r.classList.contains("cm-line"))break;n=r}let i=s.nodeType==1?s.getBoundingClientRect():ze(s,0,Math.max(s.nodeValue.length,1)).getBoundingClientRect();return e-i.left>5}function vr(s,t){let e=s.lineBlockAt(t);if(Array.isArray(e.type)){for(let i of e.type)if(i.to>t||i.to==t&&(i.to==e.to||i.type==wt.Text))return i}return e}function sd(s,t,e,i){let n=vr(s,t.head),r=!i||n.type!=wt.Text||!(s.lineWrapping||n.widgetLineBreaks)?null:s.coordsAtPos(t.assoc<0&&t.head>n.from?t.head-1:t.head);if(r){let o=s.dom.getBoundingClientRect(),l=s.textDirectionAt(n.from),a=s.posAtCoords({x:e==(l==Y.LTR)?o.right-1:o.left+1,y:(r.top+r.bottom)/2});if(a!=null)return v.cursor(a,e?-1:1)}return v.cursor(e?n.to:n.from,e?-1:1)}function ol(s,t,e,i){let n=s.state.doc.lineAt(t.head),r=s.bidiSpans(n),o=s.textDirectionAt(n.from);for(let l=t,a=null;;){let h=$u(n,r,o,l,e),c=Ph;if(!h){if(n.number==(e?s.state.doc.lines:1))return l;c=`
`,n=s.state.doc.line(n.number+(e?1:-1)),r=s.bidiSpans(n),h=s.visualLineSide(n,!e)}if(a){if(!a(c))return l}else{if(!i)return h;a=i(c)}l=h}}function rd(s,t,e){let i=s.state.charCategorizer(t),n=i(e);return r=>{let o=i(r);return n==Nt.Space&&(n=o),n==o}}function od(s,t,e,i){let n=t.head,r=e?1:-1;if(n==(e?s.state.doc.length:0))return v.cursor(n,t.assoc);let o=t.goalColumn,l,a=s.contentDOM.getBoundingClientRect(),h=s.coordsAtPos(n,t.assoc||-1),c=s.documentTop;if(h)o==null&&(o=h.left-a.left),l=r<0?h.top:h.bottom;else{let d=s.viewState.lineBlockAt(n);o==null&&(o=Math.min(a.right-a.left,s.defaultCharacterWidth*(n-d.from))),l=(r<0?d.top:d.bottom)+c}let f=a.left+o,u=i??s.viewState.heightOracle.textHeight>>1;for(let d=0;;d+=10){let p=l+(u+d)*r,m=Uh(s,{x:f,y:p},!1,r);if(p<a.top||p>a.bottom||(r<0?m<n:m>n)){let g=s.docView.coordsForChar(m),y=!g||p<g.top?-1:1;return v.cursor(m,y,void 0,o)}}}function _n(s,t,e){for(;;){let i=0;for(let n of s)n.between(t-1,t+1,(r,o,l)=>{if(t>r&&t<o){let a=i||e||(t-r<o-t?-1:1);t=a<0?r:o,i=a}});if(!i)return t}}function Ss(s,t,e){let i=_n(s.state.facet(so).map(n=>n(s)),e.from,t.head>e.from?-1:1);return i==e.from?e:v.cursor(i,i<e.from?1:-1)}const Oi="￿";class ld{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(V.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=Oi}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let n=t;;){this.findPointBefore(i,n);let r=this.text.length;this.readNode(n);let o=n.nextSibling;if(o==e)break;let l=K.get(n),a=K.get(o);(l&&a?l.breakAfter:(l?l.breakAfter:jn(n))||jn(o)&&(n.nodeName!="BR"||n.cmIgnore)&&this.text.length>r)&&this.lineBreak(),n=o}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let r=-1,o=1,l;if(this.lineSeparator?(r=e.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(l=n.exec(e))&&(r=l.index,o=l[0].length),this.append(e.slice(i,r<0?e.length:r)),r<0)break;if(this.lineBreak(),o>1)for(let a of this.points)a.node==t&&a.pos>this.text.length&&(a.pos-=o-1);i=r+o}}readNode(t){if(t.cmIgnore)return;let e=K.get(t),i=e&&e.overrideDOMText;if(i!=null){this.findPointInside(t,i.length);for(let n=i.iter();!n.next().done;)n.lineBreak?this.lineBreak():this.append(n.value)}else t.nodeType==3?this.readTextNode(t):t.nodeName=="BR"?t.nextSibling&&this.lineBreak():t.nodeType==1&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(t.nodeType==3?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+(ad(t,i.node,i.offset)?e:0))}}function ad(s,t,e){for(;;){if(!t||e<ae(t))return!1;if(t==s)return!0;e=We(t)+1,t=t.parentNode}}class ll{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class hd{constructor(t,e,i,n){this.typeOver=n,this.bounds=null,this.text="",this.domChanged=e>-1;let{impreciseHead:r,impreciseAnchor:o}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let l=r||o?[]:ud(t),a=new ld(l,t.state);a.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=a.text,this.newSel=dd(l,this.bounds.from)}else{let l=t.observer.selectionRange,a=r&&r.node==l.focusNode&&r.offset==l.focusOffset||!hr(t.contentDOM,l.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(l.focusNode,l.focusOffset),h=o&&o.node==l.anchorNode&&o.offset==l.anchorOffset||!hr(t.contentDOM,l.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(l.anchorNode,l.anchorOffset),c=t.viewport;if((T.ios||T.chrome)&&t.state.selection.main.empty&&a!=h&&(c.from>0||c.to<t.state.doc.length)){let f=Math.min(a,h),u=Math.max(a,h),d=c.from-f,p=c.to-u;(d==0||d==1||f==0)&&(p==0||p==-1||u==t.state.doc.length)&&(a=0,h=t.state.doc.length)}this.newSel=v.single(h,a)}}}function Gh(s,t){let e,{newSel:i}=t,n=s.state.selection.main,r=s.inputState.lastKeyTime>Date.now()-100?s.inputState.lastKeyCode:-1;if(t.bounds){let{from:o,to:l}=t.bounds,a=n.from,h=null;(r===8||T.android&&t.text.length<l-o)&&(a=n.to,h="end");let c=fd(s.state.doc.sliceString(o,l,Oi),t.text,a-o,h);c&&(T.chrome&&r==13&&c.toB==c.from+2&&t.text.slice(c.from,c.toB)==Oi+Oi&&c.toB--,e={from:o+c.from,to:o+c.toA,insert:F.of(t.text.slice(c.from,c.toB).split(Oi))})}else i&&(!s.hasFocus&&s.state.facet(de)||i.main.eq(n))&&(i=null);if(!e&&!i)return!1;if(!e&&t.typeOver&&!n.empty&&i&&i.main.empty?e={from:n.from,to:n.to,insert:s.state.doc.slice(n.from,n.to)}:(T.mac||T.android)&&e&&e.from==e.to&&e.from==n.head-1&&/^\. ?$/.test(e.insert.toString())&&s.contentDOM.getAttribute("autocorrect")=="off"?(i&&e.insert.length==2&&(i=v.single(i.main.anchor-1,i.main.head-1)),e={from:e.from,to:e.to,insert:F.of([e.insert.toString().replace("."," ")])}):e&&e.from>=n.from&&e.to<=n.to&&(e.from!=n.from||e.to!=n.to)&&n.to-n.from-(e.to-e.from)<=4?e={from:n.from,to:n.to,insert:s.state.doc.slice(n.from,e.from).append(e.insert).append(s.state.doc.slice(e.to,n.to))}:T.chrome&&e&&e.from==e.to&&e.from==n.head&&e.insert.toString()==`
 `&&s.lineWrapping&&(i&&(i=v.single(i.main.anchor-1,i.main.head-1)),e={from:n.from,to:n.to,insert:F.of([" "])}),e)return oo(s,e,i,r);if(i&&!i.main.eq(n)){let o=!1,l="select";return s.inputState.lastSelectionTime>Date.now()-50&&(s.inputState.lastSelectionOrigin=="select"&&(o=!0),l=s.inputState.lastSelectionOrigin),s.dispatch({selection:i,scrollIntoView:o,userEvent:l}),!0}else return!1}function oo(s,t,e,i=-1){if(T.ios&&s.inputState.flushIOSKey(t))return!0;let n=s.state.selection.main;if(T.android&&(t.to==n.to&&(t.from==n.from||t.from==n.from-1&&s.state.sliceDoc(t.from,n.from)==" ")&&t.insert.length==1&&t.insert.lines==2&&si(s.contentDOM,"Enter",13)||(t.from==n.from-1&&t.to==n.to&&t.insert.length==0||i==8&&t.insert.length<t.to-t.from&&t.to>n.head)&&si(s.contentDOM,"Backspace",8)||t.from==n.from&&t.to==n.to+1&&t.insert.length==0&&si(s.contentDOM,"Delete",46)))return!0;let r=t.insert.toString();s.inputState.composing>=0&&s.inputState.composing++;let o,l=()=>o||(o=cd(s,t,e));return s.state.facet(Ih).some(a=>a(s,t.from,t.to,r,l))||s.dispatch(l()),!0}function cd(s,t,e){let i,n=s.state,r=n.selection.main;if(t.from>=r.from&&t.to<=r.to&&t.to-t.from>=(r.to-r.from)/3&&(!e||e.main.empty&&e.main.from==t.from+t.insert.length)&&s.inputState.composing<0){let l=r.from<t.from?n.sliceDoc(r.from,t.from):"",a=r.to>t.to?n.sliceDoc(t.to,r.to):"";i=n.replaceSelection(s.state.toText(l+t.insert.sliceString(0,void 0,s.state.lineBreak)+a))}else{let l=n.changes(t),a=e&&e.main.to<=l.newLength?e.main:void 0;if(n.selection.ranges.length>1&&s.inputState.composing>=0&&t.to<=r.to&&t.to>=r.to-10){let h=s.state.sliceDoc(t.from,t.to),c,f=e&&Kh(s,e.main.head);if(f){let p=t.insert.length-(t.to-t.from);c={from:f.from,to:f.to-p}}else c=s.state.doc.lineAt(r.head);let u=r.to-t.to,d=r.to-r.from;i=n.changeByRange(p=>{if(p.from==r.from&&p.to==r.to)return{changes:l,range:a||p.map(l)};let m=p.to-u,g=m-h.length;if(p.to-p.from!=d||s.state.sliceDoc(g,m)!=h||p.to>=c.from&&p.from<=c.to)return{range:p};let y=n.changes({from:g,to:m,insert:t.insert}),w=p.to-r.to;return{changes:y,range:a?v.range(Math.max(0,a.anchor+w),Math.max(0,a.head+w)):p.map(y)}})}else i={changes:l,selection:a&&n.selection.replaceRange(a)}}let o="input.type";return(s.composing||s.inputState.compositionPendingChange&&s.inputState.compositionEndedAt>Date.now()-50)&&(s.inputState.compositionPendingChange=!1,o+=".compose",s.inputState.compositionFirstChange&&(o+=".start",s.inputState.compositionFirstChange=!1)),n.update(i,{userEvent:o,scrollIntoView:!0})}function fd(s,t,e,i){let n=Math.min(s.length,t.length),r=0;for(;r<n&&s.charCodeAt(r)==t.charCodeAt(r);)r++;if(r==n&&s.length==t.length)return null;let o=s.length,l=t.length;for(;o>0&&l>0&&s.charCodeAt(o-1)==t.charCodeAt(l-1);)o--,l--;if(i=="end"){let a=Math.max(0,r-Math.min(o,l));e-=o+a-r}if(o<r&&s.length<t.length){let a=e<=r&&e>=o?r-e:0;r-=a,l=r+(l-o),o=r}else if(l<r){let a=e<=r&&e>=l?r-e:0;r-=a,o=r+(o-l),l=r}return{from:r,toA:o,toB:l}}function ud(s){let t=[];if(s.root.activeElement!=s.contentDOM)return t;let{anchorNode:e,anchorOffset:i,focusNode:n,focusOffset:r}=s.observer.selectionRange;return e&&(t.push(new ll(e,i)),(n!=e||r!=i)&&t.push(new ll(n,r))),t}function dd(s,t){if(s.length==0)return null;let e=s[0].pos,i=s.length==2?s[1].pos:e;return e>-1&&i>-1?v.single(e+t,i+t):null}class pd{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.tabFocusMode=-1,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,T.safari&&t.contentDOM.addEventListener("input",()=>null),T.gecko&&Bd(t.contentDOM.ownerDocument)}handleEvent(t){!kd(this.view,t)||this.ignoreDuringComposition(t)||t.type=="keydown"&&this.keydown(t)||(this.view.updateState!=0?Promise.resolve().then(()=>this.runHandlers(t.type,t)):this.runHandlers(t.type,t))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let n of i.observers)n(this.view,e);for(let n of i.handlers){if(e.defaultPrevented)break;if(n(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=md(t),i=this.handlers,n=this.view.contentDOM;for(let r in e)if(r!="scroll"){let o=!e[r].handlers.length,l=i[r];l&&o!=!l.handlers.length&&(n.removeEventListener(r,this.handleEvent),l=null),l||n.addEventListener(r,this.handleEvent,{passive:o})}for(let r in i)r!="scroll"&&!e[r]&&n.removeEventListener(r,this.handleEvent);this.handlers=e}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),t.keyCode==9&&this.tabFocusMode>-1&&(!this.tabFocusMode||Date.now()<=this.tabFocusMode))return!0;if(this.tabFocusMode>0&&t.keyCode!=27&&Jh.indexOf(t.keyCode)<0&&(this.tabFocusMode=-1),T.android&&T.chrome&&!t.synthetic&&(t.keyCode==13||t.keyCode==8))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let e;return T.ios&&!t.synthetic&&!t.altKey&&!t.metaKey&&((e=Yh.find(i=>i.keyCode==t.keyCode))&&!t.ctrlKey||gd.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(this.pendingIOSKey=e||t,setTimeout(()=>this.flushIOSKey(),250),!0):(t.keyCode!=229&&this.view.observer.forceFlush(),!1)}flushIOSKey(t){let e=this.pendingIOSKey;return!e||e.key=="Enter"&&t&&t.from<t.to&&/^\S+$/.test(t.insert.toString())?!1:(this.pendingIOSKey=void 0,si(this.view.contentDOM,e.key,e.keyCode,e instanceof KeyboardEvent?e:void 0))}ignoreDuringComposition(t){return/^key/.test(t.type)?this.composing>0?!0:T.safari&&!T.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100?(this.compositionPendingKey=!1,!0):!1:!1}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.view.observer.update(t),this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function al(s,t){return(e,i)=>{try{return t.call(s,i,e)}catch(n){Mt(e.state,n)}}}function md(s){let t=Object.create(null);function e(i){return t[i]||(t[i]={observers:[],handlers:[]})}for(let i of s){let n=i.spec;if(n&&n.domEventHandlers)for(let r in n.domEventHandlers){let o=n.domEventHandlers[r];o&&e(r).handlers.push(al(i.value,o))}if(n&&n.domEventObservers)for(let r in n.domEventObservers){let o=n.domEventObservers[r];o&&e(r).observers.push(al(i.value,o))}}for(let i in Yt)e(i).handlers.push(Yt[i]);for(let i in $t)e(i).observers.push($t[i]);return t}const Yh=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],gd="dthko",Jh=[16,17,18,20,91,92,224,225],un=6;function dn(s){return Math.max(0,s)*.7+8}function bd(s,t){return Math.max(Math.abs(s.clientX-t.clientX),Math.abs(s.clientY-t.clientY))}class yd{constructor(t,e,i,n){this.view=t,this.startEvent=e,this.style=i,this.mustSelect=n,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParents=Du(t.contentDOM),this.atoms=t.state.facet(so).map(o=>o(t));let r=t.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(V.allowMultipleSelections)&&wd(t,e),this.dragging=vd(t,e)&&Zh(e)==1?null:!1}start(t){this.dragging===!1&&this.select(t)}move(t){if(t.buttons==0)return this.destroy();if(this.dragging||this.dragging==null&&bd(this.startEvent,t)<10)return;this.select(this.lastEvent=t);let e=0,i=0,n=0,r=0,o=this.view.win.innerWidth,l=this.view.win.innerHeight;this.scrollParents.x&&({left:n,right:o}=this.scrollParents.x.getBoundingClientRect()),this.scrollParents.y&&({top:r,bottom:l}=this.scrollParents.y.getBoundingClientRect());let a=ro(this.view);t.clientX-a.left<=n+un?e=-dn(n-t.clientX):t.clientX+a.right>=o-un&&(e=dn(t.clientX-o)),t.clientY-a.top<=r+un?i=-dn(r-t.clientY):t.clientY+a.bottom>=l-un&&(i=dn(t.clientY-l)),this.setScrollSpeed(e,i)}up(t){this.dragging==null&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){let{x:t,y:e}=this.scrollSpeed;t&&this.scrollParents.x&&(this.scrollParents.x.scrollLeft+=t,t=0),e&&this.scrollParents.y&&(this.scrollParents.y.scrollTop+=e,e=0),(t||e)&&this.view.win.scrollBy(t,e),this.dragging===!1&&this.select(this.lastEvent)}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let n=t.ranges[i],r=null;if(n.empty){let o=_n(this.atoms,n.from,0);o!=n.from&&(r=v.cursor(o,-1))}else{let o=_n(this.atoms,n.from,-1),l=_n(this.atoms,n.to,1);(o!=n.from||l!=n.to)&&(r=v.range(n.from==n.anchor?o:l,n.from==n.head?o:l))}r&&(e||(e=t.ranges.slice()),e[i]=r)}return e?v.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));(this.mustSelect||!i.eq(e.state.selection,this.dragging===!1))&&this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){t.transactions.some(e=>e.isUserEvent("input.type"))?this.destroy():this.style.update(t)&&setTimeout(()=>this.select(this.lastEvent),20)}}function wd(s,t){let e=s.state.facet(Eh);return e.length?e[0](t):T.mac?t.metaKey:t.ctrlKey}function xd(s,t){let e=s.state.facet(Lh);return e.length?e[0](t):T.mac?!t.altKey:!t.ctrlKey}function vd(s,t){let{main:e}=s.state.selection;if(e.empty)return!1;let i=Vi(s.root);if(!i||i.rangeCount==0)return!0;let n=i.getRangeAt(0).getClientRects();for(let r=0;r<n.length;r++){let o=n[r];if(o.left<=t.clientX&&o.right>=t.clientX&&o.top<=t.clientY&&o.bottom>=t.clientY)return!0}return!1}function kd(s,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let e=t.target,i;e!=s.contentDOM;e=e.parentNode)if(!e||e.nodeType==11||(i=K.get(e))&&i.ignoreEvent(t))return!1;return!0}const Yt=Object.create(null),$t=Object.create(null),Xh=T.ie&&T.ie_version<15||T.ios&&T.webkit_version<604;function Sd(s){let t=s.dom.parentNode;if(!t)return;let e=t.appendChild(document.createElement("textarea"));e.style.cssText="position: fixed; left: -10000px; top: 10px",e.focus(),setTimeout(()=>{s.focus(),e.remove(),Qh(s,e.value)},50)}function ls(s,t,e){for(let i of s.facet(t))e=i(e,s);return e}function Qh(s,t){t=ls(s.state,eo,t);let{state:e}=s,i,n=1,r=e.toText(t),o=r.lines==e.selection.ranges.length;if(kr!=null&&e.selection.ranges.every(a=>a.empty)&&kr==r.toString()){let a=-1;i=e.changeByRange(h=>{let c=e.doc.lineAt(h.from);if(c.from==a)return{range:h};a=c.from;let f=e.toText((o?r.line(n++).text:t)+e.lineBreak);return{changes:{from:c.from,insert:f},range:v.cursor(h.from+f.length)}})}else o?i=e.changeByRange(a=>{let h=r.line(n++);return{changes:{from:a.from,to:a.to,insert:h.text},range:v.cursor(a.from+h.length)}}):i=e.replaceSelection(r);s.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}$t.scroll=s=>{s.inputState.lastScrollTop=s.scrollDOM.scrollTop,s.inputState.lastScrollLeft=s.scrollDOM.scrollLeft};Yt.keydown=(s,t)=>(s.inputState.setSelectionOrigin("select"),t.keyCode==27&&s.inputState.tabFocusMode!=0&&(s.inputState.tabFocusMode=Date.now()+2e3),!1);$t.touchstart=(s,t)=>{s.inputState.lastTouchTime=Date.now(),s.inputState.setSelectionOrigin("select.pointer")};$t.touchmove=s=>{s.inputState.setSelectionOrigin("select.pointer")};Yt.mousedown=(s,t)=>{if(s.observer.flush(),s.inputState.lastTouchTime>Date.now()-2e3)return!1;let e=null;for(let i of s.state.facet(Rh))if(e=i(s,t),e)break;if(!e&&t.button==0&&(e=Md(s,t)),e){let i=!s.hasFocus;s.inputState.startMouseSelection(new yd(s,t,e,i)),i&&s.observer.ignore(()=>{dh(s.contentDOM);let r=s.root.activeElement;r&&!r.contains(s.contentDOM)&&r.blur()});let n=s.inputState.mouseSelection;if(n)return n.start(t),n.dragging===!1}return!1};function hl(s,t,e,i){if(i==1)return v.cursor(t,e);if(i==2)return Zu(s.state,t,e);{let n=tt.find(s.docView,t),r=s.state.doc.lineAt(n?n.posAtEnd:t),o=n?n.posAtStart:r.from,l=n?n.posAtEnd:r.to;return l<s.state.doc.length&&l==r.to&&l++,v.range(o,l)}}let cl=(s,t,e)=>t>=e.top&&t<=e.bottom&&s>=e.left&&s<=e.right;function Cd(s,t,e,i){let n=tt.find(s.docView,t);if(!n)return 1;let r=t-n.posAtStart;if(r==0)return 1;if(r==n.length)return-1;let o=n.coordsAt(r,-1);if(o&&cl(e,i,o))return-1;let l=n.coordsAt(r,1);return l&&cl(e,i,l)?1:o&&o.bottom>=i?-1:1}function fl(s,t){let e=s.posAtCoords({x:t.clientX,y:t.clientY},!1);return{pos:e,bias:Cd(s,e,t.clientX,t.clientY)}}const Ad=T.ie&&T.ie_version<=11;let ul=null,dl=0,pl=0;function Zh(s){if(!Ad)return s.detail;let t=ul,e=pl;return ul=s,pl=Date.now(),dl=!t||e>Date.now()-400&&Math.abs(t.clientX-s.clientX)<2&&Math.abs(t.clientY-s.clientY)<2?(dl+1)%3:1}function Md(s,t){let e=fl(s,t),i=Zh(t),n=s.state.selection;return{update(r){r.docChanged&&(e.pos=r.changes.mapPos(e.pos),n=n.map(r.changes))},get(r,o,l){let a=fl(s,r),h,c=hl(s,a.pos,a.bias,i);if(e.pos!=a.pos&&!o){let f=hl(s,e.pos,e.bias,i),u=Math.min(f.from,c.from),d=Math.max(f.to,c.to);c=u<c.from?v.range(u,d):v.range(d,u)}return o?n.replaceRange(n.main.extend(c.from,c.to)):l&&i==1&&n.ranges.length>1&&(h=Dd(n,a.pos))?h:l?n.addRange(c):v.create([c])}}}function Dd(s,t){for(let e=0;e<s.ranges.length;e++){let{from:i,to:n}=s.ranges[e];if(i<=t&&n>=t)return v.create(s.ranges.slice(0,e).concat(s.ranges.slice(e+1)),s.mainIndex==e?0:s.mainIndex-(s.mainIndex>e?1:0))}return null}Yt.dragstart=(s,t)=>{let{selection:{main:e}}=s.state;if(t.target.draggable){let n=s.docView.nearest(t.target);if(n&&n.isWidget){let r=n.posAtStart,o=r+n.length;(r>=e.to||o<=e.from)&&(e=v.range(r,o))}}let{inputState:i}=s;return i.mouseSelection&&(i.mouseSelection.dragging=!0),i.draggedContent=e,t.dataTransfer&&(t.dataTransfer.setData("Text",ls(s.state,io,s.state.sliceDoc(e.from,e.to))),t.dataTransfer.effectAllowed="copyMove"),!1};Yt.dragend=s=>(s.inputState.draggedContent=null,!1);function ml(s,t,e,i){if(e=ls(s.state,eo,e),!e)return;let n=s.posAtCoords({x:t.clientX,y:t.clientY},!1),{draggedContent:r}=s.inputState,o=i&&r&&xd(s,t)?{from:r.from,to:r.to}:null,l={from:n,insert:e},a=s.state.changes(o?[o,l]:l);s.focus(),s.dispatch({changes:a,selection:{anchor:a.mapPos(n,-1),head:a.mapPos(n,1)},userEvent:o?"move.drop":"input.drop"}),s.inputState.draggedContent=null}Yt.drop=(s,t)=>{if(!t.dataTransfer)return!1;if(s.state.readOnly)return!0;let e=t.dataTransfer.files;if(e&&e.length){let i=Array(e.length),n=0,r=()=>{++n==e.length&&ml(s,t,i.filter(o=>o!=null).join(s.state.lineBreak),!1)};for(let o=0;o<e.length;o++){let l=new FileReader;l.onerror=r,l.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(l.result)||(i[o]=l.result),r()},l.readAsText(e[o])}return!0}else{let i=t.dataTransfer.getData("Text");if(i)return ml(s,t,i,!0),!0}return!1};Yt.paste=(s,t)=>{if(s.state.readOnly)return!0;s.observer.flush();let e=Xh?null:t.clipboardData;return e?(Qh(s,e.getData("text/plain")||e.getData("text/uri-list")),!0):(Sd(s),!1)};function Td(s,t){let e=s.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.value=t,i.focus(),i.selectionEnd=t.length,i.selectionStart=0,setTimeout(()=>{i.remove(),s.focus()},50)}function Od(s){let t=[],e=[],i=!1;for(let n of s.selection.ranges)n.empty||(t.push(s.sliceDoc(n.from,n.to)),e.push(n));if(!t.length){let n=-1;for(let{from:r}of s.selection.ranges){let o=s.doc.lineAt(r);o.number>n&&(t.push(o.text),e.push({from:o.from,to:Math.min(s.doc.length,o.to+1)})),n=o.number}i=!0}return{text:ls(s,io,t.join(s.lineBreak)),ranges:e,linewise:i}}let kr=null;Yt.copy=Yt.cut=(s,t)=>{let{text:e,ranges:i,linewise:n}=Od(s.state);if(!e&&!n)return!1;kr=n?e:null,t.type=="cut"&&!s.state.readOnly&&s.dispatch({changes:i,scrollIntoView:!0,userEvent:"delete.cut"});let r=Xh?null:t.clipboardData;return r?(r.clearData(),r.setData("text/plain",e),!0):(Td(s,e),!1)};const tc=be.define();function ec(s,t){let e=[];for(let i of s.facet(Nh)){let n=i(s,t);n&&e.push(n)}return e?s.update({effects:e,annotations:tc.of(!0)}):null}function ic(s){setTimeout(()=>{let t=s.hasFocus;if(t!=s.inputState.notifiedFocused){let e=ec(s.state,t);e?s.dispatch(e):s.update([])}},10)}$t.focus=s=>{s.inputState.lastFocusTime=Date.now(),!s.scrollDOM.scrollTop&&(s.inputState.lastScrollTop||s.inputState.lastScrollLeft)&&(s.scrollDOM.scrollTop=s.inputState.lastScrollTop,s.scrollDOM.scrollLeft=s.inputState.lastScrollLeft),ic(s)};$t.blur=s=>{s.observer.clearSelectionRange(),ic(s)};$t.compositionstart=$t.compositionupdate=s=>{s.observer.editContext||(s.inputState.compositionFirstChange==null&&(s.inputState.compositionFirstChange=!0),s.inputState.composing<0&&(s.inputState.composing=0))};$t.compositionend=s=>{s.observer.editContext||(s.inputState.composing=-1,s.inputState.compositionEndedAt=Date.now(),s.inputState.compositionPendingKey=!0,s.inputState.compositionPendingChange=s.observer.pendingRecords().length>0,s.inputState.compositionFirstChange=null,T.chrome&&T.android?s.observer.flushSoon():s.inputState.compositionPendingChange?Promise.resolve().then(()=>s.observer.flush()):setTimeout(()=>{s.inputState.composing<0&&s.docView.hasComposition&&s.update([])},50))};$t.contextmenu=s=>{s.inputState.lastContextMenu=Date.now()};Yt.beforeinput=(s,t)=>{var e,i;if(t.inputType=="insertReplacementText"&&s.observer.editContext){let r=(e=t.dataTransfer)===null||e===void 0?void 0:e.getData("text/plain"),o=t.getTargetRanges();if(r&&o.length){let l=o[0],a=s.posAtDOM(l.startContainer,l.startOffset),h=s.posAtDOM(l.endContainer,l.endOffset);return oo(s,{from:a,to:h,insert:s.state.toText(r)},null),!0}}let n;if(T.chrome&&T.android&&(n=Yh.find(r=>r.inputType==t.inputType))&&(s.observer.delayAndroidKey(n.key,n.keyCode),n.key=="Backspace"||n.key=="Delete")){let r=((i=window.visualViewport)===null||i===void 0?void 0:i.height)||0;setTimeout(()=>{var o;(((o=window.visualViewport)===null||o===void 0?void 0:o.height)||0)>r+10&&s.hasFocus&&(s.contentDOM.blur(),s.focus())},100)}return T.ios&&t.inputType=="deleteContentForward"&&s.observer.flushSoon(),T.safari&&t.inputType=="insertText"&&s.inputState.composing>=0&&setTimeout(()=>$t.compositionend(s,t),20),!1};const gl=new Set;function Bd(s){gl.has(s)||(gl.add(s),s.addEventListener("copy",()=>{}),s.addEventListener("cut",()=>{}))}const bl=["pre-wrap","normal","pre-line","break-spaces"];let fi=!1;function yl(){fi=!1}class Pd{constructor(t){this.lineWrapping=t,this.doc=F.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return bl.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let n=t[i];n<0?i++:this.heightSamples[Math.floor(n*10)]||(e=!0,this.heightSamples[Math.floor(n*10)]=!0)}return e}refresh(t,e,i,n,r,o){let l=bl.indexOf(t)>-1,a=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=l;if(this.lineWrapping=l,this.lineHeight=e,this.charWidth=i,this.textHeight=n,this.lineLength=r,a){this.heightSamples={};for(let h=0;h<o.length;h++){let c=o[h];c<0?h++:this.heightSamples[Math.floor(c*10)]=!0}}return a}}class Ed{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class ne{constructor(t,e,i,n,r){this.from=t,this.length=e,this.top=i,this.height=n,this._content=r}get type(){return typeof this._content=="number"?wt.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof De?this._content.widget:null}get widgetLineBreaks(){return typeof this._content=="number"?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new ne(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var G=function(s){return s[s.ByPos=0]="ByPos",s[s.ByHeight=1]="ByHeight",s[s.ByPosNoHeight=2]="ByPosNoHeight",s}(G||(G={}));const In=.001;class xt{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(this.flags&2)>0}set outdated(t){this.flags=(t?2:0)|this.flags&-3}setHeight(t){this.height!=t&&(Math.abs(this.height-t)>In&&(fi=!0),this.height=t)}replace(t,e,i){return xt.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,n){let r=this,o=i.doc;for(let l=n.length-1;l>=0;l--){let{fromA:a,toA:h,fromB:c,toB:f}=n[l],u=r.lineAt(a,G.ByPosNoHeight,i.setDoc(e),0,0),d=u.to>=h?u:r.lineAt(h,G.ByPosNoHeight,i,0,0);for(f+=d.to-h,h=d.to;l>0&&u.from<=n[l-1].toA;)a=n[l-1].fromA,c=n[l-1].fromB,l--,a<u.from&&(u=r.lineAt(a,G.ByPosNoHeight,i,0,0));c+=u.from-a,a=u.from;let p=lo.build(i.setDoc(o),t,c,f);r=Gn(r,r.replace(a,h,p))}return r.updateHeight(i,0)}static empty(){return new Bt(0,0)}static of(t){if(t.length==1)return t[0];let e=0,i=t.length,n=0,r=0;for(;;)if(e==i)if(n>r*2){let l=t[e-1];l.break?t.splice(--e,1,l.left,null,l.right):t.splice(--e,1,l.left,l.right),i+=1+l.break,n-=l.size}else if(r>n*2){let l=t[i];l.break?t.splice(i,1,l.left,null,l.right):t.splice(i,1,l.left,l.right),i+=2+l.break,r-=l.size}else break;else if(n<r){let l=t[e++];l&&(n+=l.size)}else{let l=t[--i];l&&(r+=l.size)}let o=0;return t[e-1]==null?(o=1,e--):t[e]==null&&(o=1,i++),new Ld(xt.of(t.slice(0,e)),o,xt.of(t.slice(i)))}}function Gn(s,t){return s==t?s:(s.constructor!=t.constructor&&(fi=!0),t)}xt.prototype.size=1;class nc extends xt{constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,n){return new ne(n,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,n,r){return this.blockAt(0,i,n,r)}forEachLine(t,e,i,n,r,o){t<=r+this.length&&e>=r&&o(this.blockAt(0,i,n,r))}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more&&this.setHeight(n.heights[n.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class Bt extends nc{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,n){return new ne(n,this.length,i,this.height,this.breaks)}replace(t,e,i){let n=i[0];return i.length==1&&(n instanceof Bt||n instanceof at&&n.flags&4)&&Math.abs(this.length-n.length)<10?(n instanceof at?n=new Bt(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):xt.of(i)}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more?this.setHeight(n.heights[n.index++]):(i||this.outdated)&&this.setHeight(Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class at extends xt{constructor(t){super(t,0)}heightMetrics(t,e){let i=t.doc.lineAt(e).number,n=t.doc.lineAt(e+this.length).number,r=n-i+1,o,l=0;if(t.lineWrapping){let a=Math.min(this.height,t.lineHeight*r);o=a/r,this.length>r+1&&(l=(this.height-a)/(this.length-r-1))}else o=this.height/r;return{firstLine:i,lastLine:n,perLine:o,perChar:l}}blockAt(t,e,i,n){let{firstLine:r,lastLine:o,perLine:l,perChar:a}=this.heightMetrics(e,n);if(e.lineWrapping){let h=n+(t<e.lineHeight?0:Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length)),c=e.doc.lineAt(h),f=l+c.length*a,u=Math.max(i,t-f/2);return new ne(c.from,c.length,u,f,0)}else{let h=Math.max(0,Math.min(o-r,Math.floor((t-i)/l))),{from:c,length:f}=e.doc.line(r+h);return new ne(c,f,i+l*h,l,0)}}lineAt(t,e,i,n,r){if(e==G.ByHeight)return this.blockAt(t,i,n,r);if(e==G.ByPosNoHeight){let{from:d,to:p}=i.doc.lineAt(t);return new ne(d,p-d,0,0,0)}let{firstLine:o,perLine:l,perChar:a}=this.heightMetrics(i,r),h=i.doc.lineAt(t),c=l+h.length*a,f=h.number-o,u=n+l*f+a*(h.from-r-f);return new ne(h.from,h.length,Math.max(n,Math.min(u,n+this.height-c)),c,0)}forEachLine(t,e,i,n,r,o){t=Math.max(t,r),e=Math.min(e,r+this.length);let{firstLine:l,perLine:a,perChar:h}=this.heightMetrics(i,r);for(let c=t,f=n;c<=e;){let u=i.doc.lineAt(c);if(c==t){let p=u.number-l;f+=a*p+h*(t-r-p)}let d=a+h*u.length;o(new ne(u.from,u.length,f,d,0)),f+=d,c=u.to+1}}replace(t,e,i){let n=this.length-e;if(n>0){let r=i[i.length-1];r instanceof at?i[i.length-1]=new at(r.length+n):i.push(null,new at(n-1))}if(t>0){let r=i[0];r instanceof at?i[0]=new at(t+r.length):i.unshift(new at(t-1),null)}return xt.of(i)}decomposeLeft(t,e){e.push(new at(t-1),null)}decomposeRight(t,e){e.push(null,new at(this.length-t-1))}updateHeight(t,e=0,i=!1,n){let r=e+this.length;if(n&&n.from<=e+this.length&&n.more){let o=[],l=Math.max(e,n.from),a=-1;for(n.from>e&&o.push(new at(n.from-e-1).updateHeight(t,e));l<=r&&n.more;){let c=t.doc.lineAt(l).length;o.length&&o.push(null);let f=n.heights[n.index++];a==-1?a=f:Math.abs(f-a)>=In&&(a=-2);let u=new Bt(c,f);u.outdated=!1,o.push(u),l+=c+1}l<=r&&o.push(null,new at(r-l).updateHeight(t,l));let h=xt.of(o);return(a<0||Math.abs(h.height-this.height)>=In||Math.abs(a-this.heightMetrics(t,e).perLine)>=In)&&(fi=!0),Gn(this,h)}else(i||this.outdated)&&(this.setHeight(t.heightForGap(e,e+this.length)),this.outdated=!1);return this}toString(){return`gap(${this.length})`}}class Ld extends xt{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return this.flags&1}blockAt(t,e,i,n){let r=i+this.left.height;return t<r?this.left.blockAt(t,e,i,n):this.right.blockAt(t,e,r,n+this.left.length+this.break)}lineAt(t,e,i,n,r){let o=n+this.left.height,l=r+this.left.length+this.break,a=e==G.ByHeight?t<o:t<l,h=a?this.left.lineAt(t,e,i,n,r):this.right.lineAt(t,e,i,o,l);if(this.break||(a?h.to<l:h.from>l))return h;let c=e==G.ByPosNoHeight?G.ByPosNoHeight:G.ByPos;return a?h.join(this.right.lineAt(l,c,i,o,l)):this.left.lineAt(l,c,i,n,r).join(h)}forEachLine(t,e,i,n,r,o){let l=n+this.left.height,a=r+this.left.length+this.break;if(this.break)t<a&&this.left.forEachLine(t,e,i,n,r,o),e>=a&&this.right.forEachLine(t,e,i,l,a,o);else{let h=this.lineAt(a,G.ByPos,i,n,r);t<h.from&&this.left.forEachLine(t,h.from-1,i,n,r,o),h.to>=t&&h.from<=e&&o(h),e>h.to&&this.right.forEachLine(h.to+1,e,i,l,a,o)}}replace(t,e,i){let n=this.left.length+this.break;if(e<n)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-n,e-n,i));let r=[];t>0&&this.decomposeLeft(t,r);let o=r.length;for(let l of i)r.push(l);if(t>0&&wl(r,o-1),e<this.length){let l=r.length;this.decomposeRight(e,r),wl(r,l)}return xt.of(r)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,n=i+this.break;if(t>=n)return this.right.decomposeRight(t-n,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<n&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?xt.of(this.break?[t,null,e]:[t,e]):(this.left=Gn(this.left,t),this.right=Gn(this.right,e),this.setHeight(t.height+e.height),this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,n){let{left:r,right:o}=this,l=e+r.length+this.break,a=null;return n&&n.from<=e+r.length&&n.more?a=r=r.updateHeight(t,e,i,n):r.updateHeight(t,e,i),n&&n.from<=l+o.length&&n.more?a=o=o.updateHeight(t,l,i,n):o.updateHeight(t,l,i),a?this.balanced(r,o):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function wl(s,t){let e,i;s[t]==null&&(e=s[t-1])instanceof at&&(i=s[t+1])instanceof at&&s.splice(t-1,3,new at(e.length+1+i.length))}const Rd=5;class lo{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let i=Math.min(e,this.lineEnd),n=this.nodes[this.nodes.length-1];n instanceof Bt?n.length+=i-this.pos:(i>this.pos||!this.isCovered)&&this.nodes.push(new Bt(i-this.pos,-1)),this.writtenTo=i,e>i&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0,r=i.widget?i.widget.lineBreaks:0;n<0&&(n=this.oracle.lineHeight);let o=e-t;i.block?this.addBlock(new nc(o,n,i)):(o||r||n>=Rd)&&this.addLineDeco(n,r,o)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||this.nodes[this.nodes.length-1]==null)&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new Bt(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new at(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof Bt)return t;let e=new Bt(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let n=this.ensureLine();n.length+=i,n.collapsed+=i,n.widgetHeight=Math.max(n.widgetHeight,t),n.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=this.nodes.length==0?null:this.nodes[this.nodes.length-1];this.lineStart>-1&&!(e instanceof Bt)&&!this.isCovered?this.nodes.push(new Bt(0,-1)):(this.writtenTo<this.pos||e==null)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos));let i=t;for(let n of this.nodes)n instanceof Bt&&n.updateHeight(this.oracle,i),i+=n?n.length:1;return this.nodes}static build(t,e,i,n){let r=new lo(i,t);return z.spans(e,i,n,r,0),r.finish(i)}}function _d(s,t,e){let i=new Id;return z.compare(s,t,e,i,0),i.changes}class Id{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,n){(t<e||i&&i.heightRelevant||n&&n.heightRelevant)&&Rn(t,e,this.changes,5)}}function Nd(s,t){let e=s.getBoundingClientRect(),i=s.ownerDocument,n=i.defaultView||window,r=Math.max(0,e.left),o=Math.min(n.innerWidth,e.right),l=Math.max(0,e.top),a=Math.min(n.innerHeight,e.bottom);for(let h=s.parentNode;h&&h!=i.body;)if(h.nodeType==1){let c=h,f=window.getComputedStyle(c);if((c.scrollHeight>c.clientHeight||c.scrollWidth>c.clientWidth)&&f.overflow!="visible"){let u=c.getBoundingClientRect();r=Math.max(r,u.left),o=Math.min(o,u.right),l=Math.max(l,u.top),a=Math.min(h==s.parentNode?n.innerHeight:a,u.bottom)}h=f.position=="absolute"||f.position=="fixed"?c.offsetParent:c.parentNode}else if(h.nodeType==11)h=h.host;else break;return{left:r-e.left,right:Math.max(r,o)-e.left,top:l-(e.top+t),bottom:Math.max(l,a)-(e.top+t)}}function Fd(s){let t=s.getBoundingClientRect(),e=s.ownerDocument.defaultView||window;return t.left<e.innerWidth&&t.right>0&&t.top<e.innerHeight&&t.bottom>0}function Hd(s,t){let e=s.getBoundingClientRect();return{left:0,right:e.right-e.left,top:t,bottom:e.bottom-(e.top+t)}}class Cs{constructor(t,e,i,n){this.from=t,this.to=e,this.size=i,this.displaySize=n}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],r=e[i];if(n.from!=r.from||n.to!=r.to||n.size!=r.size)return!1}return!0}draw(t,e){return I.replace({widget:new Vd(this.displaySize*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class Vd extends he{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class xl{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=vl,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=Y.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(no).some(i=>typeof i!="function"&&i.class=="cm-lineWrapping");this.heightOracle=new Pd(e),this.stateDeco=t.facet(Wi).filter(i=>typeof i!="function"),this.heightMap=xt.empty().applyChanges(this.stateDeco,F.empty,this.heightOracle.setDoc(t.doc),[new zt(0,0,0,t.doc.length)]);for(let i=0;i<2&&(this.viewport=this.getViewport(0,null),!!this.updateForViewport());i++);this.updateViewportLines(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=I.set(this.lineGaps.map(i=>i.draw(this,!1))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let n=i?e.head:e.anchor;if(!t.some(({from:r,to:o})=>n>=r&&n<=o)){let{from:r,to:o}=this.lineBlockAt(n);t.push(new pn(r,o))}}return this.viewports=t.sort((i,n)=>i.from-n.from),this.updateScaler()}updateScaler(){let t=this.scaler;return this.scaler=this.heightMap.height<=7e6?vl:new ao(this.heightOracle,this.heightMap,this.viewports),t.eq(this.scaler)?0:2}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,t=>{this.viewportLines.push(Bi(t,this.scaler))})}update(t,e=null){this.state=t.state;let i=this.stateDeco;this.stateDeco=this.state.facet(Wi).filter(c=>typeof c!="function");let n=t.changedRanges,r=zt.extendWithRanges(n,_d(i,this.stateDeco,t?t.changes:nt.empty(this.state.doc.length))),o=this.heightMap.height,l=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);yl(),this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),r),(this.heightMap.height!=o||fi)&&(t.flags|=2),l?(this.scrollAnchorPos=t.changes.mapPos(l.from,-1),this.scrollAnchorHeight=l.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=this.heightMap.height);let a=r.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<a.from||e.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,e));let h=a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,t.flags|=this.updateForViewport(),(h||!t.changes.empty||t.flags&2)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(t.changes),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(Hh)&&(this.mustEnforceCursorAssoc=!0)}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),n=this.heightOracle,r=i.whiteSpace;this.defaultTextDirection=i.direction=="rtl"?Y.RTL:Y.LTR;let o=this.heightOracle.mustRefreshForWrapping(r),l=e.getBoundingClientRect(),a=o||this.mustMeasureContent||this.contentDOMHeight!=l.height;this.contentDOMHeight=l.height,this.mustMeasureContent=!1;let h=0,c=0;if(l.width&&l.height){let{scaleX:k,scaleY:x}=uh(e,l);(k>.005&&Math.abs(this.scaleX-k)>.005||x>.005&&Math.abs(this.scaleY-x)>.005)&&(this.scaleX=k,this.scaleY=x,h|=16,o=a=!0)}let f=(parseInt(i.paddingTop)||0)*this.scaleY,u=(parseInt(i.paddingBottom)||0)*this.scaleY;(this.paddingTop!=f||this.paddingBottom!=u)&&(this.paddingTop=f,this.paddingBottom=u,h|=18),this.editorWidth!=t.scrollDOM.clientWidth&&(n.lineWrapping&&(a=!0),this.editorWidth=t.scrollDOM.clientWidth,h|=16);let d=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=d&&(this.scrollAnchorHeight=-1,this.scrollTop=d),this.scrolledToBottom=mh(t.scrollDOM);let p=(this.printing?Hd:Nd)(e,this.paddingTop),m=p.top-this.pixelViewport.top,g=p.bottom-this.pixelViewport.bottom;this.pixelViewport=p;let y=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(y!=this.inView&&(this.inView=y,y&&(a=!0)),!this.inView&&!this.scrollTarget&&!Fd(t.dom))return 0;let w=l.width;if((this.contentDOMWidth!=w||this.editorHeight!=t.scrollDOM.clientHeight)&&(this.contentDOMWidth=l.width,this.editorHeight=t.scrollDOM.clientHeight,h|=16),a){let k=t.docView.measureVisibleLineHeights(this.viewport);if(n.mustRefreshForHeights(k)&&(o=!0),o||n.lineWrapping&&Math.abs(w-this.contentDOMWidth)>n.charWidth){let{lineHeight:x,charWidth:C,textHeight:A}=t.docView.measureTextSize();o=x>0&&n.refresh(r,x,C,A,w/C,k),o&&(t.docView.minWidth=0,h|=16)}m>0&&g>0?c=Math.max(m,g):m<0&&g<0&&(c=Math.min(m,g)),yl();for(let x of this.viewports){let C=x.from==this.viewport.from?k:t.docView.measureVisibleLineHeights(x);this.heightMap=(o?xt.empty().applyChanges(this.stateDeco,F.empty,this.heightOracle,[new zt(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(n,0,o,new Ed(x.from,C))}fi&&(h|=2)}let S=!this.viewportIsAppropriate(this.viewport,c)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return S&&(h&2&&(h|=this.updateScaler()),this.viewport=this.getViewport(c,this.scrollTarget),h|=this.updateForViewport()),(h&2||S)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(o?[]:this.lineGaps,t)),h|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),h}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),n=this.heightMap,r=this.heightOracle,{visibleTop:o,visibleBottom:l}=this,a=new pn(n.lineAt(o-i*1e3,G.ByHeight,r,0,0).from,n.lineAt(l+(1-i)*1e3,G.ByHeight,r,0,0).to);if(e){let{head:h}=e.range;if(h<a.from||h>a.to){let c=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),f=n.lineAt(h,G.ByPos,r,0,0),u;e.y=="center"?u=(f.top+f.bottom)/2-c/2:e.y=="start"||e.y=="nearest"&&h<a.from?u=f.top:u=f.bottom-c,a=new pn(n.lineAt(u-1e3/2,G.ByHeight,r,0,0).from,n.lineAt(u+c+1e3/2,G.ByHeight,r,0,0).to)}}return a}mapViewport(t,e){let i=e.mapPos(t.from,-1),n=e.mapPos(t.to,1);return new pn(this.heightMap.lineAt(i,G.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(n,G.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:n}=this.heightMap.lineAt(t,G.ByPos,this.heightOracle,0,0),{bottom:r}=this.heightMap.lineAt(e,G.ByPos,this.heightOracle,0,0),{visibleTop:o,visibleBottom:l}=this;return(t==0||n<=o-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||r>=l+Math.max(10,Math.min(i,250)))&&n>o-2*1e3&&r<l+2*1e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let n of t)e.touchesRange(n.from,n.to)||i.push(new Cs(e.mapPos(n.from),e.mapPos(n.to),n.size,n.displaySize));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,n=i?1e4:2e3,r=n>>1,o=n<<1;if(this.defaultTextDirection!=Y.LTR&&!i)return[];let l=[],a=(c,f,u,d)=>{if(f-c<r)return;let p=this.state.selection.main,m=[p.from];p.empty||m.push(p.to);for(let y of m)if(y>c&&y<f){a(c,y-10,u,d),a(y+10,f,u,d);return}let g=zd(t,y=>y.from>=u.from&&y.to<=u.to&&Math.abs(y.from-c)<r&&Math.abs(y.to-f)<r&&!m.some(w=>y.from<w&&y.to>w));if(!g){if(f<u.to&&e&&i&&e.visibleRanges.some(S=>S.from<=f&&S.to>=f)){let S=e.moveToLineBoundary(v.cursor(f),!1,!0).head;S>c&&(f=S)}let y=this.gapSize(u,c,f,d),w=i||y<2e6?y:2e6;g=new Cs(c,f,y,w)}l.push(g)},h=c=>{if(c.length<o||c.type!=wt.Text)return;let f=Wd(c.from,c.to,this.stateDeco);if(f.total<o)return;let u=this.scrollTarget?this.scrollTarget.range.head:null,d,p;if(i){let m=n/this.heightOracle.lineLength*this.heightOracle.lineHeight,g,y;if(u!=null){let w=gn(f,u),S=((this.visibleBottom-this.visibleTop)/2+m)/c.height;g=w-S,y=w+S}else g=(this.visibleTop-c.top-m)/c.height,y=(this.visibleBottom-c.top+m)/c.height;d=mn(f,g),p=mn(f,y)}else{let m=f.total*this.heightOracle.charWidth,g=n*this.heightOracle.charWidth,y=0;if(m>2e6)for(let C of t)C.from>=c.from&&C.from<c.to&&C.size!=C.displaySize&&C.from*this.heightOracle.charWidth+y<this.pixelViewport.left&&(y=C.size-C.displaySize);let w=this.pixelViewport.left+y,S=this.pixelViewport.right+y,k,x;if(u!=null){let C=gn(f,u),A=((S-w)/2+g)/m;k=C-A,x=C+A}else k=(w-g)/m,x=(S+g)/m;d=mn(f,k),p=mn(f,x)}d>c.from&&a(c.from,d,c,f),p<c.to&&a(p,c.to,c,f)};for(let c of this.viewportLines)Array.isArray(c.type)?c.type.forEach(h):h(c);return l}gapSize(t,e,i,n){let r=gn(n,i)-gn(n,e);return this.heightOracle.lineWrapping?t.height*r:n.total*this.heightOracle.charWidth*r}updateLineGaps(t){Cs.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=I.set(t.map(e=>e.draw(this,this.heightOracle.lineWrapping))))}computeVisibleRanges(t){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let i=[];z.spans(e,this.viewport.from,this.viewport.to,{span(r,o){i.push({from:r,to:o})},point(){}},20);let n=0;if(i.length!=this.visibleRanges.length)n=12;else for(let r=0;r<i.length&&!(n&8);r++){let o=this.visibleRanges[r],l=i[r];(o.from!=l.from||o.to!=l.to)&&(n|=4,t&&t.mapPos(o.from,-1)==l.from&&t.mapPos(o.to,1)==l.to||(n|=8))}return this.visibleRanges=i,n}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find(e=>e.from<=t&&e.to>=t)||Bi(this.heightMap.lineAt(t,G.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return t>=this.viewportLines[0].top&&t<=this.viewportLines[this.viewportLines.length-1].bottom&&this.viewportLines.find(e=>e.top<=t&&e.bottom>=t)||Bi(this.heightMap.lineAt(this.scaler.fromDOM(t),G.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return Bi(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class pn{constructor(t,e){this.from=t,this.to=e}}function Wd(s,t,e){let i=[],n=s,r=0;return z.spans(e,s,t,{span(){},point(o,l){o>n&&(i.push({from:n,to:o}),r+=o-n),n=l}},20),n<t&&(i.push({from:n,to:t}),r+=t-n),{total:r,ranges:i}}function mn({total:s,ranges:t},e){if(e<=0)return t[0].from;if(e>=1)return t[t.length-1].to;let i=Math.floor(s*e);for(let n=0;;n++){let{from:r,to:o}=t[n],l=o-r;if(i<=l)return r+i;i-=l}}function gn(s,t){let e=0;for(let{from:i,to:n}of s.ranges){if(t<=n){e+=t-i;break}e+=n-i}return e/s.total}function zd(s,t){for(let e of s)if(t(e))return e}const vl={toDOM(s){return s},fromDOM(s){return s},scale:1,eq(s){return s==this}};class ao{constructor(t,e,i){let n=0,r=0,o=0;this.viewports=i.map(({from:l,to:a})=>{let h=e.lineAt(l,G.ByPos,t,0,0).top,c=e.lineAt(a,G.ByPos,t,0,0).bottom;return n+=c-h,{from:l,to:a,top:h,bottom:c,domTop:0,domBottom:0}}),this.scale=(7e6-n)/(e.height-n);for(let l of this.viewports)l.domTop=o+(l.top-r)*this.scale,o=l.domBottom=l.domTop+(l.bottom-l.top),r=l.bottom}toDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.top)return n+(t-i)*this.scale;if(t<=r.bottom)return r.domTop+(t-r.top);i=r.bottom,n=r.domBottom}}fromDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.domTop)return i+(t-n)/this.scale;if(t<=r.domBottom)return r.top+(t-r.domTop);i=r.bottom,n=r.domBottom}}eq(t){return t instanceof ao?this.scale==t.scale&&this.viewports.length==t.viewports.length&&this.viewports.every((e,i)=>e.from==t.viewports[i].from&&e.to==t.viewports[i].to):!1}}function Bi(s,t){if(t.scale==1)return s;let e=t.toDOM(s.top),i=t.toDOM(s.bottom);return new ne(s.from,s.length,e,i-e,Array.isArray(s._content)?s._content.map(n=>Bi(n,t)):s._content)}const bn=O.define({combine:s=>s.join(" ")}),Sr=O.define({combine:s=>s.indexOf(!0)>-1}),Cr=Ae.newName(),sc=Ae.newName(),rc=Ae.newName(),oc={"&light":"."+sc,"&dark":"."+rc};function Ar(s,t,e){return new Ae(t,{finish(i){return/&/.test(i)?i.replace(/&\w*/,n=>{if(n=="&")return s;if(!e||!e[n])throw new RangeError(`Unsupported selector: ${n}`);return e[n]}):s+" "+i}})}const $d=Ar("."+Cr,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0,overflowAnchor:"none"},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#ddd"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0,zIndex:300},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-highlightSpace":{backgroundImage:"radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%)",backgroundPosition:"center"},".cm-highlightTab":{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>')`,backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},oc),qd={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},As=T.ie&&T.ie_version<=11;class jd{constructor(t){this.view=t,this.active=!1,this.editContext=null,this.selectionRange=new Tu,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver(e=>{for(let i of e)this.queue.push(i);(T.ie&&T.ie_version<=11||T.ios&&t.composing)&&e.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),window.EditContext&&t.constructor.EDIT_CONTEXT!==!1&&!(T.chrome&&T.chrome_version<126)&&(this.editContext=new Ud(t),t.state.facet(de)&&(t.contentDOM.editContext=this.editContext.editContext)),As&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),typeof ResizeObserver=="function"&&(this.resizeScroll=new ResizeObserver(()=>{var e;((e=this.view.docView)===null||e===void 0?void 0:e.lastUpdate)<Date.now()-75&&this.onResize()}),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),typeof IntersectionObserver=="function"&&(this.intersection=new IntersectionObserver(e=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),e.length>0&&e[e.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(e=>{e.length>0&&e[e.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.editContext&&this.view.requestMeasure(this.editContext.measureReq),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(t){(t.type=="change"||!t.type)&&!t.matches||(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500))}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some((e,i)=>e!=t[i]))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,n=this.selectionRange;if(i.state.facet(de)?i.root.activeElement!=this.dom:!Ln(this.dom,n))return;let r=n.anchorNode&&i.docView.nearest(n.anchorNode);if(r&&r.ignoreEvent(t)){e||(this.selectionChanged=!1);return}(T.ie&&T.ie_version<=11||T.android&&T.chrome)&&!i.state.selection.main.empty&&n.focusNode&&Li(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=Vi(t.root);if(!e)return!1;let i=T.safari&&t.root.nodeType==11&&t.root.activeElement==this.dom&&Kd(this.view,e)||e;if(!i||this.selectionRange.eq(i))return!1;let n=Ln(this.dom,i);return n&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&Bu(this.dom,i)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(i),n&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(i.nodeType==1)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else if(i.nodeType==11)i=i.host;else break;if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let i of this.scrollTargets)i.removeEventListener("scroll",this.onScroll);for(let i of this.scrollTargets=e)i.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,qd),As&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),As&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;if(!this.delayedAndroidKey){let n=()=>{let r=this.delayedAndroidKey;r&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=r.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&r.force&&si(this.dom,r.key,r.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(n)}(!this.delayedAndroidKey||t=="Enter")&&(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(!((i=this.delayedAndroidKey)===null||i===void 0)&&i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,n=!1;for(let r of t){let o=this.readMutation(r);o&&(o.typeOver&&(n=!0),e==-1?{from:e,to:i}=o:(e=Math.min(o.from,e),i=Math.max(o.to,i)))}return{from:e,to:i,typeOver:n}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),n=this.selectionChanged&&Ln(this.dom,this.selectionRange);if(t<0&&!n)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let r=new hd(this.view,t,e,i);return this.view.docView.domChanged={newSel:r.newSel?r.newSel.main:null},r}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,n=Gh(this.view,e);return this.view.state==i&&(e.domChanged||e.newSel&&!e.newSel.main.eq(this.view.state.selection.main))&&this.view.update([]),n}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty(t.type=="attributes"),t.type=="attributes"&&(e.flags|=4),t.type=="childList"){let i=kl(e,t.previousSibling||t.target.previousSibling,-1),n=kl(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:n?e.posBefore(n):e.posAtEnd,typeOver:!1}}else return t.type=="characterData"?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener?this.printQuery.addEventListener("change",this.onPrint):this.printQuery.addListener(this.onPrint):t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener?this.printQuery.removeEventListener("change",this.onPrint):this.printQuery.removeListener(this.onPrint):t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}update(t){this.editContext&&(this.editContext.update(t),t.startState.facet(de)!=t.state.facet(de)&&(t.view.contentDOM.editContext=t.state.facet(de)?this.editContext.editContext:null))}destroy(){var t,e,i;this.stop(),(t=this.intersection)===null||t===void 0||t.disconnect(),(e=this.gapIntersection)===null||e===void 0||e.disconnect(),(i=this.resizeScroll)===null||i===void 0||i.disconnect();for(let n of this.scrollTargets)n.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey),this.editContext&&(this.view.contentDOM.editContext=null,this.editContext.destroy())}}function kl(s,t,e){for(;t;){let i=K.get(t);if(i&&i.parent==s)return i;let n=t.parentNode;t=n!=s.dom?n:e>0?t.nextSibling:t.previousSibling}return null}function Sl(s,t){let e=t.startContainer,i=t.startOffset,n=t.endContainer,r=t.endOffset,o=s.docView.domAtPos(s.state.selection.main.anchor);return Li(o.node,o.offset,n,r)&&([e,i,n,r]=[n,r,e,i]),{anchorNode:e,anchorOffset:i,focusNode:n,focusOffset:r}}function Kd(s,t){if(t.getComposedRanges){let n=t.getComposedRanges(s.root)[0];if(n)return Sl(s,n)}let e=null;function i(n){n.preventDefault(),n.stopImmediatePropagation(),e=n.getTargetRanges()[0]}return s.contentDOM.addEventListener("beforeinput",i,!0),s.dom.ownerDocument.execCommand("indent"),s.contentDOM.removeEventListener("beforeinput",i,!0),e?Sl(s,e):null}class Ud{constructor(t){this.from=0,this.to=0,this.pendingContextChange=null,this.handlers=Object.create(null),this.composing=null,this.resetRange(t.state);let e=this.editContext=new window.EditContext({text:t.state.doc.sliceString(this.from,this.to),selectionStart:this.toContextPos(Math.max(this.from,Math.min(this.to,t.state.selection.main.anchor))),selectionEnd:this.toContextPos(t.state.selection.main.head)});this.handlers.textupdate=i=>{let n=t.state.selection.main,{anchor:r,head:o}=n,l=this.toEditorPos(i.updateRangeStart),a=this.toEditorPos(i.updateRangeEnd);t.inputState.composing>=0&&!this.composing&&(this.composing={contextBase:i.updateRangeStart,editorBase:l,drifted:!1});let h={from:l,to:a,insert:F.of(i.text.split(`
`))};if(h.from==this.from&&r<this.from?h.from=r:h.to==this.to&&r>this.to&&(h.to=r),h.from==h.to&&!h.insert.length){let c=v.single(this.toEditorPos(i.selectionStart),this.toEditorPos(i.selectionEnd));c.main.eq(n)||t.dispatch({selection:c,userEvent:"select"});return}if((T.mac||T.android)&&h.from==o-1&&/^\. ?$/.test(i.text)&&t.contentDOM.getAttribute("autocorrect")=="off"&&(h={from:l,to:a,insert:F.of([i.text.replace("."," ")])}),this.pendingContextChange=h,!t.state.readOnly){let c=this.to-this.from+(h.to-h.from+h.insert.length);oo(t,h,v.single(this.toEditorPos(i.selectionStart,c),this.toEditorPos(i.selectionEnd,c)))}this.pendingContextChange&&(this.revertPending(t.state),this.setSelection(t.state))},this.handlers.characterboundsupdate=i=>{let n=[],r=null;for(let o=this.toEditorPos(i.rangeStart),l=this.toEditorPos(i.rangeEnd);o<l;o++){let a=t.coordsForChar(o);r=a&&new DOMRect(a.left,a.top,a.right-a.left,a.bottom-a.top)||r||new DOMRect,n.push(r)}e.updateCharacterBounds(i.rangeStart,n)},this.handlers.textformatupdate=i=>{let n=[];for(let r of i.getTextFormats()){let o=r.underlineStyle,l=r.underlineThickness;if(o!="None"&&l!="None"){let a=this.toEditorPos(r.rangeStart),h=this.toEditorPos(r.rangeEnd);if(a<h){let c=`text-decoration: underline ${o=="Dashed"?"dashed ":o=="Squiggle"?"wavy ":""}${l=="Thin"?1:2}px`;n.push(I.mark({attributes:{style:c}}).range(a,h))}}}t.dispatch({effects:Wh.of(I.set(n))})},this.handlers.compositionstart=()=>{t.inputState.composing<0&&(t.inputState.composing=0,t.inputState.compositionFirstChange=!0)},this.handlers.compositionend=()=>{if(t.inputState.composing=-1,t.inputState.compositionFirstChange=null,this.composing){let{drifted:i}=this.composing;this.composing=null,i&&this.reset(t.state)}};for(let i in this.handlers)e.addEventListener(i,this.handlers[i]);this.measureReq={read:i=>{this.editContext.updateControlBounds(i.contentDOM.getBoundingClientRect());let n=Vi(i.root);n&&n.rangeCount&&this.editContext.updateSelectionBounds(n.getRangeAt(0).getBoundingClientRect())}}}applyEdits(t){let e=0,i=!1,n=this.pendingContextChange;return t.changes.iterChanges((r,o,l,a,h)=>{if(i)return;let c=h.length-(o-r);if(n&&o>=n.to)if(n.from==r&&n.to==o&&n.insert.eq(h)){n=this.pendingContextChange=null,e+=c,this.to+=c;return}else n=null,this.revertPending(t.state);if(r+=e,o+=e,o<=this.from)this.from+=c,this.to+=c;else if(r<this.to){if(r<this.from||o>this.to||this.to-this.from+h.length>3e4){i=!0;return}this.editContext.updateText(this.toContextPos(r),this.toContextPos(o),h.toString()),this.to+=c}e+=c}),n&&!i&&this.revertPending(t.state),!i}update(t){let e=this.pendingContextChange,i=t.startState.selection.main;this.composing&&(this.composing.drifted||!t.changes.touchesRange(i.from,i.to)&&t.transactions.some(n=>!n.isUserEvent("input.type")&&n.changes.touchesRange(this.from,this.to)))?(this.composing.drifted=!0,this.composing.editorBase=t.changes.mapPos(this.composing.editorBase)):!this.applyEdits(t)||!this.rangeIsValid(t.state)?(this.pendingContextChange=null,this.reset(t.state)):(t.docChanged||t.selectionSet||e)&&this.setSelection(t.state),(t.geometryChanged||t.docChanged||t.selectionSet)&&t.view.requestMeasure(this.measureReq)}resetRange(t){let{head:e}=t.selection.main;this.from=Math.max(0,e-1e4),this.to=Math.min(t.doc.length,e+1e4)}reset(t){this.resetRange(t),this.editContext.updateText(0,this.editContext.text.length,t.doc.sliceString(this.from,this.to)),this.setSelection(t)}revertPending(t){let e=this.pendingContextChange;this.pendingContextChange=null,this.editContext.updateText(this.toContextPos(e.from),this.toContextPos(e.from+e.insert.length),t.doc.sliceString(e.from,e.to))}setSelection(t){let{main:e}=t.selection,i=this.toContextPos(Math.max(this.from,Math.min(this.to,e.anchor))),n=this.toContextPos(e.head);(this.editContext.selectionStart!=i||this.editContext.selectionEnd!=n)&&this.editContext.updateSelection(i,n)}rangeIsValid(t){let{head:e}=t.selection.main;return!(this.from>0&&e-this.from<500||this.to<t.doc.length&&this.to-e<500||this.to-this.from>1e4*3)}toEditorPos(t,e=this.to-this.from){t=Math.min(t,e);let i=this.composing;return i&&i.drifted?i.editorBase+(t-i.contextBase):t+this.from}toContextPos(t){let e=this.composing;return e&&e.drifted?e.contextBase+(t-e.editorBase):t-this.from}destroy(){for(let t in this.handlers)this.editContext.removeEventListener(t,this.handlers[t])}}class B{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}get compositionStarted(){return this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){var e;this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:i}=t;this.dispatchTransactions=t.dispatchTransactions||i&&(n=>n.forEach(r=>i(r,this)))||(n=>this.update(n)),this.dispatch=this.dispatch.bind(this),this._root=t.root||Ou(t.parent)||document,this.viewState=new xl(t.state||V.create(t)),t.scrollTo&&t.scrollTo.is(fn)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(Di).map(n=>new vs(n));for(let n of this.plugins)n.update(this);this.observer=new jd(this),this.inputState=new pd(this),this.inputState.ensureHandlers(this.plugins),this.docView=new el(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),!((e=document.fonts)===null||e===void 0)&&e.ready&&document.fonts.ready.then(()=>this.requestMeasure())}dispatch(...t){let e=t.length==1&&t[0]instanceof et?t:t.length==1&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e=!1,i=!1,n,r=this.state;for(let u of t){if(u.startState!=r)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");r=u.state}if(this.destroyed){this.viewState.state=r;return}let o=this.hasFocus,l=0,a=null;t.some(u=>u.annotation(tc))?(this.inputState.notifiedFocused=o,l=1):o!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=o,a=ec(r,o),a||(l=1));let h=this.observer.delayedAndroidKey,c=null;if(h?(this.observer.clearDelayedAndroidKey(),c=this.observer.readChange(),(c&&!this.state.doc.eq(r.doc)||!this.state.selection.eq(r.selection))&&(c=null)):this.observer.clear(),r.facet(V.phrases)!=this.state.facet(V.phrases))return this.setState(r);n=Un.create(this,r,t),n.flags|=l;let f=this.viewState.scrollTarget;try{this.updateState=2;for(let u of t){if(f&&(f=f.map(u.changes)),u.scrollIntoView){let{main:d}=u.state.selection;f=new ri(d.empty?d:v.cursor(d.head,d.head>d.anchor?-1:1))}for(let d of u.effects)d.is(fn)&&(f=d.value.clip(this.state))}this.viewState.update(n,f),this.bidiCache=Yn.update(this.bidiCache,n.changes),n.empty||(this.updatePlugins(n),this.inputState.update(n)),e=this.docView.update(n),this.state.facet(Ti)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(e,t.some(u=>u.isUserEvent("select.pointer")))}finally{this.updateState=0}if(n.startState.facet(bn)!=n.state.facet(bn)&&(this.viewState.mustMeasureContent=!0),(e||i||f||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),e&&this.docViewUpdate(),!n.empty)for(let u of this.state.facet(wr))try{u(n)}catch(d){Mt(this.state,d,"update listener")}(a||c)&&Promise.resolve().then(()=>{a&&this.state==a.startState&&this.dispatch(a),c&&!Gh(this,c)&&h.force&&si(this.contentDOM,h.key,h.keyCode)})}setState(t){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=t;return}this.updateState=2;let e=this.hasFocus;try{for(let i of this.plugins)i.destroy(this);this.viewState=new xl(t),this.plugins=t.facet(Di).map(i=>new vs(i)),this.pluginMap.clear();for(let i of this.plugins)i.update(this);this.docView.destroy(),this.docView=new el(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(Di),i=t.state.facet(Di);if(e!=i){let n=[];for(let r of i){let o=e.indexOf(r);if(o<0)n.push(new vs(r));else{let l=this.plugins[o];l.mustUpdate=t,n.push(l)}}for(let r of this.plugins)r.mustUpdate!=t&&r.destroy(this);this.plugins=n,this.pluginMap.clear()}else for(let n of this.plugins)n.mustUpdate=t;for(let n=0;n<this.plugins.length;n++)this.plugins[n].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let t of this.plugins){let e=t.value;if(e&&e.docViewUpdate)try{e.docViewUpdate(this)}catch(i){Mt(this.state,i,"doc view update listener")}}}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey){this.measureScheduled=-1,this.requestMeasure();return}this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,n=i.scrollTop*this.scaleY,{scrollAnchorPos:r,scrollAnchorHeight:o}=this.viewState;Math.abs(n-this.viewState.scrollTop)>1&&(o=-1),this.viewState.scrollAnchorHeight=-1;try{for(let l=0;;l++){if(o<0)if(mh(i))r=-1,o=this.viewState.heightMap.height;else{let d=this.viewState.scrollAnchorAt(n);r=d.from,o=d.top}this.updateState=1;let a=this.viewState.measure(this);if(!a&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(l>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let h=[];a&4||([this.measureRequests,h]=[h,this.measureRequests]);let c=h.map(d=>{try{return d.read(this)}catch(p){return Mt(this.state,p),Cl}}),f=Un.create(this,this.state,[]),u=!1;f.flags|=a,e?e.flags|=a:e=f,this.updateState=2,f.empty||(this.updatePlugins(f),this.inputState.update(f),this.updateAttrs(),u=this.docView.update(f),u&&this.docViewUpdate());for(let d=0;d<h.length;d++)if(c[d]!=Cl)try{let p=h[d];p.write&&p.write(c[d],this)}catch(p){Mt(this.state,p)}if(u&&this.docView.updateSelection(!0),!f.viewportChanged&&this.measureRequests.length==0){if(this.viewState.editorHeight)if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,o=-1;continue}else{let p=(r<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(r).top)-o;if(p>1||p<-1){n=n+p,i.scrollTop=n/this.scaleY,o=-1;continue}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let l of this.state.facet(wr))l(e)}get themeClasses(){return Cr+" "+(this.state.facet(Sr)?rc:sc)+" "+this.state.facet(bn)}updateAttrs(){let t=Al(this,zh,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",writingsuggestions:"false",translate:"no",contenteditable:this.state.facet(de)?"true":"false",class:"cm-content",style:`${T.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),Al(this,no,e);let i=this.observer.ignore(()=>{let n=pr(this.contentDOM,this.contentAttrs,e),r=pr(this.dom,this.editorAttrs,t);return n||r});return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let n of i.effects)if(n.is(B.announce)){e&&(this.announceDOM.textContent=""),e=!1;let r=this.announceDOM.appendChild(document.createElement("div"));r.textContent=n.value}}mountStyles(){this.styleModules=this.state.facet(Ti);let t=this.state.facet(B.cspNonce);Ae.mount(this.root,this.styleModules.concat($d).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");this.updateState==0&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),t){if(this.measureRequests.indexOf(t)>-1)return;if(t.key!=null){for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key){this.measureRequests[e]=t;return}}this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(e===void 0||e&&e.spec!=t)&&this.pluginMap.set(t,e=this.plugins.find(i=>i.spec==t)||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return Ss(this,t,ol(this,t,e,i))}moveByGroup(t,e){return Ss(this,t,ol(this,t,e,i=>rd(this,t.head,i)))}visualLineSide(t,e){let i=this.bidiSpans(t),n=this.textDirectionAt(t.from),r=i[e?i.length-1:0];return v.cursor(r.side(e,n)+t.from,r.forward(!e,n)?1:-1)}moveToLineBoundary(t,e,i=!0){return sd(this,t,e,i)}moveVertically(t,e,i){return Ss(this,t,od(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),Uh(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(t),r=this.bidiSpans(n),o=r[Se.find(r,t-n.from,-1,e)];return Qi(i,o.dir==Y.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(Fh)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>Gd)return Bh(t.length);let e=this.textDirectionAt(t.from),i;for(let r of this.bidiCache)if(r.from==t.from&&r.dir==e&&(r.fresh||Oh(r.isolates,i=tl(this,t))))return r.order;i||(i=tl(this,t));let n=zu(t.text,e,i);return this.bidiCache.push(new Yn(t.from,t.to,e,i,!0,n)),n}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||T.safari&&((t=this.inputState)===null||t===void 0?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{dh(this.contentDOM),this.docView.updateSelection()})}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((t.nodeType==9?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){this.root.activeElement==this.contentDOM&&this.contentDOM.blur();for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return fn.of(new ri(typeof t=="number"?v.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return fn.of(new ri(v.cursor(i.from),"start","start",i.top-t,e,!0))}setTabFocusMode(t){t==null?this.inputState.tabFocusMode=this.inputState.tabFocusMode<0?0:-1:typeof t=="boolean"?this.inputState.tabFocusMode=t?0:-1:this.inputState.tabFocusMode!=0&&(this.inputState.tabFocusMode=Date.now()+t)}static domEventHandlers(t){return ft.define(()=>({}),{eventHandlers:t})}static domEventObservers(t){return ft.define(()=>({}),{eventObservers:t})}static theme(t,e){let i=Ae.newName(),n=[bn.of(i),Ti.of(Ar(`.${i}`,t))];return e&&e.dark&&n.push(Sr.of(!0)),n}static baseTheme(t){return Ye.lowest(Ti.of(Ar("."+Cr,t,oc)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),n=i&&K.get(i)||K.get(t);return((e=n?.rootView)===null||e===void 0?void 0:e.view)||null}}B.styleModule=Ti;B.inputHandler=Ih;B.clipboardInputFilter=eo;B.clipboardOutputFilter=io;B.scrollHandler=Vh;B.focusChangeEffect=Nh;B.perLineTextDirection=Fh;B.exceptionSink=_h;B.updateListener=wr;B.editable=de;B.mouseSelectionStyle=Rh;B.dragMovesSelection=Lh;B.clickAddsSelectionRange=Eh;B.decorations=Wi;B.outerDecorations=$h;B.atomicRanges=so;B.bidiIsolatedRanges=qh;B.scrollMargins=jh;B.darkTheme=Sr;B.cspNonce=O.define({combine:s=>s.length?s[0]:""});B.contentAttributes=no;B.editorAttributes=zh;B.lineWrapping=B.contentAttributes.of({class:"cm-lineWrapping"});B.announce=_.define();const Gd=4096,Cl={};class Yn{constructor(t,e,i,n,r,o){this.from=t,this.to=e,this.dir=i,this.isolates=n,this.fresh=r,this.order=o}static update(t,e){if(e.empty&&!t.some(r=>r.fresh))return t;let i=[],n=t.length?t[t.length-1].dir:Y.LTR;for(let r=Math.max(0,t.length-10);r<t.length;r++){let o=t[r];o.dir==n&&!e.touchesRange(o.from,o.to)&&i.push(new Yn(e.mapPos(o.from,1),e.mapPos(o.to,-1),o.dir,o.isolates,!1,o.order))}return i}}function Al(s,t,e){for(let i=s.state.facet(t),n=i.length-1;n>=0;n--){let r=i[n],o=typeof r=="function"?r(s):r;o&&dr(o,e)}return e}const Yd=T.mac?"mac":T.windows?"win":T.linux?"linux":"key";function Jd(s,t){const e=s.split(/-(?!$)/);let i=e[e.length-1];i=="Space"&&(i=" ");let n,r,o,l;for(let a=0;a<e.length-1;++a){const h=e[a];if(/^(cmd|meta|m)$/i.test(h))l=!0;else if(/^a(lt)?$/i.test(h))n=!0;else if(/^(c|ctrl|control)$/i.test(h))r=!0;else if(/^s(hift)?$/i.test(h))o=!0;else if(/^mod$/i.test(h))t=="mac"?l=!0:r=!0;else throw new Error("Unrecognized modifier name: "+h)}return n&&(i="Alt-"+i),r&&(i="Ctrl-"+i),l&&(i="Meta-"+i),o&&(i="Shift-"+i),i}function yn(s,t,e){return t.altKey&&(s="Alt-"+s),t.ctrlKey&&(s="Ctrl-"+s),t.metaKey&&(s="Meta-"+s),e!==!1&&t.shiftKey&&(s="Shift-"+s),s}const Xd=Ye.default(B.domEventHandlers({keydown(s,t){return ep(Qd(t.state),s,t,"editor")}})),en=O.define({enables:Xd}),Ml=new WeakMap;function Qd(s){let t=s.facet(en),e=Ml.get(t);return e||Ml.set(t,e=tp(t.reduce((i,n)=>i.concat(n),[]))),e}let xe=null;const Zd=4e3;function tp(s,t=Yd){let e=Object.create(null),i=Object.create(null),n=(o,l)=>{let a=i[o];if(a==null)i[o]=l;else if(a!=l)throw new Error("Key binding "+o+" is used both as a regular binding and as a multi-stroke prefix")},r=(o,l,a,h,c)=>{var f,u;let d=e[o]||(e[o]=Object.create(null)),p=l.split(/ (?!$)/).map(y=>Jd(y,t));for(let y=1;y<p.length;y++){let w=p.slice(0,y).join(" ");n(w,!0),d[w]||(d[w]={preventDefault:!0,stopPropagation:!1,run:[S=>{let k=xe={view:S,prefix:w,scope:o};return setTimeout(()=>{xe==k&&(xe=null)},Zd),!0}]})}let m=p.join(" ");n(m,!1);let g=d[m]||(d[m]={preventDefault:!1,stopPropagation:!1,run:((u=(f=d._any)===null||f===void 0?void 0:f.run)===null||u===void 0?void 0:u.slice())||[]});a&&g.run.push(a),h&&(g.preventDefault=!0),c&&(g.stopPropagation=!0)};for(let o of s){let l=o.scope?o.scope.split(" "):["editor"];if(o.any)for(let h of l){let c=e[h]||(e[h]=Object.create(null));c._any||(c._any={preventDefault:!1,stopPropagation:!1,run:[]});let{any:f}=o;for(let u in c)c[u].run.push(d=>f(d,Mr))}let a=o[t]||o.key;if(a)for(let h of l)r(h,a,o.run,o.preventDefault,o.stopPropagation),o.shift&&r(h,"Shift-"+a,o.shift,o.preventDefault,o.stopPropagation)}return e}let Mr=null;function ep(s,t,e,i){Mr=t;let n=Cu(t),r=Pt(n,0),o=ue(r)==n.length&&n!=" ",l="",a=!1,h=!1,c=!1;xe&&xe.view==e&&xe.scope==i&&(l=xe.prefix+" ",Jh.indexOf(t.keyCode)<0&&(h=!0,xe=null));let f=new Set,u=g=>{if(g){for(let y of g.run)if(!f.has(y)&&(f.add(y),y(e)))return g.stopPropagation&&(c=!0),!0;g.preventDefault&&(g.stopPropagation&&(c=!0),h=!0)}return!1},d=s[i],p,m;return d&&(u(d[l+yn(n,t,!o)])?a=!0:o&&(t.altKey||t.metaKey||t.ctrlKey)&&!(T.windows&&t.ctrlKey&&t.altKey)&&(p=Me[t.keyCode])&&p!=n?(u(d[l+yn(p,t,!0)])||t.shiftKey&&(m=Hi[t.keyCode])!=n&&m!=p&&u(d[l+yn(m,t,!1)]))&&(a=!0):o&&t.shiftKey&&u(d[l+yn(n,t,!0)])&&(a=!0),!a&&u(d._any)&&(a=!0)),h&&(a=!0),a&&c&&t.stopPropagation(),Mr=null,a}class nn{constructor(t,e,i,n,r){this.className=t,this.left=e,this.top=i,this.width=n,this.height=r}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className!=this.className?!1:(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",this.width!=null&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(i.empty){let n=t.coordsAtPos(i.head,i.assoc||1);if(!n)return[];let r=lc(t);return[new nn(e,n.left-r.left,n.top-r.top,null,n.bottom-n.top)]}else return ip(t,e,i)}}function lc(s){let t=s.scrollDOM.getBoundingClientRect();return{left:(s.textDirection==Y.LTR?t.left:t.right-s.scrollDOM.clientWidth*s.scaleX)-s.scrollDOM.scrollLeft*s.scaleX,top:t.top-s.scrollDOM.scrollTop*s.scaleY}}function Dl(s,t,e,i){let n=s.coordsAtPos(t,e*2);if(!n)return i;let r=s.dom.getBoundingClientRect(),o=(n.top+n.bottom)/2,l=s.posAtCoords({x:r.left+1,y:o}),a=s.posAtCoords({x:r.right-1,y:o});return l==null||a==null?i:{from:Math.max(i.from,Math.min(l,a)),to:Math.min(i.to,Math.max(l,a))}}function ip(s,t,e){if(e.to<=s.viewport.from||e.from>=s.viewport.to)return[];let i=Math.max(e.from,s.viewport.from),n=Math.min(e.to,s.viewport.to),r=s.textDirection==Y.LTR,o=s.contentDOM,l=o.getBoundingClientRect(),a=lc(s),h=o.querySelector(".cm-line"),c=h&&window.getComputedStyle(h),f=l.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=l.right-(c?parseInt(c.paddingRight):0),d=vr(s,i),p=vr(s,n),m=d.type==wt.Text?d:null,g=p.type==wt.Text?p:null;if(m&&(s.lineWrapping||d.widgetLineBreaks)&&(m=Dl(s,i,1,m)),g&&(s.lineWrapping||p.widgetLineBreaks)&&(g=Dl(s,n,-1,g)),m&&g&&m.from==g.from&&m.to==g.to)return w(S(e.from,e.to,m));{let x=m?S(e.from,null,m):k(d,!1),C=g?S(null,e.to,g):k(p,!0),A=[];return(m||d).to<(g||p).from-(m&&g?1:0)||d.widgetLineBreaks>1&&x.bottom+s.defaultLineHeight/2<C.top?A.push(y(f,x.bottom,u,C.top)):x.bottom<C.top&&s.elementAtHeight((x.bottom+C.top)/2).type==wt.Text&&(x.bottom=C.top=(x.bottom+C.top)/2),w(x).concat(A).concat(w(C))}function y(x,C,A,L){return new nn(t,x-a.left,C-a.top,A-x,L-C)}function w({top:x,bottom:C,horizontal:A}){let L=[];for(let R=0;R<A.length;R+=2)L.push(y(A[R],x,A[R+1],C));return L}function S(x,C,A){let L=1e9,R=-1e9,$=[];function M(H,q,ot,bt,Lt){let it=s.coordsAtPos(H,H==A.to?-2:2),kt=s.coordsAtPos(ot,ot==A.from?2:-2);!it||!kt||(L=Math.min(it.top,kt.top,L),R=Math.max(it.bottom,kt.bottom,R),Lt==Y.LTR?$.push(r&&q?f:it.left,r&&bt?u:kt.right):$.push(!r&&bt?f:kt.left,!r&&q?u:it.right))}let E=x??A.from,W=C??A.to;for(let H of s.visibleRanges)if(H.to>E&&H.from<W)for(let q=Math.max(H.from,E),ot=Math.min(H.to,W);;){let bt=s.state.doc.lineAt(q);for(let Lt of s.bidiSpans(bt)){let it=Lt.from+bt.from,kt=Lt.to+bt.from;if(it>=ot)break;kt>q&&M(Math.max(it,q),x==null&&it<=E,Math.min(kt,ot),C==null&&kt>=W,Lt.dir)}if(q=bt.to+1,q>=ot)break}return $.length==0&&M(E,x==null,W,C==null,s.textDirection),{top:L,bottom:R,horizontal:$}}function k(x,C){let A=l.top+(C?x.top:x.bottom);return{top:A,bottom:A,horizontal:[]}}}function np(s,t){return s.constructor==t.constructor&&s.eq(t)}class sp{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t)}update(t){t.startState.facet(Nn)!=t.state.facet(Nn)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq))}docViewUpdate(t){this.layer.updateOnDocViewUpdate!==!1&&t.requestMeasure(this.measureReq)}setOrder(t){let e=0,i=t.facet(Nn);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;(t!=this.scaleX||e!=this.scaleY)&&(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`)}draw(t){if(t.length!=this.drawn.length||t.some((e,i)=>!np(e,this.drawn[i]))){let e=this.dom.firstChild,i=0;for(let n of t)n.update&&e&&n.constructor&&this.drawn[i].constructor&&n.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(n.draw(),e);for(;e;){let n=e.nextSibling;e.remove(),e=n}this.drawn=t}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}const Nn=O.define();function ac(s){return[ft.define(t=>new sp(t,s)),Nn.of(s)]}const hc=!(T.ios&&T.webkit&&T.webkit_version<534),zi=O.define({combine(s){return Je(s,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})}});function rp(s={}){return[zi.of(s),op,lp,ap,Hh.of(!0)]}function cc(s){return s.startState.facet(zi)!=s.state.facet(zi)}const op=ac({above:!0,markers(s){let{state:t}=s,e=t.facet(zi),i=[];for(let n of t.selection.ranges){let r=n==t.selection.main;if(n.empty?!r||hc:e.drawRangeCursor){let o=r?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",l=n.empty?n:v.cursor(n.head,n.head>n.anchor?-1:1);for(let a of nn.forRange(s,o,l))i.push(a)}}return i},update(s,t){s.transactions.some(i=>i.selection)&&(t.style.animationName=t.style.animationName=="cm-blink"?"cm-blink2":"cm-blink");let e=cc(s);return e&&Tl(s.state,t),s.docChanged||s.selectionSet||e},mount(s,t){Tl(t.state,s)},class:"cm-cursorLayer"});function Tl(s,t){t.style.animationDuration=s.facet(zi).cursorBlinkRate+"ms"}const lp=ac({above:!1,markers(s){return s.state.selection.ranges.map(t=>t.empty?[]:nn.forRange(s,"cm-selectionBackground",t)).reduce((t,e)=>t.concat(e))},update(s,t){return s.docChanged||s.selectionSet||s.viewportChanged||cc(s)},class:"cm-selectionLayer"}),Dr={".cm-line":{"& ::selection, &::selection":{backgroundColor:"transparent !important"}},".cm-content":{"& :focus":{caretColor:"initial !important","&::selection, & ::selection":{backgroundColor:"Highlight !important"}}}};hc&&(Dr[".cm-line"].caretColor=Dr[".cm-content"].caretColor="transparent !important");const ap=Ye.highest(B.theme(Dr));function Ol(s,t,e,i,n){t.lastIndex=0;for(let r=s.iterRange(e,i),o=e,l;!r.next().done;o+=r.value.length)if(!r.lineBreak)for(;l=t.exec(r.value);)n(o+l.index,l)}function hp(s,t){let e=s.visibleRanges;if(e.length==1&&e[0].from==s.viewport.from&&e[0].to==s.viewport.to)return e;let i=[];for(let{from:n,to:r}of e)n=Math.max(s.state.doc.lineAt(n).from,n-t),r=Math.min(s.state.doc.lineAt(r).to,r+t),i.length&&i[i.length-1].to>=n?i[i.length-1].to=r:i.push({from:n,to:r});return i}class cp{constructor(t){const{regexp:e,decoration:i,decorate:n,boundary:r,maxLength:o=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,n)this.addMatch=(l,a,h,c)=>n(c,h,h+l[0].length,l,a);else if(typeof i=="function")this.addMatch=(l,a,h,c)=>{let f=i(l,a,h);f&&c(h,h+l[0].length,f)};else if(i)this.addMatch=(l,a,h,c)=>c(h,h+l[0].length,i);else throw new RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=r,this.maxLength=o}createDeco(t){let e=new Ce,i=e.add.bind(e);for(let{from:n,to:r}of hp(t,this.maxLength))Ol(t.state.doc,this.regexp,n,r,(o,l)=>this.addMatch(l,t,o,i));return e.finish()}updateDeco(t,e){let i=1e9,n=-1;return t.docChanged&&t.changes.iterChanges((r,o,l,a)=>{a>=t.view.viewport.from&&l<=t.view.viewport.to&&(i=Math.min(l,i),n=Math.max(a,n))}),t.viewportMoved||n-i>1e3?this.createDeco(t.view):n>-1?this.updateRange(t.view,e.map(t.changes),i,n):e}updateRange(t,e,i,n){for(let r of t.visibleRanges){let o=Math.max(r.from,i),l=Math.min(r.to,n);if(l>o){let a=t.state.doc.lineAt(o),h=a.to<l?t.state.doc.lineAt(l):a,c=Math.max(r.from,a.from),f=Math.min(r.to,h.to);if(this.boundary){for(;o>a.from;o--)if(this.boundary.test(a.text[o-1-a.from])){c=o;break}for(;l<h.to;l++)if(this.boundary.test(h.text[l-h.from])){f=l;break}}let u=[],d,p=(m,g,y)=>u.push(y.range(m,g));if(a==h)for(this.regexp.lastIndex=c-a.from;(d=this.regexp.exec(a.text))&&d.index<f-a.from;)this.addMatch(d,t,d.index+a.from,p);else Ol(t.state.doc,this.regexp,c,f,(m,g)=>this.addMatch(g,t,m,p));e=e.update({filterFrom:c,filterTo:f,filter:(m,g)=>m<c||g>f,add:u})}}return e}}const Tr=/x/.unicode!=null?"gu":"g",fp=new RegExp(`[\0-\b
--­؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]`,Tr),up={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let Ms=null;function dp(){var s;if(Ms==null&&typeof document<"u"&&document.body){let t=document.body.style;Ms=((s=t.tabSize)!==null&&s!==void 0?s:t.MozTabSize)!=null}return Ms||!1}const Fn=O.define({combine(s){let t=Je(s,{render:null,specialChars:fp,addSpecialChars:null});return(t.replaceTabs=!dp())&&(t.specialChars=new RegExp("	|"+t.specialChars.source,Tr)),t.addSpecialChars&&(t.specialChars=new RegExp(t.specialChars.source+"|"+t.addSpecialChars.source,Tr)),t}});function pp(s={}){return[Fn.of(s),mp()]}let Bl=null;function mp(){return Bl||(Bl=ft.fromClass(class{constructor(s){this.view=s,this.decorations=I.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(s.state.facet(Fn)),this.decorations=this.decorator.createDeco(s)}makeDecorator(s){return new cp({regexp:s.specialChars,decoration:(t,e,i)=>{let{doc:n}=e.state,r=Pt(t[0],0);if(r==9){let o=n.lineAt(i),l=e.state.tabSize,a=mi(o.text,l,i-o.from);return I.replace({widget:new wp((l-a%l)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[r]||(this.decorationCache[r]=I.replace({widget:new yp(s,r)}))},boundary:s.replaceTabs?void 0:/[^]/})}update(s){let t=s.state.facet(Fn);s.startState.facet(Fn)!=t?(this.decorator=this.makeDecorator(t),this.decorations=this.decorator.createDeco(s.view)):this.decorations=this.decorator.updateDeco(s,this.decorations)}},{decorations:s=>s.decorations}))}const gp="•";function bp(s){return s>=32?gp:s==10?"␤":String.fromCharCode(9216+s)}class yp extends he{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){let e=bp(this.code),i=t.state.phrase("Control character")+" "+(up[this.code]||"0x"+this.code.toString(16)),n=this.options.render&&this.options.render(this.code,i,e);if(n)return n;let r=document.createElement("span");return r.textContent=e,r.title=i,r.setAttribute("aria-label",i),r.className="cm-specialChar",r}ignoreEvent(){return!1}}class wp extends he{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="	",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}class xp extends he{constructor(t){super(),this.content=t}toDOM(t){let e=document.createElement("span");return e.className="cm-placeholder",e.style.pointerEvents="none",e.appendChild(typeof this.content=="string"?document.createTextNode(this.content):typeof this.content=="function"?this.content(t):this.content.cloneNode(!0)),typeof this.content=="string"?e.setAttribute("aria-label","placeholder "+this.content):e.setAttribute("aria-hidden","true"),e}coordsAt(t){let e=t.firstChild?ai(t.firstChild):[];if(!e.length)return null;let i=window.getComputedStyle(t.parentNode),n=Qi(e[0],i.direction!="rtl"),r=parseInt(i.lineHeight);return n.bottom-n.top>r*1.5?{left:n.left,right:n.right,top:n.top,bottom:n.top+r}:n}ignoreEvent(){return!1}}function vp(s){return ft.fromClass(class{constructor(t){this.view=t,this.placeholder=s?I.set([I.widget({widget:new xp(s),side:1}).range(0)]):I.none}get decorations(){return this.view.state.doc.length?I.none:this.placeholder}},{decorations:t=>t.decorations})}const Or=2e3;function kp(s,t,e){let i=Math.min(t.line,e.line),n=Math.max(t.line,e.line),r=[];if(t.off>Or||e.off>Or||t.col<0||e.col<0){let o=Math.min(t.off,e.off),l=Math.max(t.off,e.off);for(let a=i;a<=n;a++){let h=s.doc.line(a);h.length<=l&&r.push(v.range(h.from+o,h.to+l))}}else{let o=Math.min(t.col,e.col),l=Math.max(t.col,e.col);for(let a=i;a<=n;a++){let h=s.doc.line(a),c=or(h.text,o,s.tabSize,!0);if(c<0)r.push(v.cursor(h.to));else{let f=or(h.text,l,s.tabSize);r.push(v.range(h.from+c,h.from+f))}}}return r}function Sp(s,t){let e=s.coordsAtPos(s.viewport.from);return e?Math.round(Math.abs((e.left-t)/s.defaultCharacterWidth)):-1}function Pl(s,t){let e=s.posAtCoords({x:t.clientX,y:t.clientY},!1),i=s.state.doc.lineAt(e),n=e-i.from,r=n>Or?-1:n==i.length?Sp(s,t.clientX):mi(i.text,s.state.tabSize,e-i.from);return{line:i.number,col:r,off:n}}function Cp(s,t){let e=Pl(s,t),i=s.state.selection;return e?{update(n){if(n.docChanged){let r=n.changes.mapPos(n.startState.doc.line(e.line).from),o=n.state.doc.lineAt(r);e={line:o.number,col:e.col,off:Math.min(e.off,o.length)},i=i.map(n.changes)}},get(n,r,o){let l=Pl(s,n);if(!l)return i;let a=kp(s.state,e,l);return a.length?o?v.create(a.concat(i.ranges)):v.create(a):i}}:null}function Ap(s){let t=e=>e.altKey&&e.button==0;return B.mouseSelectionStyle.of((e,i)=>t(i)?Cp(e,i):null)}const Mp={Alt:[18,s=>!!s.altKey],Control:[17,s=>!!s.ctrlKey],Shift:[16,s=>!!s.shiftKey],Meta:[91,s=>!!s.metaKey]},Dp={style:"cursor: crosshair"};function Tp(s={}){let[t,e]=Mp[s.key||"Alt"],i=ft.fromClass(class{constructor(n){this.view=n,this.isDown=!1}set(n){this.isDown!=n&&(this.isDown=n,this.view.update([]))}},{eventObservers:{keydown(n){this.set(n.keyCode==t||e(n))},keyup(n){(n.keyCode==t||!e(n))&&this.set(!1)},mousemove(n){this.set(e(n))}}});return[i,B.contentAttributes.of(n=>{var r;return!((r=n.plugin(i))===null||r===void 0)&&r.isDown?Dp:null})]}const vi="-10000px";class fc{constructor(t,e,i,n){this.facet=e,this.createTooltipView=i,this.removeTooltipView=n,this.input=t.state.facet(e),this.tooltips=this.input.filter(o=>o);let r=null;this.tooltipViews=this.tooltips.map(o=>r=i(o,r))}update(t,e){var i;let n=t.state.facet(this.facet),r=n.filter(a=>a);if(n===this.input){for(let a of this.tooltipViews)a.update&&a.update(t);return!1}let o=[],l=e?[]:null;for(let a=0;a<r.length;a++){let h=r[a],c=-1;if(h){for(let f=0;f<this.tooltips.length;f++){let u=this.tooltips[f];u&&u.create==h.create&&(c=f)}if(c<0)o[a]=this.createTooltipView(h,a?o[a-1]:null),l&&(l[a]=!!h.above);else{let f=o[a]=this.tooltipViews[c];l&&(l[a]=e[c]),f.update&&f.update(t)}}}for(let a of this.tooltipViews)o.indexOf(a)<0&&(this.removeTooltipView(a),(i=a.destroy)===null||i===void 0||i.call(a));return e&&(l.forEach((a,h)=>e[h]=a),e.length=l.length),this.input=n,this.tooltips=r,this.tooltipViews=o,!0}}function Op(s){let t=s.dom.ownerDocument.documentElement;return{top:0,left:0,bottom:t.clientHeight,right:t.clientWidth}}const Ds=O.define({combine:s=>{var t,e,i;return{position:T.ios?"absolute":((t=s.find(n=>n.position))===null||t===void 0?void 0:t.position)||"fixed",parent:((e=s.find(n=>n.parent))===null||e===void 0?void 0:e.parent)||null,tooltipSpace:((i=s.find(n=>n.tooltipSpace))===null||i===void 0?void 0:i.tooltipSpace)||Op}}}),El=new WeakMap,ho=ft.fromClass(class{constructor(s){this.view=s,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let t=s.state.facet(Ds);this.position=t.position,this.parent=t.parent,this.classes=s.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.resizeObserver=typeof ResizeObserver=="function"?new ResizeObserver(()=>this.measureSoon()):null,this.manager=new fc(s,co,(e,i)=>this.createTooltip(e,i),e=>{this.resizeObserver&&this.resizeObserver.unobserve(e.dom),e.dom.remove()}),this.above=this.manager.tooltips.map(e=>!!e.above),this.intersectionObserver=typeof IntersectionObserver=="function"?new IntersectionObserver(e=>{Date.now()>this.lastTransaction-50&&e.length>0&&e[e.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),s.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let s of this.manager.tooltipViews)this.intersectionObserver.observe(s.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(s){s.transactions.length&&(this.lastTransaction=Date.now());let t=this.manager.update(s,this.above);t&&this.observeIntersection();let e=t||s.geometryChanged,i=s.state.facet(Ds);if(i.position!=this.position&&!this.madeAbsolute){this.position=i.position;for(let n of this.manager.tooltipViews)n.dom.style.position=this.position;e=!0}if(i.parent!=this.parent){this.parent&&this.container.remove(),this.parent=i.parent,this.createContainer();for(let n of this.manager.tooltipViews)this.container.appendChild(n.dom);e=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);e&&this.maybeMeasure()}createTooltip(s,t){let e=s.create(this.view),i=t?t.dom:null;if(e.dom.classList.add("cm-tooltip"),s.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let n=document.createElement("div");n.className="cm-tooltip-arrow",e.dom.appendChild(n)}return e.dom.style.position=this.position,e.dom.style.top=vi,e.dom.style.left="0px",this.container.insertBefore(e.dom,i),e.mount&&e.mount(this.view),this.resizeObserver&&this.resizeObserver.observe(e.dom),e}destroy(){var s,t,e;this.view.win.removeEventListener("resize",this.measureSoon);for(let i of this.manager.tooltipViews)i.dom.remove(),(s=i.destroy)===null||s===void 0||s.call(i);this.parent&&this.container.remove(),(t=this.resizeObserver)===null||t===void 0||t.disconnect(),(e=this.intersectionObserver)===null||e===void 0||e.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let s=1,t=1,e=!1;if(this.position=="fixed"&&this.manager.tooltipViews.length){let{dom:r}=this.manager.tooltipViews[0];if(T.gecko)e=r.offsetParent!=this.container.ownerDocument.body;else if(r.style.top==vi&&r.style.left=="0px"){let o=r.getBoundingClientRect();e=Math.abs(o.top+1e4)>1||Math.abs(o.left)>1}}if(e||this.position=="absolute")if(this.parent){let r=this.parent.getBoundingClientRect();r.width&&r.height&&(s=r.width/this.parent.offsetWidth,t=r.height/this.parent.offsetHeight)}else({scaleX:s,scaleY:t}=this.view.viewState);let i=this.view.scrollDOM.getBoundingClientRect(),n=ro(this.view);return{visible:{left:i.left+n.left,top:i.top+n.top,right:i.right-n.right,bottom:i.bottom-n.bottom},parent:this.parent?this.container.getBoundingClientRect():this.view.dom.getBoundingClientRect(),pos:this.manager.tooltips.map((r,o)=>{let l=this.manager.tooltipViews[o];return l.getCoords?l.getCoords(r.pos):this.view.coordsAtPos(r.pos)}),size:this.manager.tooltipViews.map(({dom:r})=>r.getBoundingClientRect()),space:this.view.state.facet(Ds).tooltipSpace(this.view),scaleX:s,scaleY:t,makeAbsolute:e}}writeMeasure(s){var t;if(s.makeAbsolute){this.madeAbsolute=!0,this.position="absolute";for(let l of this.manager.tooltipViews)l.dom.style.position="absolute"}let{visible:e,space:i,scaleX:n,scaleY:r}=s,o=[];for(let l=0;l<this.manager.tooltips.length;l++){let a=this.manager.tooltips[l],h=this.manager.tooltipViews[l],{dom:c}=h,f=s.pos[l],u=s.size[l];if(!f||a.clip!==!1&&(f.bottom<=Math.max(e.top,i.top)||f.top>=Math.min(e.bottom,i.bottom)||f.right<Math.max(e.left,i.left)-.1||f.left>Math.min(e.right,i.right)+.1)){c.style.top=vi;continue}let d=a.arrow?h.dom.querySelector(".cm-tooltip-arrow"):null,p=d?7:0,m=u.right-u.left,g=(t=El.get(h))!==null&&t!==void 0?t:u.bottom-u.top,y=h.offset||Pp,w=this.view.textDirection==Y.LTR,S=u.width>i.right-i.left?w?i.left:i.right-u.width:w?Math.max(i.left,Math.min(f.left-(d?14:0)+y.x,i.right-m)):Math.min(Math.max(i.left,f.left-m+(d?14:0)-y.x),i.right-m),k=this.above[l];!a.strictSide&&(k?f.top-g-p-y.y<i.top:f.bottom+g+p+y.y>i.bottom)&&k==i.bottom-f.bottom>f.top-i.top&&(k=this.above[l]=!k);let x=(k?f.top-i.top:i.bottom-f.bottom)-p;if(x<g&&h.resize!==!1){if(x<this.view.defaultLineHeight){c.style.top=vi;continue}El.set(h,g),c.style.height=(g=x)/r+"px"}else c.style.height&&(c.style.height="");let C=k?f.top-g-p-y.y:f.bottom+p+y.y,A=S+m;if(h.overlap!==!0)for(let L of o)L.left<A&&L.right>S&&L.top<C+g&&L.bottom>C&&(C=k?L.top-g-2-p:L.bottom+p+2);if(this.position=="absolute"?(c.style.top=(C-s.parent.top)/r+"px",Ll(c,(S-s.parent.left)/n)):(c.style.top=C/r+"px",Ll(c,S/n)),d){let L=f.left+(w?y.x:-y.x)-(S+14-7);d.style.left=L/n+"px"}h.overlap!==!0&&o.push({left:S,top:C,right:A,bottom:C+g}),c.classList.toggle("cm-tooltip-above",k),c.classList.toggle("cm-tooltip-below",!k),h.positioned&&h.positioned(s.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let s of this.manager.tooltipViews)s.dom.style.top=vi}},{eventObservers:{scroll(){this.maybeMeasure()}}});function Ll(s,t){let e=parseInt(s.style.left,10);(isNaN(e)||Math.abs(t-e)>1)&&(s.style.left=t+"px")}const Bp=B.baseTheme({".cm-tooltip":{zIndex:500,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:`${7*2}px`,position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),Pp={x:0,y:0},co=O.define({enables:[ho,Bp]}),Jn=O.define({combine:s=>s.reduce((t,e)=>t.concat(e),[])});class as{static create(t){return new as(t)}constructor(t){this.view=t,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new fc(t,Jn,(e,i)=>this.createHostedView(e,i),e=>e.dom.remove())}createHostedView(t,e){let i=t.create(this.view);return i.dom.classList.add("cm-tooltip-section"),this.dom.insertBefore(i.dom,e?e.dom.nextSibling:this.dom.firstChild),this.mounted&&i.mount&&i.mount(this.view),i}mount(t){for(let e of this.manager.tooltipViews)e.mount&&e.mount(t);this.mounted=!0}positioned(t){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned(t)}update(t){this.manager.update(t)}destroy(){var t;for(let e of this.manager.tooltipViews)(t=e.destroy)===null||t===void 0||t.call(e)}passProp(t){let e;for(let i of this.manager.tooltipViews){let n=i[t];if(n!==void 0){if(e===void 0)e=n;else if(e!==n)return}}return e}get offset(){return this.passProp("offset")}get getCoords(){return this.passProp("getCoords")}get overlap(){return this.passProp("overlap")}get resize(){return this.passProp("resize")}}const Ep=co.compute([Jn],s=>{let t=s.facet(Jn);return t.length===0?null:{pos:Math.min(...t.map(e=>e.pos)),end:Math.max(...t.map(e=>{var i;return(i=e.end)!==null&&i!==void 0?i:e.pos})),create:as.create,above:t[0].above,arrow:t.some(e=>e.arrow)}});class Lp{constructor(t,e,i,n,r){this.view=t,this.source=e,this.field=i,this.setHover=n,this.hoverTime=r,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:t.dom,time:0},this.checkHover=this.checkHover.bind(this),t.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),t.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active.length)return;let t=Date.now()-this.lastMove.time;t<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-t):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{view:t,lastMove:e}=this,i=t.docView.nearest(e.target);if(!i)return;let n,r=1;if(i instanceof ke)n=i.posAtStart;else{if(n=t.posAtCoords(e),n==null)return;let l=t.coordsAtPos(n);if(!l||e.y<l.top||e.y>l.bottom||e.x<l.left-t.defaultCharacterWidth||e.x>l.right+t.defaultCharacterWidth)return;let a=t.bidiSpans(t.state.doc.lineAt(n)).find(c=>c.from<=n&&c.to>=n),h=a&&a.dir==Y.RTL?-1:1;r=e.x<l.left?-h:h}let o=this.source(t,n,r);if(o?.then){let l=this.pending={pos:n};o.then(a=>{this.pending==l&&(this.pending=null,a&&!(Array.isArray(a)&&!a.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(a)?a:[a])}))},a=>Mt(t.state,a,"hover tooltip"))}else o&&!(Array.isArray(o)&&!o.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(o)?o:[o])})}get tooltip(){let t=this.view.plugin(ho),e=t?t.manager.tooltips.findIndex(i=>i.create==as.create):-1;return e>-1?t.manager.tooltipViews[e]:null}mousemove(t){var e,i;this.lastMove={x:t.clientX,y:t.clientY,target:t.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let{active:n,tooltip:r}=this;if(n.length&&r&&!Rp(r.dom,t)||this.pending){let{pos:o}=n[0]||this.pending,l=(i=(e=n[0])===null||e===void 0?void 0:e.end)!==null&&i!==void 0?i:o;(o==l?this.view.posAtCoords(this.lastMove)!=o:!_p(this.view,o,l,t.clientX,t.clientY))&&(this.view.dispatch({effects:this.setHover.of([])}),this.pending=null)}}mouseleave(t){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1;let{active:e}=this;if(e.length){let{tooltip:i}=this;i&&i.dom.contains(t.relatedTarget)?this.watchTooltipLeave(i.dom):this.view.dispatch({effects:this.setHover.of([])})}}watchTooltipLeave(t){let e=i=>{t.removeEventListener("mouseleave",e),this.active.length&&!this.view.dom.contains(i.relatedTarget)&&this.view.dispatch({effects:this.setHover.of([])})};t.addEventListener("mouseleave",e)}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}const wn=4;function Rp(s,t){let{left:e,right:i,top:n,bottom:r}=s.getBoundingClientRect(),o;if(o=s.querySelector(".cm-tooltip-arrow")){let l=o.getBoundingClientRect();n=Math.min(l.top,n),r=Math.max(l.bottom,r)}return t.clientX>=e-wn&&t.clientX<=i+wn&&t.clientY>=n-wn&&t.clientY<=r+wn}function _p(s,t,e,i,n,r){let o=s.scrollDOM.getBoundingClientRect(),l=s.documentTop+s.documentPadding.top+s.contentHeight;if(o.left>i||o.right<i||o.top>n||Math.min(o.bottom,l)<n)return!1;let a=s.posAtCoords({x:i,y:n},!1);return a>=t&&a<=e}function Ip(s,t={}){let e=_.define(),i=Tt.define({create(){return[]},update(n,r){if(n.length&&(t.hideOnChange&&(r.docChanged||r.selection)?n=[]:t.hideOn&&(n=n.filter(o=>!t.hideOn(r,o))),r.docChanged)){let o=[];for(let l of n){let a=r.changes.mapPos(l.pos,-1,ct.TrackDel);if(a!=null){let h=Object.assign(Object.create(null),l);h.pos=a,h.end!=null&&(h.end=r.changes.mapPos(h.end)),o.push(h)}}n=o}for(let o of r.effects)o.is(e)&&(n=o.value),o.is(Np)&&(n=[]);return n},provide:n=>Jn.from(n)});return{active:i,extension:[i,ft.define(n=>new Lp(n,s,i,e,t.hoverTime||300)),Ep]}}function uc(s,t){let e=s.plugin(ho);if(!e)return null;let i=e.manager.tooltips.indexOf(t);return i<0?null:e.manager.tooltipViews[i]}const Np=_.define(),Rl=O.define({combine(s){let t,e;for(let i of s)t=t||i.topContainer,e=e||i.bottomContainer;return{topContainer:t,bottomContainer:e}}});function Fp(s,t){let e=s.plugin(dc),i=e?e.specs.indexOf(t):-1;return i>-1?e.panels[i]:null}const dc=ft.fromClass(class{constructor(s){this.input=s.state.facet(Br),this.specs=this.input.filter(e=>e),this.panels=this.specs.map(e=>e(s));let t=s.state.facet(Rl);this.top=new xn(s,!0,t.topContainer),this.bottom=new xn(s,!1,t.bottomContainer),this.top.sync(this.panels.filter(e=>e.top)),this.bottom.sync(this.panels.filter(e=>!e.top));for(let e of this.panels)e.dom.classList.add("cm-panel"),e.mount&&e.mount()}update(s){let t=s.state.facet(Rl);this.top.container!=t.topContainer&&(this.top.sync([]),this.top=new xn(s.view,!0,t.topContainer)),this.bottom.container!=t.bottomContainer&&(this.bottom.sync([]),this.bottom=new xn(s.view,!1,t.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let e=s.state.facet(Br);if(e!=this.input){let i=e.filter(a=>a),n=[],r=[],o=[],l=[];for(let a of i){let h=this.specs.indexOf(a),c;h<0?(c=a(s.view),l.push(c)):(c=this.panels[h],c.update&&c.update(s)),n.push(c),(c.top?r:o).push(c)}this.specs=i,this.panels=n,this.top.sync(r),this.bottom.sync(o);for(let a of l)a.dom.classList.add("cm-panel"),a.mount&&a.mount()}else for(let i of this.panels)i.update&&i.update(s)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:s=>B.scrollMargins.of(t=>{let e=t.plugin(s);return e&&{top:e.top.scrollMargin(),bottom:e.bottom.scrollMargin()}})});class xn{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&t.indexOf(e)<0&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(this.panels.length==0){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let e=this.container||this.view.dom;e.insertBefore(this.dom,this.top?e.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=_l(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=_l(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(!(!this.container||this.classes==this.view.themeClasses)){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function _l(s){let t=s.nextSibling;return s.remove(),t}const Br=O.define({enables:dc});class Te extends Ve{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}Te.prototype.elementClass="";Te.prototype.toDOM=void 0;Te.prototype.mapMode=ct.TrackBefore;Te.prototype.startSide=Te.prototype.endSide=-1;Te.prototype.point=!0;const Ts=O.define(),Hp=O.define(),Vp={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>z.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},_i=O.define();function Wp(s){return[pc(),_i.of(Object.assign(Object.assign({},Vp),s))]}const Il=O.define({combine:s=>s.some(t=>t)});function pc(s){return[zp]}const zp=ft.fromClass(class{constructor(s){this.view=s,this.prevViewport=s.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=s.state.facet(_i).map(t=>new Fl(s,t));for(let t of this.gutters)this.dom.appendChild(t.dom);this.fixed=!s.state.facet(Il),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),s.scrollDOM.insertBefore(this.dom,s.contentDOM)}update(s){if(this.updateGutters(s)){let t=this.prevViewport,e=s.view.viewport,i=Math.min(t.to,e.to)-Math.max(t.from,e.from);this.syncGutters(i<(e.to-e.from)*.8)}s.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px"),this.view.state.facet(Il)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=s.view.viewport}syncGutters(s){let t=this.dom.nextSibling;s&&this.dom.remove();let e=z.iter(this.view.state.facet(Ts),this.view.viewport.from),i=[],n=this.gutters.map(r=>new $p(r,this.view.viewport,-this.view.documentPadding.top));for(let r of this.view.viewportLineBlocks)if(i.length&&(i=[]),Array.isArray(r.type)){let o=!0;for(let l of r.type)if(l.type==wt.Text&&o){Pr(e,i,l.from);for(let a of n)a.line(this.view,l,i);o=!1}else if(l.widget)for(let a of n)a.widget(this.view,l)}else if(r.type==wt.Text){Pr(e,i,r.from);for(let o of n)o.line(this.view,r,i)}else if(r.widget)for(let o of n)o.widget(this.view,r);for(let r of n)r.finish();s&&this.view.scrollDOM.insertBefore(this.dom,t)}updateGutters(s){let t=s.startState.facet(_i),e=s.state.facet(_i),i=s.docChanged||s.heightChanged||s.viewportChanged||!z.eq(s.startState.facet(Ts),s.state.facet(Ts),s.view.viewport.from,s.view.viewport.to);if(t==e)for(let n of this.gutters)n.update(s)&&(i=!0);else{i=!0;let n=[];for(let r of e){let o=t.indexOf(r);o<0?n.push(new Fl(this.view,r)):(this.gutters[o].update(s),n.push(this.gutters[o]))}for(let r of this.gutters)r.dom.remove(),n.indexOf(r)<0&&r.destroy();for(let r of n)this.dom.appendChild(r.dom);this.gutters=n}return i}destroy(){for(let s of this.gutters)s.destroy();this.dom.remove()}},{provide:s=>B.scrollMargins.of(t=>{let e=t.plugin(s);return!e||e.gutters.length==0||!e.fixed?null:t.textDirection==Y.LTR?{left:e.dom.offsetWidth*t.scaleX}:{right:e.dom.offsetWidth*t.scaleX}})});function Nl(s){return Array.isArray(s)?s:[s]}function Pr(s,t,e){for(;s.value&&s.from<=e;)s.from==e&&t.push(s.value),s.next()}class $p{constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=z.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:n}=this,r=(e.top-this.height)/t.scaleY,o=e.height/t.scaleY;if(this.i==n.elements.length){let l=new mc(t,o,r,i);n.elements.push(l),n.dom.appendChild(l.dom)}else n.elements[this.i].update(t,o,r,i);this.height=e.bottom,this.i++}line(t,e,i){let n=[];Pr(this.cursor,n,e.from),i.length&&(n=n.concat(i));let r=this.gutter.config.lineMarker(t,e,n);r&&n.unshift(r);let o=this.gutter;n.length==0&&!o.config.renderEmptyElements||this.addElement(t,e,n)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e),n=i?[i]:null;for(let r of t.state.facet(Hp)){let o=r(t,e.widget,e);o&&(n||(n=[])).push(o)}n&&this.addElement(t,e,n)}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class Fl{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,n=>{let r=n.target,o;if(r!=this.dom&&this.dom.contains(r)){for(;r.parentNode!=this.dom;)r=r.parentNode;let a=r.getBoundingClientRect();o=(a.top+a.bottom)/2}else o=n.clientY;let l=t.lineBlockAtHeight(o-t.documentTop);e.domEventHandlers[i](t,l,n)&&n.preventDefault()});this.markers=Nl(e.markers(t)),e.initialSpacer&&(this.spacer=new mc(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=Nl(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let n=this.config.updateSpacer(this.spacer.markers[0],t);n!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[n])}let i=t.view.viewport;return!z.eq(this.markers,e,i.from,i.to)||(this.config.lineMarkerChange?this.config.lineMarkerChange(t):!1)}destroy(){for(let t of this.elements)t.destroy()}}class mc{constructor(t,e,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,n)}update(t,e,i,n){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),qp(this.markers,n)||this.setMarkers(t,n)}setMarkers(t,e){let i="cm-gutterElement",n=this.dom.firstChild;for(let r=0,o=0;;){let l=o,a=r<e.length?e[r++]:null,h=!1;if(a){let c=a.elementClass;c&&(i+=" "+c);for(let f=o;f<this.markers.length;f++)if(this.markers[f].compare(a)){l=f,h=!0;break}}else l=this.markers.length;for(;o<l;){let c=this.markers[o++];if(c.toDOM){c.destroy(n);let f=n.nextSibling;n.remove(),n=f}}if(!a)break;a.toDOM&&(h?n=n.nextSibling:this.dom.insertBefore(a.toDOM(t),n)),h&&o++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}function qp(s,t){if(s.length!=t.length)return!1;for(let e=0;e<s.length;e++)if(!s[e].compare(t[e]))return!1;return!0}const jp=O.define(),Kp=O.define(),ti=O.define({combine(s){return Je(s,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let n in e){let r=i[n],o=e[n];i[n]=r?(l,a,h)=>r(l,a,h)||o(l,a,h):o}return i}})}});class Os extends Te{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function Bs(s,t){return s.state.facet(ti).formatNumber(t,s.state)}const Up=_i.compute([ti],s=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers(t){return t.state.facet(jp)},lineMarker(t,e,i){return i.some(n=>n.toDOM)?null:new Os(Bs(t,t.state.doc.lineAt(e.from).number))},widgetMarker:(t,e,i)=>{for(let n of t.state.facet(Kp)){let r=n(t,e,i);if(r)return r}return null},lineMarkerChange:t=>t.startState.facet(ti)!=t.state.facet(ti),initialSpacer(t){return new Os(Bs(t,Hl(t.state.doc.lines)))},updateSpacer(t,e){let i=Bs(e.view,Hl(e.view.state.doc.lines));return i==t.number?t:new Os(i)},domEventHandlers:s.facet(ti).domEventHandlers}));function Gp(s={}){return[ti.of(s),pc(),Up]}function Hl(s){let t=9;for(;t<s;)t=t*10+9;return t}const Yp=1024;let Jp=0;class Ft{constructor(t,e){this.from=t,this.to=e}}class N{constructor(t={}){this.id=Jp++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return typeof t!="function"&&(t=vt.match(t)),e=>{let i=t(e);return i===void 0?null:[this,i]}}}N.closedBy=new N({deserialize:s=>s.split(" ")});N.openedBy=new N({deserialize:s=>s.split(" ")});N.group=new N({deserialize:s=>s.split(" ")});N.isolate=new N({deserialize:s=>{if(s&&s!="rtl"&&s!="ltr"&&s!="auto")throw new RangeError("Invalid value for isolate: "+s);return s||"auto"}});N.contextHash=new N({perNode:!0});N.lookAhead=new N({perNode:!0});N.mounted=new N({perNode:!0});class $i{constructor(t,e,i){this.tree=t,this.overlay=e,this.parser=i}static get(t){return t&&t.props&&t.props[N.mounted.id]}}const Xp=Object.create(null);class vt{constructor(t,e,i,n=0){this.name=t,this.props=e,this.id=i,this.flags=n}static define(t){let e=t.props&&t.props.length?Object.create(null):Xp,i=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(t.name==null?8:0),n=new vt(t.name||"",e,t.id,i);if(t.props){for(let r of t.props)if(Array.isArray(r)||(r=r(n)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");e[r[0].id]=r[1]}}return n}prop(t){return this.props[t.id]}get isTop(){return(this.flags&1)>0}get isSkipped(){return(this.flags&2)>0}get isError(){return(this.flags&4)>0}get isAnonymous(){return(this.flags&8)>0}is(t){if(typeof t=="string"){if(this.name==t)return!0;let e=this.prop(N.group);return e?e.indexOf(t)>-1:!1}return this.id==t}static match(t){let e=Object.create(null);for(let i in t)for(let n of i.split(" "))e[n]=t[i];return i=>{for(let n=i.prop(N.group),r=-1;r<(n?n.length:0);r++){let o=e[r<0?i.name:n[r]];if(o)return o}}}}vt.none=new vt("",Object.create(null),0,8);class fo{constructor(t){this.types=t;for(let e=0;e<t.length;e++)if(t[e].id!=e)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let e=[];for(let i of this.types){let n=null;for(let r of t){let o=r(i);o&&(n||(n=Object.assign({},i.props)),n[o[0].id]=o[1])}e.push(n?new vt(i.name,n,i.id,i.flags):i)}return new fo(e)}}const vn=new WeakMap,Vl=new WeakMap;var J;(function(s){s[s.ExcludeBuffers=1]="ExcludeBuffers",s[s.IncludeAnonymous=2]="IncludeAnonymous",s[s.IgnoreMounts=4]="IgnoreMounts",s[s.IgnoreOverlays=8]="IgnoreOverlays"})(J||(J={}));class U{constructor(t,e,i,n,r){if(this.type=t,this.children=e,this.positions=i,this.length=n,this.props=null,r&&r.length){this.props=Object.create(null);for(let[o,l]of r)this.props[typeof o=="number"?o:o.id]=l}}toString(){let t=$i.get(this);if(t&&!t.overlay)return t.tree.toString();let e="";for(let i of this.children){let n=i.toString();n&&(e&&(e+=","),e+=n)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(e.length?"("+e+")":""):e}cursor(t=0){return new Xn(this.topNode,t)}cursorAt(t,e=0,i=0){let n=vn.get(this)||this.topNode,r=new Xn(n);return r.moveTo(t,e),vn.set(this,r._tree),r}get topNode(){return new mt(this,0,0,null)}resolve(t,e=0){let i=qi(vn.get(this)||this.topNode,t,e,!1);return vn.set(this,i),i}resolveInner(t,e=0){let i=qi(Vl.get(this)||this.topNode,t,e,!0);return Vl.set(this,i),i}resolveStack(t,e=0){return tm(this,t,e)}iterate(t){let{enter:e,leave:i,from:n=0,to:r=this.length}=t,o=t.mode||0,l=(o&J.IncludeAnonymous)>0;for(let a=this.cursor(o|J.IncludeAnonymous);;){let h=!1;if(a.from<=r&&a.to>=n&&(!l&&a.type.isAnonymous||e(a)!==!1)){if(a.firstChild())continue;h=!0}for(;h&&i&&(l||!a.type.isAnonymous)&&i(a),!a.nextSibling();){if(!a.parent())return;h=!0}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let e in this.props)t.push([+e,this.props[e]]);return t}balance(t={}){return this.children.length<=8?this:mo(vt.none,this.children,this.positions,0,this.children.length,0,this.length,(e,i,n)=>new U(this.type,e,i,n,this.propValues),t.makeTree||((e,i,n)=>new U(vt.none,e,i,n)))}static build(t){return em(t)}}U.empty=new U(vt.none,[],[],0);class uo{constructor(t,e){this.buffer=t,this.index=e}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new uo(this.buffer,this.index)}}class Oe{constructor(t,e,i){this.buffer=t,this.length=e,this.set=i}get type(){return vt.none}toString(){let t=[];for(let e=0;e<this.buffer.length;)t.push(this.childString(e)),e=this.buffer[e+3];return t.join(",")}childString(t){let e=this.buffer[t],i=this.buffer[t+3],n=this.set.types[e],r=n.name;if(/\W/.test(r)&&!n.isError&&(r=JSON.stringify(r)),t+=4,i==t)return r;let o=[];for(;t<i;)o.push(this.childString(t)),t=this.buffer[t+3];return r+"("+o.join(",")+")"}findChild(t,e,i,n,r){let{buffer:o}=this,l=-1;for(let a=t;a!=e&&!(gc(r,n,o[a+1],o[a+2])&&(l=a,i>0));a=o[a+3]);return l}slice(t,e,i){let n=this.buffer,r=new Uint16Array(e-t),o=0;for(let l=t,a=0;l<e;){r[a++]=n[l++],r[a++]=n[l++]-i;let h=r[a++]=n[l++]-i;r[a++]=n[l++]-t,o=Math.max(o,h)}return new Oe(r,o,this.set)}}function gc(s,t,e,i){switch(s){case-2:return e<t;case-1:return i>=t&&e<t;case 0:return e<t&&i>t;case 1:return e<=t&&i>t;case 2:return i>t;case 4:return!0}}function qi(s,t,e,i){for(var n;s.from==s.to||(e<1?s.from>=t:s.from>t)||(e>-1?s.to<=t:s.to<t);){let o=!i&&s instanceof mt&&s.index<0?null:s.parent;if(!o)return s;s=o}let r=i?0:J.IgnoreOverlays;if(i)for(let o=s,l=o.parent;l;o=l,l=o.parent)o instanceof mt&&o.index<0&&((n=l.enter(t,e,r))===null||n===void 0?void 0:n.from)!=o.from&&(s=l);for(;;){let o=s.enter(t,e,r);if(!o)return s;s=o}}class bc{cursor(t=0){return new Xn(this,t)}getChild(t,e=null,i=null){let n=Wl(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return Wl(this,t,e,i)}resolve(t,e=0){return qi(this,t,e,!1)}resolveInner(t,e=0){return qi(this,t,e,!0)}matchContext(t){return Er(this.parent,t)}enterUnfinishedNodesBefore(t){let e=this.childBefore(t),i=this;for(;e;){let n=e.lastChild;if(!n||n.to!=e.to)break;n.type.isError&&n.from==n.to?(i=e,e=n.prevSibling):e=n}return i}get node(){return this}get next(){return this.parent}}class mt extends bc{constructor(t,e,i,n){super(),this._tree=t,this.from=e,this.index=i,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(t,e,i,n,r=0){for(let o=this;;){for(let{children:l,positions:a}=o._tree,h=e>0?l.length:-1;t!=h;t+=e){let c=l[t],f=a[t]+o.from;if(gc(n,i,f,f+c.length)){if(c instanceof Oe){if(r&J.ExcludeBuffers)continue;let u=c.findChild(0,c.buffer.length,e,i-f,n);if(u>-1)return new re(new Qp(o,c,t,f),null,u)}else if(r&J.IncludeAnonymous||!c.type.isAnonymous||po(c)){let u;if(!(r&J.IgnoreMounts)&&(u=$i.get(c))&&!u.overlay)return new mt(u.tree,f,t,o);let d=new mt(c,f,t,o);return r&J.IncludeAnonymous||!d.type.isAnonymous?d:d.nextChild(e<0?c.children.length-1:0,e,i,n)}}}if(r&J.IncludeAnonymous||!o.type.isAnonymous||(o.index>=0?t=o.index+e:t=e<0?-1:o._parent._tree.children.length,o=o._parent,!o))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this._tree.children.length-1,-1,t,-2)}enter(t,e,i=0){let n;if(!(i&J.IgnoreOverlays)&&(n=$i.get(this._tree))&&n.overlay){let r=t-this.from;for(let{from:o,to:l}of n.overlay)if((e>0?o<=r:o<r)&&(e<0?l>=r:l>r))return new mt(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,e,i)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function Wl(s,t,e,i){let n=s.cursor(),r=[];if(!n.firstChild())return r;if(e!=null){for(let o=!1;!o;)if(o=n.type.is(e),!n.nextSibling())return r}for(;;){if(i!=null&&n.type.is(i))return r;if(n.type.is(t)&&r.push(n.node),!n.nextSibling())return i==null?r:[]}}function Er(s,t,e=t.length-1){for(let i=s;e>=0;i=i.parent){if(!i)return!1;if(!i.type.isAnonymous){if(t[e]&&t[e]!=i.name)return!1;e--}}return!0}class Qp{constructor(t,e,i,n){this.parent=t,this.buffer=e,this.index=i,this.start=n}}class re extends bc{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(t,e,i){super(),this.context=t,this._parent=e,this.index=i,this.type=t.buffer.set.types[t.buffer.buffer[i]]}child(t,e,i){let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.context.start,i);return r<0?null:new re(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,e,i=0){if(i&J.ExcludeBuffers)return null;let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],e>0?1:-1,t-this.context.start,e);return r<0?null:new re(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,e=t.buffer[this.index+3];return e<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new re(this.context,this._parent,e):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,e=this._parent?this._parent.index+4:0;return this.index==e?this.externalSibling(-1):new re(this.context,this._parent,t.findChild(e,this.index,-1,0,4))}get tree(){return null}toTree(){let t=[],e=[],{buffer:i}=this.context,n=this.index+4,r=i.buffer[this.index+3];if(r>n){let o=i.buffer[this.index+1];t.push(i.slice(n,r,o)),e.push(0)}return new U(this.type,t,e,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function yc(s){if(!s.length)return null;let t=0,e=s[0];for(let r=1;r<s.length;r++){let o=s[r];(o.from>e.from||o.to<e.to)&&(e=o,t=r)}let i=e instanceof mt&&e.index<0?null:e.parent,n=s.slice();return i?n[t]=i:n.splice(t,1),new Zp(n,e)}class Zp{constructor(t,e){this.heads=t,this.node=e}get next(){return yc(this.heads)}}function tm(s,t,e){let i=s.resolveInner(t,e),n=null;for(let r=i instanceof mt?i:i.context.parent;r;r=r.parent)if(r.index<0){let o=r.parent;(n||(n=[i])).push(o.resolve(t,e)),r=o}else{let o=$i.get(r.tree);if(o&&o.overlay&&o.overlay[0].from<=t&&o.overlay[o.overlay.length-1].to>=t){let l=new mt(o.tree,o.overlay[0].from+r.from,-1,r);(n||(n=[i])).push(qi(l,t,e,!1))}}return n?yc(n):i}class Xn{get name(){return this.type.name}constructor(t,e=0){if(this.mode=e,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof mt)this.yieldNode(t);else{this._tree=t.context.parent,this.buffer=t.context;for(let i=t._parent;i;i=i._parent)this.stack.unshift(i.index);this.bufferNode=t,this.yieldBuf(t.index)}}yieldNode(t){return t?(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0):!1}yieldBuf(t,e){this.index=t;let{start:i,buffer:n}=this.buffer;return this.type=e||n.set.types[n.buffer[t]],this.from=i+n.buffer[t+1],this.to=i+n.buffer[t+2],!0}yield(t){return t?t instanceof mt?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)):!1}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,e,i){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree._tree.children.length-1:0,t,e,i,this.mode));let{buffer:n}=this.buffer,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.buffer.start,i);return r<0?!1:(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,e,i=this.mode){return this.buffer?i&J.ExcludeBuffers?!1:this.enterChild(1,t,e):this.yield(this._tree.enter(t,e,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&J.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=this.mode&J.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return this._tree._parent?this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode)):!1;let{buffer:e}=this.buffer,i=this.stack.length-1;if(t<0){let n=i<0?0:this.stack[i]+4;if(this.index!=n)return this.yieldBuf(e.findChild(n,this.index,-1,0,4))}else{let n=e.buffer[this.index+3];if(n<(i<0?e.buffer.length:e.buffer[this.stack[i]+3]))return this.yieldBuf(n)}return i<0?this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode)):!1}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let e,i,{buffer:n}=this;if(n){if(t>0){if(this.index<n.buffer.buffer.length)return!1}else for(let r=0;r<this.index;r++)if(n.buffer.buffer[r+3]<this.index)return!1;({index:e,parent:i}=n)}else({index:e,_parent:i}=this._tree);for(;i;{index:e,_parent:i}=i)if(e>-1)for(let r=e+t,o=t<0?-1:i._tree.children.length;r!=o;r+=t){let l=i._tree.children[r];if(this.mode&J.IncludeAnonymous||l instanceof Oe||!l.type.isAnonymous||po(l))return!1}return!0}move(t,e){if(e&&this.enterChild(t,0,4))return!0;for(;;){if(this.sibling(t))return!0;if(this.atLastNode(t)||!this.parent())return!1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,e=0){for(;(this.from==this.to||(e<1?this.from>=t:this.from>t)||(e>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,e););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,e=null,i=0;if(t&&t.context==this.buffer)t:for(let n=this.index,r=this.stack.length;r>=0;){for(let o=t;o;o=o._parent)if(o.index==n){if(n==this.index)return o;e=o,i=r+1;break t}n=this.stack[--r]}for(let n=i;n<this.stack.length;n++)e=new re(this.buffer,e,this.stack[n]);return this.bufferNode=new re(this.buffer,e,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(t,e){for(let i=0;;){let n=!1;if(this.type.isAnonymous||t(this)!==!1){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0)}for(;;){if(n&&e&&e(this),n=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,n=!0}}}matchContext(t){if(!this.buffer)return Er(this.node.parent,t);let{buffer:e}=this.buffer,{types:i}=e.set;for(let n=t.length-1,r=this.stack.length-1;n>=0;r--){if(r<0)return Er(this._tree,t,n);let o=i[e.buffer[this.stack[r]]];if(!o.isAnonymous){if(t[n]&&t[n]!=o.name)return!1;n--}}return!0}}function po(s){return s.children.some(t=>t instanceof Oe||!t.type.isAnonymous||po(t))}function em(s){var t;let{buffer:e,nodeSet:i,maxBufferLength:n=Yp,reused:r=[],minRepeatType:o=i.types.length}=s,l=Array.isArray(e)?new uo(e,e.length):e,a=i.types,h=0,c=0;function f(x,C,A,L,R,$){let{id:M,start:E,end:W,size:H}=l,q=c,ot=h;for(;H<0;)if(l.next(),H==-1){let P=r[M];A.push(P),L.push(E-x);return}else if(H==-3){h=M;return}else if(H==-4){c=M;return}else throw new RangeError(`Unrecognized record size: ${H}`);let bt=a[M],Lt,it,kt=E-x;if(W-E<=n&&(it=g(l.pos-C,R))){let P=new Uint16Array(it.size-it.skip),X=l.pos-it.size,Z=P.length;for(;l.pos>X;)Z=y(it.start,P,Z);Lt=new Oe(P,W-it.start,i),kt=it.start-x}else{let P=l.pos-H;l.next();let X=[],Z=[],Rt=M>=o?M:-1,fe=0,Xe=W;for(;l.pos>P;)Rt>=0&&l.id==Rt&&l.size>=0?(l.end<=Xe-n&&(p(X,Z,E,fe,l.end,Xe,Rt,q,ot),fe=X.length,Xe=l.end),l.next()):$>2500?u(E,P,X,Z):f(E,P,X,Z,Rt,$+1);if(Rt>=0&&fe>0&&fe<X.length&&p(X,Z,E,fe,E,Xe,Rt,q,ot),X.reverse(),Z.reverse(),Rt>-1&&fe>0){let Ot=d(bt,ot);Lt=mo(bt,X,Z,0,X.length,0,W-E,Ot,Ot)}else Lt=m(bt,X,Z,W-E,q-W,ot)}A.push(Lt),L.push(kt)}function u(x,C,A,L){let R=[],$=0,M=-1;for(;l.pos>C;){let{id:E,start:W,end:H,size:q}=l;if(q>4)l.next();else{if(M>-1&&W<M)break;M<0&&(M=H-n),R.push(E,W,H),$++,l.next()}}if($){let E=new Uint16Array($*4),W=R[R.length-2];for(let H=R.length-3,q=0;H>=0;H-=3)E[q++]=R[H],E[q++]=R[H+1]-W,E[q++]=R[H+2]-W,E[q++]=q;A.push(new Oe(E,R[2]-W,i)),L.push(W-x)}}function d(x,C){return(A,L,R)=>{let $=0,M=A.length-1,E,W;if(M>=0&&(E=A[M])instanceof U){if(!M&&E.type==x&&E.length==R)return E;(W=E.prop(N.lookAhead))&&($=L[M]+E.length+W)}return m(x,A,L,R,$,C)}}function p(x,C,A,L,R,$,M,E,W){let H=[],q=[];for(;x.length>L;)H.push(x.pop()),q.push(C.pop()+A-R);x.push(m(i.types[M],H,q,$-R,E-$,W)),C.push(R-A)}function m(x,C,A,L,R,$,M){if($){let E=[N.contextHash,$];M=M?[E].concat(M):[E]}if(R>25){let E=[N.lookAhead,R];M=M?[E].concat(M):[E]}return new U(x,C,A,L,M)}function g(x,C){let A=l.fork(),L=0,R=0,$=0,M=A.end-n,E={size:0,start:0,skip:0};t:for(let W=A.pos-x;A.pos>W;){let H=A.size;if(A.id==C&&H>=0){E.size=L,E.start=R,E.skip=$,$+=4,L+=4,A.next();continue}let q=A.pos-H;if(H<0||q<W||A.start<M)break;let ot=A.id>=o?4:0,bt=A.start;for(A.next();A.pos>q;){if(A.size<0)if(A.size==-3)ot+=4;else break t;else A.id>=o&&(ot+=4);A.next()}R=bt,L+=H,$+=ot}return(C<0||L==x)&&(E.size=L,E.start=R,E.skip=$),E.size>4?E:void 0}function y(x,C,A){let{id:L,start:R,end:$,size:M}=l;if(l.next(),M>=0&&L<o){let E=A;if(M>4){let W=l.pos-(M-4);for(;l.pos>W;)A=y(x,C,A)}C[--A]=E,C[--A]=$-x,C[--A]=R-x,C[--A]=L}else M==-3?h=L:M==-4&&(c=L);return A}let w=[],S=[];for(;l.pos>0;)f(s.start||0,s.bufferStart||0,w,S,-1,0);let k=(t=s.length)!==null&&t!==void 0?t:w.length?S[0]+w[0].length:0;return new U(a[s.topID],w.reverse(),S.reverse(),k)}const zl=new WeakMap;function Hn(s,t){if(!s.isAnonymous||t instanceof Oe||t.type!=s)return 1;let e=zl.get(t);if(e==null){e=1;for(let i of t.children){if(i.type!=s||!(i instanceof U)){e=1;break}e+=Hn(s,i)}zl.set(t,e)}return e}function mo(s,t,e,i,n,r,o,l,a){let h=0;for(let p=i;p<n;p++)h+=Hn(s,t[p]);let c=Math.ceil(h*1.5/8),f=[],u=[];function d(p,m,g,y,w){for(let S=g;S<y;){let k=S,x=m[S],C=Hn(s,p[S]);for(S++;S<y;S++){let A=Hn(s,p[S]);if(C+A>=c)break;C+=A}if(S==k+1){if(C>c){let A=p[k];d(A.children,A.positions,0,A.children.length,m[k]+w);continue}f.push(p[k])}else{let A=m[S-1]+p[S-1].length-x;f.push(mo(s,p,m,k,S,x,A,null,a))}u.push(x+w-r)}}return d(t,e,i,n,0),(l||a)(f,u,o)}class w1{constructor(){this.map=new WeakMap}setBuffer(t,e,i){let n=this.map.get(t);n||this.map.set(t,n=new Map),n.set(e,i)}getBuffer(t,e){let i=this.map.get(t);return i&&i.get(e)}set(t,e){t instanceof re?this.setBuffer(t.context.buffer,t.index,e):t instanceof mt&&this.map.set(t.tree,e)}get(t){return t instanceof re?this.getBuffer(t.context.buffer,t.index):t instanceof mt?this.map.get(t.tree):void 0}cursorSet(t,e){t.buffer?this.setBuffer(t.buffer.buffer,t.index,e):this.map.set(t.tree,e)}cursorGet(t){return t.buffer?this.getBuffer(t.buffer.buffer,t.index):this.map.get(t.tree)}}class me{constructor(t,e,i,n,r=!1,o=!1){this.from=t,this.to=e,this.tree=i,this.offset=n,this.open=(r?1:0)|(o?2:0)}get openStart(){return(this.open&1)>0}get openEnd(){return(this.open&2)>0}static addTree(t,e=[],i=!1){let n=[new me(0,t.length,t,0,!1,i)];for(let r of e)r.to>t.length&&n.push(r);return n}static applyChanges(t,e,i=128){if(!e.length)return t;let n=[],r=1,o=t.length?t[0]:null;for(let l=0,a=0,h=0;;l++){let c=l<e.length?e[l]:null,f=c?c.fromA:1e9;if(f-a>=i)for(;o&&o.from<f;){let u=o;if(a>=u.from||f<=u.to||h){let d=Math.max(u.from,a)-h,p=Math.min(u.to,f)-h;u=d>=p?null:new me(d,p,u.tree,u.offset+h,l>0,!!c)}if(u&&n.push(u),o.to>f)break;o=r<t.length?t[r++]:null}if(!c)break;a=c.toA,h=c.toA-c.toB}return n}}class wc{startParse(t,e,i){return typeof t=="string"&&(t=new im(t)),i=i?i.length?i.map(n=>new Ft(n.from,n.to)):[new Ft(0,0)]:[new Ft(0,t.length)],this.createParse(t,e||[],i)}parse(t,e,i){let n=this.startParse(t,e,i);for(;;){let r=n.advance();if(r)return r}}}class im{constructor(t){this.string=t}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return!1}read(t,e){return this.string.slice(t,e)}}function x1(s){return(t,e,i,n)=>new sm(t,s,e,i,n)}class $l{constructor(t,e,i,n,r){this.parser=t,this.parse=e,this.overlay=i,this.target=n,this.from=r}}function ql(s){if(!s.length||s.some(t=>t.from>=t.to))throw new RangeError("Invalid inner parse ranges given: "+JSON.stringify(s))}class nm{constructor(t,e,i,n,r,o,l){this.parser=t,this.predicate=e,this.mounts=i,this.index=n,this.start=r,this.target=o,this.prev=l,this.depth=0,this.ranges=[]}}const Lr=new N({perNode:!0});class sm{constructor(t,e,i,n,r){this.nest=e,this.input=i,this.fragments=n,this.ranges=r,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=t}advance(){if(this.baseParse){let i=this.baseParse.advance();if(!i)return null;if(this.baseParse=null,this.baseTree=i,this.startInner(),this.stoppedAt!=null)for(let n of this.inner)n.parse.stopAt(this.stoppedAt)}if(this.innerDone==this.inner.length){let i=this.baseTree;return this.stoppedAt!=null&&(i=new U(i.type,i.children,i.positions,i.length,i.propValues.concat([[Lr,this.stoppedAt]]))),i}let t=this.inner[this.innerDone],e=t.parse.advance();if(e){this.innerDone++;let i=Object.assign(Object.create(null),t.target.props);i[N.mounted.id]=new $i(e,t.overlay,t.parser),t.target.props=i}return null}get parsedPos(){if(this.baseParse)return 0;let t=this.input.length;for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].from<t&&(t=Math.min(t,this.inner[e].parse.parsedPos));return t}stopAt(t){if(this.stoppedAt=t,this.baseParse)this.baseParse.stopAt(t);else for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].parse.stopAt(t)}startInner(){let t=new lm(this.fragments),e=null,i=null,n=new Xn(new mt(this.baseTree,this.ranges[0].from,0,null),J.IncludeAnonymous|J.IgnoreMounts);t:for(let r,o;;){let l=!0,a;if(this.stoppedAt!=null&&n.from>=this.stoppedAt)l=!1;else if(t.hasNode(n)){if(e){let h=e.mounts.find(c=>c.frag.from<=n.from&&c.frag.to>=n.to&&c.mount.overlay);if(h)for(let c of h.mount.overlay){let f=c.from+h.pos,u=c.to+h.pos;f>=n.from&&u<=n.to&&!e.ranges.some(d=>d.from<u&&d.to>f)&&e.ranges.push({from:f,to:u})}}l=!1}else if(i&&(o=rm(i.ranges,n.from,n.to)))l=o!=2;else if(!n.type.isAnonymous&&(r=this.nest(n,this.input))&&(n.from<n.to||!r.overlay)){n.tree||om(n);let h=t.findMounts(n.from,r.parser);if(typeof r.overlay=="function")e=new nm(r.parser,r.overlay,h,this.inner.length,n.from,n.tree,e);else{let c=Ul(this.ranges,r.overlay||(n.from<n.to?[new Ft(n.from,n.to)]:[]));c.length&&ql(c),(c.length||!r.overlay)&&this.inner.push(new $l(r.parser,c.length?r.parser.startParse(this.input,Gl(h,c),c):r.parser.startParse(""),r.overlay?r.overlay.map(f=>new Ft(f.from-n.from,f.to-n.from)):null,n.tree,c.length?c[0].from:n.from)),r.overlay?c.length&&(i={ranges:c,depth:0,prev:i}):l=!1}}else if(e&&(a=e.predicate(n))&&(a===!0&&(a=new Ft(n.from,n.to)),a.from<a.to)){let h=e.ranges.length-1;h>=0&&e.ranges[h].to==a.from?e.ranges[h]={from:e.ranges[h].from,to:a.to}:e.ranges.push(a)}if(l&&n.firstChild())e&&e.depth++,i&&i.depth++;else for(;!n.nextSibling();){if(!n.parent())break t;if(e&&!--e.depth){let h=Ul(this.ranges,e.ranges);h.length&&(ql(h),this.inner.splice(e.index,0,new $l(e.parser,e.parser.startParse(this.input,Gl(e.mounts,h),h),e.ranges.map(c=>new Ft(c.from-e.start,c.to-e.start)),e.target,h[0].from))),e=e.prev}i&&!--i.depth&&(i=i.prev)}}}}function rm(s,t,e){for(let i of s){if(i.from>=e)break;if(i.to>t)return i.from<=t&&i.to>=e?2:1}return 0}function jl(s,t,e,i,n,r){if(t<e){let o=s.buffer[t+1];i.push(s.slice(t,e,o)),n.push(o-r)}}function om(s){let{node:t}=s,e=[],i=t.context.buffer;do e.push(s.index),s.parent();while(!s.tree);let n=s.tree,r=n.children.indexOf(i),o=n.children[r],l=o.buffer,a=[r];function h(c,f,u,d,p,m){let g=e[m],y=[],w=[];jl(o,c,g,y,w,d);let S=l[g+1],k=l[g+2];a.push(y.length);let x=m?h(g+4,l[g+3],o.set.types[l[g]],S,k-S,m-1):t.toTree();return y.push(x),w.push(S-d),jl(o,l[g+3],f,y,w,d),new U(u,y,w,p)}n.children[r]=h(0,l.length,vt.none,0,o.length,e.length-1);for(let c of a){let f=s.tree.children[c],u=s.tree.positions[c];s.yield(new mt(f,u+s.from,c,s._tree))}}class Kl{constructor(t,e){this.offset=e,this.done=!1,this.cursor=t.cursor(J.IncludeAnonymous|J.IgnoreMounts)}moveTo(t){let{cursor:e}=this,i=t-this.offset;for(;!this.done&&e.from<i;)e.to>=t&&e.enter(i,1,J.IgnoreOverlays|J.ExcludeBuffers)||e.next(!1)||(this.done=!0)}hasNode(t){if(this.moveTo(t.from),!this.done&&this.cursor.from+this.offset==t.from&&this.cursor.tree)for(let e=this.cursor.tree;;){if(e==t.tree)return!0;if(e.children.length&&e.positions[0]==0&&e.children[0]instanceof U)e=e.children[0];else break}return!1}}class lm{constructor(t){var e;if(this.fragments=t,this.curTo=0,this.fragI=0,t.length){let i=this.curFrag=t[0];this.curTo=(e=i.tree.prop(Lr))!==null&&e!==void 0?e:i.to,this.inner=new Kl(i.tree,-i.offset)}else this.curFrag=this.inner=null}hasNode(t){for(;this.curFrag&&t.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=t.from&&this.curTo>=t.to&&this.inner.hasNode(t)}nextFrag(){var t;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else{let e=this.curFrag=this.fragments[this.fragI];this.curTo=(t=e.tree.prop(Lr))!==null&&t!==void 0?t:e.to,this.inner=new Kl(e.tree,-e.offset)}}findMounts(t,e){var i;let n=[];if(this.inner){this.inner.cursor.moveTo(t,1);for(let r=this.inner.cursor.node;r;r=r.parent){let o=(i=r.tree)===null||i===void 0?void 0:i.prop(N.mounted);if(o&&o.parser==e)for(let l=this.fragI;l<this.fragments.length;l++){let a=this.fragments[l];if(a.from>=r.to)break;a.tree==this.curFrag.tree&&n.push({frag:a,pos:r.from-a.offset,mount:o})}}}return n}}function Ul(s,t){let e=null,i=t;for(let n=1,r=0;n<s.length;n++){let o=s[n-1].to,l=s[n].from;for(;r<i.length;r++){let a=i[r];if(a.from>=l)break;a.to<=o||(e||(i=e=t.slice()),a.from<o?(e[r]=new Ft(a.from,o),a.to>l&&e.splice(r+1,0,new Ft(l,a.to))):a.to>l?e[r--]=new Ft(l,a.to):e.splice(r--,1))}}return i}function am(s,t,e,i){let n=0,r=0,o=!1,l=!1,a=-1e9,h=[];for(;;){let c=n==s.length?1e9:o?s[n].to:s[n].from,f=r==t.length?1e9:l?t[r].to:t[r].from;if(o!=l){let u=Math.max(a,e),d=Math.min(c,f,i);u<d&&h.push(new Ft(u,d))}if(a=Math.min(c,f),a==1e9)break;c==a&&(o?(o=!1,n++):o=!0),f==a&&(l?(l=!1,r++):l=!0)}return h}function Gl(s,t){let e=[];for(let{pos:i,mount:n,frag:r}of s){let o=i+(n.overlay?n.overlay[0].from:0),l=o+n.tree.length,a=Math.max(r.from,o),h=Math.min(r.to,l);if(n.overlay){let c=n.overlay.map(u=>new Ft(u.from+i,u.to+i)),f=am(t,c,a,h);for(let u=0,d=a;;u++){let p=u==f.length,m=p?h:f[u].from;if(m>d&&e.push(new me(d,m,n.tree,-o,r.from>=d||r.openStart,r.to<=m||r.openEnd)),p)break;d=f[u].to}}else e.push(new me(a,h,n.tree,-o,r.from>=o||r.openStart,r.to<=l||r.openEnd))}return e}let hm=0;class _t{constructor(t,e,i,n){this.name=t,this.set=e,this.base=i,this.modified=n,this.id=hm++}toString(){let{name:t}=this;for(let e of this.modified)e.name&&(t=`${e.name}(${t})`);return t}static define(t,e){let i=typeof t=="string"?t:"?";if(t instanceof _t&&(e=t),e?.base)throw new Error("Can not derive from a modified tag");let n=new _t(i,[],null,[]);if(n.set.push(n),e)for(let r of e.set)n.set.push(r);return n}static defineModifier(t){let e=new Qn(t);return i=>i.modified.indexOf(e)>-1?i:Qn.get(i.base||i,i.modified.concat(e).sort((n,r)=>n.id-r.id))}}let cm=0;class Qn{constructor(t){this.name=t,this.instances=[],this.id=cm++}static get(t,e){if(!e.length)return t;let i=e[0].instances.find(l=>l.base==t&&fm(e,l.modified));if(i)return i;let n=[],r=new _t(t.name,n,t,e);for(let l of e)l.instances.push(r);let o=um(e);for(let l of t.set)if(!l.modified.length)for(let a of o)n.push(Qn.get(l,a));return r}}function fm(s,t){return s.length==t.length&&s.every((e,i)=>e==t[i])}function um(s){let t=[[]];for(let e=0;e<s.length;e++)for(let i=0,n=t.length;i<n;i++)t.push(t[i].concat(s[e]));return t.sort((e,i)=>i.length-e.length)}function dm(s){let t=Object.create(null);for(let e in s){let i=s[e];Array.isArray(i)||(i=[i]);for(let n of e.split(" "))if(n){let r=[],o=2,l=n;for(let f=0;;){if(l=="..."&&f>0&&f+3==n.length){o=1;break}let u=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(l);if(!u)throw new RangeError("Invalid path: "+n);if(r.push(u[0]=="*"?"":u[0][0]=='"'?JSON.parse(u[0]):u[0]),f+=u[0].length,f==n.length)break;let d=n[f++];if(f==n.length&&d=="!"){o=0;break}if(d!="/")throw new RangeError("Invalid path: "+n);l=n.slice(f)}let a=r.length-1,h=r[a];if(!h)throw new RangeError("Invalid path: "+n);let c=new Zn(i,o,a>0?r.slice(0,a):null);t[h]=c.sort(t[h])}}return xc.add(t)}const xc=new N;class Zn{constructor(t,e,i,n){this.tags=t,this.mode=e,this.context=i,this.next=n}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}Zn.empty=new Zn([],2,null);function vc(s,t){let e=Object.create(null);for(let r of s)if(!Array.isArray(r.tag))e[r.tag.id]=r.class;else for(let o of r.tag)e[o.id]=r.class;let{scope:i,all:n=null}=t||{};return{style:r=>{let o=n;for(let l of r)for(let a of l.set){let h=e[a.id];if(h){o=o?o+" "+h:h;break}}return o},scope:i}}function pm(s,t){let e=null;for(let i of s){let n=i.style(t);n&&(e=e?e+" "+n:n)}return e}function mm(s,t,e,i=0,n=s.length){let r=new gm(i,Array.isArray(t)?t:[t],e);r.highlightRange(s.cursor(),i,n,"",r.highlighters),r.flush(n)}class gm{constructor(t,e,i){this.at=t,this.highlighters=e,this.span=i,this.class=""}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e)}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class)}highlightRange(t,e,i,n,r){let{type:o,from:l,to:a}=t;if(l>=i||a<=e)return;o.isTop&&(r=this.highlighters.filter(d=>!d.scope||d.scope(o)));let h=n,c=bm(t)||Zn.empty,f=pm(r,c.tags);if(f&&(h&&(h+=" "),h+=f,c.mode==1&&(n+=(n?" ":"")+f)),this.startSpan(Math.max(e,l),h),c.opaque)return;let u=t.tree&&t.tree.prop(N.mounted);if(u&&u.overlay){let d=t.node.enter(u.overlay[0].from+l,1),p=this.highlighters.filter(g=>!g.scope||g.scope(u.tree.type)),m=t.firstChild();for(let g=0,y=l;;g++){let w=g<u.overlay.length?u.overlay[g]:null,S=w?w.from+l:a,k=Math.max(e,y),x=Math.min(i,S);if(k<x&&m)for(;t.from<x&&(this.highlightRange(t,k,x,n,r),this.startSpan(Math.min(x,t.to),h),!(t.to>=S||!t.nextSibling())););if(!w||S>i)break;y=w.to+l,y>e&&(this.highlightRange(d.cursor(),Math.max(e,w.from+l),Math.min(i,y),"",p),this.startSpan(Math.min(i,y),h))}m&&t.parent()}else if(t.firstChild()){u&&(n="");do if(!(t.to<=e)){if(t.from>=i)break;this.highlightRange(t,e,i,n,r),this.startSpan(Math.min(i,t.to),h)}while(t.nextSibling());t.parent()}}}function bm(s){let t=s.type.prop(xc);for(;t&&t.context&&!s.matchContext(t.context);)t=t.next;return t||null}const D=_t.define,kn=D(),ye=D(),Yl=D(ye),Jl=D(ye),we=D(),Sn=D(we),Ps=D(we),te=D(),Ee=D(te),Qt=D(),Zt=D(),Rr=D(),ki=D(Rr),Cn=D(),b={comment:kn,lineComment:D(kn),blockComment:D(kn),docComment:D(kn),name:ye,variableName:D(ye),typeName:Yl,tagName:D(Yl),propertyName:Jl,attributeName:D(Jl),className:D(ye),labelName:D(ye),namespace:D(ye),macroName:D(ye),literal:we,string:Sn,docString:D(Sn),character:D(Sn),attributeValue:D(Sn),number:Ps,integer:D(Ps),float:D(Ps),bool:D(we),regexp:D(we),escape:D(we),color:D(we),url:D(we),keyword:Qt,self:D(Qt),null:D(Qt),atom:D(Qt),unit:D(Qt),modifier:D(Qt),operatorKeyword:D(Qt),controlKeyword:D(Qt),definitionKeyword:D(Qt),moduleKeyword:D(Qt),operator:Zt,derefOperator:D(Zt),arithmeticOperator:D(Zt),logicOperator:D(Zt),bitwiseOperator:D(Zt),compareOperator:D(Zt),updateOperator:D(Zt),definitionOperator:D(Zt),typeOperator:D(Zt),controlOperator:D(Zt),punctuation:Rr,separator:D(Rr),bracket:ki,angleBracket:D(ki),squareBracket:D(ki),paren:D(ki),brace:D(ki),content:te,heading:Ee,heading1:D(Ee),heading2:D(Ee),heading3:D(Ee),heading4:D(Ee),heading5:D(Ee),heading6:D(Ee),contentSeparator:D(te),list:D(te),quote:D(te),emphasis:D(te),strong:D(te),link:D(te),monospace:D(te),strikethrough:D(te),inserted:D(),deleted:D(),changed:D(),invalid:D(),meta:Cn,documentMeta:D(Cn),annotation:D(Cn),processingInstruction:D(Cn),definition:_t.defineModifier("definition"),constant:_t.defineModifier("constant"),function:_t.defineModifier("function"),standard:_t.defineModifier("standard"),local:_t.defineModifier("local"),special:_t.defineModifier("special")};for(let s in b){let t=b[s];t instanceof _t&&(t.name=s)}vc([{tag:b.link,class:"tok-link"},{tag:b.heading,class:"tok-heading"},{tag:b.emphasis,class:"tok-emphasis"},{tag:b.strong,class:"tok-strong"},{tag:b.keyword,class:"tok-keyword"},{tag:b.atom,class:"tok-atom"},{tag:b.bool,class:"tok-bool"},{tag:b.url,class:"tok-url"},{tag:b.labelName,class:"tok-labelName"},{tag:b.inserted,class:"tok-inserted"},{tag:b.deleted,class:"tok-deleted"},{tag:b.literal,class:"tok-literal"},{tag:b.string,class:"tok-string"},{tag:b.number,class:"tok-number"},{tag:[b.regexp,b.escape,b.special(b.string)],class:"tok-string2"},{tag:b.variableName,class:"tok-variableName"},{tag:b.local(b.variableName),class:"tok-variableName tok-local"},{tag:b.definition(b.variableName),class:"tok-variableName tok-definition"},{tag:b.special(b.variableName),class:"tok-variableName2"},{tag:b.definition(b.propertyName),class:"tok-propertyName tok-definition"},{tag:b.typeName,class:"tok-typeName"},{tag:b.namespace,class:"tok-namespace"},{tag:b.className,class:"tok-className"},{tag:b.macroName,class:"tok-macroName"},{tag:b.propertyName,class:"tok-propertyName"},{tag:b.operator,class:"tok-operator"},{tag:b.comment,class:"tok-comment"},{tag:b.meta,class:"tok-meta"},{tag:b.invalid,class:"tok-invalid"},{tag:b.punctuation,class:"tok-punctuation"}]);var Es;const Ne=new N;function kc(s){return O.define({combine:s?t=>t.concat(s):void 0})}const ym=new N;class Ht{constructor(t,e,i=[],n=""){this.data=t,this.name=n,V.prototype.hasOwnProperty("tree")||Object.defineProperty(V.prototype,"tree",{get(){return ut(this)}}),this.parser=e,this.extension=[Be.of(this),V.languageData.of((r,o,l)=>{let a=Xl(r,o,l),h=a.type.prop(Ne);if(!h)return[];let c=r.facet(h),f=a.type.prop(ym);if(f){let u=a.resolve(o-a.from,l);for(let d of f)if(d.test(u,r)){let p=r.facet(d.facet);return d.type=="replace"?p:p.concat(c)}}return c})].concat(i)}isActiveAt(t,e,i=-1){return Xl(t,e,i).type.prop(Ne)==this.data}findRegions(t){let e=t.facet(Be);if(e?.data==this.data)return[{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return[];let i=[],n=(r,o)=>{if(r.prop(Ne)==this.data){i.push({from:o,to:o+r.length});return}let l=r.prop(N.mounted);if(l){if(l.tree.prop(Ne)==this.data){if(l.overlay)for(let a of l.overlay)i.push({from:a.from+o,to:a.to+o});else i.push({from:o,to:o+r.length});return}else if(l.overlay){let a=i.length;if(n(l.tree,l.overlay[0].from+o),i.length>a)return}}for(let a=0;a<r.children.length;a++){let h=r.children[a];h instanceof U&&n(h,r.positions[a]+o)}};return n(ut(t),0),i}get allowsNesting(){return!0}}Ht.setState=_.define();function Xl(s,t,e){let i=s.facet(Be),n=ut(s).topNode;if(!i||i.allowsNesting)for(let r=n;r;r=r.enter(t,e,J.ExcludeBuffers))r.type.isTop&&(n=r);return n}class _r extends Ht{constructor(t,e,i){super(t,e,[],i),this.parser=e}static define(t){let e=kc(t.languageData);return new _r(e,t.parser.configure({props:[Ne.add(i=>i.isTop?e:void 0)]}),t.name)}configure(t,e){return new _r(this.data,this.parser.configure(t),e||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function ut(s){let t=s.field(Ht.state,!1);return t?t.tree:U.empty}class wm{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter()}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,e){let i=this.cursorPos-this.string.length;return t<i||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-i,e-i)}}let Si=null;class ui{constructor(t,e,i=[],n,r,o,l,a){this.parser=t,this.state=e,this.fragments=i,this.tree=n,this.treeLen=r,this.viewport=o,this.skipped=l,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(t,e,i){return new ui(t,e,[],U.empty,0,i,[],null)}startParse(){return this.parser.startParse(new wm(this.state.doc),this.fragments)}work(t,e){return e!=null&&e>=this.state.doc.length&&(e=void 0),this.tree!=U.empty&&this.isDone(e??this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var i;if(typeof t=="number"){let n=Date.now()+t;t=()=>Date.now()>n}for(this.parse||(this.parse=this.startParse()),e!=null&&(this.parse.stoppedAt==null||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let n=this.parse.advance();if(n)if(this.fragments=this.withoutTempSkipped(me.addTree(n,this.fragments,this.parse.stoppedAt!=null)),this.treeLen=(i=this.parse.stoppedAt)!==null&&i!==void 0?i:this.state.doc.length,this.tree=n,this.parse=null,this.treeLen<(e??this.state.doc.length))this.parse=this.startParse();else return!0;if(t())return!1}})}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((this.parse.stoppedAt==null||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext(()=>{for(;!(e=this.parse.advance()););}),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(me.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let e=Si;Si=this;try{return t()}finally{Si=e}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=Ql(t,e.from,e.to);return t}changes(t,e){let{fragments:i,tree:n,treeLen:r,viewport:o,skipped:l}=this;if(this.takeTree(),!t.empty){let a=[];if(t.iterChangedRanges((h,c,f,u)=>a.push({fromA:h,toA:c,fromB:f,toB:u})),i=me.applyChanges(i,a),n=U.empty,r=0,o={from:t.mapPos(o.from,-1),to:t.mapPos(o.to,1)},this.skipped.length){l=[];for(let h of this.skipped){let c=t.mapPos(h.from,1),f=t.mapPos(h.to,-1);c<f&&l.push({from:c,to:f})}}}return new ui(this.parser,e,i,n,r,o,l,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let e=this.skipped.length;for(let i=0;i<this.skipped.length;i++){let{from:n,to:r}=this.skipped[i];n<t.to&&r>t.from&&(this.fragments=Ql(this.fragments,n,r),this.skipped.splice(i--,1))}return this.skipped.length>=e?!1:(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,e){this.skipped.push({from:t,to:e})}static getSkippingParser(t){return new class extends wc{createParse(e,i,n){let r=n[0].from,o=n[n.length-1].to;return{parsedPos:r,advance(){let a=Si;if(a){for(let h of n)a.tempSkipped.push(h);t&&(a.scheduleOn=a.scheduleOn?Promise.all([a.scheduleOn,t]):t)}return this.parsedPos=o,new U(vt.none,[],[],o-r)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&e[0].from==0&&e[0].to>=t}static get(){return Si}}function Ql(s,t,e){return me.applyChanges(s,[{fromA:t,toA:e,fromB:t,toB:e}])}class di{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let e=this.context.changes(t.changes,t.state),i=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,i)||e.takeTree(),new di(e)}static init(t){let e=Math.min(3e3,t.doc.length),i=ui.create(t.facet(Be).parser,t,{from:0,to:e});return i.work(20,e)||i.takeTree(),new di(i)}}Ht.state=Tt.define({create:di.init,update(s,t){for(let e of t.effects)if(e.is(Ht.setState))return e.value;return t.startState.facet(Be)!=t.state.facet(Be)?di.init(t.state):s.apply(t)}});let Sc=s=>{let t=setTimeout(()=>s(),500);return()=>clearTimeout(t)};typeof requestIdleCallback<"u"&&(Sc=s=>{let t=-1,e=setTimeout(()=>{t=requestIdleCallback(s,{timeout:400})},100);return()=>t<0?clearTimeout(e):cancelIdleCallback(t)});const Ls=typeof navigator<"u"&&(!((Es=navigator.scheduling)===null||Es===void 0)&&Es.isInputPending)?()=>navigator.scheduling.isInputPending():null,xm=ft.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let e=this.view.state.field(Ht.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e)}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(Ht.state);(e.tree!=e.context.tree||!e.context.isDone(t.doc.length))&&(this.working=Sc(this.work))}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,r=i.field(Ht.state);if(r.tree==r.context.tree&&r.context.isDone(n+1e5))return;let o=Date.now()+Math.min(this.chunkBudget,100,t&&!Ls?Math.max(25,t.timeRemaining()-5):1e9),l=r.context.treeLen<n&&i.doc.length>n+1e3,a=r.context.work(()=>Ls&&Ls()||Date.now()>o,n+(l?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:Ht.setState.of(new di(r.context))})),this.chunkBudget>0&&!(a&&!l)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then(()=>this.scheduleWork()).catch(e=>Mt(this.view.state,e)).then(()=>this.workScheduled--),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),Be=O.define({combine(s){return s.length?s[0]:null},enables:s=>[Ht.state,xm,B.contentAttributes.compute([s],t=>{let e=t.facet(s);return e&&e.name?{"data-language":e.name}:{}})]});class k1{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e]}}class Cc{constructor(t,e,i,n,r,o=void 0){this.name=t,this.alias=e,this.extensions=i,this.filename=n,this.loadFunc=r,this.support=o,this.loading=null}load(){return this.loading||(this.loading=this.loadFunc().then(t=>this.support=t,t=>{throw this.loading=null,t}))}static of(t){let{load:e,support:i}=t;if(!e){if(!i)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");e=()=>Promise.resolve(i)}return new Cc(t.name,(t.alias||[]).concat(t.name).map(n=>n.toLowerCase()),t.extensions||[],t.filename,e,i)}static matchFilename(t,e){for(let n of t)if(n.filename&&n.filename.test(e))return n;let i=/\.([^.]+)$/.exec(e);if(i){for(let n of t)if(n.extensions.indexOf(i[1])>-1)return n}return null}static matchLanguageName(t,e,i=!0){e=e.toLowerCase();for(let n of t)if(n.alias.some(r=>r==e))return n;if(i)for(let n of t)for(let r of n.alias){let o=e.indexOf(r);if(o>-1&&(r.length>2||!/\w/.test(e[o-1])&&!/\w/.test(e[o+r.length])))return n}return null}}const vm=O.define(),hs=O.define({combine:s=>{if(!s.length)return"  ";let t=s[0];if(!t||/\S/.test(t)||Array.from(t).some(e=>e!=t[0]))throw new Error("Invalid indent unit: "+JSON.stringify(s[0]));return t}});function qe(s){let t=s.facet(hs);return t.charCodeAt(0)==9?s.tabSize*t.length:t.length}function ji(s,t){let e="",i=s.tabSize,n=s.facet(hs)[0];if(n=="	"){for(;t>=i;)e+="	",t-=i;n=" "}for(let r=0;r<t;r++)e+=n;return e}function go(s,t){s instanceof V&&(s=new cs(s));for(let i of s.state.facet(vm)){let n=i(s,t);if(n!==void 0)return n}let e=ut(s.state);return e.length>=t?km(s,e,t):null}class cs{constructor(t,e={}){this.state=t,this.options=e,this.unit=qe(t)}lineAt(t,e=1){let i=this.state.doc.lineAt(t),{simulateBreak:n,simulateDoubleBreak:r}=this.options;return n!=null&&n>=i.from&&n<=i.to?r&&n==t?{text:"",from:t}:(e<0?n<t:n<=t)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return"";let{text:i,from:n}=this.lineAt(t,e);return i.slice(t-n,Math.min(i.length,t+100-n))}column(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.countColumn(i,t-n),o=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return o>-1&&(r+=o-this.countColumn(i,i.search(/\S|$/))),r}countColumn(t,e=t.length){return mi(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.options.overrideIndentation;if(r){let o=r(n);if(o>-1)return o}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const Ac=new N;function km(s,t,e){let i=t.resolveStack(e),n=t.resolveInner(e,-1).resolve(e,0).enterUnfinishedNodesBefore(e);if(n!=i.node){let r=[];for(let o=n;o&&!(o.from==i.node.from&&o.type==i.node.type);o=o.parent)r.push(o);for(let o=r.length-1;o>=0;o--)i={node:r[o],next:i}}return Mc(i,s,e)}function Mc(s,t,e){for(let i=s;i;i=i.next){let n=Cm(i.node);if(n)return n(bo.create(t,e,i))}return 0}function Sm(s){return s.pos==s.options.simulateBreak&&s.options.simulateDoubleBreak}function Cm(s){let t=s.type.prop(Ac);if(t)return t;let e=s.firstChild,i;if(e&&(i=e.type.prop(N.closedBy))){let n=s.lastChild,r=n&&i.indexOf(n.name)>-1;return o=>Dc(o,!0,1,void 0,r&&!Sm(o)?n.from:void 0)}return s.parent==null?Am:null}function Am(){return 0}class bo extends cs{constructor(t,e,i){super(t.state,t.options),this.base=t,this.pos=e,this.context=i}get node(){return this.context.node}static create(t,e,i){return new bo(t,e,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(t){let e=this.state.doc.lineAt(t.from);for(;;){let i=t.resolve(e.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(Mm(i,t))break;e=this.state.doc.lineAt(i.from)}return this.lineIndent(e.from)}continue(){return Mc(this.context.next,this.base,this.pos)}}function Mm(s,t){for(let e=t;e;e=e.parent)if(s==e)return!0;return!1}function Dm(s){let t=s.node,e=t.childAfter(t.from),i=t.lastChild;if(!e)return null;let n=s.options.simulateBreak,r=s.state.doc.lineAt(e.from),o=n==null||n<=r.from?r.to:Math.min(r.to,n);for(let l=e.to;;){let a=t.childAfter(l);if(!a||a==i)return null;if(!a.type.isSkipped){if(a.from>=o)return null;let h=/^ */.exec(r.text.slice(e.to-r.from))[0].length;return{from:e.from,to:e.to+h}}l=a.to}}function S1({closing:s,align:t=!0,units:e=1}){return i=>Dc(i,t,e,s)}function Dc(s,t,e,i,n){let r=s.textAfter,o=r.match(/^\s*/)[0].length,l=i&&r.slice(o,o+i.length)==i||n==s.pos+o,a=t?Dm(s):null;return a?l?s.column(a.from):s.column(a.to):s.baseIndent+(l?0:s.unit*e)}const C1=s=>s.baseIndent;function A1({except:s,units:t=1}={}){return e=>{let i=s&&s.test(e.textAfter);return e.baseIndent+(i?0:t*e.unit)}}const Tm=200;function Om(){return V.transactionFilter.of(s=>{if(!s.docChanged||!s.isUserEvent("input.type")&&!s.isUserEvent("input.complete"))return s;let t=s.startState.languageDataAt("indentOnInput",s.startState.selection.main.head);if(!t.length)return s;let e=s.newDoc,{head:i}=s.newSelection.main,n=e.lineAt(i);if(i>n.from+Tm)return s;let r=e.sliceString(n.from,i);if(!t.some(h=>h.test(r)))return s;let{state:o}=s,l=-1,a=[];for(let{head:h}of o.selection.ranges){let c=o.doc.lineAt(h);if(c.from==l)continue;l=c.from;let f=go(o,c.from);if(f==null)continue;let u=/^\s*/.exec(c.text)[0],d=ji(o,f);u!=d&&a.push({from:c.from,to:c.from+u.length,insert:d})}return a.length?[s,{changes:a,sequential:!0}]:s})}const Bm=O.define(),Pm=new N;function M1(s){let t=s.firstChild,e=s.lastChild;return t&&t.to<e.from?{from:t.to,to:e.type.isError?s.to:e.from}:null}function Em(s,t,e){let i=ut(s);if(i.length<e)return null;let n=i.resolveStack(e,1),r=null;for(let o=n;o;o=o.next){let l=o.node;if(l.to<=e||l.from>e)continue;if(r&&l.from<t)break;let a=l.type.prop(Pm);if(a&&(l.to<i.length-50||i.length==s.doc.length||!Lm(l))){let h=a(l,s);h&&h.from<=e&&h.from>=t&&h.to>e&&(r=h)}}return r}function Lm(s){let t=s.lastChild;return t&&t.to==s.to&&t.type.isError}function ts(s,t,e){for(let i of s.facet(Bm)){let n=i(s,t,e);if(n)return n}return Em(s,t,e)}function Tc(s,t){let e=t.mapPos(s.from,1),i=t.mapPos(s.to,-1);return e>=i?void 0:{from:e,to:i}}const fs=_.define({map:Tc}),sn=_.define({map:Tc});function Oc(s){let t=[];for(let{head:e}of s.state.selection.ranges)t.some(i=>i.from<=e&&i.to>=e)||t.push(s.lineBlockAt(e));return t}const je=Tt.define({create(){return I.none},update(s,t){s=s.map(t.changes);for(let e of t.effects)if(e.is(fs)&&!Rm(s,e.value.from,e.value.to)){let{preparePlaceholder:i}=t.state.facet(Ec),n=i?I.replace({widget:new Wm(i(t.state,e.value))}):Zl;s=s.update({add:[n.range(e.value.from,e.value.to)]})}else e.is(sn)&&(s=s.update({filter:(i,n)=>e.value.from!=i||e.value.to!=n,filterFrom:e.value.from,filterTo:e.value.to}));if(t.selection){let e=!1,{head:i}=t.selection.main;s.between(i,i,(n,r)=>{n<i&&r>i&&(e=!0)}),e&&(s=s.update({filterFrom:i,filterTo:i,filter:(n,r)=>r<=i||n>=i}))}return s},provide:s=>B.decorations.from(s),toJSON(s,t){let e=[];return s.between(0,t.doc.length,(i,n)=>{e.push(i,n)}),e},fromJSON(s){if(!Array.isArray(s)||s.length%2)throw new RangeError("Invalid JSON for fold state");let t=[];for(let e=0;e<s.length;){let i=s[e++],n=s[e++];if(typeof i!="number"||typeof n!="number")throw new RangeError("Invalid JSON for fold state");t.push(Zl.range(i,n))}return I.set(t,!0)}});function es(s,t,e){var i;let n=null;return(i=s.field(je,!1))===null||i===void 0||i.between(t,e,(r,o)=>{(!n||n.from>r)&&(n={from:r,to:o})}),n}function Rm(s,t,e){let i=!1;return s.between(t,t,(n,r)=>{n==t&&r==e&&(i=!0)}),i}function Bc(s,t){return s.field(je,!1)?t:t.concat(_.appendConfig.of(Lc()))}const _m=s=>{for(let t of Oc(s)){let e=ts(s.state,t.from,t.to);if(e)return s.dispatch({effects:Bc(s.state,[fs.of(e),Pc(s,e)])}),!0}return!1},Im=s=>{if(!s.state.field(je,!1))return!1;let t=[];for(let e of Oc(s)){let i=es(s.state,e.from,e.to);i&&t.push(sn.of(i),Pc(s,i,!1))}return t.length&&s.dispatch({effects:t}),t.length>0};function Pc(s,t,e=!0){let i=s.state.doc.lineAt(t.from).number,n=s.state.doc.lineAt(t.to).number;return B.announce.of(`${s.state.phrase(e?"Folded lines":"Unfolded lines")} ${i} ${s.state.phrase("to")} ${n}.`)}const Nm=s=>{let{state:t}=s,e=[];for(let i=0;i<t.doc.length;){let n=s.lineBlockAt(i),r=ts(t,n.from,n.to);r&&e.push(fs.of(r)),i=(r?s.lineBlockAt(r.to):n).to+1}return e.length&&s.dispatch({effects:Bc(s.state,e)}),!!e.length},Fm=s=>{let t=s.state.field(je,!1);if(!t||!t.size)return!1;let e=[];return t.between(0,s.state.doc.length,(i,n)=>{e.push(sn.of({from:i,to:n}))}),s.dispatch({effects:e}),!0},Hm=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:_m},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:Im},{key:"Ctrl-Alt-[",run:Nm},{key:"Ctrl-Alt-]",run:Fm}],Vm={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},Ec=O.define({combine(s){return Je(s,Vm)}});function Lc(s){return[je,qm]}function Rc(s,t){let{state:e}=s,i=e.facet(Ec),n=o=>{let l=s.lineBlockAt(s.posAtDOM(o.target)),a=es(s.state,l.from,l.to);a&&s.dispatch({effects:sn.of(a)}),o.preventDefault()};if(i.placeholderDOM)return i.placeholderDOM(s,n,t);let r=document.createElement("span");return r.textContent=i.placeholderText,r.setAttribute("aria-label",e.phrase("folded code")),r.title=e.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=n,r}const Zl=I.replace({widget:new class extends he{toDOM(s){return Rc(s,null)}}});class Wm extends he{constructor(t){super(),this.value=t}eq(t){return this.value==t.value}toDOM(t){return Rc(t,this.value)}}const zm={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class Rs extends Te{constructor(t,e){super(),this.config=t,this.open=e}eq(t){return this.config==t.config&&this.open==t.open}toDOM(t){if(this.config.markerDOM)return this.config.markerDOM(this.open);let e=document.createElement("span");return e.textContent=this.open?this.config.openText:this.config.closedText,e.title=t.state.phrase(this.open?"Fold line":"Unfold line"),e}}function $m(s={}){let t=Object.assign(Object.assign({},zm),s),e=new Rs(t,!0),i=new Rs(t,!1),n=ft.fromClass(class{constructor(o){this.from=o.viewport.from,this.markers=this.buildMarkers(o)}update(o){(o.docChanged||o.viewportChanged||o.startState.facet(Be)!=o.state.facet(Be)||o.startState.field(je,!1)!=o.state.field(je,!1)||ut(o.startState)!=ut(o.state)||t.foldingChanged(o))&&(this.markers=this.buildMarkers(o.view))}buildMarkers(o){let l=new Ce;for(let a of o.viewportLineBlocks){let h=es(o.state,a.from,a.to)?i:ts(o.state,a.from,a.to)?e:null;h&&l.add(a.from,a.from,h)}return l.finish()}}),{domEventHandlers:r}=t;return[n,Wp({class:"cm-foldGutter",markers(o){var l;return((l=o.plugin(n))===null||l===void 0?void 0:l.markers)||z.empty},initialSpacer(){return new Rs(t,!1)},domEventHandlers:Object.assign(Object.assign({},r),{click:(o,l,a)=>{if(r.click&&r.click(o,l,a))return!0;let h=es(o.state,l.from,l.to);if(h)return o.dispatch({effects:sn.of(h)}),!0;let c=ts(o.state,l.from,l.to);return c?(o.dispatch({effects:fs.of(c)}),!0):!1}})}),Lc()]}const qm=B.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class gi{constructor(t,e){this.specs=t;let i;function n(l){let a=Ae.newName();return(i||(i=Object.create(null)))["."+a]=l,a}const r=typeof e.all=="string"?e.all:e.all?n(e.all):void 0,o=e.scope;this.scope=o instanceof Ht?l=>l.prop(Ne)==o.data:o?l=>l==o:void 0,this.style=vc(t.map(l=>({tag:l.tag,class:l.class||n(Object.assign({},l,{tag:null}))})),{all:r}).style,this.module=i?new Ae(i):null,this.themeType=e.themeType}static define(t,e){return new gi(t,e||{})}}const Ir=O.define(),_c=O.define({combine(s){return s.length?[s[0]]:null}});function _s(s){let t=s.facet(Ir);return t.length?t:s.facet(_c)}function yo(s,t){let e=[Km],i;return s instanceof gi&&(s.module&&e.push(B.styleModule.of(s.module)),i=s.themeType),t?.fallback?e.push(_c.of(s)):i?e.push(Ir.computeN([B.darkTheme],n=>n.facet(B.darkTheme)==(i=="dark")?[s]:[])):e.push(Ir.of(s)),e}class jm{constructor(t){this.markCache=Object.create(null),this.tree=ut(t.state),this.decorations=this.buildDeco(t,_s(t.state)),this.decoratedTo=t.viewport.to}update(t){let e=ut(t.state),i=_s(t.state),n=i!=_s(t.startState),{viewport:r}=t.view,o=t.changes.mapPos(this.decoratedTo,1);e.length<r.to&&!n&&e.type==this.tree.type&&o>=r.to?(this.decorations=this.decorations.map(t.changes),this.decoratedTo=o):(e!=this.tree||t.viewportChanged||n)&&(this.tree=e,this.decorations=this.buildDeco(t.view,i),this.decoratedTo=r.to)}buildDeco(t,e){if(!e||!this.tree.length)return I.none;let i=new Ce;for(let{from:n,to:r}of t.visibleRanges)mm(this.tree,e,(o,l,a)=>{i.add(o,l,this.markCache[a]||(this.markCache[a]=I.mark({class:a})))},n,r);return i.finish()}}const Km=Ye.high(ft.fromClass(jm,{decorations:s=>s.decorations})),Um=gi.define([{tag:b.meta,color:"#404740"},{tag:b.link,textDecoration:"underline"},{tag:b.heading,textDecoration:"underline",fontWeight:"bold"},{tag:b.emphasis,fontStyle:"italic"},{tag:b.strong,fontWeight:"bold"},{tag:b.strikethrough,textDecoration:"line-through"},{tag:b.keyword,color:"#708"},{tag:[b.atom,b.bool,b.url,b.contentSeparator,b.labelName],color:"#219"},{tag:[b.literal,b.inserted],color:"#164"},{tag:[b.string,b.deleted],color:"#a11"},{tag:[b.regexp,b.escape,b.special(b.string)],color:"#e40"},{tag:b.definition(b.variableName),color:"#00f"},{tag:b.local(b.variableName),color:"#30a"},{tag:[b.typeName,b.namespace],color:"#085"},{tag:b.className,color:"#167"},{tag:[b.special(b.variableName),b.macroName],color:"#256"},{tag:b.definition(b.propertyName),color:"#00c"},{tag:b.comment,color:"#940"},{tag:b.invalid,color:"#f00"}]),Gm=1e4,Ym="()[]{}",Jm=new N;function Nr(s,t,e){let i=s.prop(t<0?N.openedBy:N.closedBy);if(i)return i;if(s.name.length==1){let n=e.indexOf(s.name);if(n>-1&&n%2==(t<0?1:0))return[e[n+t]]}return null}function Fr(s){let t=s.type.prop(Jm);return t?t(s.node):s}function ei(s,t,e,i={}){let n=i.maxScanDistance||Gm,r=i.brackets||Ym,o=ut(s),l=o.resolveInner(t,e);for(let a=l;a;a=a.parent){let h=Nr(a.type,e,r);if(h&&a.from<a.to){let c=Fr(a);if(c&&(e>0?t>=c.from&&t<c.to:t>c.from&&t<=c.to))return Xm(s,t,e,a,c,h,r)}}return Qm(s,t,e,o,l.type,n,r)}function Xm(s,t,e,i,n,r,o){let l=i.parent,a={from:n.from,to:n.to},h=0,c=l?.cursor();if(c&&(e<0?c.childBefore(i.from):c.childAfter(i.to)))do if(e<0?c.to<=i.from:c.from>=i.to){if(h==0&&r.indexOf(c.type.name)>-1&&c.from<c.to){let f=Fr(c);return{start:a,end:f?{from:f.from,to:f.to}:void 0,matched:!0}}else if(Nr(c.type,e,o))h++;else if(Nr(c.type,-e,o)){if(h==0){let f=Fr(c);return{start:a,end:f&&f.from<f.to?{from:f.from,to:f.to}:void 0,matched:!1}}h--}}while(e<0?c.prevSibling():c.nextSibling());return{start:a,matched:!1}}function Qm(s,t,e,i,n,r,o){let l=e<0?s.sliceDoc(t-1,t):s.sliceDoc(t,t+1),a=o.indexOf(l);if(a<0||a%2==0!=e>0)return null;let h={from:e<0?t-1:t,to:e>0?t+1:t},c=s.doc.iterRange(t,e>0?s.doc.length:0),f=0;for(let u=0;!c.next().done&&u<=r;){let d=c.value;e<0&&(u+=d.length);let p=t+u*e;for(let m=e>0?0:d.length-1,g=e>0?d.length:-1;m!=g;m+=e){let y=o.indexOf(d[m]);if(!(y<0||i.resolveInner(p+m,1).type!=n))if(y%2==0==e>0)f++;else{if(f==1)return{start:h,end:{from:p+m,to:p+m+1},matched:y>>1==a>>1};f--}}e>0&&(u+=d.length)}return c.done?{start:h,matched:!1}:null}function ta(s,t,e,i=0,n=0){t==null&&(t=s.search(/[^\s\u00a0]/),t==-1&&(t=s.length));let r=n;for(let o=i;o<t;o++)s.charCodeAt(o)==9?r+=e-r%e:r++;return r}class Ic{constructor(t,e,i,n){this.string=t,this.tabSize=e,this.indentUnit=i,this.overrideIndent=n,this.pos=0,this.start=0,this.lastColumnPos=0,this.lastColumnValue=0}eol(){return this.pos>=this.string.length}sol(){return this.pos==0}peek(){return this.string.charAt(this.pos)||void 0}next(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)}eat(t){let e=this.string.charAt(this.pos),i;if(typeof t=="string"?i=e==t:i=e&&(t instanceof RegExp?t.test(e):t(e)),i)return++this.pos,e}eatWhile(t){let e=this.pos;for(;this.eat(t););return this.pos>e}eatSpace(){let t=this.pos;for(;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t}skipToEnd(){this.pos=this.string.length}skipTo(t){let e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0}backUp(t){this.pos-=t}column(){return this.lastColumnPos<this.start&&(this.lastColumnValue=ta(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue}indentation(){var t;return(t=this.overrideIndent)!==null&&t!==void 0?t:ta(this.string,null,this.tabSize)}match(t,e,i){if(typeof t=="string"){let n=o=>i?o.toLowerCase():o,r=this.string.substr(this.pos,t.length);return n(r)==n(t)?(e!==!1&&(this.pos+=t.length),!0):null}else{let n=this.string.slice(this.pos).match(t);return n&&n.index>0?null:(n&&e!==!1&&(this.pos+=n[0].length),n)}}current(){return this.string.slice(this.start,this.pos)}}function Zm(s){return{name:s.name||"",token:s.token,blankLine:s.blankLine||(()=>{}),startState:s.startState||(()=>!0),copyState:s.copyState||tg,indent:s.indent||(()=>null),languageData:s.languageData||{},tokenTable:s.tokenTable||xo}}function tg(s){if(typeof s!="object")return s;let t={};for(let e in s){let i=s[e];t[e]=i instanceof Array?i.slice():i}return t}const ea=new WeakMap;class Kt extends Ht{constructor(t){let e=kc(t.languageData),i=Zm(t),n,r=new class extends wc{createParse(o,l,a){return new ig(n,o,l,a)}};super(e,r,[],t.name),this.topNode=rg(e,this),n=this,this.streamParser=i,this.stateAfter=new N({perNode:!0}),this.tokenTable=t.tokenTable?new Vc(i.tokenTable):sg}static define(t){return new Kt(t)}getIndent(t){let e,{overrideIndentation:i}=t.options;i&&(e=ea.get(t.state),e!=null&&e<t.pos-1e4&&(e=void 0));let n=wo(this,t.node.tree,t.node.from,t.node.from,e??t.pos),r,o;if(n?(o=n.state,r=n.pos+1):(o=this.streamParser.startState(t.unit),r=t.node.from),t.pos-r>1e4)return null;for(;r<t.pos;){let a=t.state.doc.lineAt(r),h=Math.min(t.pos,a.to);if(a.length){let c=i?i(a.from):-1,f=new Ic(a.text,t.state.tabSize,t.unit,c<0?void 0:c);for(;f.pos<h-a.from;)Fc(this.streamParser.token,f,o)}else this.streamParser.blankLine(o,t.unit);if(h==t.pos)break;r=a.to+1}let l=t.lineAt(t.pos);return i&&e==null&&ea.set(t.state,l.from),this.streamParser.indent(o,/^\s*(.*)/.exec(l.text)[1],t)}get allowsNesting(){return!1}}function wo(s,t,e,i,n){let r=e>=i&&e+t.length<=n&&t.prop(s.stateAfter);if(r)return{state:s.streamParser.copyState(r),pos:e+t.length};for(let o=t.children.length-1;o>=0;o--){let l=t.children[o],a=e+t.positions[o],h=l instanceof U&&a<n&&wo(s,l,a,i,n);if(h)return h}return null}function Nc(s,t,e,i,n){if(n&&e<=0&&i>=t.length)return t;!n&&e==0&&t.type==s.topNode&&(n=!0);for(let r=t.children.length-1;r>=0;r--){let o=t.positions[r],l=t.children[r],a;if(o<i&&l instanceof U){if(!(a=Nc(s,l,e-o,i-o,n)))break;return n?new U(t.type,t.children.slice(0,r).concat(a),t.positions.slice(0,r+1),o+a.length):a}}return null}function eg(s,t,e,i,n){for(let r of t){let o=r.from+(r.openStart?25:0),l=r.to-(r.openEnd?25:0),a=o<=e&&l>e&&wo(s,r.tree,0-r.offset,e,l),h;if(a&&a.pos<=i&&(h=Nc(s,r.tree,e+r.offset,a.pos+r.offset,!1)))return{state:a.state,tree:h}}return{state:s.streamParser.startState(n?qe(n):4),tree:U.empty}}class ig{constructor(t,e,i,n){this.lang=t,this.input=e,this.fragments=i,this.ranges=n,this.stoppedAt=null,this.chunks=[],this.chunkPos=[],this.chunk=[],this.chunkReused=void 0,this.rangeIndex=0,this.to=n[n.length-1].to;let r=ui.get(),o=n[0].from,{state:l,tree:a}=eg(t,i,o,this.to,r?.state);this.state=l,this.parsedPos=this.chunkStart=o+a.length;for(let h=0;h<a.children.length;h++)this.chunks.push(a.children[h]),this.chunkPos.push(a.positions[h]);r&&this.parsedPos<r.viewport.from-1e5&&n.some(h=>h.from<=r.viewport.from&&h.to>=r.viewport.from)&&(this.state=this.lang.streamParser.startState(qe(r.state)),r.skipUntilInView(this.parsedPos,r.viewport.from),this.parsedPos=r.viewport.from),this.moveRangeIndex()}advance(){let t=ui.get(),e=this.stoppedAt==null?this.to:Math.min(this.to,this.stoppedAt),i=Math.min(e,this.chunkStart+2048);for(t&&(i=Math.min(i,t.viewport.to));this.parsedPos<i;)this.parseLine(t);return this.chunkStart<this.parsedPos&&this.finishChunk(),this.parsedPos>=e?this.finish():t&&this.parsedPos>=t.viewport.to?(t.skipUntilInView(this.parsedPos,e),this.finish()):null}stopAt(t){this.stoppedAt=t}lineAfter(t){let e=this.input.chunk(t);if(this.input.lineChunks)e==`
`&&(e="");else{let i=e.indexOf(`
`);i>-1&&(e=e.slice(0,i))}return t+e.length<=this.to?e:e.slice(0,this.to-t)}nextLine(){let t=this.parsedPos,e=this.lineAfter(t),i=t+e.length;for(let n=this.rangeIndex;;){let r=this.ranges[n].to;if(r>=i||(e=e.slice(0,r-(i-e.length)),n++,n==this.ranges.length))break;let o=this.ranges[n].from,l=this.lineAfter(o);e+=l,i=o+l.length}return{line:e,end:i}}skipGapsTo(t,e,i){for(;;){let n=this.ranges[this.rangeIndex].to,r=t+e;if(i>0?n>r:n>=r)break;let o=this.ranges[++this.rangeIndex].from;e+=o-n}return e}moveRangeIndex(){for(;this.ranges[this.rangeIndex].to<this.parsedPos;)this.rangeIndex++}emitToken(t,e,i,n){let r=4;if(this.ranges.length>1){n=this.skipGapsTo(e,n,1),e+=n;let l=this.chunk.length;n=this.skipGapsTo(i,n,-1),i+=n,r+=this.chunk.length-l}let o=this.chunk.length-4;return r==4&&o>=0&&this.chunk[o]==t&&this.chunk[o+2]==e?this.chunk[o+2]=i:this.chunk.push(t,e,i,r),n}parseLine(t){let{line:e,end:i}=this.nextLine(),n=0,{streamParser:r}=this.lang,o=new Ic(e,t?t.state.tabSize:4,t?qe(t.state):2);if(o.eol())r.blankLine(this.state,o.indentUnit);else for(;!o.eol();){let l=Fc(r.token,o,this.state);if(l&&(n=this.emitToken(this.lang.tokenTable.resolve(l),this.parsedPos+o.start,this.parsedPos+o.pos,n)),o.start>1e4)break}this.parsedPos=i,this.moveRangeIndex(),this.parsedPos<this.to&&this.parsedPos++}finishChunk(){let t=U.build({buffer:this.chunk,start:this.chunkStart,length:this.parsedPos-this.chunkStart,nodeSet:ng,topID:0,maxBufferLength:2048,reused:this.chunkReused});t=new U(t.type,t.children,t.positions,t.length,[[this.lang.stateAfter,this.lang.streamParser.copyState(this.state)]]),this.chunks.push(t),this.chunkPos.push(this.chunkStart-this.ranges[0].from),this.chunk=[],this.chunkReused=void 0,this.chunkStart=this.parsedPos}finish(){return new U(this.lang.topNode,this.chunks,this.chunkPos,this.parsedPos-this.ranges[0].from).balance()}}function Fc(s,t,e){t.start=t.pos;for(let i=0;i<10;i++){let n=s(t,e);if(t.pos>t.start)return n}throw new Error("Stream parser failed to advance stream.")}const xo=Object.create(null),Ki=[vt.none],ng=new fo(Ki),ia=[],na=Object.create(null),Hc=Object.create(null);for(let[s,t]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])Hc[s]=Wc(xo,t);class Vc{constructor(t){this.extra=t,this.table=Object.assign(Object.create(null),Hc)}resolve(t){return t?this.table[t]||(this.table[t]=Wc(this.extra,t)):0}}const sg=new Vc(xo);function Is(s,t){ia.indexOf(s)>-1||(ia.push(s),console.warn(t))}function Wc(s,t){let e=[];for(let l of t.split(" ")){let a=[];for(let h of l.split(".")){let c=s[h]||b[h];c?typeof c=="function"?a.length?a=a.map(c):Is(h,`Modifier ${h} used at start of tag`):a.length?Is(h,`Tag ${h} used as modifier`):a=Array.isArray(c)?c:[c]:Is(h,`Unknown highlighting tag ${h}`)}for(let h of a)e.push(h)}if(!e.length)return 0;let i=t.replace(/ /g,"_"),n=i+" "+e.map(l=>l.id),r=na[n];if(r)return r.id;let o=na[n]=vt.define({id:Ki.length,name:i,props:[dm({[i]:e})]});return Ki.push(o),o.id}function rg(s,t){let e=vt.define({id:Ki.length,name:"Document",props:[Ne.add(()=>s),Ac.add(()=>i=>t.getIndent(i))],top:!0});return Ki.push(e),e}Y.RTL,Y.LTR;const og=s=>{let{state:t}=s,e=t.doc.lineAt(t.selection.main.from),i=ko(s.state,e.from);return i.line?lg(s):i.block?hg(s):!1};function vo(s,t){return({state:e,dispatch:i})=>{if(e.readOnly)return!1;let n=s(t,e);return n?(i(e.update(n)),!0):!1}}const lg=vo(ug,0),ag=vo(zc,0),hg=vo((s,t)=>zc(s,t,fg(t)),0);function ko(s,t){let e=s.languageDataAt("commentTokens",t);return e.length?e[0]:{}}const Ci=50;function cg(s,{open:t,close:e},i,n){let r=s.sliceDoc(i-Ci,i),o=s.sliceDoc(n,n+Ci),l=/\s*$/.exec(r)[0].length,a=/^\s*/.exec(o)[0].length,h=r.length-l;if(r.slice(h-t.length,h)==t&&o.slice(a,a+e.length)==e)return{open:{pos:i-l,margin:l&&1},close:{pos:n+a,margin:a&&1}};let c,f;n-i<=2*Ci?c=f=s.sliceDoc(i,n):(c=s.sliceDoc(i,i+Ci),f=s.sliceDoc(n-Ci,n));let u=/^\s*/.exec(c)[0].length,d=/\s*$/.exec(f)[0].length,p=f.length-d-e.length;return c.slice(u,u+t.length)==t&&f.slice(p,p+e.length)==e?{open:{pos:i+u+t.length,margin:/\s/.test(c.charAt(u+t.length))?1:0},close:{pos:n-d-e.length,margin:/\s/.test(f.charAt(p-1))?1:0}}:null}function fg(s){let t=[];for(let e of s.selection.ranges){let i=s.doc.lineAt(e.from),n=e.to<=i.to?i:s.doc.lineAt(e.to);n.from>i.from&&n.from==e.to&&(n=e.to==i.to+1?i:s.doc.lineAt(e.to-1));let r=t.length-1;r>=0&&t[r].to>i.from?t[r].to=n.to:t.push({from:i.from+/^\s*/.exec(i.text)[0].length,to:n.to})}return t}function zc(s,t,e=t.selection.ranges){let i=e.map(r=>ko(t,r.from).block);if(!i.every(r=>r))return null;let n=e.map((r,o)=>cg(t,i[o],r.from,r.to));if(s!=2&&!n.every(r=>r))return{changes:t.changes(e.map((r,o)=>n[o]?[]:[{from:r.from,insert:i[o].open+" "},{from:r.to,insert:" "+i[o].close}]))};if(s!=1&&n.some(r=>r)){let r=[];for(let o=0,l;o<n.length;o++)if(l=n[o]){let a=i[o],{open:h,close:c}=l;r.push({from:h.pos-a.open.length,to:h.pos+h.margin},{from:c.pos-c.margin,to:c.pos+a.close.length})}return{changes:r}}return null}function ug(s,t,e=t.selection.ranges){let i=[],n=-1;for(let{from:r,to:o}of e){let l=i.length,a=1e9,h=ko(t,r).line;if(h){for(let c=r;c<=o;){let f=t.doc.lineAt(c);if(f.from>n&&(r==o||o>f.from)){n=f.from;let u=/^\s*/.exec(f.text)[0].length,d=u==f.length,p=f.text.slice(u,u+h.length)==h?u:-1;u<f.text.length&&u<a&&(a=u),i.push({line:f,comment:p,token:h,indent:u,empty:d,single:!1})}c=f.to+1}if(a<1e9)for(let c=l;c<i.length;c++)i[c].indent<i[c].line.text.length&&(i[c].indent=a);i.length==l+1&&(i[l].single=!0)}}if(s!=2&&i.some(r=>r.comment<0&&(!r.empty||r.single))){let r=[];for(let{line:l,token:a,indent:h,empty:c,single:f}of i)(f||!c)&&r.push({from:l.from+h,insert:a+" "});let o=t.changes(r);return{changes:o,selection:t.selection.map(o,1)}}else if(s!=1&&i.some(r=>r.comment>=0)){let r=[];for(let{line:o,comment:l,token:a}of i)if(l>=0){let h=o.from+l,c=h+a.length;o.text[c-o.from]==" "&&c++,r.push({from:h,to:c})}return{changes:r}}return null}const Hr=be.define(),dg=be.define(),pg=O.define(),$c=O.define({combine(s){return Je(s,{minDepth:100,newGroupDelay:500,joinToEvent:(t,e)=>e},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(t,e)=>(i,n)=>t(i,n)||e(i,n)})}}),qc=Tt.define({create(){return oe.empty},update(s,t){let e=t.state.facet($c),i=t.annotation(Hr);if(i){let a=Dt.fromTransaction(t,i.selection),h=i.side,c=h==0?s.undone:s.done;return a?c=is(c,c.length,e.minDepth,a):c=Uc(c,t.startState.selection),new oe(h==0?i.rest:c,h==0?c:i.rest)}let n=t.annotation(dg);if((n=="full"||n=="before")&&(s=s.isolate()),t.annotation(et.addToHistory)===!1)return t.changes.empty?s:s.addMapping(t.changes.desc);let r=Dt.fromTransaction(t),o=t.annotation(et.time),l=t.annotation(et.userEvent);return r?s=s.addChanges(r,o,l,e,t):t.selection&&(s=s.addSelection(t.startState.selection,o,l,e.newGroupDelay)),(n=="full"||n=="after")&&(s=s.isolate()),s},toJSON(s){return{done:s.done.map(t=>t.toJSON()),undone:s.undone.map(t=>t.toJSON())}},fromJSON(s){return new oe(s.done.map(Dt.fromJSON),s.undone.map(Dt.fromJSON))}});function mg(s={}){return[qc,$c.of(s),B.domEventHandlers({beforeinput(t,e){let i=t.inputType=="historyUndo"?jc:t.inputType=="historyRedo"?Vr:null;return i?(t.preventDefault(),i(e)):!1}})]}function us(s,t){return function({state:e,dispatch:i}){if(!t&&e.readOnly)return!1;let n=e.field(qc,!1);if(!n)return!1;let r=n.pop(s,e,t);return r?(i(r),!0):!1}}const jc=us(0,!1),Vr=us(1,!1),gg=us(0,!0),bg=us(1,!0);class Dt{constructor(t,e,i,n,r){this.changes=t,this.effects=e,this.mapped=i,this.startSelection=n,this.selectionsAfter=r}setSelAfter(t){return new Dt(this.changes,this.effects,this.mapped,this.startSelection,t)}toJSON(){var t,e,i;return{changes:(t=this.changes)===null||t===void 0?void 0:t.toJSON(),mapped:(e=this.mapped)===null||e===void 0?void 0:e.toJSON(),startSelection:(i=this.startSelection)===null||i===void 0?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(n=>n.toJSON())}}static fromJSON(t){return new Dt(t.changes&&nt.fromJSON(t.changes),[],t.mapped&&le.fromJSON(t.mapped),t.startSelection&&v.fromJSON(t.startSelection),t.selectionsAfter.map(v.fromJSON))}static fromTransaction(t,e){let i=Vt;for(let n of t.startState.facet(pg)){let r=n(t);r.length&&(i=i.concat(r))}return!i.length&&t.changes.empty?null:new Dt(t.changes.invert(t.startState.doc),i,void 0,e||t.startState.selection,Vt)}static selection(t){return new Dt(void 0,Vt,void 0,void 0,t)}}function is(s,t,e,i){let n=t+1>e+20?t-e-1:0,r=s.slice(n,t);return r.push(i),r}function yg(s,t){let e=[],i=!1;return s.iterChangedRanges((n,r)=>e.push(n,r)),t.iterChangedRanges((n,r,o,l)=>{for(let a=0;a<e.length;){let h=e[a++],c=e[a++];l>=h&&o<=c&&(i=!0)}}),i}function wg(s,t){return s.ranges.length==t.ranges.length&&s.ranges.filter((e,i)=>e.empty!=t.ranges[i].empty).length===0}function Kc(s,t){return s.length?t.length?s.concat(t):s:t}const Vt=[],xg=200;function Uc(s,t){if(s.length){let e=s[s.length-1],i=e.selectionsAfter.slice(Math.max(0,e.selectionsAfter.length-xg));return i.length&&i[i.length-1].eq(t)?s:(i.push(t),is(s,s.length-1,1e9,e.setSelAfter(i)))}else return[Dt.selection([t])]}function vg(s){let t=s[s.length-1],e=s.slice();return e[s.length-1]=t.setSelAfter(t.selectionsAfter.slice(0,t.selectionsAfter.length-1)),e}function Ns(s,t){if(!s.length)return s;let e=s.length,i=Vt;for(;e;){let n=kg(s[e-1],t,i);if(n.changes&&!n.changes.empty||n.effects.length){let r=s.slice(0,e);return r[e-1]=n,r}else t=n.mapped,e--,i=n.selectionsAfter}return i.length?[Dt.selection(i)]:Vt}function kg(s,t,e){let i=Kc(s.selectionsAfter.length?s.selectionsAfter.map(l=>l.map(t)):Vt,e);if(!s.changes)return Dt.selection(i);let n=s.changes.map(t),r=t.mapDesc(s.changes,!0),o=s.mapped?s.mapped.composeDesc(r):r;return new Dt(n,_.mapEffects(s.effects,t),o,s.startSelection.map(r),i)}const Sg=/^(input\.type|delete)($|\.)/;class oe{constructor(t,e,i=0,n=void 0){this.done=t,this.undone=e,this.prevTime=i,this.prevUserEvent=n}isolate(){return this.prevTime?new oe(this.done,this.undone):this}addChanges(t,e,i,n,r){let o=this.done,l=o[o.length-1];return l&&l.changes&&!l.changes.empty&&t.changes&&(!i||Sg.test(i))&&(!l.selectionsAfter.length&&e-this.prevTime<n.newGroupDelay&&n.joinToEvent(r,yg(l.changes,t.changes))||i=="input.type.compose")?o=is(o,o.length-1,n.minDepth,new Dt(t.changes.compose(l.changes),Kc(_.mapEffects(t.effects,l.changes),l.effects),l.mapped,l.startSelection,Vt)):o=is(o,o.length,n.minDepth,t),new oe(o,Vt,e,i)}addSelection(t,e,i,n){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Vt;return r.length>0&&e-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&wg(r[r.length-1],t)?this:new oe(Uc(this.done,t),this.undone,e,i)}addMapping(t){return new oe(Ns(this.done,t),Ns(this.undone,t),this.prevTime,this.prevUserEvent)}pop(t,e,i){let n=t==0?this.done:this.undone;if(n.length==0)return null;let r=n[n.length-1],o=r.selectionsAfter[0]||e.selection;if(i&&r.selectionsAfter.length)return e.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:Hr.of({side:t,rest:vg(n),selection:o}),userEvent:t==0?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let l=n.length==1?Vt:n.slice(0,n.length-1);return r.mapped&&(l=Ns(l,r.mapped)),e.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:Hr.of({side:t,rest:l,selection:o}),filter:!1,userEvent:t==0?"undo":"redo",scrollIntoView:!0})}else return null}}oe.empty=new oe(Vt,Vt);const Cg=[{key:"Mod-z",run:jc,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:Vr,preventDefault:!0},{linux:"Ctrl-Shift-z",run:Vr,preventDefault:!0},{key:"Mod-u",run:gg,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:bg,preventDefault:!0}];function bi(s,t){return v.create(s.ranges.map(t),s.mainIndex)}function ce(s,t){return s.update({selection:t,scrollIntoView:!0,userEvent:"select"})}function Jt({state:s,dispatch:t},e){let i=bi(s.selection,e);return i.eq(s.selection,!0)?!1:(t(ce(s,i)),!0)}function ds(s,t){return v.cursor(t?s.to:s.from)}function Gc(s,t){return Jt(s,e=>e.empty?s.moveByChar(e,t):ds(e,t))}function gt(s){return s.textDirectionAt(s.state.selection.main.head)==Y.LTR}const Yc=s=>Gc(s,!gt(s)),Jc=s=>Gc(s,gt(s));function Xc(s,t){return Jt(s,e=>e.empty?s.moveByGroup(e,t):ds(e,t))}const Ag=s=>Xc(s,!gt(s)),Mg=s=>Xc(s,gt(s));function Dg(s,t,e){if(t.type.prop(e))return!0;let i=t.to-t.from;return i&&(i>2||/[^\s,.;:]/.test(s.sliceDoc(t.from,t.to)))||t.firstChild}function ps(s,t,e){let i=ut(s).resolveInner(t.head),n=e?N.closedBy:N.openedBy;for(let a=t.head;;){let h=e?i.childAfter(a):i.childBefore(a);if(!h)break;Dg(s,h,n)?i=h:a=e?h.to:h.from}let r=i.type.prop(n),o,l;return r&&(o=e?ei(s,i.from,1):ei(s,i.to,-1))&&o.matched?l=e?o.end.to:o.end.from:l=e?i.to:i.from,v.cursor(l,e?-1:1)}const Tg=s=>Jt(s,t=>ps(s.state,t,!gt(s))),Og=s=>Jt(s,t=>ps(s.state,t,gt(s)));function Qc(s,t){return Jt(s,e=>{if(!e.empty)return ds(e,t);let i=s.moveVertically(e,t);return i.head!=e.head?i:s.moveToLineBoundary(e,t)})}const Zc=s=>Qc(s,!1),tf=s=>Qc(s,!0);function ef(s){let t=s.scrollDOM.clientHeight<s.scrollDOM.scrollHeight-2,e=0,i=0,n;if(t){for(let r of s.state.facet(B.scrollMargins)){let o=r(s);o?.top&&(e=Math.max(o?.top,e)),o?.bottom&&(i=Math.max(o?.bottom,i))}n=s.scrollDOM.clientHeight-e-i}else n=(s.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:e,marginBottom:i,selfScroll:t,height:Math.max(s.defaultLineHeight,n-5)}}function nf(s,t){let e=ef(s),{state:i}=s,n=bi(i.selection,o=>o.empty?s.moveVertically(o,t,e.height):ds(o,t));if(n.eq(i.selection))return!1;let r;if(e.selfScroll){let o=s.coordsAtPos(i.selection.main.head),l=s.scrollDOM.getBoundingClientRect(),a=l.top+e.marginTop,h=l.bottom-e.marginBottom;o&&o.top>a&&o.bottom<h&&(r=B.scrollIntoView(n.main.head,{y:"start",yMargin:o.top-a}))}return s.dispatch(ce(i,n),{effects:r}),!0}const sa=s=>nf(s,!1),Wr=s=>nf(s,!0);function Pe(s,t,e){let i=s.lineBlockAt(t.head),n=s.moveToLineBoundary(t,e);if(n.head==t.head&&n.head!=(e?i.to:i.from)&&(n=s.moveToLineBoundary(t,e,!1)),!e&&n.head==i.from&&i.length){let r=/^\s*/.exec(s.state.sliceDoc(i.from,Math.min(i.from+100,i.to)))[0].length;r&&t.head!=i.from+r&&(n=v.cursor(i.from+r))}return n}const Bg=s=>Jt(s,t=>Pe(s,t,!0)),Pg=s=>Jt(s,t=>Pe(s,t,!1)),Eg=s=>Jt(s,t=>Pe(s,t,!gt(s))),Lg=s=>Jt(s,t=>Pe(s,t,gt(s))),Rg=s=>Jt(s,t=>v.cursor(s.lineBlockAt(t.head).from,1)),_g=s=>Jt(s,t=>v.cursor(s.lineBlockAt(t.head).to,-1));function Ig(s,t,e){let i=!1,n=bi(s.selection,r=>{let o=ei(s,r.head,-1)||ei(s,r.head,1)||r.head>0&&ei(s,r.head-1,1)||r.head<s.doc.length&&ei(s,r.head+1,-1);if(!o||!o.end)return r;i=!0;let l=o.start.from==r.head?o.end.to:o.end.from;return v.cursor(l)});return i?(t(ce(s,n)),!0):!1}const Ng=({state:s,dispatch:t})=>Ig(s,t);function qt(s,t){let e=bi(s.state.selection,i=>{let n=t(i);return v.range(i.anchor,n.head,n.goalColumn,n.bidiLevel||void 0)});return e.eq(s.state.selection)?!1:(s.dispatch(ce(s.state,e)),!0)}function sf(s,t){return qt(s,e=>s.moveByChar(e,t))}const rf=s=>sf(s,!gt(s)),of=s=>sf(s,gt(s));function lf(s,t){return qt(s,e=>s.moveByGroup(e,t))}const Fg=s=>lf(s,!gt(s)),Hg=s=>lf(s,gt(s)),Vg=s=>qt(s,t=>ps(s.state,t,!gt(s))),Wg=s=>qt(s,t=>ps(s.state,t,gt(s)));function af(s,t){return qt(s,e=>s.moveVertically(e,t))}const hf=s=>af(s,!1),cf=s=>af(s,!0);function ff(s,t){return qt(s,e=>s.moveVertically(e,t,ef(s).height))}const ra=s=>ff(s,!1),oa=s=>ff(s,!0),zg=s=>qt(s,t=>Pe(s,t,!0)),$g=s=>qt(s,t=>Pe(s,t,!1)),qg=s=>qt(s,t=>Pe(s,t,!gt(s))),jg=s=>qt(s,t=>Pe(s,t,gt(s))),Kg=s=>qt(s,t=>v.cursor(s.lineBlockAt(t.head).from)),Ug=s=>qt(s,t=>v.cursor(s.lineBlockAt(t.head).to)),la=({state:s,dispatch:t})=>(t(ce(s,{anchor:0})),!0),aa=({state:s,dispatch:t})=>(t(ce(s,{anchor:s.doc.length})),!0),ha=({state:s,dispatch:t})=>(t(ce(s,{anchor:s.selection.main.anchor,head:0})),!0),ca=({state:s,dispatch:t})=>(t(ce(s,{anchor:s.selection.main.anchor,head:s.doc.length})),!0),Gg=({state:s,dispatch:t})=>(t(s.update({selection:{anchor:0,head:s.doc.length},userEvent:"select"})),!0),Yg=({state:s,dispatch:t})=>{let e=ms(s).map(({from:i,to:n})=>v.range(i,Math.min(n+1,s.doc.length)));return t(s.update({selection:v.create(e),userEvent:"select"})),!0},Jg=({state:s,dispatch:t})=>{let e=bi(s.selection,i=>{let n=ut(s),r=n.resolveStack(i.from,1);if(i.empty){let o=n.resolveStack(i.from,-1);o.node.from>=r.node.from&&o.node.to<=r.node.to&&(r=o)}for(let o=r;o;o=o.next){let{node:l}=o;if((l.from<i.from&&l.to>=i.to||l.to>i.to&&l.from<=i.from)&&o.next)return v.range(l.to,l.from)}return i});return e.eq(s.selection)?!1:(t(ce(s,e)),!0)},Xg=({state:s,dispatch:t})=>{let e=s.selection,i=null;return e.ranges.length>1?i=v.create([e.main]):e.main.empty||(i=v.create([v.cursor(e.main.head)])),i?(t(ce(s,i)),!0):!1};function rn(s,t){if(s.state.readOnly)return!1;let e="delete.selection",{state:i}=s,n=i.changeByRange(r=>{let{from:o,to:l}=r;if(o==l){let a=t(r);a<o?(e="delete.backward",a=An(s,a,!1)):a>o&&(e="delete.forward",a=An(s,a,!0)),o=Math.min(o,a),l=Math.max(l,a)}else o=An(s,o,!1),l=An(s,l,!0);return o==l?{range:r}:{changes:{from:o,to:l},range:v.cursor(o,o<r.head?-1:1)}});return n.changes.empty?!1:(s.dispatch(i.update(n,{scrollIntoView:!0,userEvent:e,effects:e=="delete.selection"?B.announce.of(i.phrase("Selection deleted")):void 0})),!0)}function An(s,t,e){if(s instanceof B)for(let i of s.state.facet(B.atomicRanges).map(n=>n(s)))i.between(t,t,(n,r)=>{n<t&&r>t&&(t=e?r:n)});return t}const uf=(s,t,e)=>rn(s,i=>{let n=i.from,{state:r}=s,o=r.doc.lineAt(n),l,a;if(e&&!t&&n>o.from&&n<o.from+200&&!/[^ \t]/.test(l=o.text.slice(0,n-o.from))){if(l[l.length-1]=="	")return n-1;let h=mi(l,r.tabSize),c=h%qe(r)||qe(r);for(let f=0;f<c&&l[l.length-1-f]==" ";f++)n--;a=n}else a=yt(o.text,n-o.from,t,t)+o.from,a==n&&o.number!=(t?r.doc.lines:1)?a+=t?1:-1:!t&&/[\ufe00-\ufe0f]/.test(o.text.slice(a-o.from,n-o.from))&&(a=yt(o.text,a-o.from,!1,!1)+o.from);return a}),zr=s=>uf(s,!1,!0),df=s=>uf(s,!0,!1),pf=(s,t)=>rn(s,e=>{let i=e.head,{state:n}=s,r=n.doc.lineAt(i),o=n.charCategorizer(i);for(let l=null;;){if(i==(t?r.to:r.from)){i==e.head&&r.number!=(t?n.doc.lines:1)&&(i+=t?1:-1);break}let a=yt(r.text,i-r.from,t)+r.from,h=r.text.slice(Math.min(i,a)-r.from,Math.max(i,a)-r.from),c=o(h);if(l!=null&&c!=l)break;(h!=" "||i!=e.head)&&(l=c),i=a}return i}),mf=s=>pf(s,!1),Qg=s=>pf(s,!0),Zg=s=>rn(s,t=>{let e=s.lineBlockAt(t.head).to;return t.head<e?e:Math.min(s.state.doc.length,t.head+1)}),t0=s=>rn(s,t=>{let e=s.moveToLineBoundary(t,!1).head;return t.head>e?e:Math.max(0,t.head-1)}),e0=s=>rn(s,t=>{let e=s.moveToLineBoundary(t,!0).head;return t.head<e?e:Math.min(s.state.doc.length,t.head+1)}),i0=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let e=s.changeByRange(i=>({changes:{from:i.from,to:i.to,insert:F.of(["",""])},range:v.cursor(i.from)}));return t(s.update(e,{scrollIntoView:!0,userEvent:"input"})),!0},n0=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let e=s.changeByRange(i=>{if(!i.empty||i.from==0||i.from==s.doc.length)return{range:i};let n=i.from,r=s.doc.lineAt(n),o=n==r.from?n-1:yt(r.text,n-r.from,!1)+r.from,l=n==r.to?n+1:yt(r.text,n-r.from,!0)+r.from;return{changes:{from:o,to:l,insert:s.doc.slice(n,l).append(s.doc.slice(o,n))},range:v.cursor(l)}});return e.changes.empty?!1:(t(s.update(e,{scrollIntoView:!0,userEvent:"move.character"})),!0)};function ms(s){let t=[],e=-1;for(let i of s.selection.ranges){let n=s.doc.lineAt(i.from),r=s.doc.lineAt(i.to);if(!i.empty&&i.to==r.from&&(r=s.doc.lineAt(i.to-1)),e>=n.number){let o=t[t.length-1];o.to=r.to,o.ranges.push(i)}else t.push({from:n.from,to:r.to,ranges:[i]});e=r.number+1}return t}function gf(s,t,e){if(s.readOnly)return!1;let i=[],n=[];for(let r of ms(s)){if(e?r.to==s.doc.length:r.from==0)continue;let o=s.doc.lineAt(e?r.to+1:r.from-1),l=o.length+1;if(e){i.push({from:r.to,to:o.to},{from:r.from,insert:o.text+s.lineBreak});for(let a of r.ranges)n.push(v.range(Math.min(s.doc.length,a.anchor+l),Math.min(s.doc.length,a.head+l)))}else{i.push({from:o.from,to:r.from},{from:r.to,insert:s.lineBreak+o.text});for(let a of r.ranges)n.push(v.range(a.anchor-l,a.head-l))}}return i.length?(t(s.update({changes:i,scrollIntoView:!0,selection:v.create(n,s.selection.mainIndex),userEvent:"move.line"})),!0):!1}const s0=({state:s,dispatch:t})=>gf(s,t,!1),r0=({state:s,dispatch:t})=>gf(s,t,!0);function bf(s,t,e){if(s.readOnly)return!1;let i=[];for(let n of ms(s))e?i.push({from:n.from,insert:s.doc.slice(n.from,n.to)+s.lineBreak}):i.push({from:n.to,insert:s.lineBreak+s.doc.slice(n.from,n.to)});return t(s.update({changes:i,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const o0=({state:s,dispatch:t})=>bf(s,t,!1),l0=({state:s,dispatch:t})=>bf(s,t,!0),a0=s=>{if(s.state.readOnly)return!1;let{state:t}=s,e=t.changes(ms(t).map(({from:n,to:r})=>(n>0?n--:r<t.doc.length&&r++,{from:n,to:r}))),i=bi(t.selection,n=>{let r;if(s.lineWrapping){let o=s.lineBlockAt(n.head),l=s.coordsAtPos(n.head,n.assoc||1);l&&(r=o.bottom+s.documentTop-l.bottom+s.defaultLineHeight/2)}return s.moveVertically(n,!0,r)}).map(e);return s.dispatch({changes:e,selection:i,scrollIntoView:!0,userEvent:"delete.line"}),!0};function h0(s,t){if(/\(\)|\[\]|\{\}/.test(s.sliceDoc(t-1,t+1)))return{from:t,to:t};let e=ut(s).resolveInner(t),i=e.childBefore(t),n=e.childAfter(t),r;return i&&n&&i.to<=t&&n.from>=t&&(r=i.type.prop(N.closedBy))&&r.indexOf(n.name)>-1&&s.doc.lineAt(i.to).from==s.doc.lineAt(n.from).from&&!/\S/.test(s.sliceDoc(i.to,n.from))?{from:i.to,to:n.from}:null}const fa=yf(!1),c0=yf(!0);function yf(s){return({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange(n=>{let{from:r,to:o}=n,l=t.doc.lineAt(r),a=!s&&r==o&&h0(t,r);s&&(r=o=(o<=l.to?l:t.doc.lineAt(o)).to);let h=new cs(t,{simulateBreak:r,simulateDoubleBreak:!!a}),c=go(h,r);for(c==null&&(c=mi(/^\s*/.exec(t.doc.lineAt(r).text)[0],t.tabSize));o<l.to&&/\s/.test(l.text[o-l.from]);)o++;a?{from:r,to:o}=a:r>l.from&&r<l.from+100&&!/\S/.test(l.text.slice(0,r))&&(r=l.from);let f=["",ji(t,c)];return a&&f.push(ji(t,h.lineIndent(l.from,-1))),{changes:{from:r,to:o,insert:F.of(f)},range:v.cursor(r+1+f[1].length)}});return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}}function So(s,t){let e=-1;return s.changeByRange(i=>{let n=[];for(let o=i.from;o<=i.to;){let l=s.doc.lineAt(o);l.number>e&&(i.empty||i.to>l.from)&&(t(l,n,i),e=l.number),o=l.to+1}let r=s.changes(n);return{changes:n,range:v.range(r.mapPos(i.anchor,1),r.mapPos(i.head,1))}})}const f0=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let e=Object.create(null),i=new cs(s,{overrideIndentation:r=>{let o=e[r];return o??-1}}),n=So(s,(r,o,l)=>{let a=go(i,r.from);if(a==null)return;/\S/.test(r.text)||(a=0);let h=/^\s*/.exec(r.text)[0],c=ji(s,a);(h!=c||l.from<r.from+h.length)&&(e[r.from]=a,o.push({from:r.from,to:r.from+h.length,insert:c}))});return n.changes.empty||t(s.update(n,{userEvent:"indent"})),!0},wf=({state:s,dispatch:t})=>s.readOnly?!1:(t(s.update(So(s,(e,i)=>{i.push({from:e.from,insert:s.facet(hs)})}),{userEvent:"input.indent"})),!0),xf=({state:s,dispatch:t})=>s.readOnly?!1:(t(s.update(So(s,(e,i)=>{let n=/^\s*/.exec(e.text)[0];if(!n)return;let r=mi(n,s.tabSize),o=0,l=ji(s,Math.max(0,r-qe(s)));for(;o<n.length&&o<l.length&&n.charCodeAt(o)==l.charCodeAt(o);)o++;i.push({from:e.from+o,to:e.from+n.length,insert:l.slice(o)})}),{userEvent:"delete.dedent"})),!0),u0=s=>(s.setTabFocusMode(),!0),d0=[{key:"Ctrl-b",run:Yc,shift:rf,preventDefault:!0},{key:"Ctrl-f",run:Jc,shift:of},{key:"Ctrl-p",run:Zc,shift:hf},{key:"Ctrl-n",run:tf,shift:cf},{key:"Ctrl-a",run:Rg,shift:Kg},{key:"Ctrl-e",run:_g,shift:Ug},{key:"Ctrl-d",run:df},{key:"Ctrl-h",run:zr},{key:"Ctrl-k",run:Zg},{key:"Ctrl-Alt-h",run:mf},{key:"Ctrl-o",run:i0},{key:"Ctrl-t",run:n0},{key:"Ctrl-v",run:Wr}],p0=[{key:"ArrowLeft",run:Yc,shift:rf,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:Ag,shift:Fg,preventDefault:!0},{mac:"Cmd-ArrowLeft",run:Eg,shift:qg,preventDefault:!0},{key:"ArrowRight",run:Jc,shift:of,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:Mg,shift:Hg,preventDefault:!0},{mac:"Cmd-ArrowRight",run:Lg,shift:jg,preventDefault:!0},{key:"ArrowUp",run:Zc,shift:hf,preventDefault:!0},{mac:"Cmd-ArrowUp",run:la,shift:ha},{mac:"Ctrl-ArrowUp",run:sa,shift:ra},{key:"ArrowDown",run:tf,shift:cf,preventDefault:!0},{mac:"Cmd-ArrowDown",run:aa,shift:ca},{mac:"Ctrl-ArrowDown",run:Wr,shift:oa},{key:"PageUp",run:sa,shift:ra},{key:"PageDown",run:Wr,shift:oa},{key:"Home",run:Pg,shift:$g,preventDefault:!0},{key:"Mod-Home",run:la,shift:ha},{key:"End",run:Bg,shift:zg,preventDefault:!0},{key:"Mod-End",run:aa,shift:ca},{key:"Enter",run:fa,shift:fa},{key:"Mod-a",run:Gg},{key:"Backspace",run:zr,shift:zr},{key:"Delete",run:df},{key:"Mod-Backspace",mac:"Alt-Backspace",run:mf},{key:"Mod-Delete",mac:"Alt-Delete",run:Qg},{mac:"Mod-Backspace",run:t0},{mac:"Mod-Delete",run:e0}].concat(d0.map(s=>({mac:s.key,run:s.run,shift:s.shift}))),m0=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:Tg,shift:Vg},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:Og,shift:Wg},{key:"Alt-ArrowUp",run:s0},{key:"Shift-Alt-ArrowUp",run:o0},{key:"Alt-ArrowDown",run:r0},{key:"Shift-Alt-ArrowDown",run:l0},{key:"Escape",run:Xg},{key:"Mod-Enter",run:c0},{key:"Alt-l",mac:"Ctrl-l",run:Yg},{key:"Mod-i",run:Jg,preventDefault:!0},{key:"Mod-[",run:xf},{key:"Mod-]",run:wf},{key:"Mod-Alt-\\",run:f0},{key:"Shift-Mod-k",run:a0},{key:"Shift-Mod-\\",run:Ng},{key:"Mod-/",run:og},{key:"Alt-A",run:ag},{key:"Ctrl-m",mac:"Shift-Alt-m",run:u0}].concat(p0),g0={key:"Tab",run:wf,shift:xf};class vf{constructor(t,e,i,n){this.state=t,this.pos=e,this.explicit=i,this.view=n,this.abortListeners=[],this.abortOnDocChange=!1}tokenBefore(t){let e=ut(this.state).resolveInner(this.pos,-1);for(;e&&t.indexOf(e.name)<0;)e=e.parent;return e?{from:e.from,to:this.pos,text:this.state.sliceDoc(e.from,this.pos),type:e.type}:null}matchBefore(t){let e=this.state.doc.lineAt(this.pos),i=Math.max(e.from,this.pos-250),n=e.text.slice(i-e.from,this.pos-e.from),r=n.search(kf(t,!1));return r<0?null:{from:i+r,to:this.pos,text:n.slice(r)}}get aborted(){return this.abortListeners==null}addEventListener(t,e,i){t=="abort"&&this.abortListeners&&(this.abortListeners.push(e),i&&i.onDocChange&&(this.abortOnDocChange=!0))}}function ua(s){let t=Object.keys(s).join(""),e=/\w/.test(t);return e&&(t=t.replace(/\w/g,"")),`[${e?"\\w":""}${t.replace(/[^\w\s]/g,"\\$&")}]`}function b0(s){let t=Object.create(null),e=Object.create(null);for(let{label:n}of s){t[n[0]]=!0;for(let r=1;r<n.length;r++)e[n[r]]=!0}let i=ua(t)+ua(e)+"*$";return[new RegExp("^"+i),new RegExp(i)]}function y0(s){let t=s.map(n=>typeof n=="string"?{label:n}:n),[e,i]=t.every(n=>/^\w+$/.test(n.label))?[/\w*$/,/\w+$/]:b0(t);return n=>{let r=n.matchBefore(i);return r||n.explicit?{from:r?r.from:n.pos,options:t,validFor:e}:null}}function D1(s,t){return e=>{for(let i=ut(e.state).resolveInner(e.pos,-1);i;i=i.parent){if(s.indexOf(i.name)>-1)return null;if(i.type.isTop)break}return t(e)}}class da{constructor(t,e,i,n){this.completion=t,this.source=e,this.match=i,this.score=n}}function He(s){return s.selection.main.from}function kf(s,t){var e;let{source:i}=s,n=t&&i[0]!="^",r=i[i.length-1]!="$";return!n&&!r?s:new RegExp(`${n?"^":""}(?:${i})${r?"$":""}`,(e=s.flags)!==null&&e!==void 0?e:s.ignoreCase?"i":"")}const Co=be.define();function w0(s,t,e,i){let{main:n}=s.selection,r=e-n.from,o=i-n.from;return Object.assign(Object.assign({},s.changeByRange(l=>{if(l!=n&&e!=i&&s.sliceDoc(l.from+r,l.from+o)!=s.sliceDoc(e,i))return{range:l};let a=s.toText(t);return{changes:{from:l.from+r,to:i==n.from?l.to:l.from+o,insert:a},range:v.cursor(l.from+r+a.length)}})),{scrollIntoView:!0,userEvent:"input.complete"})}const pa=new WeakMap;function x0(s){if(!Array.isArray(s))return s;let t=pa.get(s);return t||pa.set(s,t=y0(s)),t}const ns=_.define(),Ui=_.define();class v0{constructor(t){this.pattern=t,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let e=0;e<t.length;){let i=Pt(t,e),n=ue(i);this.chars.push(i);let r=t.slice(e,e+n),o=r.toUpperCase();this.folded.push(Pt(o==r?r.toLowerCase():o,0)),e+=n}this.astral=t.length!=this.chars.length}ret(t,e){return this.score=t,this.matched=e,this}match(t){if(this.pattern.length==0)return this.ret(-100,[]);if(t.length<this.pattern.length)return null;let{chars:e,folded:i,any:n,precise:r,byWord:o}=this;if(e.length==1){let w=Pt(t,0),S=ue(w),k=S==t.length?0:-100;if(w!=e[0])if(w==i[0])k+=-200;else return null;return this.ret(k,[0,S])}let l=t.indexOf(this.pattern);if(l==0)return this.ret(t.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=e.length,h=0;if(l<0){for(let w=0,S=Math.min(t.length,200);w<S&&h<a;){let k=Pt(t,w);(k==e[h]||k==i[h])&&(n[h++]=w),w+=ue(k)}if(h<a)return null}let c=0,f=0,u=!1,d=0,p=-1,m=-1,g=/[a-z]/.test(t),y=!0;for(let w=0,S=Math.min(t.length,200),k=0;w<S&&f<a;){let x=Pt(t,w);l<0&&(c<a&&x==e[c]&&(r[c++]=w),d<a&&(x==e[d]||x==i[d]?(d==0&&(p=w),m=w+1,d++):d=0));let C,A=x<255?x>=48&&x<=57||x>=97&&x<=122?2:x>=65&&x<=90?1:0:(C=Qa(x))!=C.toLowerCase()?1:C!=C.toUpperCase()?2:0;(!w||A==1&&g||k==0&&A!=0)&&(e[f]==x||i[f]==x&&(u=!0)?o[f++]=w:o.length&&(y=!1)),k=A,w+=ue(x)}return f==a&&o[0]==0&&y?this.result(-100+(u?-200:0),o,t):d==a&&p==0?this.ret(-200-t.length+(m==t.length?0:-100),[0,m]):l>-1?this.ret(-700-t.length,[l,l+this.pattern.length]):d==a?this.ret(-900-t.length,[p,m]):f==a?this.result(-100+(u?-200:0)+-700+(y?0:-1100),o,t):e.length==2?null:this.result((n[0]?-700:0)+-200+-1100,n,t)}result(t,e,i){let n=[],r=0;for(let o of e){let l=o+(this.astral?ue(Pt(i,o)):1);r&&n[r-1]==o?n[r-1]=l:(n[r++]=o,n[r++]=l)}return this.ret(t-i.length,n)}}class k0{constructor(t){this.pattern=t,this.matched=[],this.score=0,this.folded=t.toLowerCase()}match(t){if(t.length<this.pattern.length)return null;let e=t.slice(0,this.pattern.length),i=e==this.pattern?0:e.toLowerCase()==this.folded?-200:null;return i==null?null:(this.matched=[0,e.length],this.score=i+(t.length==this.pattern.length?0:-100),this)}}const rt=O.define({combine(s){return Je(s,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:S0,filterStrict:!1,compareCompletions:(t,e)=>t.label.localeCompare(e.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(t,e)=>t&&e,closeOnBlur:(t,e)=>t&&e,icons:(t,e)=>t&&e,tooltipClass:(t,e)=>i=>ma(t(i),e(i)),optionClass:(t,e)=>i=>ma(t(i),e(i)),addToOptions:(t,e)=>t.concat(e),filterStrict:(t,e)=>t||e})}});function ma(s,t){return s?t?s+" "+t:s:t}function S0(s,t,e,i,n,r){let o=s.textDirection==Y.RTL,l=o,a=!1,h="top",c,f,u=t.left-n.left,d=n.right-t.right,p=i.right-i.left,m=i.bottom-i.top;if(l&&u<Math.min(p,d)?l=!1:!l&&d<Math.min(p,u)&&(l=!0),p<=(l?u:d))c=Math.max(n.top,Math.min(e.top,n.bottom-m))-t.top,f=Math.min(400,l?u:d);else{a=!0,f=Math.min(400,(o?t.right:n.right-t.left)-30);let w=n.bottom-t.bottom;w>=m||w>t.top?c=e.bottom-t.top:(h="bottom",c=t.bottom-e.top)}let g=(t.bottom-t.top)/r.offsetHeight,y=(t.right-t.left)/r.offsetWidth;return{style:`${h}: ${c/g}px; max-width: ${f/y}px`,class:"cm-completionInfo-"+(a?o?"left-narrow":"right-narrow":l?"left":"right")}}function C0(s){let t=s.addToOptions.slice();return s.icons&&t.push({render(e){let i=document.createElement("div");return i.classList.add("cm-completionIcon"),e.type&&i.classList.add(...e.type.split(/\s+/g).map(n=>"cm-completionIcon-"+n)),i.setAttribute("aria-hidden","true"),i},position:20}),t.push({render(e,i,n,r){let o=document.createElement("span");o.className="cm-completionLabel";let l=e.displayLabel||e.label,a=0;for(let h=0;h<r.length;){let c=r[h++],f=r[h++];c>a&&o.appendChild(document.createTextNode(l.slice(a,c)));let u=o.appendChild(document.createElement("span"));u.appendChild(document.createTextNode(l.slice(c,f))),u.className="cm-completionMatchedText",a=f}return a<l.length&&o.appendChild(document.createTextNode(l.slice(a))),o},position:50},{render(e){if(!e.detail)return null;let i=document.createElement("span");return i.className="cm-completionDetail",i.textContent=e.detail,i},position:80}),t.sort((e,i)=>e.position-i.position).map(e=>e.render)}function Fs(s,t,e){if(s<=e)return{from:0,to:s};if(t<0&&(t=0),t<=s>>1){let n=Math.floor(t/e);return{from:n*e,to:(n+1)*e}}let i=Math.floor((s-t)/e);return{from:s-(i+1)*e,to:s-i*e}}class A0{constructor(t,e,i){this.view=t,this.stateField=e,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:a=>this.placeInfo(a),key:this},this.space=null,this.currentClass="";let n=t.state.field(e),{options:r,selected:o}=n.open,l=t.state.facet(rt);this.optionContent=C0(l),this.optionClass=l.optionClass,this.tooltipClass=l.tooltipClass,this.range=Fs(r.length,o,l.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(t.state),this.dom.addEventListener("mousedown",a=>{let{options:h}=t.state.field(e).open;for(let c=a.target,f;c&&c!=this.dom;c=c.parentNode)if(c.nodeName=="LI"&&(f=/-(\d+)$/.exec(c.id))&&+f[1]<h.length){this.applyCompletion(t,h[+f[1]]),a.preventDefault();return}}),this.dom.addEventListener("focusout",a=>{let h=t.state.field(this.stateField,!1);h&&h.tooltip&&t.state.facet(rt).closeOnBlur&&a.relatedTarget!=t.contentDOM&&t.dispatch({effects:Ui.of(null)})}),this.showOptions(r,n.id)}mount(){this.updateSel()}showOptions(t,e){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(t,e,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)})}update(t){var e;let i=t.state.field(this.stateField),n=t.startState.field(this.stateField);if(this.updateTooltipClass(t.state),i!=n){let{options:r,selected:o,disabled:l}=i.open;(!n.open||n.open.options!=r)&&(this.range=Fs(r.length,o,t.state.facet(rt).maxRenderedOptions),this.showOptions(r,i.id)),this.updateSel(),l!=((e=n.open)===null||e===void 0?void 0:e.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!l)}}updateTooltipClass(t){let e=this.tooltipClass(t);if(e!=this.currentClass){for(let i of this.currentClass.split(" "))i&&this.dom.classList.remove(i);for(let i of e.split(" "))i&&this.dom.classList.add(i);this.currentClass=e}}positioned(t){this.space=t,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let t=this.view.state.field(this.stateField),e=t.open;if((e.selected>-1&&e.selected<this.range.from||e.selected>=this.range.to)&&(this.range=Fs(e.options.length,e.selected,this.view.state.facet(rt).maxRenderedOptions),this.showOptions(e.options,t.id)),this.updateSelectedOption(e.selected)){this.destroyInfo();let{completion:i}=e.options[e.selected],{info:n}=i;if(!n)return;let r=typeof n=="string"?document.createTextNode(n):n(i);if(!r)return;"then"in r?r.then(o=>{o&&this.view.state.field(this.stateField,!1)==t&&this.addInfoPane(o,i)}).catch(o=>Mt(this.view.state,o,"completion info")):this.addInfoPane(r,i)}}addInfoPane(t,e){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",t.nodeType!=null)i.appendChild(t),this.infoDestroy=null;else{let{dom:n,destroy:r}=t;i.appendChild(n),this.infoDestroy=r||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(t){let e=null;for(let i=this.list.firstChild,n=this.range.from;i;i=i.nextSibling,n++)i.nodeName!="LI"||!i.id?n--:n==t?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),e=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return e&&D0(this.list,e),e}measureInfo(){let t=this.dom.querySelector("[aria-selected]");if(!t||!this.info)return null;let e=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),n=t.getBoundingClientRect(),r=this.space;if(!r){let o=this.dom.ownerDocument.documentElement;r={left:0,top:0,right:o.clientWidth,bottom:o.clientHeight}}return n.top>Math.min(r.bottom,e.bottom)-10||n.bottom<Math.max(r.top,e.top)+10?null:this.view.state.facet(rt).positionInfo(this.view,e,n,i,r,this.dom)}placeInfo(t){this.info&&(t?(t.style&&(this.info.style.cssText=t.style),this.info.className="cm-tooltip cm-completionInfo "+(t.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(t,e,i){const n=document.createElement("ul");n.id=e,n.setAttribute("role","listbox"),n.setAttribute("aria-expanded","true"),n.setAttribute("aria-label",this.view.state.phrase("Completions")),n.addEventListener("mousedown",o=>{o.target==n&&o.preventDefault()});let r=null;for(let o=i.from;o<i.to;o++){let{completion:l,match:a}=t[o],{section:h}=l;if(h){let u=typeof h=="string"?h:h.name;if(u!=r&&(o>i.from||i.from==0))if(r=u,typeof h!="string"&&h.header)n.appendChild(h.header(h));else{let d=n.appendChild(document.createElement("completion-section"));d.textContent=u}}const c=n.appendChild(document.createElement("li"));c.id=e+"-"+o,c.setAttribute("role","option");let f=this.optionClass(l);f&&(c.className=f);for(let u of this.optionContent){let d=u(l,this.view.state,this.view,a);d&&c.appendChild(d)}}return i.from&&n.classList.add("cm-completionListIncompleteTop"),i.to<t.length&&n.classList.add("cm-completionListIncompleteBottom"),n}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function M0(s,t){return e=>new A0(e,s,t)}function D0(s,t){let e=s.getBoundingClientRect(),i=t.getBoundingClientRect(),n=e.height/s.offsetHeight;i.top<e.top?s.scrollTop-=(e.top-i.top)/n:i.bottom>e.bottom&&(s.scrollTop+=(i.bottom-e.bottom)/n)}function ga(s){return(s.boost||0)*100+(s.apply?10:0)+(s.info?5:0)+(s.type?1:0)}function T0(s,t){let e=[],i=null,n=h=>{e.push(h);let{section:c}=h.completion;if(c){i||(i=[]);let f=typeof c=="string"?c:c.name;i.some(u=>u.name==f)||i.push(typeof c=="string"?{name:f}:c)}},r=t.facet(rt);for(let h of s)if(h.hasResult()){let c=h.result.getMatch;if(h.result.filter===!1)for(let f of h.result.options)n(new da(f,h.source,c?c(f):[],1e9-e.length));else{let f=t.sliceDoc(h.from,h.to),u,d=r.filterStrict?new k0(f):new v0(f);for(let p of h.result.options)if(u=d.match(p.label)){let m=p.displayLabel?c?c(p,u.matched):[]:u.matched;n(new da(p,h.source,m,u.score+(p.boost||0)))}}}if(i){let h=Object.create(null),c=0,f=(u,d)=>{var p,m;return((p=u.rank)!==null&&p!==void 0?p:1e9)-((m=d.rank)!==null&&m!==void 0?m:1e9)||(u.name<d.name?-1:1)};for(let u of i.sort(f))c-=1e5,h[u.name]=c;for(let u of e){let{section:d}=u.completion;d&&(u.score+=h[typeof d=="string"?d:d.name])}}let o=[],l=null,a=r.compareCompletions;for(let h of e.sort((c,f)=>f.score-c.score||a(c.completion,f.completion))){let c=h.completion;!l||l.label!=c.label||l.detail!=c.detail||l.type!=null&&c.type!=null&&l.type!=c.type||l.apply!=c.apply||l.boost!=c.boost?o.push(h):ga(h.completion)>ga(l)&&(o[o.length-1]=h),l=h.completion}return o}class ii{constructor(t,e,i,n,r,o){this.options=t,this.attrs=e,this.tooltip=i,this.timestamp=n,this.selected=r,this.disabled=o}setSelected(t,e){return t==this.selected||t>=this.options.length?this:new ii(this.options,ba(e,t),this.tooltip,this.timestamp,t,this.disabled)}static build(t,e,i,n,r,o){if(n&&!o&&t.some(h=>h.isPending))return n.setDisabled();let l=T0(t,e);if(!l.length)return n&&t.some(h=>h.isPending)?n.setDisabled():null;let a=e.facet(rt).selectOnOpen?0:-1;if(n&&n.selected!=a&&n.selected!=-1){let h=n.options[n.selected].completion;for(let c=0;c<l.length;c++)if(l[c].completion==h){a=c;break}}return new ii(l,ba(i,a),{pos:t.reduce((h,c)=>c.hasResult()?Math.min(h,c.from):h,1e8),create:R0,above:r.aboveCursor},n?n.timestamp:Date.now(),a,!1)}map(t){return new ii(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:t.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}setDisabled(){return new ii(this.options,this.attrs,this.tooltip,this.timestamp,this.selected,!0)}}class ss{constructor(t,e,i){this.active=t,this.id=e,this.open=i}static start(){return new ss(E0,"cm-ac-"+Math.floor(Math.random()*2e6).toString(36),null)}update(t){let{state:e}=t,i=e.facet(rt),r=(i.override||e.languageDataAt("autocomplete",He(e)).map(x0)).map(a=>(this.active.find(c=>c.source==a)||new Wt(a,this.active.some(c=>c.state!=0)?1:0)).update(t,i));r.length==this.active.length&&r.every((a,h)=>a==this.active[h])&&(r=this.active);let o=this.open,l=t.effects.some(a=>a.is(Ao));o&&t.docChanged&&(o=o.map(t.changes)),t.selection||r.some(a=>a.hasResult()&&t.changes.touchesRange(a.from,a.to))||!O0(r,this.active)||l?o=ii.build(r,e,this.id,o,i,l):o&&o.disabled&&!r.some(a=>a.isPending)&&(o=null),!o&&r.every(a=>!a.isPending)&&r.some(a=>a.hasResult())&&(r=r.map(a=>a.hasResult()?new Wt(a.source,0):a));for(let a of t.effects)a.is(Cf)&&(o=o&&o.setSelected(a.value,this.id));return r==this.active&&o==this.open?this:new ss(r,this.id,o)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:this.active.length?B0:P0}}function O0(s,t){if(s==t)return!0;for(let e=0,i=0;;){for(;e<s.length&&!s[e].hasResult();)e++;for(;i<t.length&&!t[i].hasResult();)i++;let n=e==s.length,r=i==t.length;if(n||r)return n==r;if(s[e++].result!=t[i++].result)return!1}}const B0={"aria-autocomplete":"list"},P0={};function ba(s,t){let e={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":s};return t>-1&&(e["aria-activedescendant"]=s+"-"+t),e}const E0=[];function Sf(s,t){if(s.isUserEvent("input.complete")){let i=s.annotation(Co);if(i&&t.activateOnCompletion(i))return 12}let e=s.isUserEvent("input.type");return e&&t.activateOnTyping?5:e?1:s.isUserEvent("delete.backward")?2:s.selection?8:s.docChanged?16:0}class Wt{constructor(t,e,i=!1){this.source=t,this.state=e,this.explicit=i}hasResult(){return!1}get isPending(){return this.state==1}update(t,e){let i=Sf(t,e),n=this;(i&8||i&16&&this.touches(t))&&(n=new Wt(n.source,0)),i&4&&n.state==0&&(n=new Wt(this.source,1)),n=n.updateFor(t,i);for(let r of t.effects)if(r.is(ns))n=new Wt(n.source,1,r.value);else if(r.is(Ui))n=new Wt(n.source,0);else if(r.is(Ao))for(let o of r.value)o.source==n.source&&(n=o);return n}updateFor(t,e){return this.map(t.changes)}map(t){return this}touches(t){return t.changes.touchesRange(He(t.state))}}class oi extends Wt{constructor(t,e,i,n,r,o){super(t,3,e),this.limit=i,this.result=n,this.from=r,this.to=o}hasResult(){return!0}updateFor(t,e){var i;if(!(e&3))return this.map(t.changes);let n=this.result;n.map&&!t.changes.empty&&(n=n.map(n,t.changes));let r=t.changes.mapPos(this.from),o=t.changes.mapPos(this.to,1),l=He(t.state);if(l>o||!n||e&2&&(He(t.startState)==this.from||l<this.limit))return new Wt(this.source,e&4?1:0);let a=t.changes.mapPos(this.limit);return L0(n.validFor,t.state,r,o)?new oi(this.source,this.explicit,a,n,r,o):n.update&&(n=n.update(n,r,o,new vf(t.state,l,!1)))?new oi(this.source,this.explicit,a,n,n.from,(i=n.to)!==null&&i!==void 0?i:He(t.state)):new Wt(this.source,1,this.explicit)}map(t){return t.empty?this:(this.result.map?this.result.map(this.result,t):this.result)?new oi(this.source,this.explicit,t.mapPos(this.limit),this.result,t.mapPos(this.from),t.mapPos(this.to,1)):new Wt(this.source,0)}touches(t){return t.changes.touchesRange(this.from,this.to)}}function L0(s,t,e,i){if(!s)return!1;let n=t.sliceDoc(e,i);return typeof s=="function"?s(n,e,i,t):kf(s,!0).test(n)}const Ao=_.define({map(s,t){return s.map(e=>e.map(t))}}),Cf=_.define(),At=Tt.define({create(){return ss.start()},update(s,t){return s.update(t)},provide:s=>[co.from(s,t=>t.tooltip),B.contentAttributes.from(s,t=>t.attrs)]});function Mo(s,t){const e=t.completion.apply||t.completion.label;let i=s.state.field(At).active.find(n=>n.source==t.source);return i instanceof oi?(typeof e=="string"?s.dispatch(Object.assign(Object.assign({},w0(s.state,e,i.from,i.to)),{annotations:Co.of(t.completion)})):e(s,t.completion,i.from,i.to),!0):!1}const R0=M0(At,Mo);function Mn(s,t="option"){return e=>{let i=e.state.field(At,!1);if(!i||!i.open||i.open.disabled||Date.now()-i.open.timestamp<e.state.facet(rt).interactionDelay)return!1;let n=1,r;t=="page"&&(r=uc(e,i.open.tooltip))&&(n=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let{length:o}=i.open.options,l=i.open.selected>-1?i.open.selected+n*(s?1:-1):s?0:o-1;return l<0?l=t=="page"?0:o-1:l>=o&&(l=t=="page"?o-1:0),e.dispatch({effects:Cf.of(l)}),!0}}const Af=s=>{let t=s.state.field(At,!1);return s.state.readOnly||!t||!t.open||t.open.selected<0||t.open.disabled||Date.now()-t.open.timestamp<s.state.facet(rt).interactionDelay?!1:Mo(s,t.open.options[t.open.selected])},ya=s=>s.state.field(At,!1)?(s.dispatch({effects:ns.of(!0)}),!0):!1,_0=s=>{let t=s.state.field(At,!1);return!t||!t.active.some(e=>e.state!=0)?!1:(s.dispatch({effects:Ui.of(null)}),!0)};class I0{constructor(t,e){this.active=t,this.context=e,this.time=Date.now(),this.updates=[],this.done=void 0}}const N0=50,F0=1e3,H0=ft.fromClass(class{constructor(s){this.view=s,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0;for(let t of s.state.field(At).active)t.isPending&&this.startQuery(t)}update(s){let t=s.state.field(At),e=s.state.facet(rt);if(!s.selectionSet&&!s.docChanged&&s.startState.field(At)==t)return;let i=s.transactions.some(r=>{let o=Sf(r,e);return o&8||(r.selection||r.docChanged)&&!(o&3)});for(let r=0;r<this.running.length;r++){let o=this.running[r];if(i||o.context.abortOnDocChange&&s.docChanged||o.updates.length+s.transactions.length>N0&&Date.now()-o.time>F0){for(let l of o.context.abortListeners)try{l()}catch(a){Mt(this.view.state,a)}o.context.abortListeners=null,this.running.splice(r--,1)}else o.updates.push(...s.transactions)}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),s.transactions.some(r=>r.effects.some(o=>o.is(ns)))&&(this.pendingStart=!0);let n=this.pendingStart?50:e.activateOnTypingDelay;if(this.debounceUpdate=t.active.some(r=>r.isPending&&!this.running.some(o=>o.active.source==r.source))?setTimeout(()=>this.startUpdate(),n):-1,this.composing!=0)for(let r of s.transactions)r.isUserEvent("input.type")?this.composing=2:this.composing==2&&r.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:s}=this.view,t=s.field(At);for(let e of t.active)e.isPending&&!this.running.some(i=>i.active.source==e.source)&&this.startQuery(e);this.running.length&&t.open&&t.open.disabled&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(rt).updateSyncTime))}startQuery(s){let{state:t}=this.view,e=He(t),i=new vf(t,e,s.explicit,this.view),n=new I0(s,i);this.running.push(n),Promise.resolve(s.source(i)).then(r=>{n.context.aborted||(n.done=r||null,this.scheduleAccept())},r=>{this.view.dispatch({effects:Ui.of(null)}),Mt(this.view.state,r)})}scheduleAccept(){this.running.every(s=>s.done!==void 0)?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(rt).updateSyncTime))}accept(){var s;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let t=[],e=this.view.state.facet(rt),i=this.view.state.field(At);for(let n=0;n<this.running.length;n++){let r=this.running[n];if(r.done===void 0)continue;if(this.running.splice(n--,1),r.done){let l=He(r.updates.length?r.updates[0].startState:this.view.state),a=Math.min(l,r.done.from+(r.active.explicit?0:1)),h=new oi(r.active.source,r.active.explicit,a,r.done,r.done.from,(s=r.done.to)!==null&&s!==void 0?s:l);for(let c of r.updates)h=h.update(c,e);if(h.hasResult()){t.push(h);continue}}let o=i.active.find(l=>l.source==r.active.source);if(o&&o.isPending)if(r.done==null){let l=new Wt(r.active.source,0);for(let a of r.updates)l=l.update(a,e);l.isPending||t.push(l)}else this.startQuery(o)}(t.length||i.open&&i.open.disabled)&&this.view.dispatch({effects:Ao.of(t)})}},{eventHandlers:{blur(s){let t=this.view.state.field(At,!1);if(t&&t.tooltip&&this.view.state.facet(rt).closeOnBlur){let e=t.open&&uc(this.view,t.open.tooltip);(!e||!e.dom.contains(s.relatedTarget))&&setTimeout(()=>this.view.dispatch({effects:Ui.of(null)}),10)}},compositionstart(){this.composing=1},compositionend(){this.composing==3&&setTimeout(()=>this.view.dispatch({effects:ns.of(!1)}),20),this.composing=0}}}),V0=typeof navigator=="object"&&/Win/.test(navigator.platform),W0=Ye.highest(B.domEventHandlers({keydown(s,t){let e=t.state.field(At,!1);if(!e||!e.open||e.open.disabled||e.open.selected<0||s.key.length>1||s.ctrlKey&&!(V0&&s.altKey)||s.metaKey)return!1;let i=e.open.options[e.open.selected],n=e.active.find(o=>o.source==i.source),r=i.completion.commitCharacters||n.result.commitCharacters;return r&&r.indexOf(s.key)>-1&&Mo(t,i),!1}})),Mf=B.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class z0{constructor(t,e,i,n){this.field=t,this.line=e,this.from=i,this.to=n}}class Do{constructor(t,e,i){this.field=t,this.from=e,this.to=i}map(t){let e=t.mapPos(this.from,-1,ct.TrackDel),i=t.mapPos(this.to,1,ct.TrackDel);return e==null||i==null?null:new Do(this.field,e,i)}}class To{constructor(t,e){this.lines=t,this.fieldPositions=e}instantiate(t,e){let i=[],n=[e],r=t.doc.lineAt(e),o=/^\s*/.exec(r.text)[0];for(let a of this.lines){if(i.length){let h=o,c=/^\t*/.exec(a)[0].length;for(let f=0;f<c;f++)h+=t.facet(hs);n.push(e+h.length-c),a=h+a.slice(c)}i.push(a),e+=a.length+1}let l=this.fieldPositions.map(a=>new Do(a.field,n[a.line]+a.from,n[a.line]+a.to));return{text:i,ranges:l}}static parse(t){let e=[],i=[],n=[],r;for(let o of t.split(/\r\n?|\n/)){for(;r=/[#$]\{(?:(\d+)(?::([^}]*))?|((?:\\[{}]|[^}])*))\}/.exec(o);){let l=r[1]?+r[1]:null,a=r[2]||r[3]||"",h=-1,c=a.replace(/\\[{}]/g,f=>f[1]);for(let f=0;f<e.length;f++)(l!=null?e[f].seq==l:c&&e[f].name==c)&&(h=f);if(h<0){let f=0;for(;f<e.length&&(l==null||e[f].seq!=null&&e[f].seq<l);)f++;e.splice(f,0,{seq:l,name:c}),h=f;for(let u of n)u.field>=h&&u.field++}n.push(new z0(h,i.length,r.index,r.index+c.length)),o=o.slice(0,r.index)+a+o.slice(r.index+r[0].length)}o=o.replace(/\\([{}])/g,(l,a,h)=>{for(let c of n)c.line==i.length&&c.from>h&&(c.from--,c.to--);return a}),i.push(o)}return new To(i,n)}}let $0=I.widget({widget:new class extends he{toDOM(){let s=document.createElement("span");return s.className="cm-snippetFieldPosition",s}ignoreEvent(){return!1}}}),q0=I.mark({class:"cm-snippetField"});class yi{constructor(t,e){this.ranges=t,this.active=e,this.deco=I.set(t.map(i=>(i.from==i.to?$0:q0).range(i.from,i.to)))}map(t){let e=[];for(let i of this.ranges){let n=i.map(t);if(!n)return null;e.push(n)}return new yi(e,this.active)}selectionInsideField(t){return t.ranges.every(e=>this.ranges.some(i=>i.field==this.active&&i.from<=e.from&&i.to>=e.to))}}const on=_.define({map(s,t){return s&&s.map(t)}}),j0=_.define(),Gi=Tt.define({create(){return null},update(s,t){for(let e of t.effects){if(e.is(on))return e.value;if(e.is(j0)&&s)return new yi(s.ranges,e.value)}return s&&t.docChanged&&(s=s.map(t.changes)),s&&t.selection&&!s.selectionInsideField(t.selection)&&(s=null),s},provide:s=>B.decorations.from(s,t=>t?t.deco:I.none)});function Oo(s,t){return v.create(s.filter(e=>e.field==t).map(e=>v.range(e.from,e.to)))}function K0(s){let t=To.parse(s);return(e,i,n,r)=>{let{text:o,ranges:l}=t.instantiate(e.state,n),{main:a}=e.state.selection,h={changes:{from:n,to:r==a.from?a.to:r,insert:F.of(o)},scrollIntoView:!0,annotations:i?[Co.of(i),et.userEvent.of("input.complete")]:void 0};if(l.length&&(h.selection=Oo(l,0)),l.some(c=>c.field>0)){let c=new yi(l,0),f=h.effects=[on.of(c)];e.state.field(Gi,!1)===void 0&&f.push(_.appendConfig.of([Gi,X0,Q0,Mf]))}e.dispatch(e.state.update(h))}}function Df(s){return({state:t,dispatch:e})=>{let i=t.field(Gi,!1);if(!i||s<0&&i.active==0)return!1;let n=i.active+s,r=s>0&&!i.ranges.some(o=>o.field==n+s);return e(t.update({selection:Oo(i.ranges,n),effects:on.of(r?null:new yi(i.ranges,n)),scrollIntoView:!0})),!0}}const U0=({state:s,dispatch:t})=>s.field(Gi,!1)?(t(s.update({effects:on.of(null)})),!0):!1,G0=Df(1),Y0=Df(-1),J0=[{key:"Tab",run:G0,shift:Y0},{key:"Escape",run:U0}],wa=O.define({combine(s){return s.length?s[0]:J0}}),X0=Ye.highest(en.compute([wa],s=>s.facet(wa)));function T1(s,t){return Object.assign(Object.assign({},t),{apply:K0(s)})}const Q0=B.domEventHandlers({mousedown(s,t){let e=t.state.field(Gi,!1),i;if(!e||(i=t.posAtCoords({x:s.clientX,y:s.clientY}))==null)return!1;let n=e.ranges.find(r=>r.from<=i&&r.to>=i);return!n||n.field==e.active?!1:(t.dispatch({selection:Oo(e.ranges,n.field),effects:on.of(e.ranges.some(r=>r.field>n.field)?new yi(e.ranges,n.field):null),scrollIntoView:!0}),!0)}}),Yi={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},Fe=_.define({map(s,t){let e=t.mapPos(s,-1,ct.TrackAfter);return e??void 0}}),Bo=new class extends Ve{};Bo.startSide=1;Bo.endSide=-1;const Tf=Tt.define({create(){return z.empty},update(s,t){if(s=s.map(t.changes),t.selection){let e=t.state.doc.lineAt(t.selection.main.head);s=s.update({filter:i=>i>=e.from&&i<=e.to})}for(let e of t.effects)e.is(Fe)&&(s=s.update({add:[Bo.range(e.value,e.value+1)]}));return s}});function Z0(){return[eb,Tf]}const Hs="()[]{}<>«»»«［］｛｝";function Of(s){for(let t=0;t<Hs.length;t+=2)if(Hs.charCodeAt(t)==s)return Hs.charAt(t+1);return Qa(s<128?s:s+1)}function Bf(s,t){return s.languageDataAt("closeBrackets",t)[0]||Yi}const tb=typeof navigator=="object"&&/Android\b/.test(navigator.userAgent),eb=B.inputHandler.of((s,t,e,i)=>{if((tb?s.composing:s.compositionStarted)||s.state.readOnly)return!1;let n=s.state.selection.main;if(i.length>2||i.length==2&&ue(Pt(i,0))==1||t!=n.from||e!=n.to)return!1;let r=sb(s.state,i);return r?(s.dispatch(r),!0):!1}),ib=({state:s,dispatch:t})=>{if(s.readOnly)return!1;let i=Bf(s,s.selection.main.head).brackets||Yi.brackets,n=null,r=s.changeByRange(o=>{if(o.empty){let l=rb(s.doc,o.head);for(let a of i)if(a==l&&gs(s.doc,o.head)==Of(Pt(a,0)))return{changes:{from:o.head-a.length,to:o.head+a.length},range:v.cursor(o.head-a.length)}}return{range:n=o}});return n||t(s.update(r,{scrollIntoView:!0,userEvent:"delete.backward"})),!n},nb=[{key:"Backspace",run:ib}];function sb(s,t){let e=Bf(s,s.selection.main.head),i=e.brackets||Yi.brackets;for(let n of i){let r=Of(Pt(n,0));if(t==n)return r==n?ab(s,n,i.indexOf(n+n+n)>-1,e):ob(s,n,r,e.before||Yi.before);if(t==r&&Pf(s,s.selection.main.from))return lb(s,n,r)}return null}function Pf(s,t){let e=!1;return s.field(Tf).between(0,s.doc.length,i=>{i==t&&(e=!0)}),e}function gs(s,t){let e=s.sliceString(t,t+2);return e.slice(0,ue(Pt(e,0)))}function rb(s,t){let e=s.sliceString(t-2,t);return ue(Pt(e,0))==e.length?e:e.slice(1)}function ob(s,t,e,i){let n=null,r=s.changeByRange(o=>{if(!o.empty)return{changes:[{insert:t,from:o.from},{insert:e,from:o.to}],effects:Fe.of(o.to+t.length),range:v.range(o.anchor+t.length,o.head+t.length)};let l=gs(s.doc,o.head);return!l||/\s/.test(l)||i.indexOf(l)>-1?{changes:{insert:t+e,from:o.head},effects:Fe.of(o.head+t.length),range:v.cursor(o.head+t.length)}:{range:n=o}});return n?null:s.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function lb(s,t,e){let i=null,n=s.changeByRange(r=>r.empty&&gs(s.doc,r.head)==e?{changes:{from:r.head,to:r.head+e.length,insert:e},range:v.cursor(r.head+e.length)}:i={range:r});return i?null:s.update(n,{scrollIntoView:!0,userEvent:"input.type"})}function ab(s,t,e,i){let n=i.stringPrefixes||Yi.stringPrefixes,r=null,o=s.changeByRange(l=>{if(!l.empty)return{changes:[{insert:t,from:l.from},{insert:t,from:l.to}],effects:Fe.of(l.to+t.length),range:v.range(l.anchor+t.length,l.head+t.length)};let a=l.head,h=gs(s.doc,a),c;if(h==t){if(xa(s,a))return{changes:{insert:t+t,from:a},effects:Fe.of(a+t.length),range:v.cursor(a+t.length)};if(Pf(s,a)){let u=e&&s.sliceDoc(a,a+t.length*3)==t+t+t?t+t+t:t;return{changes:{from:a,to:a+u.length,insert:u},range:v.cursor(a+u.length)}}}else{if(e&&s.sliceDoc(a-2*t.length,a)==t+t&&(c=va(s,a-2*t.length,n))>-1&&xa(s,c))return{changes:{insert:t+t+t+t,from:a},effects:Fe.of(a+t.length),range:v.cursor(a+t.length)};if(s.charCategorizer(a)(h)!=Nt.Word&&va(s,a,n)>-1&&!hb(s,a,t,n))return{changes:{insert:t+t,from:a},effects:Fe.of(a+t.length),range:v.cursor(a+t.length)}}return{range:r=l}});return r?null:s.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function xa(s,t){let e=ut(s).resolveInner(t+1);return e.parent&&e.from==t}function hb(s,t,e,i){let n=ut(s).resolveInner(t,-1),r=i.reduce((o,l)=>Math.max(o,l.length),0);for(let o=0;o<5;o++){let l=s.sliceDoc(n.from,Math.min(n.to,n.from+e.length+r)),a=l.indexOf(e);if(!a||a>-1&&i.indexOf(l.slice(0,a))>-1){let c=n.firstChild;for(;c&&c.from==n.from&&c.to-c.from>e.length+a;){if(s.sliceDoc(c.to-e.length,c.to)==e)return!1;c=c.firstChild}return!0}let h=n.to==t&&n.parent;if(!h)break;n=h}return!1}function va(s,t,e){let i=s.charCategorizer(t);if(i(s.sliceDoc(t-1,t))!=Nt.Word)return t;for(let n of e){let r=t-n.length;if(s.sliceDoc(r,t)==n&&i(s.sliceDoc(r-1,r))!=Nt.Word)return r}return-1}function cb(s={}){return[W0,At,rt.of(s),H0,fb,Mf]}const Ef=[{key:"Ctrl-Space",run:ya},{mac:"Alt-`",run:ya},{key:"Escape",run:_0},{key:"ArrowDown",run:Mn(!0)},{key:"ArrowUp",run:Mn(!1)},{key:"PageDown",run:Mn(!0,"page")},{key:"PageUp",run:Mn(!1,"page")},{key:"Enter",run:Af}],fb=Ye.highest(en.computeN([rt],s=>s.facet(rt).defaultKeymap?[Ef]:[])),ub="#2E3235",ee="#DDDDDD",Ii="#B9D2FF",Dn="#b0b0b0",db="#e0e0e0",Lf="#808080",Vs="#000000",pb="#A54543",Rf="#fc6d24",Le="#fda331",Ws="#8abeb7",ka="#b5bd68",Ai="#6fb3d2",Mi="#cc99cc",mb="#6987AF",Sa=Rf,Ca="#292d30",Tn=Ii+"30",gb=ub,zs=ee,bb="#202325",Aa=ee,yb=B.theme({"&":{color:ee,backgroundColor:gb},".cm-content":{caretColor:Aa},".cm-cursor, .cm-dropCursor":{borderLeftColor:Aa},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:bb},".cm-panels":{backgroundColor:Ca,color:Dn},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:Ii,outline:`1px solid ${Dn}`,color:Vs},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:db,color:Vs},".cm-activeLine":{backgroundColor:Tn},".cm-selectionMatch":{backgroundColor:Tn},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${Dn}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:Ii,color:Vs},".cm-gutters":{borderRight:"1px solid #ffffff10",color:Lf,backgroundColor:Ca},".cm-activeLineGutter":{backgroundColor:Tn},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:Ii},".cm-tooltip":{border:"none",backgroundColor:zs},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:zs,borderBottomColor:zs},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:Tn,color:Dn}}},{dark:!0}),wb=gi.define([{tag:b.keyword,color:Le},{tag:[b.name,b.deleted,b.character,b.propertyName,b.macroName],color:ka},{tag:[b.variableName],color:Ai},{tag:[b.function(b.variableName)],color:Le},{tag:[b.labelName],color:Rf},{tag:[b.color,b.constant(b.name),b.standard(b.name)],color:Le},{tag:[b.definition(b.name),b.separator],color:Mi},{tag:[b.brace],color:Mi},{tag:[b.annotation],color:Sa},{tag:[b.number,b.changed,b.annotation,b.modifier,b.self,b.namespace],color:Le},{tag:[b.typeName,b.className],color:Ai},{tag:[b.operator,b.operatorKeyword],color:Mi},{tag:[b.tagName],color:Le},{tag:[b.squareBracket],color:Mi},{tag:[b.angleBracket],color:Mi},{tag:[b.attributeName],color:Ai},{tag:[b.regexp],color:Le},{tag:[b.quote],color:ee},{tag:[b.string],color:ka},{tag:b.link,color:mb,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[b.url,b.escape,b.special(b.string)],color:Ws},{tag:[b.meta],color:pb},{tag:[b.comment],color:Lf,fontStyle:"italic"},{tag:b.monospace,color:ee},{tag:b.strong,fontWeight:"bold",color:Le},{tag:b.emphasis,fontStyle:"italic",color:Ai},{tag:b.strikethrough,textDecoration:"line-through"},{tag:b.heading,fontWeight:"bold",color:ee},{tag:b.special(b.heading1),fontWeight:"bold",color:ee},{tag:b.heading1,fontWeight:"bold",color:ee},{tag:[b.heading2,b.heading3,b.heading4],fontWeight:"bold",color:ee},{tag:[b.heading5,b.heading6],color:ee},{tag:[b.atom,b.bool,b.special(b.variableName)],color:Ws},{tag:[b.processingInstruction,b.inserted],color:Ws},{tag:[b.contentSeparator],color:Ai},{tag:b.invalid,color:Ii,borderBottom:`1px dotted ${Sa}`}]),xb=[yb,yo(wb)],Ma="#2e3440",Po="#3b4252",Da="#434c5e",On="#4c566a",Ta="#e5e9f0",$r="#eceff4",$s="#8fbcbb",Oa="#88c0d0",vb="#81a1c1",jt="#5e81ac",kb="#bf616a",Ze="#d08770",qs="#ebcb8b",Ba="#a3be8c",Sb="#b48ead",Pa="#d30102",Eo=$r,js=Eo,Cb="#ffffff",Ks=Po,Ab=Eo,Ea=Po,Mb=B.theme({"&":{color:Ma,backgroundColor:Cb},".cm-content":{caretColor:Ea},".cm-cursor, .cm-dropCursor":{borderLeftColor:Ea},"&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:Ab},".cm-panels":{backgroundColor:Eo,color:On},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:`1px solid ${On}`},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:Ta},".cm-activeLine":{backgroundColor:js},".cm-selectionMatch":{backgroundColor:Ta},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{outline:`1px solid ${On}`},"&.cm-focused .cm-matchingBracket":{backgroundColor:$r},".cm-gutters":{backgroundColor:$r,color:Ma,border:"none"},".cm-activeLineGutter":{backgroundColor:js},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:Ks},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:Ks,borderBottomColor:Ks},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:js,color:On}}},{dark:!1}),Db=gi.define([{tag:b.keyword,color:jt},{tag:[b.name,b.deleted,b.character,b.propertyName,b.macroName],color:Ze},{tag:[b.variableName],color:Ze},{tag:[b.function(b.variableName)],color:jt},{tag:[b.labelName],color:vb},{tag:[b.color,b.constant(b.name),b.standard(b.name)],color:jt},{tag:[b.definition(b.name),b.separator],color:Ba},{tag:[b.brace],color:$s},{tag:[b.annotation],color:Pa},{tag:[b.number,b.changed,b.annotation,b.modifier,b.self,b.namespace],color:Oa},{tag:[b.typeName,b.className],color:qs},{tag:[b.operator,b.operatorKeyword],color:Ba},{tag:[b.tagName],color:Sb},{tag:[b.squareBracket],color:kb},{tag:[b.angleBracket],color:Ze},{tag:[b.attributeName],color:qs},{tag:[b.regexp],color:jt},{tag:[b.quote],color:Po},{tag:[b.string],color:Ze},{tag:b.link,color:$s,textDecoration:"underline",textUnderlinePosition:"under"},{tag:[b.url,b.escape,b.special(b.string)],color:Ze},{tag:[b.meta],color:Oa},{tag:[b.comment],color:Da,fontStyle:"italic"},{tag:b.strong,fontWeight:"bold",color:jt},{tag:b.emphasis,fontStyle:"italic",color:jt},{tag:b.strikethrough,textDecoration:"line-through"},{tag:b.heading,fontWeight:"bold",color:jt},{tag:b.special(b.heading1),fontWeight:"bold",color:jt},{tag:b.heading1,fontWeight:"bold",color:jt},{tag:[b.heading2,b.heading3,b.heading4],fontWeight:"bold",color:jt},{tag:[b.heading5,b.heading6],color:jt},{tag:[b.atom,b.bool,b.special(b.variableName)],color:Ze},{tag:[b.processingInstruction,b.inserted],color:$s},{tag:[b.contentSeparator],color:qs},{tag:b.invalid,color:Da,borderBottom:`1px dotted ${Pa}`}]),Tb=[Mb,yo(Db)];function se(){var s=arguments[0];typeof s=="string"&&(s=document.createElement(s));var t=1,e=arguments[1];if(e&&typeof e=="object"&&e.nodeType==null&&!Array.isArray(e)){for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var n=e[i];typeof n=="string"?s.setAttribute(i,n):n!=null&&(s[i]=n)}t++}for(;t<arguments.length;t++)_f(s,arguments[t]);return s}function _f(s,t){if(typeof t=="string")s.appendChild(document.createTextNode(t));else if(t!=null)if(t.nodeType!=null)s.appendChild(t);else if(Array.isArray(t))for(var e=0;e<t.length;e++)_f(s,t[e]);else throw new RangeError("Unsupported child node: "+t)}class La{constructor(t,e,i){this.from=t,this.to=e,this.diagnostic=i}}class _e{constructor(t,e,i){this.diagnostics=t,this.panel=e,this.selected=i}static init(t,e,i){let n=t,r=i.facet(Ji).markerFilter;r&&(n=r(n,i));let o=t.slice().sort((f,u)=>f.from-u.from||f.to-u.to),l=new Ce,a=[],h=0;for(let f=0;;){let u=f==o.length?null:o[f];if(!u&&!a.length)break;let d,p;for(a.length?(d=h,p=a.reduce((g,y)=>Math.min(g,y.to),u&&u.from>d?u.from:1e8)):(d=u.from,p=u.to,a.push(u),f++);f<o.length;){let g=o[f];if(g.from==d&&(g.to>g.from||g.to==d))a.push(g),f++,p=Math.min(g.to,p);else{p=Math.min(g.from,p);break}}let m=Wb(a);if(a.some(g=>g.from==g.to||g.from==g.to-1&&i.doc.lineAt(g.from).to==g.from))l.add(d,d,I.widget({widget:new Nb(m),diagnostics:a.slice()}));else{let g=a.reduce((y,w)=>w.markClass?y+" "+w.markClass:y,"");l.add(d,p,I.mark({class:"cm-lintRange cm-lintRange-"+m+g,diagnostics:a.slice(),inclusiveEnd:a.some(y=>y.to>p)}))}h=p;for(let g=0;g<a.length;g++)a[g].to<=h&&a.splice(g--,1)}let c=l.finish();return new _e(c,e,pi(c))}}function pi(s,t=null,e=0){let i=null;return s.between(e,1e9,(n,r,{spec:o})=>{if(!(t&&o.diagnostics.indexOf(t)<0))if(!i)i=new La(n,r,t||o.diagnostics[0]);else{if(o.diagnostics.indexOf(i.diagnostic)<0)return!1;i=new La(i.from,r,i.diagnostic)}}),i}function Ob(s,t){let e=t.pos,i=t.end||e,n=s.state.facet(Ji).hideOn(s,e,i);if(n!=null)return n;let r=s.startState.doc.lineAt(t.pos);return!!(s.effects.some(o=>o.is(If))||s.changes.touchesRange(r.from,Math.max(r.to,i)))}function Bb(s,t){return s.field(Et,!1)?t:t.concat(_.appendConfig.of(zb))}const If=_.define(),Lo=_.define(),Nf=_.define(),Et=Tt.define({create(){return new _e(I.none,null,null)},update(s,t){if(t.docChanged&&s.diagnostics.size){let e=s.diagnostics.map(t.changes),i=null,n=s.panel;if(s.selected){let r=t.changes.mapPos(s.selected.from,1);i=pi(e,s.selected.diagnostic,r)||pi(e,null,r)}!e.size&&n&&t.state.facet(Ji).autoPanel&&(n=null),s=new _e(e,n,i)}for(let e of t.effects)if(e.is(If)){let i=t.state.facet(Ji).autoPanel?e.value.length?Xi.open:null:s.panel;s=_e.init(e.value,i,t.state)}else e.is(Lo)?s=new _e(s.diagnostics,e.value?Xi.open:null,s.selected):e.is(Nf)&&(s=new _e(s.diagnostics,s.panel,e.value));return s},provide:s=>[Br.from(s,t=>t.panel),B.decorations.from(s,t=>t.diagnostics)]}),Pb=I.mark({class:"cm-lintRange cm-lintRange-active"});function Eb(s,t,e){let{diagnostics:i}=s.state.field(Et),n,r=-1,o=-1;i.between(t-(e<0?1:0),t+(e>0?1:0),(a,h,{spec:c})=>{if(t>=a&&t<=h&&(a==h||(t>a||e>0)&&(t<h||e<0)))return n=c.diagnostics,r=a,o=h,!1});let l=s.state.facet(Ji).tooltipFilter;return n&&l&&(n=l(n,s.state)),n?{pos:r,end:o,above:s.state.doc.lineAt(r).to<o,create(){return{dom:Lb(s,n)}}}:null}function Lb(s,t){return se("ul",{class:"cm-tooltip-lint"},t.map(e=>Hf(s,e,!1)))}const Rb=s=>{let t=s.state.field(Et,!1);(!t||!t.panel)&&s.dispatch({effects:Bb(s.state,[Lo.of(!0)])});let e=Fp(s,Xi.open);return e&&e.dom.querySelector(".cm-panel-lint ul").focus(),!0},Ra=s=>{let t=s.state.field(Et,!1);return!t||!t.panel?!1:(s.dispatch({effects:Lo.of(!1)}),!0)},_b=s=>{let t=s.state.field(Et,!1);if(!t)return!1;let e=s.state.selection.main,i=t.diagnostics.iter(e.to+1);return!i.value&&(i=t.diagnostics.iter(0),!i.value||i.from==e.from&&i.to==e.to)?!1:(s.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0}),!0)},Ib=[{key:"Mod-Shift-m",run:Rb,preventDefault:!0},{key:"F8",run:_b}],Ji=O.define({combine(s){return Object.assign({sources:s.map(t=>t.source).filter(t=>t!=null)},Je(s.map(t=>t.config),{delay:750,markerFilter:null,tooltipFilter:null,needsRefresh:null,hideOn:()=>null},{needsRefresh:(t,e)=>t?e?i=>t(i)||e(i):t:e}))}});function Ff(s){let t=[];if(s)t:for(let{name:e}of s){for(let i=0;i<e.length;i++){let n=e[i];if(/[a-zA-Z]/.test(n)&&!t.some(r=>r.toLowerCase()==n.toLowerCase())){t.push(n);continue t}}t.push("")}return t}function Hf(s,t,e){var i;let n=e?Ff(t.actions):[];return se("li",{class:"cm-diagnostic cm-diagnostic-"+t.severity},se("span",{class:"cm-diagnosticText"},t.renderMessage?t.renderMessage(s):t.message),(i=t.actions)===null||i===void 0?void 0:i.map((r,o)=>{let l=!1,a=u=>{if(u.preventDefault(),l)return;l=!0;let d=pi(s.state.field(Et).diagnostics,t);d&&r.apply(s,d.from,d.to)},{name:h}=r,c=n[o]?h.indexOf(n[o]):-1,f=c<0?h:[h.slice(0,c),se("u",h.slice(c,c+1)),h.slice(c+1)];return se("button",{type:"button",class:"cm-diagnosticAction",onclick:a,onmousedown:a,"aria-label":` Action: ${h}${c<0?"":` (access key "${n[o]})"`}.`},f)}),t.source&&se("div",{class:"cm-diagnosticSource"},t.source))}class Nb extends he{constructor(t){super(),this.sev=t}eq(t){return t.sev==this.sev}toDOM(){return se("span",{class:"cm-lintPoint cm-lintPoint-"+this.sev})}}class _a{constructor(t,e){this.diagnostic=e,this.id="item_"+Math.floor(Math.random()*4294967295).toString(16),this.dom=Hf(t,e,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class Xi{constructor(t){this.view=t,this.items=[];let e=n=>{if(n.keyCode==27)Ra(this.view),this.view.focus();else if(n.keyCode==38||n.keyCode==33)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(n.keyCode==40||n.keyCode==34)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(n.keyCode==36)this.moveSelection(0);else if(n.keyCode==35)this.moveSelection(this.items.length-1);else if(n.keyCode==13)this.view.focus();else if(n.keyCode>=65&&n.keyCode<=90&&this.selectedIndex>=0){let{diagnostic:r}=this.items[this.selectedIndex],o=Ff(r.actions);for(let l=0;l<o.length;l++)if(o[l].toUpperCase().charCodeAt(0)==n.keyCode){let a=pi(this.view.state.field(Et).diagnostics,r);a&&r.actions[l].apply(t,a.from,a.to)}}else return;n.preventDefault()},i=n=>{for(let r=0;r<this.items.length;r++)this.items[r].dom.contains(n.target)&&this.moveSelection(r)};this.list=se("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:e,onclick:i}),this.dom=se("div",{class:"cm-panel-lint"},this.list,se("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>Ra(this.view)},"×")),this.update()}get selectedIndex(){let t=this.view.state.field(Et).selected;if(!t)return-1;for(let e=0;e<this.items.length;e++)if(this.items[e].diagnostic==t.diagnostic)return e;return-1}update(){let{diagnostics:t,selected:e}=this.view.state.field(Et),i=0,n=!1,r=null,o=new Set;for(t.between(0,this.view.state.doc.length,(l,a,{spec:h})=>{for(let c of h.diagnostics){if(o.has(c))continue;o.add(c);let f=-1,u;for(let d=i;d<this.items.length;d++)if(this.items[d].diagnostic==c){f=d;break}f<0?(u=new _a(this.view,c),this.items.splice(i,0,u),n=!0):(u=this.items[f],f>i&&(this.items.splice(i,f-i),n=!0)),e&&u.diagnostic==e.diagnostic?u.dom.hasAttribute("aria-selected")||(u.dom.setAttribute("aria-selected","true"),r=u):u.dom.hasAttribute("aria-selected")&&u.dom.removeAttribute("aria-selected"),i++}});i<this.items.length&&!(this.items.length==1&&this.items[0].diagnostic.from<0);)n=!0,this.items.pop();this.items.length==0&&(this.items.push(new _a(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),n=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:l,panel:a})=>{let h=a.height/this.list.offsetHeight;l.top<a.top?this.list.scrollTop-=(a.top-l.top)/h:l.bottom>a.bottom&&(this.list.scrollTop+=(l.bottom-a.bottom)/h)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),n&&this.sync()}sync(){let t=this.list.firstChild;function e(){let i=t;t=i.nextSibling,i.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){for(;t!=i.dom;)e();t=i.dom.nextSibling}else this.list.insertBefore(i.dom,t);for(;t;)e()}moveSelection(t){if(this.selectedIndex<0)return;let e=this.view.state.field(Et),i=pi(e.diagnostics,this.items[t].diagnostic);i&&this.view.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0,effects:Nf.of(i)})}static open(t){return new Xi(t)}}function Fb(s,t='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${t}>${encodeURIComponent(s)}</svg>')`}function Bn(s){return Fb(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${s}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const Hb=B.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnostic-hint":{borderLeft:"5px solid #66d"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px",cursor:"pointer"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:Bn("#d11")},".cm-lintRange-warning":{backgroundImage:Bn("orange")},".cm-lintRange-info":{backgroundImage:Bn("#999")},".cm-lintRange-hint":{backgroundImage:Bn("#66d")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-lintPoint-hint":{"&:after":{borderBottomColor:"#66d"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}});function Vb(s){return s=="error"?4:s=="warning"?3:s=="info"?2:1}function Wb(s){let t="hint",e=1;for(let i of s){let n=Vb(i.severity);n>e&&(e=n,t=i.severity)}return t}const zb=[Et,B.decorations.compute([Et],s=>{let{selected:t,panel:e}=s.field(Et);return!t||!e||t.from==t.to?I.none:I.set([Pb.range(t.from,t.to)])}),Ip(Eb,{hideOn:Ob}),Hb],$b=[pp(),mg(),$m(),rp(),V.allowMultipleSelections.of(!0),Om(),yo(Um,{fallback:!0}),Z0(),Ap(),Tp(),en.of([...nb,...m0,...Cg,...Hm,...Ef,...Ib])],qb=["standardSQL","msSQL","mySQL","mariaDB","sqlite","cassandra","plSQL","hive","pgSQL","gql","gpSQL","sparkSQL","esper"],Ia={python:()=>lt(()=>import("./index-TAdSoOLO.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27]),import.meta.url).then(s=>s.python()),c:()=>lt(()=>import("./clike-wD8xDpL-.js"),[],import.meta.url).then(s=>Kt.define(s.c)),cpp:()=>lt(()=>import("./clike-wD8xDpL-.js"),[],import.meta.url).then(s=>Kt.define(s.cpp)),markdown:async()=>{const[s,t]=await Promise.all([lt(()=>import("./index-Bz6aSYMe.js"),__vite__mapDeps([28,29,1,30,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,31]),import.meta.url),lt(()=>import("./frontmatter-CCCjReic.js"),__vite__mapDeps([32,33,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27]),import.meta.url)]);return s.markdown({extensions:[t.frontmatter]})},json:()=>lt(()=>import("./index-DlT7JNkm.js"),__vite__mapDeps([34,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27]),import.meta.url).then(s=>s.json()),html:()=>lt(()=>import("./index-HByxFVxO.js"),__vite__mapDeps([29,1,30,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,31]),import.meta.url).then(s=>s.html()),css:()=>lt(()=>import("./index-BMz6jcp6.js"),__vite__mapDeps([30,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27]),import.meta.url).then(s=>s.css()),javascript:()=>lt(()=>import("./index-CnmEBB3i.js"),__vite__mapDeps([31,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27]),import.meta.url).then(s=>s.javascript()),jinja2:()=>lt(()=>import("./jinja2-C4DGRd-O.js"),[],import.meta.url).then(s=>Kt.define(s.jinja2)),typescript:()=>lt(()=>import("./index-CnmEBB3i.js"),__vite__mapDeps([31,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27]),import.meta.url).then(s=>s.javascript({typescript:!0})),yaml:()=>lt(()=>import("./yaml-DsCXHVTH.js"),[],import.meta.url).then(s=>Kt.define(s.yaml)),dockerfile:()=>lt(()=>import("./dockerfile-D3l6Kuvz.js"),[],import.meta.url).then(s=>Kt.define(s.dockerFile)),shell:()=>lt(()=>import("./shell-CjFT_Tl9.js"),[],import.meta.url).then(s=>Kt.define(s.shell)),r:()=>lt(()=>import("./r-DUYO_cvP.js"),[],import.meta.url).then(s=>Kt.define(s.r)),sql:()=>lt(()=>import("./sql-C4g8LzGK.js"),[],import.meta.url).then(s=>Kt.define(s.standardSQL)),...Object.fromEntries(qb.map(s=>["sql-"+s,()=>lt(()=>import("./sql-C4g8LzGK.js"),[],import.meta.url).then(t=>Kt.define(t[s]))]))},jb={py:"python",md:"markdown",js:"javascript",ts:"typescript",sh:"shell"};async function Kb(s){const t=Ia[s]||Ia[jb[s]]||void 0;if(t)return t()}const{SvelteComponent:Ub,append:Gb,attr:Us,binding_callbacks:Yb,detach:Jb,element:Na,flush:St,init:Xb,insert:Qb,noop:Fa,safe_not_equal:Zb}=window.__gradio__svelte__internal,{createEventDispatcher:ty,onMount:ey}=window.__gradio__svelte__internal;function iy(s){let t,e,i;return{c(){t=Na("div"),e=Na("div"),Us(e,"class",i="codemirror-wrapper "+s[0]+" svelte-scxcch"),Us(t,"class","wrap svelte-scxcch")},m(n,r){Qb(n,t,r),Gb(t,e),s[16](e)},p(n,r){r[0]&1&&i!==(i="codemirror-wrapper "+n[0]+" svelte-scxcch")&&Us(e,"class",i)},i:Fa,o:Fa,d(n){n&&Jb(t),s[16](null)}}}function ny(s){let t=s.dom.querySelectorAll(".cm-gutterElement");if(t.length===0)return null;for(var e=0;e<t.length;e++){let i=t[e],n=getComputedStyle(i)?.height??"0px";if(n!="0px")return n}return null}function sy(s,t,e){let{class_names:i=""}=t,{value:n=""}=t,{dark_mode:r}=t,{basic:o=!0}=t,{language:l}=t,{lines:a=5}=t,{max_lines:h=null}=t,{extensions:c=[]}=t,{use_tab:f=!0}=t,{readonly:u=!1}=t,{placeholder:d=void 0}=t,{wrap_lines:p=!1}=t,{show_line_numbers:m=!0}=t,{autocomplete:g=!1}=t;const y=ty();let w,S,k;async function x(P){const X=await Kb(P);e(15,w=X)}function C(P){k&&P!==k.state.doc.toString()&&k.dispatch({changes:{from:0,to:k.state.doc.length,insert:P}})}function A(){k&&k.requestMeasure({read:M})}function L(){const P=new B({parent:S,state:ot(n)});return P.dom.addEventListener("focus",R,!0),P.dom.addEventListener("blur",$,!0),P}function R(){y("focus")}function $(){y("blur")}function M(P){let X=P.dom.querySelector(".cm-scroller");if(!X)return null;const Z=ny(P);if(!Z)return null;const Rt=a==1?1:a+1;X.style.minHeight=`calc(${Z} * ${Rt})`,h&&(X.style.maxHeight=`calc(${Z} * ${h+1})`)}function E(P){if(P.docChanged){const Z=P.state.doc.toString();e(2,n=Z),y("change",Z)}k.requestMeasure({read:M})}function W(){return[...bt(o,f,d,u,w,m),H,...Lt(),...c]}const H=B.theme({"&":{fontSize:"var(--text-sm)",backgroundColor:"var(--border-color-secondary)"},".cm-content":{paddingTop:"5px",paddingBottom:"5px",color:"var(--body-text-color)",fontFamily:"var(--font-mono)",minHeight:"100%"},".cm-gutterElement":{marginRight:"var(--spacing-xs)"},".cm-gutters":{marginRight:"1px",borderRight:"1px solid var(--border-color-primary)",backgroundColor:"var(--block-background-fill);",color:"var(--body-text-color-subdued)"},".cm-focused":{outline:"none"},".cm-scroller":{height:"auto"},".cm-cursor":{borderLeftColor:"var(--body-text-color)"}}),q=B.theme({".cm-tooltip-autocomplete":{"& > ul":{backgroundColor:"var(--background-fill-primary)",color:"var(--body-text-color)"},"& > ul > li[aria-selected]":{backgroundColor:"var(--color-accent-soft)",color:"var(--body-text-color)"}}});function ot(P){return V.create({doc:P??void 0,extensions:W()})}function bt(P,X,Z,Rt,fe,Xe){const Ot=[B.editable.of(!Rt),V.readOnly.of(Rt),B.contentAttributes.of({"aria-label":"Code input container"})];return P&&Ot.push($b),X&&Ot.push(en.of([{key:"Tab",run:Af},g0])),Z&&Ot.push(vp(Z)),fe&&Ot.push(fe),Xe&&Ot.push(Gp()),g&&(Ot.push(cb()),Ot.push(q)),Ot.push(B.updateListener.of(E)),p&&Ot.push(B.lineWrapping),Ot}function Lt(){const P=[];return r?P.push(xb):P.push(Tb),P}function it(){k?.dispatch({effects:_.reconfigure.of(W())})}ey(()=>(k=L(),()=>k?.destroy()));function kt(P){Yb[P?"unshift":"push"](()=>{S=P,e(1,S)})}return s.$$set=P=>{"class_names"in P&&e(0,i=P.class_names),"value"in P&&e(2,n=P.value),"dark_mode"in P&&e(3,r=P.dark_mode),"basic"in P&&e(4,o=P.basic),"language"in P&&e(5,l=P.language),"lines"in P&&e(6,a=P.lines),"max_lines"in P&&e(7,h=P.max_lines),"extensions"in P&&e(8,c=P.extensions),"use_tab"in P&&e(9,f=P.use_tab),"readonly"in P&&e(10,u=P.readonly),"placeholder"in P&&e(11,d=P.placeholder),"wrap_lines"in P&&e(12,p=P.wrap_lines),"show_line_numbers"in P&&e(13,m=P.show_line_numbers),"autocomplete"in P&&e(14,g=P.autocomplete)},s.$$.update=()=>{s.$$.dirty[0]&32&&x(l),s.$$.dirty[0]&33792&&it(),s.$$.dirty[0]&4&&C(n)},A(),[i,S,n,r,o,l,a,h,c,f,u,d,p,m,g,w,kt]}class ry extends Ub{constructor(t){super(),Xb(this,t,sy,iy,Zb,{class_names:0,value:2,dark_mode:3,basic:4,language:5,lines:6,max_lines:7,extensions:8,use_tab:9,readonly:10,placeholder:11,wrap_lines:12,show_line_numbers:13,autocomplete:14},null,[-1,-1])}get class_names(){return this.$$.ctx[0]}set class_names(t){this.$$set({class_names:t}),St()}get value(){return this.$$.ctx[2]}set value(t){this.$$set({value:t}),St()}get dark_mode(){return this.$$.ctx[3]}set dark_mode(t){this.$$set({dark_mode:t}),St()}get basic(){return this.$$.ctx[4]}set basic(t){this.$$set({basic:t}),St()}get language(){return this.$$.ctx[5]}set language(t){this.$$set({language:t}),St()}get lines(){return this.$$.ctx[6]}set lines(t){this.$$set({lines:t}),St()}get max_lines(){return this.$$.ctx[7]}set max_lines(t){this.$$set({max_lines:t}),St()}get extensions(){return this.$$.ctx[8]}set extensions(t){this.$$set({extensions:t}),St()}get use_tab(){return this.$$.ctx[9]}set use_tab(t){this.$$set({use_tab:t}),St()}get readonly(){return this.$$.ctx[10]}set readonly(t){this.$$set({readonly:t}),St()}get placeholder(){return this.$$.ctx[11]}set placeholder(t){this.$$set({placeholder:t}),St()}get wrap_lines(){return this.$$.ctx[12]}set wrap_lines(t){this.$$set({wrap_lines:t}),St()}get show_line_numbers(){return this.$$.ctx[13]}set show_line_numbers(t){this.$$set({show_line_numbers:t}),St()}get autocomplete(){return this.$$.ctx[14]}set autocomplete(t){this.$$set({autocomplete:t}),St()}}const Vf=ry,{SvelteComponent:oy,create_component:ly,destroy_component:ay,flush:hy,init:cy,mount_component:fy,safe_not_equal:uy,transition_in:dy,transition_out:py}=window.__gradio__svelte__internal,{onDestroy:my}=window.__gradio__svelte__internal;function gy(s){let t,e;return t=new qa({props:{Icon:s[0]?zn:Ro}}),t.$on("click",s[1]),{c(){ly(t.$$.fragment)},m(i,n){fy(t,i,n),e=!0},p(i,[n]){const r={};n&1&&(r.Icon=i[0]?zn:Ro),t.$set(r)},i(i){e||(dy(t.$$.fragment,i),e=!0)},o(i){py(t.$$.fragment,i),e=!1},d(i){ay(t,i)}}}function by(s,t,e){let i=!1,{value:n}=t,r;function o(){e(0,i=!0),r&&clearTimeout(r),r=setTimeout(()=>{e(0,i=!1)},2e3)}async function l(){"clipboard"in navigator&&(await navigator.clipboard.writeText(n),o())}return my(()=>{r&&clearTimeout(r)}),s.$$set=a=>{"value"in a&&e(2,n=a.value)},[i,l,n]}class yy extends oy{constructor(t){super(),cy(this,t,by,gy,uy,{value:2})}get value(){return this.$$.ctx[2]}set value(t){this.$$set({value:t}),hy()}}const Wf=yy,{SvelteComponent:wy,create_component:zf,destroy_component:$f,flush:Ha,init:xy,mount_component:qf,safe_not_equal:vy,transition_in:jf,transition_out:Kf}=window.__gradio__svelte__internal,{onDestroy:ky}=window.__gradio__svelte__internal;function Sy(s){let t,e;return t=new qa({props:{Icon:s[0]?zn:_o}}),{c(){zf(t.$$.fragment)},m(i,n){qf(t,i,n),e=!0},p(i,n){const r={};n&1&&(r.Icon=i[0]?zn:_o),t.$set(r)},i(i){e||(jf(t.$$.fragment,i),e=!0)},o(i){Kf(t.$$.fragment,i),e=!1},d(i){$f(t,i)}}}function Cy(s){let t,e;return t=new Yf({props:{download:"file."+s[2],href:s[1],$$slots:{default:[Sy]},$$scope:{ctx:s}}}),t.$on("click",s[3]),{c(){zf(t.$$.fragment)},m(i,n){qf(t,i,n),e=!0},p(i,[n]){const r={};n&4&&(r.download="file."+i[2]),n&2&&(r.href=i[1]),n&129&&(r.$$scope={dirty:n,ctx:i}),t.$set(r)},i(i){e||(jf(t.$$.fragment,i),e=!0)},o(i){Kf(t.$$.fragment,i),e=!1},d(i){$f(t,i)}}}function Ay(s){return{py:"py",python:"py",md:"md",markdown:"md",json:"json",html:"html",css:"css",js:"js",javascript:"js",ts:"ts",typescript:"ts",yaml:"yaml",yml:"yml",dockerfile:"dockerfile",sh:"sh",shell:"sh",r:"r",c:"c",cpp:"cpp"}[s]||"txt"}function My(s,t,e){let i,n,{value:r}=t,{language:o}=t,l=!1,a;function h(){e(0,l=!0),a&&clearTimeout(a),a=setTimeout(()=>{e(0,l=!1)},2e3)}return ky(()=>{a&&clearTimeout(a)}),s.$$set=c=>{"value"in c&&e(4,r=c.value),"language"in c&&e(5,o=c.language)},s.$$.update=()=>{s.$$.dirty&32&&e(2,i=Ay(o)),s.$$.dirty&16&&e(1,n=URL.createObjectURL(new Blob([r])))},[l,n,i,h,r,o]}class Dy extends wy{constructor(t){super(),xy(this,t,My,Cy,vy,{value:4,language:5})}get value(){return this.$$.ctx[4]}set value(t){this.$$set({value:t}),Ha()}get language(){return this.$$.ctx[5]}set language(t){this.$$set({language:t}),Ha()}}const Uf=Dy,{SvelteComponent:Ty,create_component:qr,destroy_component:jr,detach:Oy,flush:Va,init:By,insert:Py,mount_component:Kr,safe_not_equal:Ey,space:Ly,transition_in:Ur,transition_out:Gr}=window.__gradio__svelte__internal;function Ry(s){let t,e,i,n;return t=new Uf({props:{value:s[0],language:s[1]}}),i=new Wf({props:{value:s[0]}}),{c(){qr(t.$$.fragment),e=Ly(),qr(i.$$.fragment)},m(r,o){Kr(t,r,o),Py(r,e,o),Kr(i,r,o),n=!0},p(r,o){const l={};o&1&&(l.value=r[0]),o&2&&(l.language=r[1]),t.$set(l);const a={};o&1&&(a.value=r[0]),i.$set(a)},i(r){n||(Ur(t.$$.fragment,r),Ur(i.$$.fragment,r),n=!0)},o(r){Gr(t.$$.fragment,r),Gr(i.$$.fragment,r),n=!1},d(r){r&&Oy(e),jr(t,r),jr(i,r)}}}function _y(s){let t,e;return t=new Jf({props:{$$slots:{default:[Ry]},$$scope:{ctx:s}}}),{c(){qr(t.$$.fragment)},m(i,n){Kr(t,i,n),e=!0},p(i,[n]){const r={};n&7&&(r.$$scope={dirty:n,ctx:i}),t.$set(r)},i(i){e||(Ur(t.$$.fragment,i),e=!0)},o(i){Gr(t.$$.fragment,i),e=!1},d(i){jr(t,i)}}}function Iy(s,t,e){let{value:i}=t,{language:n}=t;return s.$$set=r=>{"value"in r&&e(0,i=r.value),"language"in r&&e(1,n=r.language)},[i,n]}class Ny extends Ty{constructor(t){super(),By(this,t,Iy,_y,Ey,{value:0,language:1})}get value(){return this.$$.ctx[0]}set value(t){this.$$set({value:t}),Va()}get language(){return this.$$.ctx[1]}set language(t){this.$$set({language:t}),Va()}}const Gf=Ny,{SvelteComponent:Fy,add_flush_callback:Hy,assign:Vy,bind:Wy,binding_callbacks:zy,check_outros:Wa,create_component:Ke,destroy_component:Ue,detach:Vn,empty:$y,flush:st,get_spread_object:qy,get_spread_update:jy,group_outros:za,init:Ky,insert:Wn,mount_component:Ge,safe_not_equal:Uy,space:Yr,transition_in:It,transition_out:Ut}=window.__gradio__svelte__internal,{afterUpdate:Gy}=window.__gradio__svelte__internal;function $a(s){let t,e;return t=new Zf({props:{Icon:ja,show_label:s[9],label:s[8],float:!1}}),{c(){Ke(t.$$.fragment)},m(i,n){Ge(t,i,n),e=!0},p(i,n){const r={};n&512&&(r.show_label=i[9]),n&256&&(r.label=i[8]),t.$set(r)},i(i){e||(It(t.$$.fragment,i),e=!0)},o(i){Ut(t.$$.fragment,i),e=!1},d(i){Ue(t,i)}}}function Yy(s){let t,e,i,n,r;t=new Gf({props:{language:s[2],value:s[0]}});function o(a){s[20](a)}let l={language:s[2],lines:s[3],max_lines:s[4],dark_mode:s[17],wrap_lines:s[13],show_line_numbers:s[14],autocomplete:s[15],readonly:!s[16]};return s[0]!==void 0&&(l.value=s[0]),i=new Vf({props:l}),zy.push(()=>Wy(i,"value",o)),i.$on("blur",s[21]),i.$on("focus",s[22]),{c(){Ke(t.$$.fragment),e=Yr(),Ke(i.$$.fragment)},m(a,h){Ge(t,a,h),Wn(a,e,h),Ge(i,a,h),r=!0},p(a,h){const c={};h&4&&(c.language=a[2]),h&1&&(c.value=a[0]),t.$set(c);const f={};h&4&&(f.language=a[2]),h&8&&(f.lines=a[3]),h&16&&(f.max_lines=a[4]),h&8192&&(f.wrap_lines=a[13]),h&16384&&(f.show_line_numbers=a[14]),h&32768&&(f.autocomplete=a[15]),h&65536&&(f.readonly=!a[16]),!n&&h&1&&(n=!0,f.value=a[0],Hy(()=>n=!1)),i.$set(f)},i(a){r||(It(t.$$.fragment,a),It(i.$$.fragment,a),r=!0)},o(a){Ut(t.$$.fragment,a),Ut(i.$$.fragment,a),r=!1},d(a){a&&Vn(e),Ue(t,a),Ue(i,a)}}}function Jy(s){let t,e;return t=new tu({props:{unpadded_box:!0,size:"large",$$slots:{default:[Xy]},$$scope:{ctx:s}}}),{c(){Ke(t.$$.fragment)},m(i,n){Ge(t,i,n),e=!0},p(i,n){const r={};n&16777216&&(r.$$scope={dirty:n,ctx:i}),t.$set(r)},i(i){e||(It(t.$$.fragment,i),e=!0)},o(i){Ut(t.$$.fragment,i),e=!1},d(i){Ue(t,i)}}}function Xy(s){let t,e;return t=new ja({}),{c(){Ke(t.$$.fragment)},m(i,n){Ge(t,i,n),e=!0},i(i){e||(It(t.$$.fragment,i),e=!0)},o(i){Ut(t.$$.fragment,i),e=!1},d(i){Ue(t,i)}}}function Qy(s){let t,e,i,n,r,o,l;const a=[{autoscroll:s[1].autoscroll},{i18n:s[1].i18n},s[10]];let h={};for(let p=0;p<a.length;p+=1)h=Vy(h,a[p]);t=new Xf({props:h}),t.$on("clear_status",s[19]);let c=s[9]&&$a(s);const f=[Jy,Yy],u=[];function d(p,m){return!p[0]&&!p[16]?0:1}return n=d(s),r=u[n]=f[n](s),{c(){Ke(t.$$.fragment),e=Yr(),c&&c.c(),i=Yr(),r.c(),o=$y()},m(p,m){Ge(t,p,m),Wn(p,e,m),c&&c.m(p,m),Wn(p,i,m),u[n].m(p,m),Wn(p,o,m),l=!0},p(p,m){const g=m&1026?jy(a,[m&2&&{autoscroll:p[1].autoscroll},m&2&&{i18n:p[1].i18n},m&1024&&qy(p[10])]):{};t.$set(g),p[9]?c?(c.p(p,m),m&512&&It(c,1)):(c=$a(p),c.c(),It(c,1),c.m(i.parentNode,i)):c&&(za(),Ut(c,1,1,()=>{c=null}),Wa());let y=n;n=d(p),n===y?u[n].p(p,m):(za(),Ut(u[y],1,1,()=>{u[y]=null}),Wa(),r=u[n],r?r.p(p,m):(r=u[n]=f[n](p),r.c()),It(r,1),r.m(o.parentNode,o))},i(p){l||(It(t.$$.fragment,p),It(c),It(r),l=!0)},o(p){Ut(t.$$.fragment,p),Ut(c),Ut(r),l=!1},d(p){p&&(Vn(e),Vn(i),Vn(o)),Ue(t,p),c&&c.d(p),u[n].d(p)}}}function Zy(s){let t,e;return t=new Qf({props:{height:s[4]&&"fit-content",variant:"solid",padding:!1,elem_id:s[5],elem_classes:s[6],visible:s[7],scale:s[11],min_width:s[12],$$slots:{default:[Qy]},$$scope:{ctx:s}}}),{c(){Ke(t.$$.fragment)},m(i,n){Ge(t,i,n),e=!0},p(i,[n]){const r={};n&16&&(r.height=i[4]&&"fit-content"),n&32&&(r.elem_id=i[5]),n&64&&(r.elem_classes=i[6]),n&128&&(r.visible=i[7]),n&2048&&(r.scale=i[11]),n&4096&&(r.min_width=i[12]),n&16901919&&(r.$$scope={dirty:n,ctx:i}),t.$set(r)},i(i){e||(It(t.$$.fragment,i),e=!0)},o(i){Ut(t.$$.fragment,i),e=!1},d(i){Ue(t,i)}}}function t1(s,t,e){let{gradio:i}=t,{value:n=""}=t,{value_is_output:r=!1}=t,{language:o=""}=t,{lines:l=5}=t,{max_lines:a=void 0}=t,{elem_id:h=""}=t,{elem_classes:c=[]}=t,{visible:f=!0}=t,{label:u=i.i18n("code.code")}=t,{show_label:d=!0}=t,{loading_status:p}=t,{scale:m=null}=t,{min_width:g=void 0}=t,{wrap_lines:y=!1}=t,{show_line_numbers:w=!0}=t,{autocomplete:S=!1}=t,{interactive:k}=t,x=i.theme==="dark";function C(){i.dispatch("change",n),r||i.dispatch("input")}Gy(()=>{e(18,r=!1)});const A=()=>i.dispatch("clear_status",p);function L(M){n=M,e(0,n)}const R=()=>i.dispatch("blur"),$=()=>i.dispatch("focus");return s.$$set=M=>{"gradio"in M&&e(1,i=M.gradio),"value"in M&&e(0,n=M.value),"value_is_output"in M&&e(18,r=M.value_is_output),"language"in M&&e(2,o=M.language),"lines"in M&&e(3,l=M.lines),"max_lines"in M&&e(4,a=M.max_lines),"elem_id"in M&&e(5,h=M.elem_id),"elem_classes"in M&&e(6,c=M.elem_classes),"visible"in M&&e(7,f=M.visible),"label"in M&&e(8,u=M.label),"show_label"in M&&e(9,d=M.show_label),"loading_status"in M&&e(10,p=M.loading_status),"scale"in M&&e(11,m=M.scale),"min_width"in M&&e(12,g=M.min_width),"wrap_lines"in M&&e(13,y=M.wrap_lines),"show_line_numbers"in M&&e(14,w=M.show_line_numbers),"autocomplete"in M&&e(15,S=M.autocomplete),"interactive"in M&&e(16,k=M.interactive)},s.$$.update=()=>{s.$$.dirty&1&&C()},[n,i,o,l,a,h,c,f,u,d,p,m,g,y,w,S,k,x,r,A,L,R,$]}class e1 extends Fy{constructor(t){super(),Ky(this,t,t1,Zy,Uy,{gradio:1,value:0,value_is_output:18,language:2,lines:3,max_lines:4,elem_id:5,elem_classes:6,visible:7,label:8,show_label:9,loading_status:10,scale:11,min_width:12,wrap_lines:13,show_line_numbers:14,autocomplete:15,interactive:16})}get gradio(){return this.$$.ctx[1]}set gradio(t){this.$$set({gradio:t}),st()}get value(){return this.$$.ctx[0]}set value(t){this.$$set({value:t}),st()}get value_is_output(){return this.$$.ctx[18]}set value_is_output(t){this.$$set({value_is_output:t}),st()}get language(){return this.$$.ctx[2]}set language(t){this.$$set({language:t}),st()}get lines(){return this.$$.ctx[3]}set lines(t){this.$$set({lines:t}),st()}get max_lines(){return this.$$.ctx[4]}set max_lines(t){this.$$set({max_lines:t}),st()}get elem_id(){return this.$$.ctx[5]}set elem_id(t){this.$$set({elem_id:t}),st()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(t){this.$$set({elem_classes:t}),st()}get visible(){return this.$$.ctx[7]}set visible(t){this.$$set({visible:t}),st()}get label(){return this.$$.ctx[8]}set label(t){this.$$set({label:t}),st()}get show_label(){return this.$$.ctx[9]}set show_label(t){this.$$set({show_label:t}),st()}get loading_status(){return this.$$.ctx[10]}set loading_status(t){this.$$set({loading_status:t}),st()}get scale(){return this.$$.ctx[11]}set scale(t){this.$$set({scale:t}),st()}get min_width(){return this.$$.ctx[12]}set min_width(t){this.$$set({min_width:t}),st()}get wrap_lines(){return this.$$.ctx[13]}set wrap_lines(t){this.$$set({wrap_lines:t}),st()}get show_line_numbers(){return this.$$.ctx[14]}set show_line_numbers(t){this.$$set({show_line_numbers:t}),st()}get autocomplete(){return this.$$.ctx[15]}set autocomplete(t){this.$$set({autocomplete:t}),st()}get interactive(){return this.$$.ctx[16]}set interactive(t){this.$$set({interactive:t}),st()}}const O1=Object.freeze(Object.defineProperty({__proto__:null,BaseCode:Vf,BaseCopy:Wf,BaseDownload:Uf,BaseExample:eu,BaseWidget:Gf,default:e1},Symbol.toStringTag,{value:"Module"}));export{ui as A,Jm as B,vf as C,Yp as D,v as E,B as F,A1 as G,C1 as H,J as I,ym as J,O1 as K,_r as L,fo as N,wc as P,Kt as S,U as T,vt as a,N as b,M1 as c,ut as d,Ac as e,Pm as f,S1 as g,k1 as h,D1 as i,y0 as j,w1 as k,T1 as l,_t as m,mi as n,Ye as o,x1 as p,en as q,V as r,dm as s,b as t,Ht as u,hs as v,kc as w,Ne as x,Bm as y,Cc as z};
//# sourceMappingURL=Index-Cvh-nP3Y.js.map
