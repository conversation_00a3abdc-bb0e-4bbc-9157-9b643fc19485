"""
文档生成模块
用于将小说内容和插图保存为Word文档
"""

import os
import re
import json
from typing import List, Dict, Any, Optional
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

def save_novel_to_word(novel_dir: str, output_path: Optional[str] = None) -> str:
    """
    将小说内容和插图保存为Word文档

    参数:
        novel_dir: 小说目录
        output_path: 输出路径，如果为None则使用小说目录下的novel.docx

    返回:
        保存的文件路径
    """
    print(f"开始生成Word文档，小说目录: {novel_dir}")

    # 读取小说状态
    state_file = os.path.join(novel_dir, "novel_state.json")
    if not os.path.exists(state_file):
        raise FileNotFoundError(f"找不到小说状态文件: {state_file}")

    with open(state_file, "r", encoding="utf-8") as f:
        novel_state = json.load(f)

    # 获取小说信息
    title = novel_state.get("title", "未知标题")
    genre = novel_state.get("genre", "未知类型")
    style = novel_state.get("style", "未知风格")
    scene = novel_state.get("scene", "未知场景")
    characters = novel_state.get("characters", "未知角色")

    # 检查是否有已完成的章节
    chapters_status = novel_state.get("chapters_status", {})
    completed_chapters = [int(ch_num) for ch_num, status in chapters_status.items() if status == "completed"]
    print(f"已完成章节: {completed_chapters}")

    # 确定输出路径
    if output_path is None:
        output_path = os.path.join(novel_dir, f"{title}.docx")

    # 创建Word文档
    doc = Document()

    # 设置文档标题
    title_paragraph = doc.add_paragraph()
    title_run = title_paragraph.add_run(f"《{title}》")
    title_run.font.size = Pt(24)
    title_run.bold = True
    title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 添加小说信息
    info_paragraph = doc.add_paragraph()
    info_paragraph.add_run(f"类型: {genre}\n").bold = True
    info_paragraph.add_run(f"风格: {style}\n").bold = True
    info_paragraph.add_run(f"场景: {scene}\n").bold = True
    info_paragraph.add_run(f"角色: {characters}").bold = True

    # 添加分隔线
    doc.add_paragraph("=" * 50)

    # 获取章节文件列表
    chapter_files = []
    chapters_dir = os.path.join(novel_dir, "chapters")
    if os.path.exists(chapters_dir):
        print(f"扫描章节目录: {chapters_dir}")
        for file in os.listdir(chapters_dir):
            if file.startswith("chapter_") and file.endswith("_final.txt"):
                try:
                    chapter_num = int(file.split("_")[1])
                    chapter_files.append((chapter_num, os.path.join(chapters_dir, file)))
                    print(f"找到章节文件: {file}")
                except Exception as e:
                    print(f"处理文件 {file} 时出错: {e}")

    # 按章节号排序
    chapter_files.sort()
    print(f"共找到 {len(chapter_files)} 个章节文件")

    # 确保插图目录存在
    illustrations_dir = os.path.join(novel_dir, "illustrations")
    os.makedirs(illustrations_dir, exist_ok=True)

    # 检查插图文件
    illustration_files = []
    if os.path.exists(illustrations_dir):
        for file in os.listdir(illustrations_dir):
            if file.startswith("chapter_") and (file.endswith(".png") or file.endswith(".jpg")):
                try:
                    chapter_num = int(file.split("_")[1].split(".")[0])
                    illustration_files.append((chapter_num, os.path.join(illustrations_dir, file)))
                    print(f"找到插图文件: {file}")
                except Exception as e:
                    print(f"处理插图文件 {file} 时出错: {e}")

    # 创建插图映射
    illustration_map = {chapter_num: file_path for chapter_num, file_path in illustration_files}
    print(f"共找到 {len(illustration_map)} 个插图文件")

    # 添加章节内容
    for chapter_num, chapter_file in chapter_files:
        # 读取章节内容
        with open(chapter_file, "r", encoding="utf-8") as f:
            chapter_content = f.read()

        # 提取章节标题
        title_match = re.match(r'^第\d+章[：:](.*?)$', chapter_content.split('\n')[0])
        if title_match:
            chapter_title = f"第{chapter_num}章：{title_match.group(1).strip()}"
        else:
            chapter_title = f"第{chapter_num}章"

        # 添加章节标题
        heading = doc.add_heading(level=1)
        heading.add_run(chapter_title).bold = True

        # 查找对应的插图
        illustrations_dir = os.path.join(novel_dir, "illustrations")
        illustration_file = os.path.join(illustrations_dir, f"chapter_{chapter_num}.png")
        if os.path.exists(illustration_file):
            # 添加插图
            doc.add_picture(illustration_file, width=Inches(6))

            # 添加图片说明
            caption = doc.add_paragraph(f"《{title}》第{chapter_num}章插图")
            caption.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加章节内容
        # 跳过第一行（标题）
        content_lines = chapter_content.split('\n')[1:]
        content = '\n'.join(content_lines)

        # 按段落添加内容
        for paragraph_text in content.split('\n\n'):
            if paragraph_text.strip():
                doc.add_paragraph(paragraph_text.strip())

        # 添加分隔线
        if chapter_num < len(chapter_files):
            doc.add_paragraph("=" * 50)

    # 保存文档
    doc.save(output_path)

    return output_path

if __name__ == "__main__":
    # 测试保存小说为Word文档
    import sys
    if len(sys.argv) > 1:
        novel_dir = sys.argv[1]
        output_path = save_novel_to_word(novel_dir)
        print(f"小说已保存为Word文档: {output_path}")
    else:
        print("请提供小说目录路径")
