"""
润色相关提示词管理模块
集中管理所有与润色相关的提示词
"""

class PolishPrompts:
    """润色相关提示词"""
    
    @staticmethod
    def polish_content_prompt(content):
        """生成内容润色提示词"""
        return f"""
请对以下小说章节内容进行润色，提升文学性和可读性，但不要改变原有的情节和人物设定。

原始内容：
{content}

润色要求：
1. 改进语言表达，使其更加生动、优美
2. 增强描写的细节和感染力
3. 优化对话，使其更加自然、生动
4. 调整段落结构，提高阅读流畅度
5. 修正可能存在的语法错误和表达不当
6. 保持原有的情节、人物和核心内容不变

请直接给出润色后的完整内容，不需要解释或标注修改之处。
"""

    @staticmethod
    def expand_content_prompt(content):
        """生成内容扩写提示词"""
        return f"""
请对以下小说章节内容进行扩写，使其更加丰富、详实，但不要改变原有的情节和人物设定。

原始内容：
{content}

扩写要求：
1. 增加场景描写的细节，使读者能够更好地想象场景
2. 丰富人物的心理活动和情感变化
3. 适当增加对话内容，展现人物性格和关系
4. 在关键情节处增加更多细节描写
5. 保持原有的情节发展和人物设定不变
6. 扩写后的内容应比原内容增加约30%的篇幅

请直接给出扩写后的完整内容，不需要解释或标注修改之处。
"""
