"""
小说管理器模块
负责管理整个小说创作过程
"""

import os
import json
import re
from typing import Dict, List, Any, Optional, Union, Tuple, Callable, cast
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt, Mm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.section import WD_ORIENT
import traceback

# 导入记忆管理、一致性检查和叙事控制模块
try:
    from src.memory.memory_integration import (
        get_memory_manager, enhance_prompt_with_memory,
        update_memory_from_chapter, add_segment_to_memory,
        clear_short_term_memory, check_consistency as memory_check_consistency
    )
    from src.consistency.consistency_checker import (
        get_consistency_checker, check_consistency as consistency_check_consistency,
        save_consistency_log
    )
    from src.narrative.narrative_controller import (
        get_narrative_controller, generate_branch_options,
        select_optimal_path, generate_next_chapter_outline
    )
    ADVANCED_FEATURES_ENABLED = True
    print("高级功能已在小说管理器中启用")
except ImportError as e:
    print(f"警告: 小说管理器无法导入高级功能模块: {e}")
    ADVANCED_FEATURES_ENABLED = False

try:
    from src.core.agent import Agent, AGENT_REGISTRY
except ImportError:
    # 如果导入失败，创建简单的Agent类
    class Agent:
        def __init__(self, name, model=None):
            self.name = name
            self.model = model

    # 创建简单的AGENT_REGISTRY类
    class AgentRegistry:
        def __init__(self):
            self.agents = {}

        def register(self, agent):
            self.agents[agent.name] = agent
            return agent

        def get_agent(self, name):
            return self.agents.get(name)

    AGENT_REGISTRY = AgentRegistry()

try:
    from src.core.prompt_manager import PromptManager
except ImportError:
    # 如果导入失败，创建简单的PromptManager类
    class PromptManager:
        def get_prompt(self, prompt_name, **kwargs):
            return f"提示词 {prompt_name} 示例内容"

try:
    from src.core.model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES
except ImportError:
    # 如果导入失败，创建简单的配置
    AI_MODEL_CONFIG = {
        "agent_models": {},
        "default_model": "glm-4-flash"
    }
    DEFAULT_MODEL_NAMES = {"glm": "glm-4-flash"}

# 初始化提示词管理器
prompt_manager = PromptManager()

class Chapter:
    """小说章节类"""

    def __init__(self, number: int, title: str = "", content: str = ""):
        self.number = number
        self.title = title or f"第{number}章"
        self.content = content
        self.status = "pending"  # pending, in_progress, completed
        self.illustrations = []  # 插图路径列表
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "number": self.number,
            "title": self.title,
            "status": self.status,
            "illustrations": self.illustrations,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Chapter':
        """从字典创建章节"""
        chapter = cls(
            number=data.get("number", 1),
            title=data.get("title", ""),
            content=data.get("content", "")
        )
        chapter.status = data.get("status", "pending")
        chapter.illustrations = data.get("illustrations", [])
        chapter.created_at = data.get("created_at", datetime.now().isoformat())
        chapter.updated_at = data.get("updated_at", chapter.created_at)
        return chapter

class Novel:
    """小说类"""

    def __init__(self, title: str, author: str = "AI作家", genre: str = "", style: str = "", outline: str = ""):
        self.title = title
        self.author = author
        self.genre = genre
        self.style = style
        self.outline = outline
        self.chapters = []  # 章节列表
        self.scene = ""  # 场景
        self.characters = ""  # 角色
        self.total_chapters = 1  # 总章节数
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at

        # 创作状态信息
        self.status = "in_progress"  # 小说总体状态: in_progress, completed, paused
        self.current_chapter = 1  # 当前正在创作的章节
        self.completed_chapters = 0  # 已完成的章节数
        self.last_writing_time = self.created_at  # 最后一次写作时间
        self.last_chapter_completed = 0  # 最后完成的章节编号
        self.chapter_status = {}  # 各章节状态记录，键为章节编号，值为状态(not_started, in_progress, completed, incomplete)

        # 大纲信息
        self.chapter_outlines = {}  # 各章节大纲，键为章节编号，值为大纲内容
        self.main_plot = ""  # 主要情节
        self.sub_plots = []  # 子情节列表
        self.outline_updated_at = self.created_at  # 大纲最后更新时间
        self.max_chapter_in_outline = 0  # 大纲中包含的最大章节编号

        # 创作过程信息
        self.writing_progress = 0.0  # 写作进度，0-1之间的浮点数
        self.estimated_completion_time = ""  # 预计完成时间
        self.writing_speed = 0  # 写作速度，字符/小时
        self.total_word_count = 0  # 总字数
        self.chapter_word_counts = {}  # 各章节字数，键为章节编号，值为字数
        self.average_chapter_length = 0  # 平均章节长度
        self.min_chapter_length = 3000  # 最小章节长度，用于判断章节是否完整

        # 版本控制信息
        self.version = "1.0"  # 当前版本
        self.revision_history = []  # 修订历史

        # 其他元数据
        self.tags = []  # 标签列表
        self.notes = ""  # 写作笔记
        self.target_audience = ""  # 目标读者群体

        # 文件路径信息
        self.novel_dir = ""  # 小说目录
        self.outline_file = ""  # 大纲文件路径
        self.chapter_files = {}  # 各章节文件路径，键为章节编号，值为文件路径

        # 进度跟踪信息
        self.generation_attempts = {}  # 各章节生成尝试次数，键为章节编号，值为尝试次数
        self.last_error = ""  # 最后一次错误信息

    def is_chapter_complete(self, chapter: Chapter) -> bool:
        """
        检查章节是否完整

        参数:
            chapter: 要检查的章节对象

        返回:
            章节是否完整
        """
        # 检查章节是否有内容
        if not hasattr(chapter, 'content') or not chapter.content:
            return False

        # 检查章节内容长度是否足够
        if len(chapter.content) < self.min_chapter_length:
            return False

        # 检查章节内容是否包含错误信息
        error_indicators = [
            "API请求失败",
            "调用API超时",
            "调用API出错",
            "生成内容失败",
            "Read timed out",
            "HTTPSConnectionPool",
            "Connection refused",
            "API响应中没有生成内容"
        ]

        if any(indicator in chapter.content for indicator in error_indicators):
            return False

        # 检查章节内容是否包含必要的结构
        # 例如，检查是否有段落分隔符
        if '\n\n' not in chapter.content:
            return False

        # 检查章节内容是否有足够的段落
        paragraphs = chapter.content.split('\n\n')
        if len(paragraphs) < 10:  # 假设一个完整的章节至少有10个段落
            return False

        return True

    def add_chapter(self, chapter: Chapter) -> None:
        """
        添加章节并更新相关统计信息

        参数:
            chapter: 要添加的章节对象
        """
        # 检查是否已存在相同编号的章节
        for existing_chapter in self.chapters:
            if existing_chapter.number == chapter.number:
                print(f"警告: 章节 {chapter.number} 已存在，将覆盖原有章节")
                # 移除原有章节
                self.chapters.remove(existing_chapter)
                break

        # 添加新章节
        self.chapters.append(chapter)

        # 检查章节是否完整
        is_complete = self.is_chapter_complete(chapter)

        # 更新章节状态
        if hasattr(chapter, 'status'):
            # 如果状态是已完成，但内容不完整，则标记为不完整
            if chapter.status == "completed" and not is_complete:
                chapter.status = "incomplete"
                print(f"章节 {chapter.number} 内容不完整，标记为不完整")
            self.chapter_status[chapter.number] = chapter.status
        else:
            # 根据完整性检查结果设置状态
            if is_complete:
                self.chapter_status[chapter.number] = "completed"  # 完整的章节
            else:
                self.chapter_status[chapter.number] = "incomplete"  # 不完整的章节

        # 更新字数统计
        if hasattr(chapter, 'content') and chapter.content:
            word_count = len(chapter.content)
            self.chapter_word_counts[chapter.number] = word_count

            # 更新总字数
            self.total_word_count = sum(self.chapter_word_counts.values())

            # 更新平均章节长度
            self.average_chapter_length = self.total_word_count / len(self.chapters)

        # 更新已完成章节数
        self.completed_chapters = sum(1 for ch in self.chapters if getattr(ch, 'status', '') == 'completed')

        # 更新最后完成的章节编号
        if getattr(chapter, 'status', '') == 'completed':
            self.last_chapter_completed = max(self.last_chapter_completed, chapter.number)
        elif getattr(chapter, 'status', '') == 'in_progress':
            # 如果章节正在进行中，记录当前进度
            print(f"章节 {chapter.number} 正在进行中，更新进度信息")

        # 更新写作进度
        if self.total_chapters > 0:
            self.writing_progress = self.completed_chapters / self.total_chapters

        # 更新当前章节
        self.current_chapter = self.last_chapter_completed + 1

        # 更新生成尝试次数
        if chapter.number not in self.generation_attempts:
            self.generation_attempts[chapter.number] = 1
        else:
            self.generation_attempts[chapter.number] += 1

        # 更新最后写作时间
        self.last_writing_time = datetime.now().isoformat()
        self.updated_at = self.last_writing_time

    def get_chapter(self, number: int) -> Optional[Chapter]:
        """获取章节"""
        for chapter in self.chapters:
            if chapter.number == number:
                return chapter
        return None

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            # 基本信息
            "title": self.title,
            "author": self.author,
            "genre": self.genre,
            "style": self.style,
            "outline": self.outline,
            "scene": self.scene,
            "characters": self.characters,
            "total_chapters": self.total_chapters,
            "chapters": [chapter.to_dict() for chapter in self.chapters],
            "created_at": self.created_at,
            "updated_at": self.updated_at,

            # 创作状态信息
            "status": self.status,
            "current_chapter": self.current_chapter,
            "completed_chapters": self.completed_chapters,
            "last_writing_time": self.last_writing_time,
            "last_chapter_completed": self.last_chapter_completed,
            "chapter_status": self.chapter_status,

            # 大纲信息
            "chapter_outlines": self.chapter_outlines,
            "main_plot": self.main_plot,
            "sub_plots": self.sub_plots,
            "outline_updated_at": self.outline_updated_at,
            "max_chapter_in_outline": self.max_chapter_in_outline,

            # 创作过程信息
            "writing_progress": self.writing_progress,
            "estimated_completion_time": self.estimated_completion_time,
            "writing_speed": self.writing_speed,
            "total_word_count": self.total_word_count,
            "chapter_word_counts": self.chapter_word_counts,
            "average_chapter_length": self.average_chapter_length,
            "min_chapter_length": self.min_chapter_length,

            # 版本控制信息
            "version": self.version,
            "revision_history": self.revision_history,

            # 其他元数据
            "tags": self.tags,
            "notes": self.notes,
            "target_audience": self.target_audience,

            # 文件路径信息
            "novel_dir": self.novel_dir,
            "outline_file": self.outline_file,
            "chapter_files": self.chapter_files,

            # 进度跟踪信息
            "generation_attempts": self.generation_attempts,
            "last_error": self.last_error
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Novel':
        """从字典创建小说"""
        novel = cls(
            title=data.get("title", ""),
            author=data.get("author", "AI作家"),
            genre=data.get("genre", ""),
            style=data.get("style", ""),
            outline=data.get("outline", "")
        )
        novel.scene = data.get("scene", "")
        novel.characters = data.get("characters", "")
        novel.total_chapters = data.get("total_chapters", 1)
        novel.created_at = data.get("created_at", datetime.now().isoformat())
        novel.updated_at = data.get("updated_at", novel.created_at)

        # 加载创作状态信息
        novel.status = data.get("status", "in_progress")
        novel.current_chapter = data.get("current_chapter", 1)
        novel.completed_chapters = data.get("completed_chapters", 0)
        novel.last_writing_time = data.get("last_writing_time", novel.created_at)
        novel.last_chapter_completed = data.get("last_chapter_completed", 0)
        novel.chapter_status = data.get("chapter_status", {})

        # 加载大纲信息
        novel.chapter_outlines = data.get("chapter_outlines", {})
        novel.main_plot = data.get("main_plot", "")
        novel.sub_plots = data.get("sub_plots", [])
        novel.outline_updated_at = data.get("outline_updated_at", novel.created_at)
        novel.max_chapter_in_outline = data.get("max_chapter_in_outline", 0)

        # 加载创作过程信息
        novel.writing_progress = data.get("writing_progress", 0.0)
        novel.estimated_completion_time = data.get("estimated_completion_time", "")
        novel.writing_speed = data.get("writing_speed", 0)
        novel.total_word_count = data.get("total_word_count", 0)
        novel.chapter_word_counts = data.get("chapter_word_counts", {})
        novel.average_chapter_length = data.get("average_chapter_length", 0)
        novel.min_chapter_length = data.get("min_chapter_length", 3000)

        # 加载版本控制信息
        novel.version = data.get("version", "1.0")
        novel.revision_history = data.get("revision_history", [])

        # 加载其他元数据
        novel.tags = data.get("tags", [])
        novel.notes = data.get("notes", "")
        novel.target_audience = data.get("target_audience", "")

        # 加载文件路径信息
        novel.novel_dir = data.get("novel_dir", "")
        novel.outline_file = data.get("outline_file", "")
        novel.chapter_files = data.get("chapter_files", {})

        # 加载进度跟踪信息
        novel.generation_attempts = data.get("generation_attempts", {})
        novel.last_error = data.get("last_error", "")

        # 添加章节
        for chapter_data in data.get("chapters", []):
            chapter = Chapter.from_dict(chapter_data)
            novel.add_chapter(chapter)

        return novel

    def save_as_document(self, output_path: str, include_illustrations: bool = True) -> str:
        """
        将小说保存为Word文档，只保存最终版本的内容和插图

        参数:
            output_path: 输出路径
            include_illustrations: 是否包含插图

        返回:
            保存的文件路径
        """
        try:
            # 创建文档
            doc = Document()

            # 添加标题
            doc.add_heading(self.title, 0)

            # 添加作者
            doc.add_paragraph(f"作者: {self.author}")

            # 添加创建时间
            created_at = datetime.fromisoformat(self.created_at)
            doc.add_paragraph(f"创建时间: {created_at.strftime('%Y-%m-%d %H:%M:%S')}")

            # 添加类型和风格
            if self.genre or self.style:
                doc.add_paragraph(f"类型: {self.genre}, 风格: {self.style}")

            # 添加分隔线
            doc.add_paragraph("=" * 50)

            # 只添加已完成的章节
            completed_chapters = [ch for ch in self.chapters if getattr(ch, 'status', '') == 'completed']

            # 按章节编号排序
            completed_chapters.sort(key=lambda x: x.number)

            # 如果没有已完成的章节，显示提示
            if not completed_chapters:
                doc.add_paragraph("小说正在创作中，暂时没有已完成的章节。")
            else:
                # 添加已完成的章节
                for chapter in completed_chapters:
                    # 检查章节是否完整
                    if not self.is_chapter_complete(chapter):
                        print(f"警告: 章节 {chapter.number} 不完整，跳过")
                        continue

                    # 添加章节标题
                    doc.add_heading(f"第{chapter.number}章 {chapter.title}", 1)

                    # 添加章节内容
                    if hasattr(chapter, 'content') and chapter.content:
                        # 按段落分割内容
                        paragraphs = chapter.content.split('\n\n')
                        for para in paragraphs:
                            if para.strip():
                                doc.add_paragraph(para.strip())

                    # 添加插图 - 确保插图在章节末尾
                    if include_illustrations and hasattr(chapter, 'illustrations') and chapter.illustrations:
                        # 添加一个空行作为分隔
                        doc.add_paragraph("")

                        for img_path in chapter.illustrations:
                            if img_path and os.path.exists(img_path):
                                try:
                                    # 添加图片并设置为横向宽度
                                    # 添加新的节以设置横向页面
                                    section = doc.add_section()
                                    section.orientation = WD_ORIENT.LANDSCAPE
                                    section.page_width, section.page_height = section.page_height, section.page_width

                                    # 设置页面边距
                                    section.left_margin = Mm(25.4)
                                    section.right_margin = Mm(25.4)
                                    section.top_margin = Mm(25.4)
                                    section.bottom_margin = Mm(25.4)

                                    # 添加图片并设置适当的宽度，横向模式下可以设置更大的宽度
                                    doc.add_picture(img_path, width=Inches(9))

                                    # 添加图片说明
                                    caption = doc.add_paragraph(f"图：第{chapter.number}章风景插图", style="Caption")
                                    from docx.enum.text import WD_ALIGN_PARAGRAPH
                                    caption.alignment = WD_ALIGN_PARAGRAPH.CENTER

                                    # 添加新的节以还原为纵向页面
                                    section = doc.add_section()
                                    section.orientation = WD_ORIENT.PORTRAIT
                                    section.page_height, section.page_width = section.page_width, section.page_height

                                    # 设置页面边距
                                    section.left_margin = Mm(25.4)
                                    section.right_margin = Mm(25.4)
                                    section.top_margin = Mm(25.4)
                                    section.bottom_margin = Mm(25.4)
                                except Exception as img_error:
                                    print(f"添加插图时出错: {img_error}")

            # 保存文档
            try:
                # 确保输出目录存在
                output_dir = os.path.dirname(output_path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir, exist_ok=True)
                    print(f"创建输出目录: {output_dir}")

                # 尝试保存文档
                doc.save(output_path)
                print(f"小说已成功保存为Word文档: {output_path}")

                # 验证文件是否存在
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"Word文档大小: {file_size} 字节")
                else:
                    print(f"警告: 文档保存后无法找到: {output_path}")
            except Exception as save_error:
                print(f"保存Word文档时出错: {save_error}")
                traceback.print_exc()
                return ""

            return output_path
        except Exception as e:
            print(f"保存Word文档时出错: {e}")
            traceback.print_exc()
            return ""

class NovelManager:
    """
    小说管理器，负责管理整个小说创作过程
    """

    def __init__(self, name: str = "NovelManager"):
        """初始化小说管理器"""
        self.name = name
        self.prompt_manager = prompt_manager
        self.novels = {}  # 小说字典，键为小说标题，值为Novel对象

    def create_novel(self, title: str, author: str = "AI作家", genre: str = "", style: str = "",
                    outline: str = "", total_chapters: int = 1, output_dir: str = "output",
                    scene: str = "", characters: str = "") -> Novel:
        """
        创建新小说

        参数:
            title: 小说标题
            author: 作者
            genre: 类型
            style: 风格
            outline: 大纲
            total_chapters: 总章节数
            output_dir: 输出目录
            scene: 场景
            characters: 角色

        返回:
            创建的小说对象
        """
        # 创建小说对象
        novel = Novel(title, author, genre, style, outline)
        novel.scene = scene
        novel.characters = characters
        novel.total_chapters = total_chapters

        # 创建输出目录
        # 检查是否已经在小说目录中
        if os.path.basename(output_dir) == title or os.path.basename(os.path.dirname(output_dir)) == title:
            # 如果已经在小说目录中，直接使用该目录
            novel_dir = output_dir
            print(f"继续在现有目录中写作: {novel_dir}")
        else:
            # 否则创建新目录
            novel_dir = os.path.join(output_dir, title)
            os.makedirs(novel_dir, exist_ok=True)
            print(f"创建新目录: {novel_dir}")

        # 设置小说目录和文件路径信息
        novel.novel_dir = novel_dir
        novel.outline_file = os.path.join(novel_dir, "novel_outline.txt")

        # 保存小说信息
        novel_info_path = os.path.join(novel_dir, "novel_info.json")
        with open(novel_info_path, "w", encoding="utf-8") as f:
            json.dump(novel.to_dict(), f, ensure_ascii=False, indent=2)
        print(f"已创建小说信息文件: {novel_info_path}")

        # 添加到小说字典
        self.novels[title] = novel

        return novel

    def load_novel(self, novel_dir: str) -> Optional[Novel]:
        """
        加载小说

        参数:
            novel_dir: 小说目录

        返回:
            加载的小说对象，如果加载失败则返回None
        """
        try:
            # 检查小说信息文件
            novel_info_path = os.path.join(novel_dir, "novel_info.json")
            if not os.path.exists(novel_info_path):
                print(f"小说信息文件不存在: {novel_info_path}")
                return None

            # 加载小说信息
            with open(novel_info_path, "r", encoding="utf-8") as f:
                novel_data = json.load(f)

            # 创建小说对象
            novel = Novel.from_dict(novel_data)

            # 设置小说目录
            novel.novel_dir = novel_dir

            # 检查并加载大纲文件
            outline_file = os.path.join(novel_dir, "novel_outline.txt")
            chapter_outline_file = os.path.join(novel_dir, "chapter_outline.txt")

            # 优先使用novel_outline.txt，如果不存在则使用chapter_outline.txt
            if os.path.exists(outline_file):
                with open(outline_file, "r", encoding="utf-8") as f:
                    novel.outline = f.read()
                novel.outline_file = outline_file
                print(f"从{outline_file}加载大纲成功")
            elif os.path.exists(chapter_outline_file):
                with open(chapter_outline_file, "r", encoding="utf-8") as f:
                    novel.outline = f.read()
                novel.outline_file = chapter_outline_file
                print(f"从{chapter_outline_file}加载大纲成功")
            else:
                print(f"警告: 未找到大纲文件")

            # 分析大纲中的章节信息
            if novel.outline:
                import re
                # 查找大纲中的所有章节标记
                chapter_headers = re.findall(r'## 第(\d+)章', novel.outline)
                if chapter_headers:
                    # 更新大纲中的最大章节编号
                    novel.max_chapter_in_outline = max(int(num) for num in chapter_headers)
                    print(f"大纲中包含的最大章节编号为: {novel.max_chapter_in_outline}")

                    # 提取各章节大纲并保存到chapter_outlines字典
                    for chapter_num in [int(num) for num in chapter_headers]:
                        chapter_pattern = rf"## 第{chapter_num}章[\s\S]*?(?=## 第{chapter_num+1}章|$)"
                        chapter_match = re.search(chapter_pattern, novel.outline)
                        if chapter_match:
                            novel.chapter_outlines[chapter_num] = chapter_match.group(0).strip()

                    print(f"已提取{len(novel.chapter_outlines)}个章节的大纲")

            # 加载章节内容和状态
            total_word_count = 0
            chapter_word_counts = {}
            chapter_files = {}

            for chapter in novel.chapters:
                # 尝试加载章节内容
                chapter_path = os.path.join(novel_dir, f"chapter_{chapter.number}_final.txt")
                if os.path.exists(chapter_path):
                    with open(chapter_path, "r", encoding="utf-8") as f:
                        chapter.content = f.read()

                    # 记录章节文件路径
                    chapter_files[chapter.number] = chapter_path

                    # 计算字数
                    word_count = len(chapter.content)
                    chapter_word_counts[chapter.number] = word_count
                    total_word_count += word_count

                    # 更新章节状态
                    novel.chapter_status[chapter.number] = "completed"

                    # 更新最后完成的章节编号
                    novel.last_chapter_completed = max(novel.last_chapter_completed, chapter.number)

            # 更新小说的字数统计信息
            novel.total_word_count = total_word_count
            novel.chapter_word_counts = chapter_word_counts
            novel.chapter_files = chapter_files

            # 计算平均章节长度
            if novel.chapters:
                novel.average_chapter_length = total_word_count / len(novel.chapters)

            # 计算写作进度
            if novel.total_chapters > 0:
                novel.writing_progress = novel.completed_chapters / novel.total_chapters
                print(f"当前写作进度: {novel.writing_progress:.2%}")

            # 更新当前章节
            novel.current_chapter = novel.last_chapter_completed + 1
            print(f"当前章节: {novel.current_chapter}")

            # 添加到小说字典
            self.novels[novel.title] = novel

            print(f"小说《{novel.title}》加载成功，共{len(novel.chapters)}章，{novel.total_word_count}字")
            return novel
        except Exception as e:
            print(f"加载小说时出错: {e}")
            traceback.print_exc()
            return None

    def save_novel(self, novel: Novel, output_dir: str) -> bool:
        """
        保存小说

        参数:
            novel: 小说对象
            output_dir: 输出目录

        返回:
            是否保存成功
        """
        try:
            # 创建输出目录
            # 检查是否已经在小说目录中
            if os.path.basename(output_dir) == novel.title or os.path.basename(os.path.dirname(output_dir)) == novel.title:
                # 如果已经在小说目录中，直接使用该目录
                novel_dir = output_dir
                print(f"继续在现有目录中保存: {novel_dir}")
            else:
                # 否则创建新目录
                novel_dir = os.path.join(output_dir, novel.title)
                os.makedirs(novel_dir, exist_ok=True)
                print(f"创建新目录: {novel_dir}")

            # 更新小说目录和文件路径信息
            novel.novel_dir = novel_dir
            novel.outline_file = os.path.join(novel_dir, "novel_outline.txt")

            # 更新小说状态信息
            novel.updated_at = datetime.now().isoformat()

            # 保存小说信息
            novel_info_path = os.path.join(novel_dir, "novel_info.json")

            # 先检查是否存在现有的novel_info.json文件
            if os.path.exists(novel_info_path):
                try:
                    # 读取现有文件，确保我们不会丢失任何信息
                    with open(novel_info_path, "r", encoding="utf-8") as f:
                        existing_data = json.load(f)

                    # 确保我们保留last_chapter_completed字段
                    if "last_chapter_completed" in existing_data and existing_data["last_chapter_completed"] > novel.last_chapter_completed:
                        print(f"保留现有的last_chapter_completed值: {existing_data['last_chapter_completed']}")
                        novel.last_chapter_completed = existing_data["last_chapter_completed"]
                except Exception as e:
                    print(f"读取现有novel_info.json文件时出错: {e}")

            # 保存更新后的小说信息
            with open(novel_info_path, "w", encoding="utf-8") as f:
                json.dump(novel.to_dict(), f, ensure_ascii=False, indent=2)
            print(f"已更新小说信息文件: {novel_info_path}")

            # 保存章节内容
            for chapter in novel.chapters:
                if hasattr(chapter, 'content') and chapter.content:
                    chapter_path = os.path.join(novel_dir, f"chapter_{chapter.number}_final.txt")
                    with open(chapter_path, "w", encoding="utf-8") as f:
                        f.write(chapter.content)

            return True
        except Exception as e:
            print(f"保存小说时出错: {e}")
            traceback.print_exc()
            return False

    def generate_chapter(self, novel: Novel, chapter_number: int, output_dir: str,
                        illustration_style: str = "写实风格") -> Tuple[bool, str]:
        """
        生成章节

        参数:
            novel: 小说对象
            chapter_number: 章节编号
            output_dir: 输出目录
            illustration_style: 插图风格

        返回:
            (成功标志, 结果信息)
        """
        try:
            # 检查章节是否已存在
            chapter = novel.get_chapter(chapter_number)
            if not chapter:
                # 创建新章节
                chapter = Chapter(chapter_number)
                novel.add_chapter(chapter)

            # 设置章节状态为进行中
            chapter.status = "in_progress"

            # 保存小说状态
            self.save_novel(novel, output_dir)

            # 创建章节上下文
            chapter_context = {
                "title": novel.title,
                "genre": novel.genre,
                "style": novel.style,
                "chapter_number": chapter_number,
                "total_chapters": len(novel.chapters),
                "chapter_title": chapter.title,
                "novel_outline": novel.outline,
                "chapter_outline": "",  # 暂时为空
                "previous_chapters": "",  # 暂时为空
                "previous_chapter_content": "",  # 暂时为空
                "output_dir": output_dir,
                "chapter_status": chapter.status
            }

            # 如果不是第一章，获取前一章内容
            if chapter_number > 1:
                prev_chapter = novel.get_chapter(chapter_number - 1)
                if prev_chapter and hasattr(prev_chapter, 'content') and prev_chapter.content:
                    chapter_context["previous_chapter_content"] = prev_chapter.content

            # 创建写作流程
            from src.agents.writing_pipeline import create_writing_pipeline, run_pipeline
            pipeline = create_writing_pipeline(illustration_style=illustration_style)

            # 创建写作智能体（用于高级功能）
            from src.agents.writing_agent import WritingAgent
            writing_agent = WritingAgent("writing")

            # 如果启用了高级功能，初始化记忆和叙事控制
            if 'ADVANCED_FEATURES_ENABLED' in globals() and ADVANCED_FEATURES_ENABLED:
                try:
                    # 初始化记忆管理
                    get_memory_manager(output_dir)
                    # 清空短期记忆，准备新章节
                    clear_short_term_memory(output_dir)

                    # 如果不是第一章，使用叙事控制生成章节大纲
                    if chapter_number > 1:
                        # 获取前一章内容
                        prev_chapter_file = os.path.join(output_dir, f"chapter_{chapter_number-1}_final.txt")
                        if os.path.exists(prev_chapter_file):
                            with open(prev_chapter_file, "r", encoding="utf-8") as f:
                                prev_chapter_content = f.read()

                            # 生成分支选项
                            print("正在生成叙事分支选项...")
                            try:
                                branches = generate_branch_options(
                                    chapter_number-1,
                                    prev_chapter_content,
                                    output_dir,
                                    writing_agent.call_llm
                                )

                                if branches:
                                    print(f"生成了 {len(branches)} 个叙事分支选项")

                                    # 选择最佳路径
                                    print("正在选择最佳叙事路径...")
                                    selected_branch = select_optimal_path(
                                        chapter_number-1,
                                        novel.outline,
                                        output_dir,
                                        writing_agent.call_llm
                                    )

                                    if selected_branch:
                                        print(f"选择了叙事分支: {selected_branch.get('title', '')}")

                                        # 生成章节大纲
                                        chapter_outline = generate_next_chapter_outline(
                                            chapter_number-1,
                                            novel.outline,
                                            output_dir,
                                            writing_agent.call_llm
                                        )

                                        # 更新章节上下文
                                        chapter_context["chapter_outline"] = chapter_outline
                                        print("使用叙事控制生成的章节大纲")
                            except Exception as narrative_error:
                                print(f"使用叙事控制时出错: {narrative_error}")
                                traceback.print_exc()
                except Exception as e:
                    print(f"初始化高级功能时出错: {e}")
                    traceback.print_exc()

            # 运行写作流程
            result = run_pipeline(pipeline, chapter_context)

            # 检查结果
            if isinstance(result, tuple) and len(result) == 2:
                success, content = result
                if success:
                    # 更新章节内容
                    chapter.content = content

                    # 检查章节是否完整
                    if novel.is_chapter_complete(chapter):
                        chapter.status = "completed"
                        print(f"章节 {chapter_number} 生成完成，内容完整")
                    else:
                        chapter.status = "incomplete"
                        print(f"章节 {chapter_number} 生成完成，但内容不完整，标记为不完整")

                        # 记录错误信息
                        novel.last_error = f"章节 {chapter_number} 内容不完整"

                        # 更新生成尝试次数
                        if chapter_number not in novel.generation_attempts:
                            novel.generation_attempts[chapter_number] = 1
                        else:
                            novel.generation_attempts[chapter_number] += 1

                    # 更新章节文件路径
                    chapter_path = os.path.join(output_dir, f"chapter_{chapter_number}_final.txt")
                    novel.chapter_files[chapter_number] = chapter_path

                    # 更新章节字数
                    word_count = len(content)
                    novel.chapter_word_counts[chapter_number] = word_count
                    novel.total_word_count = sum(novel.chapter_word_counts.values())

                    # 更新平均章节长度
                    if novel.chapters:
                        novel.average_chapter_length = novel.total_word_count / len(novel.chapters)

                    # 更新最后完成的章节编号（只有完整的章节才计入）
                    if chapter.status == "completed":
                        novel.last_chapter_completed = max(novel.last_chapter_completed, chapter_number)

                    # 更新已完成章节数
                    novel.completed_chapters = sum(1 for ch in novel.chapters if getattr(ch, 'status', '') == 'completed')

                    # 更新写作进度
                    if novel.total_chapters > 0:
                        novel.writing_progress = novel.completed_chapters / novel.total_chapters

                    # 保存小说状态
                    self.save_novel(novel, output_dir)

                    return True, f"章节 {chapter_number} 生成成功"
                else:
                    # 设置章节状态为待处理
                    chapter.status = "pending"

                    # 保存小说状态
                    self.save_novel(novel, output_dir)

                    return False, f"章节 {chapter_number} 生成失败: {content}"
            else:
                # 设置章节状态为待处理
                chapter.status = "pending"

                # 保存小说状态
                self.save_novel(novel, output_dir)

                return False, f"章节 {chapter_number} 生成失败: 无效的结果"
        except Exception as e:
            print(f"生成章节时出错: {e}")
            traceback.print_exc()
            return False, f"生成章节时出错: {e}"

    def continue_writing(self, novel_dir: str, chapters: int, progress_callback: Callable = None,
                        illustration_style: str = "写实风格") -> Tuple[bool, str]:
        """
        继续写作小说

        参数:
            novel_dir: 小说目录
            chapters: 要继续写作的章节数
            progress_callback: 进度回调函数
            illustration_style: 插图风格

        返回:
            (成功标志, 结果信息)
        """
        try:
            # 加载小说
            novel = self.load_novel(novel_dir)
            if not novel:
                return False, f"加载小说失败: {novel_dir}"

            # 确定起始章节
            start_chapter = 1
            for chapter in novel.chapters:
                if chapter.status == "completed":
                    start_chapter = max(start_chapter, chapter.number + 1)

            # 生成章节
            for i in range(chapters):
                chapter_number = start_chapter + i

                # 更新进度
                if progress_callback:
                    progress_callback(f"正在生成第 {chapter_number} 章...")

                # 生成章节
                success, message = self.generate_chapter(novel, chapter_number, novel_dir, illustration_style)

                # 更新进度
                if progress_callback:
                    progress_callback(f"第 {chapter_number} 章生成{'成功' if success else '失败'}: {message}")

                # 如果生成失败，停止生成
                if not success:
                    return False, f"生成第 {chapter_number} 章失败: {message}"

            # 保存为Word文档
            try:
                doc_path = os.path.join(novel_dir, f"{novel.title}.docx")
                print(f"尝试保存Word文档到: {doc_path}")

                # 调用save_as_document方法
                saved_path = novel.save_as_document(doc_path)

                if saved_path and os.path.exists(saved_path):
                    file_size = os.path.getsize(saved_path)
                    print(f"Word文档保存成功: {saved_path}, 大小: {file_size} 字节")

                    if progress_callback:
                        progress_callback(f"Word文档保存成功: {saved_path}, 大小: {file_size} 字节")

                    return True, f"成功生成 {chapters} 章内容，保存为 {saved_path}"
                else:
                    error_msg = f"Word文档保存失败或无法找到: {doc_path}"
                    print(error_msg)

                    if progress_callback:
                        progress_callback(error_msg)

                    # 尝试使用备用方法保存
                    try:
                        # 尝试使用简单的文本文件保存
                        txt_path = os.path.join(novel_dir, f"{novel.title}.txt")
                        with open(txt_path, "w", encoding="utf-8") as f:
                            f.write(f"标题: {novel.title}\n\n")
                            for chapter in novel.chapters:
                                if hasattr(chapter, 'content') and chapter.content:
                                    f.write(f"\n\n## 第{chapter.number}章 {chapter.title}\n\n")
                                    f.write(chapter.content)

                        print(f"已使用备用方法保存为文本文件: {txt_path}")

                        if progress_callback:
                            progress_callback(f"已使用备用方法保存为文本文件: {txt_path}")

                        return True, f"成功生成 {chapters} 章内容，保存为文本文件: {txt_path}"
                    except Exception as txt_error:
                        print(f"备用保存方法也失败: {txt_error}")
                        traceback.print_exc()
            except Exception as doc_error:
                print(f"保存Word文档时出错: {doc_error}")
                traceback.print_exc()

                if progress_callback:
                    progress_callback(f"保存Word文档时出错: {doc_error}")

            # 即使文档保存失败，也返回成功，因为章节内容已经生成并保存为文本文件
            return True, f"成功生成 {chapters} 章内容，但Word文档保存失败"
        except Exception as e:
            print(f"继续写作时出错: {e}")
            traceback.print_exc()
            return False, f"继续写作时出错: {e}"
