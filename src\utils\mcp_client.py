"""
MCP协议客户端
用于多智能体协作通信的客户端
"""

import os
import json
import time
import logging
import re
from typing import Dict, Any, Optional, List, Union, Tuple
import traceback

# 导入配置
try:
    from src.core.model_config import AI_MODEL_CONFIG, MCP_CONFIG
except ImportError:
    # 如果导入失败，创建默认配置
    AI_MODEL_CONFIG = {}
    MCP_CONFIG = {
        "enabled": True,
        "timeout": 30,  # seconds
        "retry_count": 3,
        "retry_delay": 2,  # seconds
        "log_requests": True,
        "log_responses": True,
        "log_directory": "logs/mcp"
    }

# 创建日志目录
os.makedirs(MCP_CONFIG.get("log_directory", "logs/mcp"), exist_ok=True)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(MCP_CONFIG.get("log_directory", "logs/mcp"), "mcp.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("mcp_client")

class MCPClient:
    """MCP协议客户端，用于智能体之间的通信"""

    def __init__(self, config=None):
        """初始化MCP客户端"""
        self.config = config or MCP_CONFIG
        self.session_id = f"novel_session_{int(time.time())}"

        # 创建日志目录
        os.makedirs(self.config.get("log_directory", "logs/mcp"), exist_ok=True)

        logger.info(f"初始化MCP客户端，会话ID: {self.session_id}")

    def call_llm(self,
                model: str,
                prompt: str,
                system_message: Optional[str] = None,
                temperature: Optional[float] = None,
                max_tokens: Optional[int] = None) -> str:
        """
        调用语言模型

        参数:
            model: 模型名称
            prompt: 提示词
            system_message: 系统消息
            temperature: 温度参数
            max_tokens: 最大生成token数

        返回:
            模型生成的内容
        """
        logger.info(f"调用LLM模型 {model}，提示词: {prompt[:50]}...")

        # 记录请求
        if self.config.get("log_requests", True):
            self._log_request(model, prompt, system_message)

        try:
            # 根据模型类型选择不同的API调用方式
            model_config = self._get_model_config(model)
            if not model_config:
                logger.warning(f"未找到模型配置: {model}，使用默认配置")
                model_config = {}

            api_type = model_config.get("api_type", "")

            # 设置参数
            params = {
                "temperature": temperature or model_config.get("temperature", 0.7),
                "max_tokens": max_tokens or model_config.get("max_tokens", 4096)
            }

            # 根据API类型调用不同的模型
            if api_type == "zhipu":
                # 调用智谱AI模型
                from src.llm.zhipu_api import call_zhipu_api
                response = call_zhipu_api(model, prompt, system_message, **params)
            elif api_type == "siliconflow":
                # 调用硅基流动模型
                from src.llm.siliconflow_api import call_siliconflow_api
                response = call_siliconflow_api(model, prompt, system_message, **params)
            elif api_type == "openrouter":
                # 调用OpenRouter模型
                from src.llm.openrouter_api import call_openrouter_api
                response = call_openrouter_api(model, prompt, system_message, **params)
            elif api_type == "ollama":
                # 调用Ollama本地模型
                from src.llm.ollama_api import call_ollama_api
                response = call_ollama_api(model, prompt, system_message, **params)
            else:
                # 默认使用智谱AI模型
                from src.llm.zhipu_api import call_zhipu_api
                response = call_zhipu_api(model, prompt, system_message, **params)

            # 记录响应
            if self.config.get("log_responses", True):
                self._log_response(model, response)

            return response

        except Exception as e:
            error_message = f"调用LLM模型 {model} 失败: {e}"
            logger.error(error_message)
            logger.error(traceback.format_exc())
            return f"错误: {error_message}"

    def call_agent(self, agent_name: str, message: Dict[str, Any]) -> Any:
        """
        调用智能体

        参数:
            agent_name: 智能体名称
            message: 消息内容

        返回:
            智能体的响应
        """
        logger.info(f"调用智能体 {agent_name}...")

        # 记录请求
        if self.config.get("log_requests", True):
            self._log_request("agent", agent_name, message)

        try:
            # 获取智能体
            try:
                from src.core.agent import AGENT_REGISTRY
                agent = AGENT_REGISTRY.get_agent(agent_name)
            except ImportError:
                return {"error": f"无法导入智能体注册表"}

            if not agent:
                error_message = f"未找到智能体: {agent_name}"
                logger.error(error_message)
                return {"error": error_message}

            # 处理消息
            response = agent.handle_message(message)

            # 记录响应
            if self.config.get("log_responses", True):
                self._log_response("agent", response)

            return response

        except Exception as e:
            error_message = f"调用智能体 {agent_name} 失败: {e}"
            logger.error(error_message)
            logger.error(traceback.format_exc())
            return {"error": error_message}

    def generate_illustration(self,
                             title: str,
                             scene_description: str,
                             style: str = "写实风格",
                             output_path: Optional[str] = None,
                             model_name: Optional[str] = None) -> Tuple[bool, str]:
        """
        生成插图

        参数:
            title: 小说标题
            scene_description: 场景描述
            style: 插图风格
            output_path: 输出路径
            model_name: 模型名称

        返回:
            (成功标志, 结果信息)
        """
        logger.info(f"生成插图: {title}...")

        # 记录请求
        if self.config.get("log_requests", True):
            self._log_request("illustration", title, scene_description)

        try:
            # 调用插图生成函数
            try:
                from src.illustration import generate_novel_illustration
                success, result = generate_novel_illustration(
                    title=title,
                    scene=scene_description,
                    style=style,
                    output_path=output_path,
                    model_name=model_name
                )
            except ImportError:
                return False, "无法导入插图生成模块"

            # 记录响应
            if self.config.get("log_responses", True):
                self._log_response("illustration", result)

            return success, result

        except Exception as e:
            error_message = f"生成插图失败: {e}"
            logger.error(error_message)
            logger.error(traceback.format_exc())
            return False, error_message

    def _get_model_config(self, model_name: str) -> Dict[str, Any]:
        """获取模型配置"""
        models = AI_MODEL_CONFIG.get("models", {})
        return models.get(model_name, {})

    def _log_request(self, target_type: str, target_name: str, content: Any = None):
        """记录请求"""
        log_file = os.path.join(self.config.get("log_directory", "logs/mcp"), f"requests_{self.session_id}.log")

        try:
            with open(log_file, "a", encoding="utf-8") as f:
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"[{timestamp}] REQUEST: {target_type} - {target_name}\n")
                if content:
                    if isinstance(content, str):
                        f.write(f"Content: {content[:200]}...\n")
                    else:
                        f.write(f"Content: {json.dumps(content, ensure_ascii=False)[:200]}...\n")
                f.write("-" * 50 + "\n")
        except Exception as e:
            logger.error(f"记录请求失败: {e}")

    def _log_response(self, target_type: str, content: Any = None):
        """记录响应"""
        log_file = os.path.join(self.config.get("log_directory", "logs/mcp"), f"responses_{self.session_id}.log")

        try:
            with open(log_file, "a", encoding="utf-8") as f:
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"[{timestamp}] RESPONSE: {target_type}\n")
                if content:
                    if isinstance(content, str):
                        f.write(f"Content: {content[:200]}...\n")
                    else:
                        f.write(f"Content: {json.dumps(content, ensure_ascii=False)[:200]}...\n")
                f.write("-" * 50 + "\n")
        except Exception as e:
            logger.error(f"记录响应失败: {e}")

# 创建全局MCP客户端实例
mcp_client = MCPClient()

def get_mcp_client() -> MCPClient:
    """获取MCP客户端实例"""
    return mcp_client

# 便捷函数
def call_llm(model: str, prompt: str, system_message: Optional[str] = None, **kwargs) -> str:
    """调用语言模型的便捷函数"""
    # 直接使用MCP客户端，跳过LangChain
    logger.info(f"直接调用{model}模型 API")

    # 获取模型配置
    model_config = mcp_client._get_model_config(model)
    api_type = model_config.get("api_type", "")

    # 设置参数
    temperature = kwargs.get("temperature", model_config.get("temperature", 0.7))
    max_tokens = kwargs.get("max_tokens", model_config.get("max_tokens", 4096))

    # 如果LangChain失败或不可用，使用MCP客户端
    try:
        # 直接调用相应的API
        logger.info(f"直接调用{model}模型 API")

        # 获取模型配置
        model_config = mcp_client._get_model_config(model)
        api_type = model_config.get("api_type", "")

        # 设置参数
        temperature = kwargs.get("temperature", model_config.get("temperature", 0.7))
        max_tokens = kwargs.get("max_tokens", model_config.get("max_tokens", 4096))

        # 根据API类型调用不同的模型
        if api_type == "zhipu":
            # 调用智谱AI模型
            from src.llm.zhipu_api import call_zhipu_api
            response = call_zhipu_api(model, prompt, system_message, temperature=temperature, max_tokens=max_tokens)
        elif api_type == "siliconflow":
            # 调用硅基流动模型
            from src.llm.siliconflow_api import call_siliconflow_api
            response = call_siliconflow_api(model, prompt, system_message, temperature=temperature, max_tokens=max_tokens)
        elif api_type == "openrouter":
            # 调用OpenRouter模型
            from src.llm.openrouter_api import call_openrouter_api
            response = call_openrouter_api(model, prompt, system_message, temperature=temperature, max_tokens=max_tokens)
        elif api_type == "ollama":
            # 调用Ollama本地模型
            from src.llm.ollama_api import call_ollama_api
            response = call_ollama_api(model, prompt, system_message, temperature=temperature, max_tokens=max_tokens)
        else:
            # 默认使用智谱AI模型
            from src.llm.zhipu_api import call_zhipu_api
            response = call_zhipu_api(model, prompt, system_message, temperature=temperature, max_tokens=max_tokens)

        # 检查响应是否有效
        # 处理响应可能是列表的情况（特别是图像生成模型）
        if isinstance(response, list):
            # 如果是列表，尝试提取第一个元素
            if response and len(response) > 0:
                # 如果是图像生成的响应，可能是包含图像数据的列表
                logger.info(f"收到列表响应，长度: {len(response)}")
                return response
            else:
                # 空列表
                logger.warning("模型返回了空列表")
                return "错误: 模型返回了空列表"
        elif isinstance(response, str) and response.startswith("错误:"):
            logger.warning(f"模型响应错误: {response}")
            return response

        # 检查响应长度
        if len(response.strip()) < 100:
            logger.warning(f"模型响应过短: {len(response.strip())}字")
            # 如果响应过短，尝试再次调用模型
            logger.info("尝试再次调用模型")

            # 添加更详细的提示
            enhanced_prompt = f"""请为以下小说创作一个详细的章节，字数应在3000-5000字之间。请确保内容丰富、情节生动、描写细腻。

{prompt}"""

            # 再次调用模型
            if api_type == "zhipu":
                from src.llm.zhipu_api import call_zhipu_api
                response = call_zhipu_api(model, enhanced_prompt, system_message, temperature=temperature, max_tokens=max_tokens)
            elif api_type == "siliconflow":
                from src.llm.siliconflow_api import call_siliconflow_api
                response = call_siliconflow_api(model, enhanced_prompt, system_message, temperature=temperature, max_tokens=max_tokens)
            elif api_type == "openrouter":
                from src.llm.openrouter_api import call_openrouter_api
                response = call_openrouter_api(model, enhanced_prompt, system_message, temperature=temperature, max_tokens=max_tokens)
            elif api_type == "ollama":
                from src.llm.ollama_api import call_ollama_api
                response = call_ollama_api(model, enhanced_prompt, system_message, temperature=temperature, max_tokens=max_tokens)
            else:
                from src.llm.zhipu_api import call_zhipu_api
                response = call_zhipu_api(model, enhanced_prompt, system_message, temperature=temperature, max_tokens=max_tokens)

            # 再次检查响应是否有效
            if isinstance(response, list):
                # 如果是列表，尝试提取第一个元素
                if response and len(response) > 0:
                    # 如果是图像生成的响应，可能是包含图像数据的列表
                    logger.info(f"收到列表响应，长度: {len(response)}")
                    return response
                else:
                    # 空列表
                    logger.warning("模型返回了空列表")
                    return "错误: 模型返回了空列表"
            elif isinstance(response, str) and (response.startswith("错误:") or len(response.strip()) < 100):
                logger.error(f"再次调用模型仍然失败，响应长度: {len(response.strip()) if isinstance(response, str) else 0}字")
                return f"错误: 模型生成的内容过短或无效。请检查API密钥或模型配置。"

        return response
    except Exception as e:
        # 如果调用失败，返回错误信息
        logger.error(f"调用LLM失败: {e}")
        logger.error(traceback.format_exc())
        return f"错误: 无法生成内容。调用语言模型失败: {e}"

def call_agent(agent_name: str, message: Dict[str, Any]) -> Any:
    """调用智能体的便捷函数"""
    try:
        # 尝试使用LangChain集成
        from src.utils.langchain_integration import call_agent as langchain_call_agent
        return langchain_call_agent(agent_name, message)
    except (ImportError, AttributeError):
        # 如果LangChain集成不可用，回退到MCP客户端
        return mcp_client.call_agent(agent_name, message)

def generate_illustration(title: str, scene_description: str, style: str = "写实风格",
                         output_path: Optional[str] = None, model_name: Optional[str] = None) -> Tuple[bool, str]:
    """生成插图的便捷函数"""
    try:
        # 尝试使用LangChain集成
        from src.utils.langchain_integration import generate_illustration as langchain_generate_illustration
        return langchain_generate_illustration(title, scene_description, style, output_path, model_name)
    except (ImportError, AttributeError):
        # 如果LangChain集成不可用，直接调用插图生成模块
        try:
            # 尝试直接调用插图生成模块
            try:
                from src.illustration import generate_novel_illustration
                return generate_novel_illustration(
                    title=title,
                    scene=scene_description,
                    style=style,
                    output_path=output_path,
                    model_name=model_name
                )
            except ImportError:
                # 如果插图模块不可用，尝试使用MCP客户端
                return mcp_client.generate_illustration(title, scene_description, style, output_path, model_name)
        except Exception as e:
            # 如果调用失败，返回错误信息
            logger.error(f"生成插图失败: {e}")
            return False, f"错误: 生成插图失败: {e}"
