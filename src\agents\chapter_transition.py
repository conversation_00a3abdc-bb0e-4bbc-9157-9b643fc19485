"""
章节过渡段生成模块
用于生成章节之间的无缝衔接过渡段
"""

import os
import json
from typing import Dict, Any, Optional, List, Tuple

class ChapterTransition:
    """章节过渡段生成器"""
    
    def __init__(self, llm_agent):
        """初始化过渡段生成器
        
        Args:
            llm_agent: 用于生成过渡段的LLM代理
        """
        self.llm_agent = llm_agent
    
    def generate_transition(self, 
                           previous_chapter_ending: str, 
                           next_chapter_outline: str,
                           chapter_num: int,
                           chapter_title: str) -> str:
        """生成章节过渡段
        
        Args:
            previous_chapter_ending: 前一章节的结尾内容（最后一段）
            next_chapter_outline: 下一章节的大纲
            chapter_num: 下一章节的章节号
            chapter_title: 下一章节的标题
            
        Returns:
            str: 生成的过渡段，包含前一章结尾到下一章开头的无缝衔接
        """
        # 构建过渡段生成提示词
        prompt = f"""
请为小说创建一个无缝衔接的过渡段，确保前一章结尾与下一章开头之间的自然过渡。

前一章结尾内容（最后一段）：
{previous_chapter_ending}

下一章信息：
- 章节号: 第{chapter_num}章
- 章节标题: {chapter_title}
- 章节大纲: {next_chapter_outline}

请生成一个过渡段，包含两部分：
1. 前一章结尾的最后几句话（可以适当修改以便更好地衔接）
2. 下一章开头的前几句话（必须与下一章大纲内容一致）

要求：
1. 两部分之间必须实现真正的无缝衔接，读者不应感觉到章节切换
2. 不要使用"第X章"等章节标记，只需提供内容
3. 过渡必须自然流畅，保持情节、场景和对话的连续性
4. 如果前一章结尾是对话，下一章开头应继续该对话或对该对话的直接反应
5. 如果前一章结尾是情节描述，下一章开头应继续该情节或其直接后果
6. 总长度控制在300-500字之间

请直接给出过渡段内容，不需要任何解释。
"""
        
        # 调用LLM生成过渡段
        transition = self.llm_agent.call_llm(prompt)
        
        return transition
    
    def extract_next_chapter_beginning(self, transition: str) -> str:
        """从过渡段中提取下一章的开头部分
        
        Args:
            transition: 生成的过渡段
            
        Returns:
            str: 提取的下一章开头部分
        """
        # 简单的启发式方法：取过渡段的后半部分作为下一章开头
        # 在实际应用中，可以使用更复杂的方法来识别分界点
        lines = transition.strip().split('\n')
        if len(lines) <= 2:
            return transition
        
        # 取后半部分作为下一章开头
        half_point = len(lines) // 2
        next_chapter_beginning = '\n'.join(lines[half_point:])
        
        return next_chapter_beginning
        
    def get_transition_prompt(self, previous_chapter_ending: str, next_chapter_outline: str) -> str:
        """获取用于写作下一章的过渡提示词
        
        Args:
            previous_chapter_ending: 前一章节的结尾内容
            next_chapter_outline: 下一章节的大纲
            
        Returns:
            str: 用于写作下一章的过渡提示词
        """
        prompt = f"""
为了确保章节之间的无缝衔接，请注意以下前一章的结尾内容：

{previous_chapter_ending}

你的新章节必须直接从这个场景或对话继续，不得有任何时间或场景跳跃。如果前一章结尾是对话，新章节应继续该对话或对该对话的直接反应。如果前一章结尾是情节描述，新章节应继续该情节或其直接后果。

请确保新章节的开头与前一章的结尾实现真正的无缝衔接，读者不应感觉到章节切换。
"""
        return prompt
