"""
写作智能体模块
负责创作小说内容
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, cast

try:
    from src.core.agent import Agent, AGENT_REGISTRY
except ImportError:
    # 如果导入失败，创建简单的Agent类
    class Agent:
        def __init__(self, name, model=None):
            self.name = name
            self.model = model

    # 创建简单的AGENT_REGISTRY类
    class AgentRegistry:
        def __init__(self):
            self.agents = {}

        def register(self, agent):
            self.agents[agent.name] = agent
            return agent

        def get_agent(self, name):
            return self.agents.get(name)

    AGENT_REGISTRY = AgentRegistry()

try:
    from src.core.prompt_manager import PromptManager
except ImportError:
    # 如果导入失败，创建简单的PromptManager类
    class PromptManager:
        def get_prompt(self, prompt_name, **kwargs):
            return f"提示词 {prompt_name} 示例内容"

try:
    from src.core.model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES
except ImportError:
    # 如果导入失败，创建简单的配置
    AI_MODEL_CONFIG = {
        "agent_models": {},
        "default_model": "glm-4-flash"
    }
    DEFAULT_MODEL_NAMES = {"glm": "glm-4-flash"}

# 初始化提示词管理器
prompt_manager = PromptManager()

class WritingAgent(Agent):
    """
    写作智能体，负责创作小说内容
    """

    def __init__(self, name: str, model: Optional[str] = None):
        """初始化写作智能体"""
        # 确保AI_MODEL_CONFIG是字典类型
        agent_models = cast(Dict[str, Any], AI_MODEL_CONFIG.get("agent_models", {}))
        default_model = cast(str, AI_MODEL_CONFIG.get("default_model", ""))

        # 获取模型名称
        model = model or agent_models.get("WritingAgent", default_model)
        super().__init__(name, model)
        self.prompt_manager = prompt_manager

    def handle_message(self, msg):
        """处理接收到的消息"""
        if msg['type'] == 'write':
            print(f"写作智能体接收到写作请求...")
            content = self.write_content(
                title=msg.get('title', '无标题'),
                genre=msg.get('genre', '现代小说'),
                style=msg.get('style', '写实主义'),
                scene=msg.get('scene', '未指定场景'),
                characters=msg.get('characters', '未指定角色')
            )

            # 发送结果给下一个智能体（如果指定了）
            if 'next_agent' in msg:
                self.send_message(
                    msg['next_agent'],
                    {
                        'type': 'polish',
                        'content': content,
                        'metadata': msg.get('metadata', {})
                    }
                )

            return content

    def write_content(self, title: str, genre: str, style: str, scene: str, characters: str, previous_content: Optional[str] = None) -> str:
        """
        生成小说内容

        参数:
            title: 标题
            genre: 类型
            style: 风格
            scene: 场景
            characters: 角色
            previous_content: 前文内容（如果有）

        返回:
            生成的内容
        """
        print(f"正在创作内容: {title}...")

        # 获取生成内容的提示词 - 改进大纲生成提示词，要求更加结构化的大纲
        outline_prompt = self.prompt_manager.get_prompt(
            "writing_agent_outline",
            title=title,
            genre=genre,
            style=style,
            scene=scene,
            characters=characters
        )

        # 使用更加结构化的大纲提示词
        structured_outline_prompt = f"""
你是一位专业的中文小说创作者，请为以下小说创作一个详细且结构化的内容大纲：

标题：{title}
类型：{genre}
风格：{style}
场景：{scene}
角色：{characters}

请提供以下内容：
1. 小说的核心主题和中心思想（100-200字）
2. 主要角色的详细设定，包括性格特点、背景故事和成长轨迹（每个角色200-300字）
3. 故事的整体结构，包括开端、发展、高潮和结局（300-500字）
4. 情节发展的关键点和转折点（200-300字）

请确保角色设定具体且一致，情节发展合理且连贯，符合{genre}类型和{style}风格的特点。
"""

        # 生成大纲
        outline = self.call_llm(structured_outline_prompt)

        # 构建前文分析文本
        previous_content_analysis = ""
        if previous_content:
            previous_content_analysis = f"""
前文概要：
{previous_content[:500] if previous_content else "这是小说的第一章，没有前文。"}

前文分析：
1. 时间节点：{self._extract_time_info(previous_content)}
2. 场景位置：{self._extract_location_info(previous_content)}
3. 人物状态：{self._extract_character_status(previous_content)}
4. 已发生的关键事件：{self._extract_key_events(previous_content)}
"""
        else:
            previous_content_analysis = "这是小说的第一章。"

        # 基于大纲生成内容的提示词，加入前文分析和连贯性要求
        content_prompt = f"""
你是一位专业的中文小说作家，请根据以下信息和大纲创作一个完整的小说章节：

{previous_content_analysis}

基本信息：
标题：{title}
类型：{genre}
风格：{style}
场景：{scene}
角色：{characters}

大纲：
{outline}

创作要求：
1. 必须严格按照大纲的内容和情节发展创作，不要偏离大纲设定的情节
2. 必须保持角色的一致性，不要更改角色名称或关系
3. 章节长度应为3000-5000字
4. 包含丰富的场景描写、人物对话和心理活动
5. 符合{style}的写作风格和{genre}的类型特点
6. 每个场景转换使用分隔符"* * *"标记
7. 避免出现任何提示词痕迹或AI自我指代
8. 不要引入大纲中未提及的主要角色或重大情节转折
9. 在内容中自然融入中国文化元素、历史背景或传统价值观，以吸引中国读者
10. 章节结尾必须留有悬念，创造情节转折或提出新的问题，引发读者继续阅读的欲望
11. 不要在结尾处完全解决所有冲突，保留一些悬念和未解决的问题
"""

        if previous_content:
            content_prompt += """
12. 必须与前文保持严格的连贯性，包括：
   - 时间线的自然延续
   - 场景转换的合理性
   - 人物行为的一致性
   - 已发生事件的合理衔接
"""

        content_prompt += "\n请直接给出章节内容，不需要额外的解释或说明。"

        # 生成内容
        content = self.call_llm(content_prompt)

        # 润色内容的提示词
        polish_prompt = f"""
你是一位专业的中文小说编辑，请对以下小说内容进行润色和完善：

{content}

润色要求：
1. 保持原有的情节和角色设定不变
2. 提升语言的流畅性和文学性
3. 增强场景描写的细节和氛围感
4. 使人物对话更加自然、生动
5. 确保情节发展的连贯性和合理性
6. 符合{style}的写作风格和{genre}的类型特点
7. 修正任何语法错误或表达不清的地方
8. 确保角色名称和设定的一致性

请直接给出润色后的内容，不需要额外的解释或说明。
"""

        # 生成最终内容
        final_content = self.call_llm(polish_prompt)

        return final_content

    def _extract_key_events(self, text: str) -> str:
        """提取关键事件"""
        prompt = "请简要总结以下文本中的关键事件：\n\n" + text
        return self.call_llm(prompt)

    def _extract_time_info(self, text: str) -> str:
        """提取时间信息"""
        prompt = "请从以下文本中提取最后发生的时间信息：\n\n" + text
        return self.call_llm(prompt)

    def _extract_location_info(self, text: str) -> str:
        """提取地点信息"""
        prompt = "请从以下文本中提取最后场景的地点信息：\n\n" + text
        return self.call_llm(prompt)

    def _extract_character_status(self, text: str) -> str:
        """提取角色状态"""
        prompt = "请总结以下文本中主要角色的当前状态：\n\n" + text
        return self.call_llm(prompt)

    def get_previous_chapter_ending(self, chapter_context: dict) -> str:
        """
        获取前一章节的结尾部分，用于章节衔接

        参数:
            chapter_context: 章节上下文信息

        返回:
            前一章节的结尾部分，如果没有则返回空字符串
        """
        chapter_number = chapter_context.get("chapter_number", 1)
        previous_chapter_content = chapter_context.get("previous_chapter_content", "")

        # 如果是第一章或没有前一章内容，返回空字符串
        if chapter_number <= 1 or not previous_chapter_content:
            return ""

        # 直接使用传入的前一章结尾，如果有的话
        previous_chapter_ending = chapter_context.get("previous_chapter_ending", "")
        if previous_chapter_ending:
            print(f"使用传入的前一章结尾，长度为 {len(previous_chapter_ending)} 字")
            return previous_chapter_ending

        # 如果没有传入前一章结尾，但有前一章内容，则提取结尾部分
        print(f"从前一章内容中提取结尾部分，总长度为 {len(previous_chapter_content)} 字")

        # 先尝试按段落分割，获取最后几个段落
        paragraphs = previous_chapter_content.split('\n\n')

        if len(paragraphs) >= 3:
            # 获取最后三个段落，提供更多上下文
            previous_ending = paragraphs[-3] + "\n\n" + paragraphs[-2] + "\n\n" + paragraphs[-1]
            print(f"提取前一章节的最后三个段落，长度: {len(previous_ending)} 字")
        else:
            # 如果段落不够，提取最后1500字，提供更充分的上下文
            previous_ending = previous_chapter_content[-1500:] if len(previous_chapter_content) > 1500 else previous_chapter_content
            print(f"提取前一章节的最后部分，长度: {len(previous_ending)} 字")

        # 如果提取的内容还是太长，则只取最后1000字
        if len(previous_ending) > 1000:
            previous_ending = previous_ending[-1000:]
            print(f"前一章节结尾内容太长，只取最后1000字，长度: {len(previous_ending)} 字")

        return previous_ending

    def _load_previous_chapter_content(self, chapter_context: dict, prev_chapter_num: int) -> str:
        """加载前一章的内容"""
        try:
            output_dir = chapter_context.get("output_dir", "output")

            # 首先尝试从chapters目录读取
            chapters_dir = os.path.join(output_dir, "chapters")
            prev_chapter_file = os.path.join(chapters_dir, f"chapter_{prev_chapter_num}_final.txt")

            # 如果chapters目录没有文件，从根目录读取
            if not os.path.exists(prev_chapter_file):
                prev_chapter_file = os.path.join(output_dir, f"chapter_{prev_chapter_num}_final.txt")

            if os.path.exists(prev_chapter_file):
                with open(prev_chapter_file, "r", encoding="utf-8") as f:
                    content = f.read()
                print(f"成功加载第{prev_chapter_num}章内容，长度: {len(content)} 字符")
                return content
            else:
                print(f"未找到第{prev_chapter_num}章的文件: {prev_chapter_file}")
                return ""
        except Exception as e:
            print(f"加载前一章内容时出错: {e}")
            return ""

    def write_chapter(self, chapter_context: dict) -> str:
        """
        生成小说章节内容，使用增强的上下文信息确保章节连贯性

        参数:
            chapter_context: 包含章节相关信息的字典，包括：
                - title: 小说标题
                - genre: 类型
                - style: 风格
                - scene: 场景
                - characters: 角色
                - chapter_number: 当前章节编号
                - total_chapters: 总章节数
                - chapter_title: 当前章节标题
                - novel_outline: 整体小说大纲
                - chapter_outline: 当前章节大纲
                - previous_chapters: 前面章节的摘要
                - previous_chapter_content: 前一章节的内容

        返回:
            生成的章节内容
        """
        print("\n=== 开始写作章节内容 ===")
        title = chapter_context.get("title", "")
        genre = chapter_context.get("genre", "")
        style = chapter_context.get("style", "")
        scene = chapter_context.get("scene", "")
        characters = chapter_context.get("characters", "")
        chapter_number = chapter_context.get("chapter_number", 1)
        total_chapters = chapter_context.get("total_chapters", 1)
        chapter_title = chapter_context.get("chapter_title", f"第{chapter_number}章")
        novel_outline = chapter_context.get("novel_outline", "")
        chapter_outline = chapter_context.get("chapter_outline", "")
        previous_chapters = chapter_context.get("previous_chapters", "")
        previous_chapter_content = chapter_context.get("previous_chapter_content", "")
        language = chapter_context.get("language", "中文")  # 获取语言参数

        print(f"正在创作章节: {chapter_title}...")

        # 检查当前章节状态
        chapter_status = chapter_context.get("chapter_status", "")
        if chapter_status == "in_progress":
            print(f"检测到第{chapter_number}章状态为'in_progress'，将重新写作该章")
            # 如果章节状态为"in_progress"，则忽略之前的内容，重新写作
            # 不需要做任何特殊处理，因为我们将重新生成内容

        # 获取前一章内容以确保连贯性
        if chapter_number > 1:
            previous_chapter_content = self._load_previous_chapter_content(chapter_context, chapter_number - 1)
            if previous_chapter_content:
                chapter_context["previous_chapter_content"] = previous_chapter_content
                # 提取前一章的关键信息
                chapter_context["previous_chapter_ending"] = previous_chapter_content[-1000:] if len(previous_chapter_content) > 1000 else previous_chapter_content
                print(f"已加载前一章内容，长度: {len(previous_chapter_content)} 字符")

        # 检查所有前置章节是否存在并完成
        if chapter_number > 1:
            # 首先检查是否有缺失的前置章节
            # 特别注意：只检查上一章，而不是所有前置章节
            # 这样可以确保章节按顺序生成，而不会跳过章节
            prev_num = chapter_number - 1

            # 获取小说目录
            output_dir = chapter_context.get("output_dir", "output")
            novel_dir = output_dir

            # 检查是否有小说子目录
            try:
                files = os.listdir(output_dir)
                novel_dirs = [d for d in files if os.path.isdir(os.path.join(output_dir, d))]

                # 如果有小说目录，则在小说目录中查找前置章节文件
                if novel_dirs:
                    # 提取小说标题从上下文中
                    novel_title = chapter_context.get("title", "")
                    print(f"小说标题: {novel_title}")

                    # 如果小说标题在目录列表中，则使用该目录
                    if novel_title in novel_dirs:
                        novel_dir = os.path.join(output_dir, novel_title)
                        print(f"使用小说目录: {novel_dir}")
            except Exception as e:
                print(f"检查小说目录时出错: {e}")

            # 尝试导入check_chapter_exists函数
            try:
                # 尝试从run模块导入
                from run import check_chapter_exists as check_chapter
                prev_chapter_exists = check_chapter(novel_dir, prev_num)
                if prev_chapter_exists:
                    print(f"使用check_chapter_exists函数验证第{prev_num}章已完成")
                    return ""  # 返回空字符串表示可以继续写作当前章节
                else:
                    print(f"使用check_chapter_exists函数验证第{prev_num}章不存在或未完成")
                    return f"PREVIOUS_CHAPTER_MISSING:{prev_num}"
            except ImportError:
                print("无法导入check_chapter_exists函数，使用传统方法检查前一章节")
                # 如果导入失败，继续使用原来的检查逻辑

                # 首先检查是否有状态信息
                novel_state_file = os.path.join(os.path.dirname(chapter_context.get("output_dir", "output")), "novel_state.json")
                prev_chapter_completed = False

                if os.path.exists(novel_state_file):
                    try:
                        with open(novel_state_file, "r", encoding="utf-8") as f:
                            novel_state = json.load(f)

                        # 检查前一章的状态
                        if "chapters" in novel_state:
                            for chapter in novel_state["chapters"]:
                                if chapter.get("number") == prev_num and chapter.get("status") == "completed":
                                    print(f"从状态文件中验证第{prev_num}章已完成")
                                    prev_chapter_completed = True
                                    # 如果前一章已完成，则不需要再检查文件
                                    break
                    except Exception as e:
                        print(f"读取状态文件时出错: {e}")
                        # 如果读取状态文件出错，继续检查文件

                # 如果从状态文件中确认前一章已完成，则跳过文件检查
                if prev_chapter_completed:
                    print(f"前一章已完成，跳过文件检查")
                    # 返回空字符串而不是None，以符合返回值类型
                    return ""  # 返回空字符串表示可以继续写作当前章节

            # 如果导入check_chapter_exists失败，则使用传统方法检查前一章节
            # 尝试从文件中读取前置章节内容
            try:
                # 如果前面的状态检查没有确认前一章已完成，则检查文件
                if not prev_chapter_completed:
                    file_patterns = [
                        f"chapter_{prev_num}_final.txt",
                        f"chapter_{prev_num}_completed.txt",
                        f"chapter_{prev_num}_optimized.txt",
                        f"chapter_{prev_num}_5_optimized.txt",
                        f"chapter_{prev_num}_4_reviewed.txt",
                        f"chapter_{prev_num}_3_expanded.txt",
                        f"chapter_{prev_num}_2_polished.txt",
                        f"chapter_{prev_num}_1_initial.txt"
                    ]

                    found_file = False
                    for pattern in file_patterns:
                        prev_chapter_file = os.path.join(novel_dir, pattern)
                        print(f"检查文件: {prev_chapter_file}")
                        if os.path.exists(prev_chapter_file):
                            print(f"文件存在: {prev_chapter_file}")
                            try:
                                with open(prev_chapter_file, "r", encoding="utf-8") as f:
                                    content = f.read()
                                    content_length = len(content.strip())
                                    print(f"文件内容长度: {content_length} 字符")
                                    if content_length > 1000:  # 确保内容不是空的或者太短
                                        found_file = True
                                        print(f"已验证第{prev_num}章存在且内容完整: {prev_chapter_file}")
                                        break
                                    else:
                                        print(f"文件内容太短: {prev_chapter_file}")
                            except Exception as e:
                                print(f"读取文件时出错: {prev_chapter_file}, 错误: {e}")

                    # 如果没有找到上一章节的文件，返回特殊消息
                    if not found_file:
                        print(f"警告: 第{prev_num}章不存在或内容不完整")
                        print(f"需要先创建第{prev_num}章")
                        return f"PREVIOUS_CHAPTER_MISSING:{prev_num}"
            except Exception as e:
                print(f"尝试验证第{prev_num}章时出错: {e}")
                # 如果出错但状态文件显示前一章已完成，仍然继续
                if prev_chapter_completed:
                    print(f"虽然验证第{prev_num}章时出错，但状态文件显示已完成，继续写作")
                else:
                    return f"PREVIOUS_CHAPTER_MISSING:{prev_num}"

            # 如果所有前置章节都存在，再检查前一章节的内容以确保衔接
            if not previous_chapter_content:
                print(f"警告: 前一章节内容为空，尝试从文件中读取")
                try:
                    # 使用与前面相同的输出目录，确保路径一致性
                    file_patterns = [
                        f"chapter_{chapter_number-1}_final.txt",
                        f"chapter_{chapter_number-1}_completed.txt",
                        f"chapter_{chapter_number-1}_optimized.txt",
                        f"chapter_{chapter_number-1}_5_optimized.txt",
                        f"chapter_{chapter_number-1}_4_reviewed.txt",
                        f"chapter_{chapter_number-1}_3_expanded.txt",
                        f"chapter_{chapter_number-1}_2_polished.txt",
                        f"chapter_{chapter_number-1}_1_initial.txt"
                    ]

                    for pattern in file_patterns:
                        prev_chapter_file = os.path.join(novel_dir, pattern)
                        if os.path.exists(prev_chapter_file):
                            try:
                                with open(prev_chapter_file, "r", encoding="utf-8") as f:
                                    previous_chapter_content = f.read()
                                    print(f"从文件中读取前一章节内容: {prev_chapter_file}, 长度: {len(previous_chapter_content)} 字符")
                                    break
                            except Exception as e:
                                print(f"读取前一章节内容时出错: {prev_chapter_file}, 错误: {e}")
                except Exception as e:
                    print(f"尝试读取前一章节内容时出错: {e}")

        # 尝试加载增强提示词
        try:
            # 检查是否有增强提示词
            enhance_prompt_file = os.path.join(chapter_context.get("output_dir", "output"), f"enhance_prompt_{chapter_number}.txt")
            enhance_prompt = ""
            if os.path.exists(enhance_prompt_file):
                try:
                    with open(enhance_prompt_file, "r", encoding="utf-8") as f:
                        enhance_prompt = f.read().strip()
                    print(f"加载增强提示词: {enhance_prompt_file}")
                except Exception as e:
                    print(f"读取增强提示词时出错: {e}")
            else:
                print("没有找到增强的提示，使用默认提示...")
        except Exception as e:
            print(f"尝试加载增强提示词时出错: {e}")
            enhance_prompt = ""

        # 获取前一章节的结尾部分，用于章节衔接
        previous_ending = self.get_previous_chapter_ending(chapter_context)

        # 构建章节写作提示词
        prompt = f"""
你是一位专业的中文小说作家，请根据以下信息创作一个完整的小说章节：

基本信息：
- 小说标题：{title}
- 类型：{genre}
- 风格：{style}
- 当前章节：{chapter_title}（第{chapter_number}章，共{total_chapters}章）

小说大纲：
{novel_outline}

当前章节大纲：
{chapter_outline}

"""

        # 如果有前面章节的摘要，添加到提示中
        if previous_chapters:
            prompt += f"""
前面章节摘要：
{previous_chapters}
"""

        # 如果有前一章节的结尾，添加到提示中以确保衔接
        if previous_ending:
            prompt += f"""
前一章节结尾（请确保与此内容自然衔接）：
{previous_ending}

**重要提醒**：新章节必须从前一章结尾的情况自然延续，保持人物位置、时间、情节的连贯性。不要突然改变场景或忽略前一章的结尾情况。
"""

        # 如果有增强提示词，添加到提示中
        if enhance_prompt:
            prompt += f"""
特别要求：
{enhance_prompt}
"""

        # 添加创作要求
        prompt += """
创作要求：
1. **连贯性第一**：新章节必须从前一章的确切结尾情况开始，保持人物位置、时间线、情节状态的完全一致
2. 章节长度应为3000-5000字
3. 包含丰富的场景描写、人物对话和心理活动
4. 确保与前面章节的情节和人物设定保持一致
5. 每个场景转换使用分隔符"* * *"标记
6. 避免出现任何提示词痕迹或AI自我指代
7. 章节结尾必须留有悬念，创造情节转折或提出新的问题
8. 不要在结尾处完全解决所有冲突，保留一些悬念和未解决的问题
9. **严禁**突然改变场景、时间或人物状态，必须从前一章的结尾自然延续

请直接给出章节内容，不需要额外的解释或说明。
"""

        # 调用LLM生成章节内容
        print(f"调用模型 {self.model} 生成内容...")
        chapter_content = self.call_llm(prompt)

        # 检查生成的内容是否符合要求
        content_length = len(chapter_content)
        print(f"生成的章节内容长度: {content_length} 字符")

        # 检查内容是否过短
        if content_length < 1000:
            print("警告: 生成的章节内容过短，尝试重新生成...")
            # 修改提示词，要求更长的内容
            prompt += "\n特别强调：请确保生成的内容至少有3000字，包含丰富的情节和描写。"
            chapter_content = self.call_llm(prompt)
            content_length = len(chapter_content)
            print(f"重新生成的章节内容长度: {content_length} 字符")

        # 检查内容是否有问题
        issues = []
        if content_length < 1000:
            issues.append(f"字数不足，当前仅有{content_length}字，需要进行扩写")

        # 检查是否有突然中断的情况
        if not chapter_content.strip().endswith(("。", "！", "？", "…", ".", "!", "?", "...")):
            issues.append("章节结尾不完整，可能突然中断")

        # 检查是否有重复段落
        paragraphs = chapter_content.split("\n\n")
        unique_paragraphs = set()
        duplicate_count = 0
        for para in paragraphs:
            if para.strip() in unique_paragraphs:
                duplicate_count += 1
            else:
                unique_paragraphs.add(para.strip())

        if duplicate_count > 0:
            issues.append(f"检测到{duplicate_count}个重复段落")

        # 如果有问题，打印警告
        if issues:
            print("警告: 生成的章节内容存在以下问题:")
            for issue in issues:
                print(f"- {issue}")

        return chapter_content

    def call_llm(self, prompt: str) -> str:
        """调用语言模型生成内容

        参数:
            prompt: 提示词

        返回:
            生成的内容
        """
        try:
            # 尝试导入API客户端
            from src.utils.api_client import get_api_client

            # 使用MCP客户端调用模型
            from src.utils.mcp_client import call_llm as mcp_call_llm
            print(f"调用模型 {self.model} 生成内容...")
            response = mcp_call_llm(self.model, prompt)
            return response
        except ImportError:
            print("警告: 无法导入MCP客户端，使用备用方法")
            try:
                # 备用方法：直接调用API客户端
                api_client = get_api_client()
                response = api_client.generate_content(prompt, model=self.model)
                return response
            except Exception as e:
                print(f"调用模型时出错: {e}")
                return f"生成内容失败: {e}"
        except Exception as e:
            print(f"调用模型时出错: {e}")
            return f"生成内容失败: {e}"

    def generate_outline(self, novel_title, novel_genre, novel_style, novel_dir):
        """生成小说大纲

        参数:
            novel_title: 小说标题
            novel_genre: 小说类型
            novel_style: 写作风格
            novel_dir: 小说目录

        返回:
            大纲文件路径
        """
        print("正在生成小说大纲...")

        try:
            # 尝试导入大纲生成器
            from src.agents.outline_generator import create_outline_generator
            import logging
            logger = logging.getLogger("WritingAgent")

            # 创建大纲生成器
            logger.info(f"[WritingAgent] 开始使用大纲生成器生成《{novel_title}》的大纲")
            print(f"使用大纲生成器生成《{novel_title}》的大纲")

            # 记录调用参数
            logger.info(f"[WritingAgent] 大纲生成参数: 标题={novel_title}, 类型={novel_genre}, 风格={novel_style}, 目录={novel_dir}")

            generator = create_outline_generator(
                novel_title=novel_title,
                novel_genre=novel_genre,
                novel_style=novel_style,
                novel_dir=novel_dir,
                total_chapters=30,  # 默认总章节数
                llm_caller=self.call_llm  # 使用WritingAgent的模型调用函数
            )
            logger.info(f"[WritingAgent] 大纲生成器创建成功")

            # 获取总纲
            logger.info(f"[WritingAgent] 开始获取总纲")
            master_outline = generator.get_master_outline()
            logger.info(f"[WritingAgent] 总纲获取成功, 包含{len(master_outline.get('phases', []))}个阶段")
            print(f"已生成《{novel_title}》的总纲")

            # 获取第一章大纲
            logger.info(f"[WritingAgent] 开始获取第一章大纲")
            chapter = generator.get_next_chapter_outline()
            logger.info(f"[WritingAgent] 第一章大纲获取成功: 第{chapter.chapter_num}章 - {chapter.title}")
            print(f"已生成第{chapter.chapter_num}章大纲: {chapter.title}")

            # 返回大纲文件路径
            outline_file = os.path.join(novel_dir, "novel_outline.txt")
            logger.info(f"[WritingAgent] 大纲生成完成, 文件路径: {outline_file}")
            print(f"已生成并保存小说大纲: {outline_file}")
            return outline_file

        except Exception as e:
            import traceback
            error_trace = traceback.format_exc()
            print(f"使用大纲生成器时出错: {e}")
            print("回退到使用原始方法生成大纲")

            # 记录详细错误信息
            try:
                logger.error(f"[WritingAgent] 大纲生成器出错: {e}")
                logger.error(f"[WritingAgent] 错误详情:\n{error_trace}")
            except:
                print("日志记录失败")

            # 构建提示词
            prompt = f"""你是一位专业的中文小说创作者，请为以下小说创作一个详细且结构化的内容大纲：

标题：{novel_title}
类型：{novel_genre}
风格：{novel_style}

请提供以下内容：
1. 小说的核心主题和中心思想（100-200字）
2. 主要角色的详细设定，包括性格特点、背景故事和成长轨迹（每个角色200-300字）
3. 故事的整体结构，包括开端、发展、高潮和结局（300-500字）
4. 情节发展的关键点和转折点（200-300字）

请确保角色设定具体且一致，情节发展合理且连贯，符合{novel_genre}类型和{novel_style}风格的特点。"""

            # 调用模型生成内容
            print(f"调用模型 {self.model} 生成内容...")
            response = self.call_llm(prompt)

            # 保存大纲
            outline_file = os.path.join(novel_dir, "novel_outline.txt")
            with open(outline_file, "w", encoding="utf-8") as f:
                f.write(response)

            print(f"已生成并保存小说大纲: {outline_file}")
            return outline_file
