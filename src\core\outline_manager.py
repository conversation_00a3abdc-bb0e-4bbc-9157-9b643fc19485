"""
大纲管理模块
用于处理小说大纲的创建、读取、更新等操作
"""

import os
import re
from typing import List, Dict, Optional, Tuple

class OutlineManager:
    """大纲管理器"""
    
    def __init__(self, novel_dir: str):
        """初始化大纲管理器
        
        Args:
            novel_dir: 小说目录路径
        """
        self.novel_dir = novel_dir
        self.outline_file = os.path.join(novel_dir, "novel_outline.txt")
        self.outline_content = ""
        self._load_outline()
    
    def _load_outline(self) -> bool:
        """加载大纲内容
        
        Returns:
            bool: 是否成功加载大纲
        """
        if os.path.exists(self.outline_file):
            try:
                with open(self.outline_file, "r", encoding="utf-8") as f:
                    self.outline_content = f.read()
                return True
            except Exception as e:
                print(f"加载大纲文件出错: {e}")
                return False
        return False
    
    def save_outline(self, content: str) -> bool:
        """保存大纲内容
        
        Args:
            content: 大纲内容
            
        Returns:
            bool: 是否成功保存大纲
        """
        try:
            # 确保目录存在
            os.makedirs(self.novel_dir, exist_ok=True)
            
            # 保存大纲文件
            with open(self.outline_file, "w", encoding="utf-8") as f:
                f.write(content)
            
            # 更新内存中的大纲内容
            self.outline_content = content
            return True
        except Exception as e:
            print(f"保存大纲文件出错: {e}")
            return False
    
    def append_outline(self, content: str) -> bool:
        """追加大纲内容
        
        Args:
            content: 要追加的大纲内容
            
        Returns:
            bool: 是否成功追加大纲
        """
        try:
            # 如果大纲文件不存在，直接保存
            if not os.path.exists(self.outline_file):
                return self.save_outline(content)
            
            # 追加内容到大纲文件
            with open(self.outline_file, "a", encoding="utf-8") as f:
                f.write("\n\n" + content)
            
            # 更新内存中的大纲内容
            self._load_outline()
            return True
        except Exception as e:
            print(f"追加大纲内容出错: {e}")
            return False
    
    def get_chapter_outline(self, chapter_num: int) -> str:
        """获取指定章节的大纲内容
        
        Args:
            chapter_num: 章节号
            
        Returns:
            str: 章节大纲内容
        """
        if not self.outline_content:
            return ""
        
        # 使用正则表达式匹配章节大纲
        pattern = rf"# 第{chapter_num}章[:：].*?(?=# 第\d+章[:：]|$)"
        match = re.search(pattern, self.outline_content, re.DOTALL)
        
        if match:
            return match.group(0).strip()
        return ""
    
    def get_chapter_title(self, chapter_num: int) -> str:
        """获取指定章节的标题
        
        Args:
            chapter_num: 章节号
            
        Returns:
            str: 章节标题
        """
        chapter_outline = self.get_chapter_outline(chapter_num)
        if not chapter_outline:
            return f"第{chapter_num}章"
        
        # 使用正则表达式提取章节标题
        pattern = rf"# 第{chapter_num}章[:：](.*?)$"
        match = re.search(pattern, chapter_outline, re.MULTILINE)
        
        if match:
            return match.group(1).strip()
        return f"第{chapter_num}章"
    
    def get_total_chapters(self) -> int:
        """获取大纲中的总章节数
        
        Returns:
            int: 总章节数
        """
        if not self.outline_content:
            return 0
        
        # 使用正则表达式匹配所有章节标题
        pattern = r"# 第(\d+)章[:：]"
        matches = re.findall(pattern, self.outline_content)
        
        if matches:
            return max(int(num) for num in matches)
        return 0
    
    def has_story_basics(self) -> bool:
        """检查大纲是否包含故事基本情节、人物关系和事件设定
        
        Returns:
            bool: 是否包含基本信息
        """
        if not self.outline_content:
            return False
        
        # 检查是否包含基本信息的标记
        basics_markers = [
            "# 故事基本情节",
            "# 主要人物关系",
            "# 核心事件设定"
        ]
        
        return all(marker in self.outline_content for marker in basics_markers)
    
    def extract_story_basics(self) -> Dict[str, str]:
        """提取故事基本情节、人物关系和事件设定
        
        Returns:
            Dict[str, str]: 包含基本信息的字典
        """
        if not self.outline_content:
            return {}
        
        result = {}
        
        # 提取故事基本情节
        pattern = r"# 故事基本情节\n(.*?)(?=# |$)"
        match = re.search(pattern, self.outline_content, re.DOTALL)
        if match:
            result["story_plot"] = match.group(1).strip()
        
        # 提取主要人物关系
        pattern = r"# 主要人物关系\n(.*?)(?=# |$)"
        match = re.search(pattern, self.outline_content, re.DOTALL)
        if match:
            result["character_relationships"] = match.group(1).strip()
        
        # 提取核心事件设定
        pattern = r"# 核心事件设定\n(.*?)(?=# |$)"
        match = re.search(pattern, self.outline_content, re.DOTALL)
        if match:
            result["core_events"] = match.group(1).strip()
        
        return result
