"""
Novel Reader Module

This module provides a simple reader interface for novels.
"""

import os
import json
import gradio as gr
from typing import Dict, List, Any, Optional

def load_novel_info(novel_dir: str) -> Dict[str, Any]:
    """
    Load novel information from novel_info.json
    
    Args:
        novel_dir: Novel directory
        
    Returns:
        Novel information
    """
    novel_info_path = os.path.join(novel_dir, "novel_info.json")
    if not os.path.exists(novel_info_path):
        return {}
    
    try:
        with open(novel_info_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading novel info: {e}")
        return {}

def load_chapter_content(novel_dir: str, chapter_num: int) -> str:
    """
    Load chapter content

    Args:
        novel_dir: Novel directory
        chapter_num: Chapter number

    Returns:
        Chapter content
    """
    # 首先尝试从chapters目录读取
    chapters_dir = os.path.join(novel_dir, "chapters")
    chapter_file = os.path.join(chapters_dir, f"chapter_{chapter_num}_final.txt")

    # 如果chapters目录没有文件，从根目录读取
    if not os.path.exists(chapter_file):
        chapter_file = os.path.join(novel_dir, f"chapter_{chapter_num}_final.txt")

    if not os.path.exists(chapter_file):
        return f"Chapter {chapter_num} not found"

    try:
        with open(chapter_file, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        print(f"Error loading chapter {chapter_num}: {e}")
        return f"Error loading chapter {chapter_num}: {e}"

def get_chapter_illustration(novel_dir: str, chapter_num: int) -> Optional[str]:
    """
    Get chapter illustration path
    
    Args:
        novel_dir: Novel directory
        chapter_num: Chapter number
        
    Returns:
        Illustration path or None
    """
    illustrations_dir = os.path.join(novel_dir, "illustrations")
    if not os.path.exists(illustrations_dir):
        return None
    
    illustration_path = os.path.join(illustrations_dir, f"chapter_{chapter_num}.png")
    if os.path.exists(illustration_path):
        return illustration_path
    
    return None

def create_reader_ui(novel_dir: str) -> gr.Blocks:
    """
    Create reader UI
    
    Args:
        novel_dir: Novel directory
        
    Returns:
        Gradio Blocks interface
    """
    novel_info = load_novel_info(novel_dir)
    if not novel_info:
        return gr.Blocks(title="Novel Reader - Error").launch()
    
    title = novel_info.get("title", "Untitled Novel")
    author = novel_info.get("author", "Unknown Author")
    chapters = novel_info.get("chapters", [])
    
    # Get available chapters
    available_chapters = []
    for chapter in chapters:
        chapter_num = chapter.get("number", 0)
        chapter_title = chapter.get("title", f"Chapter {chapter_num}")
        chapter_file = os.path.join(novel_dir, f"chapter_{chapter_num}_final.txt")
        if os.path.exists(chapter_file):
            available_chapters.append((chapter_num, chapter_title))
    
    # Sort chapters by number
    available_chapters.sort(key=lambda x: x[0])
    
    # Create chapter options
    chapter_options = [f"{num}. {title}" for num, title in available_chapters]
    
    with gr.Blocks(title=f"Novel Reader - {title}") as reader:
        gr.Markdown(f"# {title}")
        gr.Markdown(f"*By {author}*")
        
        with gr.Row():
            with gr.Column(scale=1):
                chapter_selector = gr.Dropdown(
                    choices=chapter_options,
                    label="Select Chapter",
                    value=chapter_options[0] if chapter_options else None
                )
                
                navigation_row = gr.Row()
                with navigation_row:
                    prev_button = gr.Button("Previous Chapter")
                    next_button = gr.Button("Next Chapter")
            
            with gr.Column(scale=3):
                chapter_title_display = gr.Markdown("## Chapter Title")
                chapter_content = gr.Textbox(
                    label="Chapter Content",
                    lines=25,
                    interactive=False
                )
                chapter_illustration = gr.Image(label="Chapter Illustration")
        
        # Function to load chapter
        def load_chapter(chapter_option):
            if not chapter_option:
                return "## No Chapter Selected", "No chapter selected", None
            
            # Extract chapter number from option
            chapter_num = int(chapter_option.split(".")[0])
            
            # Find chapter title
            chapter_title = "Chapter"
            for num, title in available_chapters:
                if num == chapter_num:
                    chapter_title = title
                    break
            
            # Load chapter content
            content = load_chapter_content(novel_dir, chapter_num)
            
            # Get illustration
            illustration = get_chapter_illustration(novel_dir, chapter_num)
            
            return f"## {chapter_title}", content, illustration
        
        # Function to navigate to previous chapter
        def prev_chapter(chapter_option):
            if not chapter_option:
                return chapter_options[0] if chapter_options else None
            
            try:
                current_index = chapter_options.index(chapter_option)
                if current_index > 0:
                    return chapter_options[current_index - 1]
                return chapter_option
            except ValueError:
                return chapter_options[0] if chapter_options else None
        
        # Function to navigate to next chapter
        def next_chapter(chapter_option):
            if not chapter_option:
                return chapter_options[0] if chapter_options else None
            
            try:
                current_index = chapter_options.index(chapter_option)
                if current_index < len(chapter_options) - 1:
                    return chapter_options[current_index + 1]
                return chapter_option
            except ValueError:
                return chapter_options[0] if chapter_options else None
        
        # Set up event handlers
        chapter_selector.change(
            load_chapter,
            inputs=[chapter_selector],
            outputs=[chapter_title_display, chapter_content, chapter_illustration]
        )
        
        prev_button.click(
            prev_chapter,
            inputs=[chapter_selector],
            outputs=[chapter_selector]
        )
        
        next_button.click(
            next_chapter,
            inputs=[chapter_selector],
            outputs=[chapter_selector]
        )
        
        # Load initial chapter
        if chapter_options:
            initial_chapter = chapter_options[0]
            chapter_title_display.value, chapter_content.value, chapter_illustration.value = load_chapter(initial_chapter)
    
    return reader

def launch_reader(novel_dir: str) -> None:
    """
    Launch reader for a novel
    
    Args:
        novel_dir: Novel directory
    """
    reader = create_reader_ui(novel_dir)
    reader.launch()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        novel_dir = sys.argv[1]
        launch_reader(novel_dir)
    else:
        print("Please provide a novel directory")
