"""
Memory Management Module for Novel Writing System

Implements a three-tier memory management system:
1. Short-term memory: Caches current chapter segments using a sliding window algorithm
2. Medium-term memory: Persistently stores key entities and clues
3. Long-term memory: Summarizes historical plot with compression layers

This module helps maintain context across long narratives and ensures consistency.
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, Tuple
import re
from collections import deque

class SlidingWindowCache:
    """
    Short-term memory implementation using a sliding window algorithm
    Caches the most recent segments of the current chapter
    """

    def __init__(self, max_size: int = 10):
        """
        Initialize the sliding window cache

        Args:
            max_size: Maximum number of segments to keep in the cache
        """
        self.max_size = max_size
        self.segments = deque(maxlen=max_size)

    def add(self, segment: str):
        """
        Add a segment to the cache

        Args:
            segment: Text segment to add
        """
        self.segments.append(segment)

    def get_recent(self, num_segments: int = None) -> str:
        """
        Get the most recent segments

        Args:
            num_segments: Number of recent segments to return (None for all)

        Returns:
            Concatenated recent segments
        """
        if num_segments is None:
            return "\n\n".join(self.segments)
        else:
            return "\n\n".join(list(self.segments)[-num_segments:])

    def clear(self):
        """Clear the cache"""
        self.segments.clear()

class EntityStore:
    """
    Medium-term memory implementation for storing key entities and clues
    """

    def __init__(self):
        """Initialize the entity store"""
        self.entities = {}
        self.relationships = {}
        self.clues = {}
        self.locations = {}
        self.time_points = {}

    def add_entity(self, name: str, attributes: Dict[str, Any]):
        """
        Add or update an entity

        Args:
            name: Entity name
            attributes: Entity attributes
        """
        if name in self.entities:
            self.entities[name].update(attributes)
        else:
            self.entities[name] = attributes

    def add_relationship(self, entity1: str, entity2: str, relationship_type: str, details: Dict[str, Any] = None):
        """
        Add a relationship between entities

        Args:
            entity1: First entity name
            entity2: Second entity name
            relationship_type: Type of relationship
            details: Additional relationship details
        """
        if details is None:
            details = {}

        key = f"{entity1}_{entity2}"
        self.relationships[key] = {
            "entity1": entity1,
            "entity2": entity2,
            "type": relationship_type,
            "details": details
        }

    def add_clue(self, clue_id: str, description: str, related_entities: List[str] = None, status: str = "active"):
        """
        Add a clue to track

        Args:
            clue_id: Unique clue identifier
            description: Clue description
            related_entities: List of related entity names
            status: Clue status (active, resolved, abandoned)
        """
        if related_entities is None:
            related_entities = []

        self.clues[clue_id] = {
            "description": description,
            "related_entities": related_entities,
            "status": status,
            "mentions": 0
        }

    def add_location(self, name: str, description: str, attributes: Dict[str, Any] = None):
        """
        Add a location

        Args:
            name: Location name
            description: Location description
            attributes: Additional location attributes
        """
        if attributes is None:
            attributes = {}

        self.locations[name] = {
            "description": description,
            "attributes": attributes
        }

    def add_time_point(self, identifier: str, description: str, relative_position: str = None):
        """
        Add a time point

        Args:
            identifier: Time point identifier
            description: Time point description
            relative_position: Position relative to other time points
        """
        self.time_points[identifier] = {
            "description": description,
            "relative_position": relative_position
        }

    def get_entity(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get entity by name

        Args:
            name: Entity name

        Returns:
            Entity attributes or None if not found
        """
        return self.entities.get(name)

    def get_relationships(self, entity_name: str) -> List[Dict[str, Any]]:
        """
        Get all relationships for an entity

        Args:
            entity_name: Entity name

        Returns:
            List of relationships
        """
        relationships = []
        for rel in self.relationships.values():
            if rel["entity1"] == entity_name or rel["entity2"] == entity_name:
                relationships.append(rel)
        return relationships

    def get_active_clues(self) -> List[Dict[str, Any]]:
        """
        Get all active clues

        Returns:
            List of active clues
        """
        return [clue for clue_id, clue in self.clues.items() if clue["status"] == "active"]

    def update_clue_status(self, clue_id: str, status: str):
        """
        Update clue status

        Args:
            clue_id: Clue identifier
            status: New status
        """
        if clue_id in self.clues:
            self.clues[clue_id]["status"] = status

    def increment_clue_mention(self, clue_id: str):
        """
        Increment the mention count for a clue

        Args:
            clue_id: Clue identifier
        """
        if clue_id in self.clues:
            self.clues[clue_id]["mentions"] += 1

    def get_unused_clues(self, threshold: int = 3) -> List[str]:
        """
        Get clues that haven't been mentioned enough

        Args:
            threshold: Mention threshold

        Returns:
            List of underused clue IDs
        """
        return [clue_id for clue_id, clue in self.clues.items()
                if clue["status"] == "active" and clue["mentions"] < threshold]

    def save(self, filepath: str):
        """
        Save entity store to file

        Args:
            filepath: Path to save file
        """
        data = {
            "entities": self.entities,
            "relationships": self.relationships,
            "clues": self.clues,
            "locations": self.locations,
            "time_points": self.time_points
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def load(self, filepath: str) -> bool:
        """
        Load entity store from file

        Args:
            filepath: Path to load file

        Returns:
            Success flag
        """
        if not os.path.exists(filepath):
            return False

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.entities = data.get("entities", {})
            self.relationships = data.get("relationships", {})
            self.clues = data.get("clues", {})
            self.locations = data.get("locations", {})
            self.time_points = data.get("time_points", {})

            return True
        except Exception as e:
            print(f"Error loading entity store: {e}")
            return False

class PlotSummary:
    """
    Long-term memory implementation for summarizing historical plot
    Uses hierarchical compression to maintain core narrative elements
    """

    def __init__(self):
        """Initialize the plot summary"""
        self.chapter_summaries = {}
        self.arc_summaries = {}
        self.global_summary = ""
        self.key_events = []

    def add_chapter_summary(self, chapter_num: int, summary: str):
        """
        Add a chapter summary

        Args:
            chapter_num: Chapter number
            summary: Chapter summary
        """
        self.chapter_summaries[chapter_num] = summary

    def create_arc_summary(self, start_chapter: int, end_chapter: int, arc_id: str, llm_caller=None):
        """
        Create a summary for a story arc

        Args:
            start_chapter: Starting chapter number
            end_chapter: Ending chapter number
            arc_id: Arc identifier
            llm_caller: Function to call LLM for summarization
        """
        if llm_caller is None:
            # If no LLM caller provided, just concatenate summaries
            chapters = range(start_chapter, end_chapter + 1)
            summaries = [self.chapter_summaries.get(ch, "") for ch in chapters if ch in self.chapter_summaries]
            self.arc_summaries[arc_id] = "\n\n".join(summaries)
            return

        # Get relevant chapter summaries
        chapters = range(start_chapter, end_chapter + 1)
        summaries = [self.chapter_summaries.get(ch, "") for ch in chapters if ch in self.chapter_summaries]

        if not summaries:
            return

        combined_summary = "\n\n".join(summaries)

        # Create a prompt for the LLM to generate an arc summary
        prompt = f"""
Please create a concise summary of the following story arc, covering chapters {start_chapter} to {end_chapter}.
Focus on the most important plot developments, character arcs, and key events.
Highlight any major revelations or turning points.

Chapter summaries:
{combined_summary}

Please provide a cohesive summary that captures the essence of this story arc in 300-500 words.
"""

        # Call LLM to generate summary
        arc_summary = llm_caller(prompt)
        self.arc_summaries[arc_id] = arc_summary

    def update_global_summary(self, llm_caller=None):
        """
        Update the global story summary

        Args:
            llm_caller: Function to call LLM for summarization
        """
        if llm_caller is None:
            # If no LLM caller provided, just use the most recent arc summary
            if self.arc_summaries:
                self.global_summary = list(self.arc_summaries.values())[-1]
            return

        # Combine arc summaries
        arc_summaries = list(self.arc_summaries.values())
        if not arc_summaries:
            return

        combined_arcs = "\n\n".join(arc_summaries)

        # Create a prompt for the LLM to generate a global summary
        prompt = f"""
Please create a comprehensive summary of the entire story so far, based on the following arc summaries.
Focus on the overall narrative, major character developments, and the most significant plot points.

Arc summaries:
{combined_arcs}

Please provide a cohesive global summary that captures the essence of the entire story in 500-800 words.
Emphasize the main narrative threads and how they connect.
"""

        # Call LLM to generate summary
        self.global_summary = llm_caller(prompt)

    def add_key_event(self, event: Dict[str, Any]):
        """
        Add a key event

        Args:
            event: Event details including description, chapter, and importance
        """
        self.key_events.append(event)

    def get_key_events(self, min_importance: int = 0) -> List[Dict[str, Any]]:
        """
        Get key events filtered by importance

        Args:
            min_importance: Minimum importance level (0-10)

        Returns:
            List of key events
        """
        return [event for event in self.key_events if event.get("importance", 0) >= min_importance]

    def save(self, filepath: str):
        """
        Save plot summary to file

        Args:
            filepath: Path to save file
        """
        data = {
            "chapter_summaries": self.chapter_summaries,
            "arc_summaries": self.arc_summaries,
            "global_summary": self.global_summary,
            "key_events": self.key_events
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def load(self, filepath: str) -> bool:
        """
        Load plot summary from file

        Args:
            filepath: Path to load file

        Returns:
            Success flag
        """
        if not os.path.exists(filepath):
            return False

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.chapter_summaries = data.get("chapter_summaries", {})
            self.arc_summaries = data.get("arc_summaries", {})
            self.global_summary = data.get("global_summary", "")
            self.key_events = data.get("key_events", [])

            return True
        except Exception as e:
            print(f"Error loading plot summary: {e}")
            return False

class MemoryManager:
    """
    Main memory manager that coordinates all three memory tiers
    """

    def __init__(self, novel_dir: str):
        """
        Initialize the memory manager

        Args:
            novel_dir: Novel directory for storing memory files
        """
        self.novel_dir = novel_dir
        self.memory_dir = os.path.join(novel_dir, "memory")
        os.makedirs(self.memory_dir, exist_ok=True)

        # Initialize memory tiers
        self.short_term = SlidingWindowCache()
        self.medium_term = EntityStore()
        self.long_term = PlotSummary()

        # Load existing memory if available
        self._load_memory()

    def _load_memory(self):
        """Load memory from files if they exist"""
        entity_path = os.path.join(self.memory_dir, "entity_store.json")
        plot_path = os.path.join(self.memory_dir, "plot_summary.json")

        self.medium_term.load(entity_path)
        self.long_term.load(plot_path)

    def save_memory(self):
        """Save memory to files"""
        entity_path = os.path.join(self.memory_dir, "entity_store.json")
        plot_path = os.path.join(self.memory_dir, "plot_summary.json")

        self.medium_term.save(entity_path)
        self.long_term.save(plot_path)

    def add_segment(self, segment: str):
        """
        Add a segment to short-term memory

        Args:
            segment: Text segment
        """
        self.short_term.add(segment)

    def clear_short_term(self):
        """Clear short-term memory"""
        self.short_term.clear()

    def get_context_for_generation(self, llm_caller=None) -> Dict[str, Any]:
        """
        Get comprehensive context for text generation

        Args:
            llm_caller: Function to call LLM for summarization

        Returns:
            Context dictionary with information from all memory tiers
        """
        # Update global summary if needed
        if llm_caller and not self.long_term.global_summary:
            self.long_term.update_global_summary(llm_caller)

        # Get active clues
        active_clues = self.medium_term.get_active_clues()

        # Get recent content from short-term memory
        recent_content = self.short_term.get_recent()

        # Compile context
        context = {
            "recent_content": recent_content,
            "global_summary": self.long_term.global_summary,
            "active_clues": active_clues,
            "key_entities": list(self.medium_term.entities.items()),
            "key_events": self.long_term.get_key_events(min_importance=7)
        }

        return context

    def extract_entities_from_text(self, text: str, llm_caller=None):
        """
        Extract entities from text and update medium-term memory

        Args:
            text: Text to analyze
            llm_caller: Function to call LLM for entity extraction
        """
        if llm_caller is None:
            # Simple regex-based entity extraction
            # Extract character names (capitalized words)
            character_pattern = r'\b[A-Z][a-z]+\b'
            potential_characters = re.findall(character_pattern, text)

            # Extract locations (after "in", "at", "to" followed by capitalized word)
            location_pattern = r'\b(?:in|at|to) ([A-Z][a-z]+)\b'
            potential_locations = re.findall(location_pattern, text)

            # Add potential entities to medium-term memory
            for character in set(potential_characters):
                self.medium_term.add_entity(character, {"type": "character", "mentions": 1})

            for location in set(potential_locations):
                self.medium_term.add_location(location, f"Location mentioned in text")

            return

        # Use LLM for sophisticated entity extraction
        prompt = f"""
Please analyze the following text and extract key entities, relationships, and clues.
Format your response as JSON with the following structure:
{{
  "characters": [
    {{"name": "Character Name", "attributes": {{"role": "...", "description": "..."}}}},
    ...
  ],
  "locations": [
    {{"name": "Location Name", "description": "..."}},
    ...
  ],
  "relationships": [
    {{"entity1": "Name1", "entity2": "Name2", "type": "...", "details": "..."}},
    ...
  ],
  "clues": [
    {{"id": "clue1", "description": "...", "related_entities": ["..."]}},
    ...
  ],
  "time_points": [
    {{"id": "time1", "description": "...", "relative_position": "..."}},
    ...
  ]
}}

Text to analyze:
{text}

Only include entities that are explicitly mentioned in the text. Be precise and accurate.
"""

        try:
            response = llm_caller(prompt)

            # Try to parse JSON response
            try:
                data = json.loads(response)

                # Update medium-term memory with extracted entities
                for character in data.get("characters", []):
                    self.medium_term.add_entity(character["name"], character.get("attributes", {}))

                for location in data.get("locations", []):
                    self.medium_term.add_location(location["name"], location.get("description", ""))

                for relationship in data.get("relationships", []):
                    self.medium_term.add_relationship(
                        relationship["entity1"],
                        relationship["entity2"],
                        relationship["type"],
                        relationship.get("details", {})
                    )

                for clue in data.get("clues", []):
                    self.medium_term.add_clue(
                        clue["id"],
                        clue["description"],
                        clue.get("related_entities", [])
                    )

                for time_point in data.get("time_points", []):
                    self.medium_term.add_time_point(
                        time_point["id"],
                        time_point["description"],
                        time_point.get("relative_position")
                    )
            except json.JSONDecodeError:
                print(f"Failed to parse entity extraction response as JSON")
        except Exception as e:
            print(f"Error in entity extraction: {e}")

    def summarize_chapter(self, chapter_num: int, chapter_content: str, llm_caller=None):
        """
        Create a summary for a chapter and update long-term memory

        Args:
            chapter_num: Chapter number
            chapter_content: Chapter content
            llm_caller: Function to call LLM for summarization
        """
        if llm_caller is None:
            # Simple summarization: take first paragraph and last paragraph
            paragraphs = chapter_content.split('\n\n')
            if len(paragraphs) >= 2:
                summary = paragraphs[0] + "\n\n" + paragraphs[-1]
            else:
                summary = chapter_content

            self.long_term.add_chapter_summary(chapter_num, summary)
            return

        # Use LLM for sophisticated summarization
        prompt = f"""
Please create a comprehensive summary of the following chapter (Chapter {chapter_num}).
Focus on key plot developments, character actions and development, important revelations, and any new clues or mysteries.
The summary should be 300-500 words and capture all essential information needed to understand the chapter's contribution to the overall story.

Chapter content:
{chapter_content[:2000]}...

Please provide a well-structured summary that could help someone recall the important elements of this chapter later.
"""

        try:
            summary = llm_caller(prompt)
            self.long_term.add_chapter_summary(chapter_num, summary)

            # Extract key events
            events_prompt = f"""
Based on the following chapter content, identify 1-3 key events that are most significant to the plot.
For each event, provide:
1. A brief description (1-2 sentences)
2. An importance rating from 1-10 (10 being extremely important to the overall story)
3. Any characters involved

Format your response as JSON:
[
  {{
    "description": "Event description",
    "importance": 8,
    "characters": ["Character1", "Character2"],
    "chapter": {chapter_num}
  }},
  ...
]

Chapter content:
{chapter_content[:2000]}...
"""

            events_response = llm_caller(events_prompt)

            try:
                # 处理响应可能已经是Python对象的情况
                if isinstance(events_response, list):
                    events = events_response
                else:
                    events = json.loads(events_response)

                for event in events:
                    self.long_term.add_key_event(event)
            except (json.JSONDecodeError, TypeError) as e:
                print(f"Failed to parse key events response as JSON: {e}")
                # 保存原始响应以便调试
                debug_dir = os.path.join(self.novel_dir, "memory", "debug")
                os.makedirs(debug_dir, exist_ok=True)
                debug_file = os.path.join(debug_dir, f"events_response_error_{chapter_num}.txt")
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(str(events_response))

        except Exception as e:
            print(f"Error in chapter summarization: {e}")

    def check_clue_usage(self):
        """
        Check for unused clues that need to be addressed

        Returns:
            List of unused clue descriptions
        """
        unused_clues = self.medium_term.get_unused_clues()
        unused_clue_details = []

        for clue_id in unused_clues:
            clue = self.medium_term.clues.get(clue_id)
            if clue:
                unused_clue_details.append(clue["description"])

        return unused_clue_details

    def get_entity_graph(self) -> Dict[str, Any]:
        """
        Get entity relationship graph

        Returns:
            Graph representation with nodes and edges
        """
        nodes = []
        edges = []

        # Add entity nodes
        for name, attributes in self.medium_term.entities.items():
            nodes.append({
                "id": name,
                "type": attributes.get("type", "character"),
                "attributes": attributes
            })

        # Add location nodes
        for name, location in self.medium_term.locations.items():
            nodes.append({
                "id": name,
                "type": "location",
                "attributes": {
                    "description": location["description"],
                    **location.get("attributes", {})
                }
            })

        # Add relationship edges
        for rel_id, relationship in self.medium_term.relationships.items():
            edges.append({
                "source": relationship["entity1"],
                "target": relationship["entity2"],
                "type": relationship["type"],
                "attributes": relationship.get("details", {})
            })

        return {
            "nodes": nodes,
            "edges": edges
        }
