"""
写作相关提示词管理模块
集中管理所有与写作相关的提示词
"""

class WritingPrompts:
    """写作相关提示词"""
    
    @staticmethod
    def chapter_writing_prompt(chapter_num, title, genre, style, scene, characters, 
                              chapter_title, chapter_outline, previous_chapters_summary, 
                              previous_chapter_content):
        """生成章节写作提示词"""
        return f"""
我需要你创作小说第{chapter_num}章的内容。

小说基本信息:
- 标题：{title}
- 类型：{genre}
- 风格：{style}
- 场景设定：{scene}
- 主要角色：{characters}

章节信息:
- 章节标题：{chapter_title}
- 章节大纲：{chapter_outline}

前面章节摘要：
{previous_chapters_summary}

前一章节结尾（最后1000字）：
{previous_chapter_content[-1000:] if previous_chapter_content else ""}

写作要求（必须严格遵循）：
1. 必须严格按照大纲创作，不得有任何偏离或遗漏大纲中的关键情节点
2. 必须使用大纲中指定的角色名称，不得更改或添加不在大纲中的主要角色
3. 必须与前一章节的结尾实现真正的无缝衔接，确保情节和对话的自然过渡
4. 必须保持角色性格、关系和背景设定的一致性，不得与前面章节冲突
5. 必须将大纲中的所有关键情节点都包含在章节内容中，不得遗漏

无缝衔接要求（非常重要）：
1. 本章开头必须直接从前一章结尾的场景、对话或情节继续，不得出现时间或场景跳跃
2. 如果前一章结尾是对话，本章开头应继续该对话或对该对话的直接反应
3. 如果前一章结尾是情节描述，本章开头应继续该情节或其直接后果
4. 不得重复前一章结尾的内容，而是要自然地继续发展

写作技巧要求：
1. 章节长度应为8000-12000字，确保内容丰富充实
2. 使用多种叙事手法，包括场景描写、对话、内心独白、回忆和情节描述
3. 创造生动、精彩的对话，展现人物性格和关系
4. 描写详尽的场景，让读者能够身临其境
5. 展现人物的心理活动和情感变化，增强人物的真实感
6. 在关键情节上用更多笔墨展开，增强戏剧性和沉浸感
7. 根据小说类型和风格，调整叙事节奏和语言风格
8. 在章节结尾留下悬念或链接，引导读者期待下一章

请直接给出章节内容，不需要任何解释。
"""

    @staticmethod
    def chapter_validation_prompt(current_chapter_outline, previous_chapter_content, content):
        """生成章节验证提示词"""
        return f"""
请作为严格的小说质量检查员，评估以下小说章节内容。你的任务是确保章节内容严格遵循大纲，并与前一章实现无缝衔接。

章节大纲：
{current_chapter_outline}

前一章结尾（最后500字）：
{previous_chapter_content[-500:] if previous_chapter_content else ""}

生成的章节内容（前1000字）：
{content[:1000]}...

请严格评估以下方面：
1. 大纲遵循度：内容是否严格遵循大纲中的情节设定和要求？是否有遗漏大纲中的关键情节点？
2. 角色一致性：是否使用了大纲中指定的角色，且角色设定与前文一致？
3. 无缝衔接：本章开头是否与前一章结尾实现了真正的无缝衔接？是否有时间或场景跳跃？
4. 内容质量：是否有内容重复、短语重复或与大纲无关的内容？

请给出详细的评估结果，并指出是否需要重新生成。如果需要重新生成，请给出具体原因和改进建议。

最后，请以下列格式给出结论：
结论: [需要重新生成] 或 [不需要重新生成]
理由: [简要理由]
"""

    @staticmethod
    def chapter_regeneration_prompt(validation_result):
        """生成章节重新生成提示词"""
        return f"""
请注意，之前的生成内容存在以下问题：
{validation_result}

请重新生成内容，并特别注意：
1. 必须严格按照大纲创作，不得有任何偏离或遗漏大纲中的关键情节点
2. 必须使用大纲中指定的角色名称，不得更改或添加不在大纲中的主要角色
3. 必须与前一章的结尾实现真正的无缝衔接，确保情节和对话的自然过渡
4. 本章开头必须直接从前一章结尾的场景、对话或情节继续，不得出现时间或场景跳跃
5. 不得重复前一章结尾的内容，而是要自然地继续发展

请直接给出章节内容，不需要额外的解释。
"""

    @staticmethod
    def chapter_summary_prompt(content):
        """生成章节摘要提示词"""
        return f"""
请为以下小说章节提供一个简短的摘要（不超过150字）:

{content[:1000]}...

请只提供摘要，不要有任何其他文字。
"""

    @staticmethod
    def transition_generation_prompt(previous_chapter_ending, next_chapter_outline, chapter_num, chapter_title):
        """生成章节过渡段提示词"""
        return f"""
请为小说创建一个无缝衔接的过渡段，确保前一章结尾与下一章开头之间的自然过渡。

前一章结尾内容（最后一段）：
{previous_chapter_ending}

下一章信息：
- 章节号: 第{chapter_num}章
- 章节标题: {chapter_title}
- 章节大纲: {next_chapter_outline}

请生成一个过渡段，包含两部分：
1. 前一章结尾的最后几句话（可以适当修改以便更好地衔接）
2. 下一章开头的前几句话（必须与下一章大纲内容一致）

要求：
1. 两部分之间必须实现真正的无缝衔接，读者不应感觉到章节切换
2. 不要使用"第X章"等章节标记，只需提供内容
3. 过渡必须自然流畅，保持情节、场景和对话的连续性
4. 如果前一章结尾是对话，下一章开头应继续该对话或对该对话的直接反应
5. 如果前一章结尾是情节描述，下一章开头应继续该情节或其直接后果
6. 总长度控制在300-500字之间

请直接给出过渡段内容，不需要任何解释。
"""
