import os
import re
import glob
import json
import logging
import threading
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import base64

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("HTMLReader")

# 全局变量
NOVEL_DIR = ""
SERVER_PORT = 4556
SERVER_HOST = "127.0.0.1"

class NovelReaderHandler(SimpleHTTPRequestHandler):
    """小说阅读器处理器"""

    def do_GET(self):
        """处理GET请求"""
        # 解析URL
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        query = parse_qs(parsed_url.query)

        # 处理API请求
        if path.startswith('/api/'):
            self.handle_api(path, query)
            return

        # 处理静态文件
        if path == '/':
            # 返回主页
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(get_index_html().encode('utf-8'))
        elif path.startswith('/illustrations/'):
            # 处理插图请求
            self.handle_illustration(path)
        else:
            # 返回404
            self.send_response(404)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'404 Not Found')

    def handle_api(self, path, query):
        """处理API请求"""
        if path == '/api/novels':
            # 获取小说列表
            self.send_json(get_novel_list())
        elif path == '/api/novel_info':
            # 获取小说信息
            novel_dir = query.get('dir', [''])[0]
            self.send_json(get_novel_info(novel_dir))
        elif path == '/api/chapters':
            # 获取章节列表
            novel_dir = query.get('dir', [''])[0]
            self.send_json(get_chapter_list(novel_dir))
        elif path == '/api/chapter_content':
            # 获取章节内容
            chapter_file = query.get('file', [''])[0]
            self.send_json(get_chapter_content(chapter_file))
        else:
            # 返回404
            self.send_response(404)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': 'API not found'}).encode('utf-8'))

    def handle_illustration(self, path):
        """处理插图请求"""
        # 从路径中提取文件名
        file_path = path[1:]  # 移除开头的斜杠

        # 如果路径以 /illustrations/ 开头，则将其解析为相对路径
        if path.startswith('/illustrations/'):
            # 从路径中提取文件名
            relative_path = path[14:]  # 移除 /illustrations/ 前缀

            # 检查是否指定了小说目录
            if NOVEL_DIR and os.path.exists(NOVEL_DIR):
                # 使用小说目录下的 illustrations 文件夹
                file_path = os.path.join(NOVEL_DIR, "illustrations", relative_path)
            else:
                # 如果没有指定小说目录，尝试使用相对路径
                file_path = os.path.join("illustrations", relative_path)

        logger.info(f"尝试加载插图: {file_path}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            self.send_response(404)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(f'Image not found: {file_path}'.encode('utf-8'))
            return

        # 确定文件类型
        if file_path.endswith('.jpg') or file_path.endswith('.jpeg'):
            content_type = 'image/jpeg'
        elif file_path.endswith('.png'):
            content_type = 'image/png'
        else:
            content_type = 'application/octet-stream'

        # 发送文件
        try:
            with open(file_path, 'rb') as f:
                self.send_response(200)
                self.send_header('Content-type', content_type)
                self.end_headers()
                self.wfile.write(f.read())
                logger.info(f"成功加载插图: {file_path}")
        except Exception as e:
            logger.error(f"读取插图时出错: {e}")
            self.send_response(500)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(f"Error reading image: {e}".encode('utf-8'))

    def send_json(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

def get_novel_list():
    """获取小说列表"""
    output_dir = "output"
    if not os.path.exists(output_dir):
        return {'novels': []}

    novels = []
    for d in os.listdir(output_dir):
        novel_dir = os.path.join(output_dir, d)
        if os.path.isdir(novel_dir):
            # 获取小说信息
            novel_info = get_novel_info(novel_dir)
            novels.append({
                'dir': novel_dir,
                'name': novel_info.get('title', d),
                'info': novel_info
            })

    return {'novels': novels}

def get_novel_info(novel_dir):
    """获取小说信息"""
    if not novel_dir or not os.path.exists(novel_dir):
        return {"title": "未知小说", "author": "未知作者", "genre": "未知类型", "style": "未知风格"}

    # 尝试从novel_info.json读取小说信息
    novel_info_file = os.path.join(novel_dir, "novel_info.json")
    if os.path.exists(novel_info_file):
        try:
            with open(novel_info_file, "r", encoding="utf-8") as f:
                novel_info = json.load(f)
            return {
                "title": novel_info.get("title", "未知小说"),
                "author": novel_info.get("author", "AI作家"),
                "genre": novel_info.get("genre", "未知类型"),
                "style": novel_info.get("style", "未知风格")
            }
        except Exception as e:
            logger.error(f"读取小说信息时出错: {e}")

    # 如果无法从novel_info.json读取，尝试从目录名称提取
    novel_name = os.path.basename(novel_dir)
    return {
        "title": novel_name,
        "author": "AI作家",
        "genre": "未知类型",
        "style": "未知风格"
    }

def get_chapter_list(novel_dir):
    """获取章节列表"""
    if not novel_dir or not os.path.exists(novel_dir):
        return {'chapters': []}

    # 首先尝试从chapters目录查找
    chapters_dir = os.path.join(novel_dir, "chapters")
    chapter_files = []

    if os.path.exists(chapters_dir):
        chapter_files = glob.glob(os.path.join(chapters_dir, "chapter_*_final.txt"))
        if not chapter_files:
            chapter_files = glob.glob(os.path.join(chapters_dir, "chapter_*.txt"))

    # 如果chapters目录没有文件，从根目录查找
    if not chapter_files:
        chapter_files = glob.glob(os.path.join(novel_dir, "chapter_*_final.txt"))
        if not chapter_files:
            chapter_files = glob.glob(os.path.join(novel_dir, "chapter_*.txt"))

    # 排序章节文件
    chapter_files.sort(key=lambda x: int(re.search(r'chapter_(\d+)', os.path.basename(x)).group(1)))

    # 构建章节列表
    chapters = []
    for file in chapter_files:
        match = re.search(r'chapter_(\d+)', os.path.basename(file))
        if match:
            chapter_num = match.group(1)
            chapter_title = f"第{chapter_num}章"

            # 查找插图
            illustration = None
            illustrations_dir = os.path.join(novel_dir, "illustrations")
            if os.path.exists(illustrations_dir):
                illustration_files = [
                    os.path.join(illustrations_dir, f)
                    for f in os.listdir(illustrations_dir)
                    if f.startswith(f"chapter_{chapter_num}") and (f.endswith(".png") or f.endswith(".jpg"))
                ]
                if illustration_files:
                    illustration = illustration_files[0]

            chapters.append({
                'file': file,
                'title': chapter_title,
                'number': int(chapter_num),
                'illustration': illustration
            })

    return {'chapters': chapters}

def get_chapter_content(chapter_file):
    """获取章节内容"""
    if not chapter_file or not os.path.exists(chapter_file):
        return {'content': '章节文件不存在', 'error': True}

    try:
        with open(chapter_file, "r", encoding="utf-8") as f:
            content = f.read()

        # 提取章节编号
        match = re.search(r'chapter_(\d+)', os.path.basename(chapter_file))
        chapter_num = match.group(1) if match else "0"

        # 获取插图
        novel_dir = os.path.dirname(chapter_file)
        illustration = None
        illustrations_dir = os.path.join(novel_dir, "illustrations")
        if os.path.exists(illustrations_dir):
            illustration_files = [
                os.path.join(illustrations_dir, f)
                for f in os.listdir(illustrations_dir)
                if f.startswith(f"chapter_{chapter_num}") and (f.endswith(".png") or f.endswith(".jpg"))
            ]
            if illustration_files:
                illustration = illustration_files[0]

        return {
            'content': content,
            'title': f"第{chapter_num}章",
            'illustration': illustration,
            'error': False
        }
    except Exception as e:
        logger.error(f"读取章节内容时出错: {e}")
        return {'content': f"读取章节内容时出错: {e}", 'error': True}

def get_index_html():
    """获取主页HTML"""
    return """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小说阅读器 - 专业版</title>
    <!-- 引入字体 -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=Noto+Serif+SC:wght@400;600;700&display=swap">
    <!-- 引入图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* 全局变量 */
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --background-light: #f8f9fa;
            --background-dark: #2c3e50;
            --text-light: #333;
            --text-dark: #ecf0f1;
            --border-radius: 8px;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
            --font-sans: 'Noto Sans SC', 'Microsoft YaHei', 'PingFang SC', sans-serif;
            --font-serif: 'Noto Serif SC', 'SimSun', 'STSong', serif;
        }

        /* 全局样式 */
        body {
            font-family: var(--font-sans);
            margin: 0;
            padding: 0;
            background-color: var(--background-light);
            color: var(--text-light);
            line-height: 1.6;
            transition: var(--transition);
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 300px;
            background-color: #fff;
            box-shadow: var(--box-shadow);
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            z-index: 10;
        }

        .sidebar h2 {
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary-color);
            color: var(--secondary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .sidebar h2 i {
            color: var(--primary-color);
        }

        .sidebar h3 {
            margin-top: 20px;
            margin-bottom: 10px;
            color: var(--secondary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .sidebar h3 i {
            color: var(--primary-color);
            font-size: 0.9em;
        }

        .novel-list, .chapter-list {
            list-style: none;
            padding: 0;
            margin: 0;
            overflow-y: auto;
        }

        .novel-list li, .chapter-list li {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            border-radius: var(--border-radius);
            margin-bottom: 5px;
            transition: var(--transition);
        }

        .novel-list li:hover, .chapter-list li:hover {
            background-color: rgba(52, 152, 219, 0.1);
            transform: translateX(5px);
        }

        .novel-list li.active, .chapter-list li.active {
            background-color: rgba(52, 152, 219, 0.2);
            font-weight: bold;
            border-left: 3px solid var(--primary-color);
        }

        /* 内容区样式 */
        .content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
            background-color: #fff;
            box-shadow: inset 0 0 10px rgba(0,0,0,0.05);
        }

        .chapter-title {
            margin-top: 0;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--primary-color);
            color: var(--secondary-color);
            font-family: var(--font-serif);
            font-size: 1.8rem;
        }

        .chapter-content {
            font-family: var(--font-serif);
            font-size: 18px;
            line-height: 1.8;
            margin-top: 20px;
            white-space: pre-wrap;
            text-align: justify;
            letter-spacing: 0.05em;
            padding: 20px;
            background-color: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .chapter-illustration {
            max-width: 100%;
            margin: 20px auto;
            display: block;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        .chapter-illustration:hover {
            transform: scale(1.01);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        /* 导航按钮 */
        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .nav-button {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-family: var(--font-sans);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
        }

        .nav-button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .nav-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 小说信息 */
        .novel-info {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            box-shadow: var(--box-shadow);
            border-left: 4px solid var(--primary-color);
        }

        .novel-info h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-family: var(--font-serif);
        }

        .novel-info p {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .novel-info p i {
            color: var(--primary-color);
            width: 20px;
            text-align: center;
        }

        /* 加载动画 */
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
            position: relative;
            height: 40px;
        }

        .loading:after {
            content: "";
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 3px solid var(--primary-color);
            border-color: var(--primary-color) transparent var(--primary-color) transparent;
            animation: loading 1.2s linear infinite;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        @keyframes loading {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* 主题切换 */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 100;
            background-color: #fff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        .theme-toggle:hover {
            transform: rotate(30deg);
        }

        .theme-toggle i {
            font-size: 20px;
            color: var(--secondary-color);
        }

        /* 阅读设置 */
        .reading-settings {
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 100;
            background-color: #fff;
            border-radius: var(--border-radius);
            padding: 15px;
            box-shadow: var(--box-shadow);
            display: none;
            width: 200px;
        }

        .reading-settings h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--secondary-color);
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .setting-group {
            margin-bottom: 15px;
        }

        .setting-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 14px;
            color: var(--secondary-color);
        }

        .setting-options {
            display: flex;
            gap: 5px;
        }

        .setting-option {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: var(--transition);
        }

        .setting-option:hover {
            background-color: #f0f0f0;
        }

        .setting-option.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 暗色模式 */
        body.dark-mode {
            background-color: var(--background-dark);
            color: var(--text-dark);
        }

        body.dark-mode .sidebar {
            background-color: #34495e;
            color: var(--text-dark);
        }

        body.dark-mode .sidebar h2,
        body.dark-mode .sidebar h3 {
            color: #ecf0f1;
            border-color: #3498db;
        }

        body.dark-mode .novel-list li,
        body.dark-mode .chapter-list li {
            border-color: #4a6278;
        }

        body.dark-mode .novel-list li:hover,
        body.dark-mode .chapter-list li:hover {
            background-color: rgba(52, 152, 219, 0.2);
        }

        body.dark-mode .novel-list li.active,
        body.dark-mode .chapter-list li.active {
            background-color: rgba(52, 152, 219, 0.3);
        }

        body.dark-mode .content {
            background-color: #2c3e50;
            box-shadow: none;
        }

        body.dark-mode .chapter-title {
            color: #ecf0f1;
        }

        body.dark-mode .chapter-content {
            background-color: #34495e;
            color: #ecf0f1;
        }

        body.dark-mode .novel-info {
            background-color: #34495e;
            color: #ecf0f1;
        }

        body.dark-mode .novel-info h3 {
            color: #ecf0f1;
        }

        body.dark-mode .theme-toggle {
            background-color: #34495e;
        }

        body.dark-mode .theme-toggle i {
            color: #ecf0f1;
        }

        body.dark-mode .reading-settings {
            background-color: #34495e;
            color: #ecf0f1;
        }

        body.dark-mode .reading-settings h4 {
            color: #ecf0f1;
            border-color: #4a6278;
        }

        body.dark-mode .setting-group label {
            color: #ecf0f1;
        }

        body.dark-mode .setting-option {
            border-color: #4a6278;
            color: #ecf0f1;
        }

        body.dark-mode .setting-option:hover {
            background-color: #4a6278;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                max-height: 300px;
            }

            .content {
                padding: 15px;
            }

            .chapter-content {
                font-size: 16px;
                padding: 15px;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        /* SVG封面 */
        .novel-cover {
            width: 100%;
            height: 200px;
            margin-bottom: 20px;
            border-radius: var(--border-radius);
            overflow: hidden;
            position: relative;
            box-shadow: var(--box-shadow);
        }

        .novel-cover svg {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 字体大小类 */
        .font-size-small {
            font-size: 16px !important;
        }

        .font-size-medium {
            font-size: 18px !important;
        }

        .font-size-large {
            font-size: 20px !important;
        }

        .font-size-xlarge {
            font-size: 22px !important;
        }

        /* 行高类 */
        .line-height-normal {
            line-height: 1.6 !important;
        }

        .line-height-comfortable {
            line-height: 1.8 !important;
        }

        .line-height-relaxed {
            line-height: 2.0 !important;
        }
    </style>
</head>
<body>
    <!-- 主题切换按钮 -->
    <div class="theme-toggle" id="theme-toggle" title="切换主题">
        <i class="fas fa-moon"></i>
    </div>

    <!-- 阅读设置面板 -->
    <div class="reading-settings" id="reading-settings">
        <h4>阅读设置</h4>

        <div class="setting-group">
            <label>字体大小</label>
            <div class="setting-options" id="font-size-options">
                <div class="setting-option" data-size="small">小</div>
                <div class="setting-option active" data-size="medium">中</div>
                <div class="setting-option" data-size="large">大</div>
                <div class="setting-option" data-size="xlarge">超大</div>
            </div>
        </div>

        <div class="setting-group">
            <label>行间距</label>
            <div class="setting-options" id="line-height-options">
                <div class="setting-option" data-height="normal">紧凑</div>
                <div class="setting-option active" data-height="comfortable">舒适</div>
                <div class="setting-option" data-height="relaxed">宽松</div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="sidebar">
            <h2><i class="fas fa-book-open"></i> 小说阅读器</h2>

            <div class="novel-section">
                <h3><i class="fas fa-list"></i> 小说列表</h3>
                <div id="loading-novels" class="loading"></div>
                <ul id="novel-list" class="novel-list"></ul>
            </div>

            <div class="chapter-section">
                <h3><i class="fas fa-bookmark"></i> 章节列表</h3>
                <div id="loading-chapters" class="loading"></div>
                <ul id="chapter-list" class="chapter-list"></ul>
            </div>
        </div>

        <div class="content">
            <div id="novel-info" class="novel-info">
                <h3>请选择小说</h3>
                <p><i class="fas fa-info-circle"></i> 从左侧列表中选择一本小说开始阅读。</p>
            </div>

            <h2 id="chapter-title" class="chapter-title">请选择章节</h2>

            <div id="loading-content" class="loading"></div>

            <img id="chapter-illustration" class="chapter-illustration" style="display: none;" />

            <div id="chapter-content" class="chapter-content">
                请从左侧列表中选择一个章节开始阅读。
            </div>

            <div class="navigation">
                <button id="prev-chapter" class="nav-button" disabled><i class="fas fa-arrow-left"></i> 上一章</button>
                <button id="next-chapter" class="nav-button" disabled>下一章 <i class="fas fa-arrow-right"></i></button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentNovel = null;
        let currentChapter = null;
        let chapters = [];
        let currentTheme = 'light';
        let currentFontSize = 'medium';
        let currentLineHeight = 'comfortable';

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 加载小说列表
            loadNovels();

            // 绑定导航按钮事件
            document.getElementById('prev-chapter').addEventListener('click', function() {
                navigateChapter('prev');
            });

            document.getElementById('next-chapter').addEventListener('click', function() {
                navigateChapter('next');
            });

            // 绑定主题切换按钮事件
            document.getElementById('theme-toggle').addEventListener('click', function() {
                toggleTheme();
            });

            // 绑定阅读设置事件
            document.getElementById('theme-toggle').addEventListener('dblclick', function(e) {
                e.preventDefault();
                toggleReadingSettings();
            });

            // 绑定字体大小选项事件
            const fontSizeOptions = document.querySelectorAll('#font-size-options .setting-option');
            fontSizeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const size = this.getAttribute('data-size');
                    setFontSize(size);

                    // 更新活动状态
                    fontSizeOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 绑定行高选项事件
            const lineHeightOptions = document.querySelectorAll('#line-height-options .setting-option');
            lineHeightOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const height = this.getAttribute('data-height');
                    setLineHeight(height);

                    // 更新活动状态
                    lineHeightOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 初始化页面效果
            initPageEffects();
        });

        // 加载小说列表
        function loadNovels() {
            document.getElementById('loading-novels').style.display = 'block';

            fetch('/api/novels')
                .then(response => response.json())
                .then(data => {
                    const novelList = document.getElementById('novel-list');
                    novelList.innerHTML = '';

                    if (data.novels.length === 0) {
                        novelList.innerHTML = '<li>没有找到小说</li>';
                    } else {
                        data.novels.forEach(novel => {
                            const li = document.createElement('li');
                            li.textContent = novel.name;
                            li.dataset.dir = novel.dir;
                            li.addEventListener('click', function() {
                                selectNovel(novel.dir);

                                // 移除其他小说的选中状态
                                document.querySelectorAll('#novel-list li').forEach(item => {
                                    item.classList.remove('active');
                                });

                                // 添加选中状态
                                this.classList.add('active');
                            });
                            novelList.appendChild(li);
                        });
                    }

                    document.getElementById('loading-novels').style.display = 'none';
                })
                .catch(error => {
                    console.error('加载小说列表时出错:', error);
                    document.getElementById('loading-novels').style.display = 'none';
                    document.getElementById('novel-list').innerHTML = '<li>加载小说列表时出错</li>';
                });
        }

        // 选择小说
        function selectNovel(novelDir) {
            currentNovel = novelDir;
            currentChapter = null;

            // 加载小说信息
            loadNovelInfo(novelDir);

            // 加载章节列表
            loadChapters(novelDir);
        }

        // 加载小说信息
        function loadNovelInfo(novelDir) {
            fetch(`/api/novel_info?dir=${encodeURIComponent(novelDir)}`)
                .then(response => response.json())
                .then(data => {
                    const novelInfo = document.getElementById('novel-info');
                    novelInfo.innerHTML = `
                        <h3>${data.title}</h3>
                        <p><i class="fas fa-user"></i> <strong>作者:</strong> ${data.author}</p>
                        <p><i class="fas fa-bookmark"></i> <strong>类型:</strong> ${data.genre}</p>
                        <p><i class="fas fa-feather-alt"></i> <strong>风格:</strong> ${data.style}</p>
                    `;
                })
                .catch(error => {
                    console.error('加载小说信息时出错:', error);
                    document.getElementById('novel-info').innerHTML = '<h3>加载小说信息时出错</h3>';
                });
        }

        // 加载章节列表
        function loadChapters(novelDir) {
            document.getElementById('loading-chapters').style.display = 'block';
            document.getElementById('chapter-list').innerHTML = '';

            fetch(`/api/chapters?dir=${encodeURIComponent(novelDir)}`)
                .then(response => response.json())
                .then(data => {
                    const chapterList = document.getElementById('chapter-list');
                    chapterList.innerHTML = '';

                    if (data.chapters.length === 0) {
                        chapterList.innerHTML = '<li>没有找到章节</li>';
                    } else {
                        chapters = data.chapters;

                        chapters.forEach(chapter => {
                            const li = document.createElement('li');
                            li.textContent = chapter.title;
                            li.dataset.file = chapter.file;
                            li.addEventListener('click', function() {
                                loadChapter(chapter.file);

                                // 移除其他章节的选中状态
                                document.querySelectorAll('#chapter-list li').forEach(item => {
                                    item.classList.remove('active');
                                });

                                // 添加选中状态
                                this.classList.add('active');
                            });
                            chapterList.appendChild(li);
                        });
                    }

                    document.getElementById('loading-chapters').style.display = 'none';
                })
                .catch(error => {
                    console.error('加载章节列表时出错:', error);
                    document.getElementById('loading-chapters').style.display = 'none';
                    document.getElementById('chapter-list').innerHTML = '<li>加载章节列表时出错</li>';
                });
        }

        // 加载章节内容
        function loadChapter(chapterFile) {
            currentChapter = chapterFile;

            document.getElementById('loading-content').style.display = 'block';
            document.getElementById('chapter-content').innerHTML = '';
            document.getElementById('chapter-illustration').style.display = 'none';

            fetch(`/api/chapter_content?file=${encodeURIComponent(chapterFile)}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('chapter-title').textContent = data.title;
                    document.getElementById('chapter-content').textContent = data.content;

                    // 显示插图
                    const illustration = document.getElementById('chapter-illustration');
                    if (data.illustration) {
                        // 将完整路径转换为相对URL
                        const imagePath = data.illustration;
                        const fileName = imagePath.split(/[\\\/]/).pop(); // 提取文件名
                        const relativeUrl = `/illustrations/${fileName}`;

                        console.log('Loading illustration:', relativeUrl);
                        illustration.src = relativeUrl;
                        illustration.style.display = 'block';
                    } else {
                        illustration.style.display = 'none';
                    }

                    // 更新导航按钮状态
                    updateNavigationButtons();

                    document.getElementById('loading-content').style.display = 'none';
                })
                .catch(error => {
                    console.error('加载章节内容时出错:', error);
                    document.getElementById('loading-content').style.display = 'none';
                    document.getElementById('chapter-content').textContent = '加载章节内容时出错';
                });
        }

        // 导航到上一章或下一章
        function navigateChapter(direction) {
            if (!currentChapter || chapters.length === 0) {
                return;
            }

            // 找到当前章节的索引
            const currentIndex = chapters.findIndex(chapter => chapter.file === currentChapter);
            if (currentIndex === -1) {
                return;
            }

            // 计算新的索引
            let newIndex;
            if (direction === 'prev') {
                newIndex = Math.max(0, currentIndex - 1);
            } else {
                newIndex = Math.min(chapters.length - 1, currentIndex + 1);
            }

            // 如果索引没有变化，说明已经是第一章或最后一章
            if (newIndex === currentIndex) {
                return;
            }

            // 加载新章节
            const newChapter = chapters[newIndex];
            loadChapter(newChapter.file);

            // 更新章节列表的选中状态
            document.querySelectorAll('#chapter-list li').forEach(item => {
                item.classList.remove('active');
                if (item.dataset.file === newChapter.file) {
                    item.classList.add('active');
                    // 滚动到可见区域
                    item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            });
        }

        // 更新导航按钮状态
        function updateNavigationButtons() {
            if (!currentChapter || chapters.length === 0) {
                document.getElementById('prev-chapter').disabled = true;
                document.getElementById('next-chapter').disabled = true;
                return;
            }

            const currentIndex = chapters.findIndex(chapter => chapter.file === currentChapter);
            if (currentIndex === -1) {
                document.getElementById('prev-chapter').disabled = true;
                document.getElementById('next-chapter').disabled = true;
                return;
            }

            document.getElementById('prev-chapter').disabled = currentIndex === 0;
            document.getElementById('next-chapter').disabled = currentIndex === chapters.length - 1;
        }

        // 切换主题
        function toggleTheme() {
            if (currentTheme === 'light') {
                document.body.classList.add('dark-mode');
                document.getElementById('theme-toggle').innerHTML = '<i class="fas fa-sun"></i>';
                currentTheme = 'dark';
            } else {
                document.body.classList.remove('dark-mode');
                document.getElementById('theme-toggle').innerHTML = '<i class="fas fa-moon"></i>';
                currentTheme = 'light';
            }
        }

        // 切换阅读设置面板
        function toggleReadingSettings() {
            const settings = document.getElementById('reading-settings');
            if (settings.style.display === 'none' || settings.style.display === '') {
                settings.style.display = 'block';
            } else {
                settings.style.display = 'none';
            }
        }

        // 设置字体大小
        function setFontSize(size) {
            const content = document.getElementById('chapter-content');

            // 移除所有字体大小类
            content.classList.remove('font-size-small', 'font-size-medium', 'font-size-large', 'font-size-xlarge');

            // 添加新的字体大小类
            content.classList.add(`font-size-${size}`);

            currentFontSize = size;
        }

        // 设置行高
        function setLineHeight(height) {
            const content = document.getElementById('chapter-content');

            // 移除所有行高类
            content.classList.remove('line-height-normal', 'line-height-comfortable', 'line-height-relaxed');

            // 添加新的行高类
            content.classList.add(`line-height-${height}`);

            currentLineHeight = height;
        }

        // 初始化页面效果
        function initPageEffects() {
            // 添加淡入效果
            document.body.classList.add('fade-in');

            // 移除淡入效果
            setTimeout(() => {
                document.body.classList.remove('fade-in');
            }, 500);

            // 初始化字体大小和行高
            setFontSize(currentFontSize);
            setLineHeight(currentLineHeight);

            // 添加设置按钮提示
            const themeToggle = document.getElementById('theme-toggle');
            themeToggle.title = '单击切换主题，双击打开设置';

            // 创建动态SVG封面
            createDynamicCover();
        }

        // 创建动态SVG封面
        function createDynamicCover() {
            // 如果已经选择了小说，为其创建动态封面
            if (currentNovel) {
                const novelInfo = document.getElementById('novel-info');

                // 创建封面容器
                const coverContainer = document.createElement('div');
                coverContainer.className = 'novel-cover';

                // 创建 SVG
                const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svg.setAttribute('viewBox', '0 0 100 50');
                svg.innerHTML = `
                    <defs>
                        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#3498db" stop-opacity="0.7" />
                            <stop offset="100%" stop-color="#2c3e50" stop-opacity="0.7" />
                        </linearGradient>
                    </defs>
                    <rect width="100" height="50" fill="url(#grad1)" />
                    <text x="50" y="25" font-family="Arial" font-size="5" fill="white" text-anchor="middle" dominant-baseline="middle">${novelInfo.querySelector('h3').textContent}</text>
                    <circle cx="80" cy="10" r="5" fill="#e74c3c">
                        <animate attributeName="r" values="5;6;5" dur="2s" repeatCount="indefinite" />
                    </circle>
                `;

                coverContainer.appendChild(svg);

                // 将封面插入到小说信息之前
                const content = document.querySelector('.content');
                content.insertBefore(coverContainer, novelInfo);
            }
        }
    </script>
</body>
</html>
"""

def start_server(novel_dir=None):
    """启动HTTP服务器"""
    global NOVEL_DIR
    if novel_dir:
        NOVEL_DIR = novel_dir

    # 创建HTTP服务器
    server = HTTPServer((SERVER_HOST, SERVER_PORT), NovelReaderHandler)
    logger.info(f"启动HTTP服务器: http://{SERVER_HOST}:{SERVER_PORT}")

    # 在新线程中启动服务器
    server_thread = threading.Thread(target=server.serve_forever)
    server_thread.daemon = True
    server_thread.start()

    # 返回服务器URL
    return f"http://{SERVER_HOST}:{SERVER_PORT}"

def launch_reader(novel_dir=None):
    """启动阅读器"""
    try:
        # 启动HTTP服务器
        url = start_server(novel_dir)

        # 等待服务器启动
        import time
        time.sleep(1)

        # 打开浏览器
        webbrowser.open(url)

        return url
    except Exception as e:
        logger.error(f"启动阅读器时出错: {e}")
        return f"启动阅读器时出错: {e}"

if __name__ == "__main__":
    # 如果直接运行此脚本，启动阅读器
    launch_reader()
