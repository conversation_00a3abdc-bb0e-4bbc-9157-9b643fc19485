"""
Embedding Manager Module

This module provides functions to manage embeddings using SiliconFlow's API.
"""

import os
import json
import requests
from typing import List, Dict, Any, Optional, Union
import numpy as np
from datetime import datetime

# Configuration
EMBEDDING_MODEL = os.environ.get("EMBEDDING_MODEL", "BAAI/bge-m3")
EMBEDDING_API_URL = os.environ.get("EMBEDDING_API_URL", "https://api.siliconflow.cn/v1/embeddings")
EMBEDDING_MODEL_ENABLED = True  # 启用嵌入模型

# Use a single SiliconFlow API key for both embedding and reranking
SILICONFLOW_API_KEY = os.environ.get("SILICONFLOW_API_KEY", "")

# If the specific keys are set, use them, otherwise fall back to the common key
EMBEDDING_API_KEY = os.environ.get("EMBEDDING_API_KEY", SILICONFLOW_API_KEY)
EMBEDDING_MODEL_NAME = os.environ.get("EMBEDDING_MODEL_NAME", "BAAI/bge-m3")
EMBEDDING_DIMENSION = 1024  # BGE-M3的嵌入维度
EMBEDDING_BATCH_SIZE = 32  # 批处理大小

# Reranking configuration
RERANKING_MODEL = os.environ.get("RERANKING_MODEL", "BAAI/bge-reranker-v2-m3")
RERANKING_API_URL = os.environ.get("RERANKING_API_URL", "https://api.siliconflow.cn/v1/rerank")
RERANKING_API_KEY = os.environ.get("RERANKING_API_KEY", SILICONFLOW_API_KEY)
RERANKING_MODEL_NAME = os.environ.get("RERANKING_MODEL_NAME", "BAAI/bge-reranker-v2-m3")
RERANKING_MODEL_ENABLED = True  # 启用重排序模型
RERANKING_TOP_K = 30  # 重排序后保留的文献数量

# Document processing configuration
DOC_PROCESSING = {
    "chunk_size": 512,  # Text chunk size for embeddings
    "chunk_overlap": 50,  # Overlap between chunks
    "max_chunks": 100,   # Maximum chunks per document
    "similarity_threshold": 0.75,  # Minimum similarity score for matches
    "top_k_matches": 5,   # Number of top matches to return
    "rerank_top_k": 10,   # Number of results to rerank
}

class EmbeddingManager:
    """Manager for embeddings and reranking"""
    
    def __init__(self, novel_dir: str):
        """
        Initialize embedding manager
        
        Args:
            novel_dir: Novel directory
        """
        self.novel_dir = novel_dir
        self.embedding_dir = os.path.join(novel_dir, "embeddings")
        os.makedirs(self.embedding_dir, exist_ok=True)
        
        self.embeddings = {}
        self.load_embeddings()
    
    def load_embeddings(self) -> bool:
        """
        Load embeddings from file
        
        Returns:
            Success flag
        """
        embeddings_file = os.path.join(self.embedding_dir, "embeddings.json")
        if os.path.exists(embeddings_file):
            try:
                with open(embeddings_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.embeddings = data
                return True
            except Exception as e:
                print(f"Error loading embeddings: {e}")
        return False
    
    def save_embeddings(self) -> bool:
        """
        Save embeddings to file
        
        Returns:
            Success flag
        """
        try:
            embeddings_file = os.path.join(self.embedding_dir, "embeddings.json")
            with open(embeddings_file, "w", encoding="utf-8") as f:
                json.dump(self.embeddings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"Error saving embeddings: {e}")
            return False
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        if not EMBEDDING_MODEL_ENABLED or not EMBEDDING_API_KEY:
            # Return a random embedding if the model is not enabled
            return [0.0] * EMBEDDING_DIMENSION
        
        try:
            # Check if we already have this embedding
            text_hash = hash(text)
            if str(text_hash) in self.embeddings:
                return self.embeddings[str(text_hash)]["embedding"]
            
            # Call SiliconFlow API
            headers = {
                "Authorization": f"Bearer {EMBEDDING_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": EMBEDDING_MODEL_NAME,
                "input": text
            }
            
            response = requests.post(
                EMBEDDING_API_URL,
                headers=headers,
                json=data
            )
            
            if response.status_code == 200:
                result = response.json()
                embedding = result["data"][0]["embedding"]
                
                # Save embedding
                self.embeddings[str(text_hash)] = {
                    "text": text[:100] + "..." if len(text) > 100 else text,
                    "embedding": embedding,
                    "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                self.save_embeddings()
                
                return embedding
            else:
                print(f"Error getting embedding: {response.status_code} {response.text}")
                # Return a zero embedding
                return [0.0] * EMBEDDING_DIMENSION
        except Exception as e:
            print(f"Error in get_embedding: {e}")
            # Return a zero embedding
            return [0.0] * EMBEDDING_DIMENSION
    
    def rerank(self, query: str, documents: List[str]) -> List[Dict[str, Any]]:
        """
        Rerank documents based on query
        
        Args:
            query: Query text
            documents: List of documents
            
        Returns:
            Reranked documents with scores
        """
        if not RERANKING_MODEL_ENABLED or not RERANKING_API_KEY or not documents:
            # Return documents with random scores if the model is not enabled
            return [{"document": doc, "score": 0.5} for doc in documents]
        
        try:
            # Call SiliconFlow API
            headers = {
                "Authorization": f"Bearer {RERANKING_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": RERANKING_MODEL_NAME,
                "query": query,
                "documents": documents,
                "top_k": min(RERANKING_TOP_K, len(documents))
            }
            
            response = requests.post(
                RERANKING_API_URL,
                headers=headers,
                json=data
            )
            
            if response.status_code == 200:
                result = response.json()
                reranked = []
                
                for item in result["results"]:
                    reranked.append({
                        "document": documents[item["document_idx"]],
                        "score": item["relevance_score"]
                    })
                
                return reranked
            else:
                print(f"Error reranking documents: {response.status_code} {response.text}")
                # Return documents with default scores
                return [{"document": doc, "score": 0.5} for doc in documents]
        except Exception as e:
            print(f"Error in rerank: {e}")
            # Return documents with default scores
            return [{"document": doc, "score": 0.5} for doc in documents]
    
    def search(self, query: str, documents: List[str]) -> List[Dict[str, Any]]:
        """
        Search documents using embeddings and reranking
        
        Args:
            query: Query text
            documents: List of documents
            
        Returns:
            Ranked documents with scores
        """
        if not documents:
            return []
        
        # Get query embedding
        query_embedding = self.get_embedding(query)
        
        # Get document embeddings
        doc_embeddings = []
        for doc in documents:
            doc_embeddings.append(self.get_embedding(doc))
        
        # Calculate similarity scores
        scores = []
        for i, doc_embedding in enumerate(doc_embeddings):
            # Calculate cosine similarity
            similarity = self.cosine_similarity(query_embedding, doc_embedding)
            scores.append({"document": documents[i], "score": similarity})
        
        # Sort by score
        scores.sort(key=lambda x: x["score"], reverse=True)
        
        # Get top k for reranking
        top_k = min(DOC_PROCESSING["rerank_top_k"], len(scores))
        top_docs = [scores[i]["document"] for i in range(top_k)]
        
        # Rerank top documents
        if RERANKING_MODEL_ENABLED and top_docs:
            reranked = self.rerank(query, top_docs)
            
            # Combine reranked with remaining
            if len(scores) > top_k:
                for i in range(top_k, len(scores)):
                    reranked.append(scores[i])
            
            return reranked
        else:
            return scores
    
    @staticmethod
    def cosine_similarity(a: List[float], b: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors
        
        Args:
            a: First vector
            b: Second vector
            
        Returns:
            Cosine similarity
        """
        if not a or not b:
            return 0.0
        
        try:
            a_array = np.array(a)
            b_array = np.array(b)
            
            dot_product = np.dot(a_array, b_array)
            norm_a = np.linalg.norm(a_array)
            norm_b = np.linalg.norm(b_array)
            
            if norm_a == 0 or norm_b == 0:
                return 0.0
            
            return dot_product / (norm_a * norm_b)
        except Exception as e:
            print(f"Error calculating cosine similarity: {e}")
            return 0.0

# Global embedding manager instance
_embedding_manager = None

def get_embedding_manager(novel_dir: str) -> EmbeddingManager:
    """
    Get or create an embedding manager for the given novel directory
    
    Args:
        novel_dir: Novel directory
        
    Returns:
        Embedding manager instance
    """
    global _embedding_manager
    
    if _embedding_manager is None or _embedding_manager.novel_dir != novel_dir:
        _embedding_manager = EmbeddingManager(novel_dir)
        print(f"Created new embedding manager for {novel_dir}")
    
    return _embedding_manager

def get_embedding(text: str, novel_dir: str) -> List[float]:
    """
    Get embedding for text
    
    Args:
        text: Text to embed
        novel_dir: Novel directory
        
    Returns:
        Embedding vector
    """
    manager = get_embedding_manager(novel_dir)
    return manager.get_embedding(text)

def search_documents(query: str, documents: List[str], novel_dir: str) -> List[Dict[str, Any]]:
    """
    Search documents using embeddings and reranking
    
    Args:
        query: Query text
        documents: List of documents
        novel_dir: Novel directory
        
    Returns:
        Ranked documents with scores
    """
    manager = get_embedding_manager(novel_dir)
    return manager.search(query, documents)

def rerank_documents(query: str, documents: List[str], novel_dir: str) -> List[Dict[str, Any]]:
    """
    Rerank documents based on query
    
    Args:
        query: Query text
        documents: List of documents
        novel_dir: Novel directory
        
    Returns:
        Reranked documents with scores
    """
    manager = get_embedding_manager(novel_dir)
    return manager.rerank(query, documents)
