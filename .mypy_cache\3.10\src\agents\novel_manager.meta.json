{"data_mtime": 1751380081, "dep_lines": [13, 14, 19, 24, 28, 39, 62, 70, 855, 859, 12, 6, 7, 8, 9, 10, 11, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 5, 10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["docx.enum.text", "docx.enum.section", "src.memory.memory_integration", "src.consistency.consistency_checker", "src.narrative.narrative_controller", "src.core.agent", "src.core.prompt_manager", "src.core.model_config", "src.agents.writing_pipeline", "src.agents.writing_agent", "docx.shared", "os", "json", "re", "typing", "datetime", "docx", "traceback", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "docx.api", "docx.document", "docx.enum", "docx.enum.base", "docx.styles", "docx.styles.style", "docx.text", "docx.text.paragraph", "enum", "io", "json.decoder", "json.encoder", "src.core", "src.memory", "src.memory.memory_manager", "types", "typing_extensions"], "hash": "268e91690bd346fca822b5e65ee0c26a8dbcee6e", "id": "src.agents.novel_manager", "ignore_all": true, "interface_hash": "b62fef93feb83cb8617cd665a1c736675f064acf", "mtime": 1745134393, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\storymc\\src\\agents\\novel_manager.py", "plugin_data": null, "size": 45976, "suppressed": [], "version_id": "1.15.0"}