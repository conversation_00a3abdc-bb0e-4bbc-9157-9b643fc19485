"""
大纲相关提示词管理模块
集中管理所有与大纲相关的提示词
"""

class OutlinePrompts:
    """大纲相关提示词"""
    
    @staticmethod
    def outline_generation_prompt(title, genre, style, scene, characters, num_chapters):
        """生成小说大纲提示词"""
        return f"""
请为一部小说创建详细的章节大纲。

小说基本信息:
- 标题：{title}
- 类型：{genre}
- 风格：{style}
- 场景设定：{scene}
- 主要角色：{characters}
- 计划章节数：{num_chapters}章

请在大纲开头提供以下内容：
1. 故事基本情节：简要描述整个故事的主要情节发展
2. 主要人物关系：详细说明主要角色之间的关系
3. 核心事件设定：列出故事中的关键事件和转折点

然后，请为每一章提供详细的大纲，包括：
1. 章节标题：简洁而有吸引力
2. 核心事件：本章发生的主要事件
3. 出场角色：本章中出现的角色
4. 与整体故事的关系：本章如何推动整体故事发展
5. 情感基调：本章的主要情感色彩

请确保章节之间有合理的连贯性和递进关系，整体故事有明确的起承转合。
每章大纲应包含足够的细节，以便能够据此写出完整的章节内容。

请使用以下格式：

# 故事基本情节
[详细描述]

# 主要人物关系
[详细描述]

# 核心事件设定
[详细描述]

# 第1章：[章节标题]
[详细大纲]

# 第2章：[章节标题]
[详细大纲]

...以此类推
"""

    @staticmethod
    def outline_continuation_prompt(existing_outline, current_chapters, additional_chapters):
        """生成大纲续写提示词"""
        return f"""
请基于现有的小说大纲，为后续章节创建详细的大纲。

现有大纲：
{existing_outline}

当前已完成的章节数：{current_chapters}章
需要新增的章节数：{additional_chapters}章

请为第{current_chapters+1}章到第{current_chapters+additional_chapters}章创建详细的大纲，确保与现有大纲的风格和内容保持一致，并自然地延续故事情节。

每一章的大纲应包括：
1. 章节标题：简洁而有吸引力
2. 核心事件：本章发生的主要事件
3. 出场角色：本章中出现的角色
4. 与整体故事的关系：本章如何推动整体故事发展
5. 情感基调：本章的主要情感色彩

请确保新增章节与已有章节之间有合理的连贯性和递进关系，并且能够自然地推动故事向结局发展。

请使用与现有大纲相同的格式：

# 第{current_chapters+1}章：[章节标题]
[详细大纲]

# 第{current_chapters+2}章：[章节标题]
[详细大纲]

...以此类推
"""

    @staticmethod
    def story_basics_generation_prompt(outline_content):
        """生成故事基本信息提示词"""
        return f"""
请基于以下小说大纲，生成故事基本情节、主要人物关系和核心事件设定。

小说大纲：
{outline_content}

请生成以下内容：
1. 故事基本情节：简要描述整个故事的主要情节发展
2. 主要人物关系：详细说明主要角色之间的关系
3. 核心事件设定：列出故事中的关键事件和转折点

请使用以下格式：

# 故事基本情节
[详细描述]

# 主要人物关系
[详细描述]

# 核心事件设定
[详细描述]
"""
