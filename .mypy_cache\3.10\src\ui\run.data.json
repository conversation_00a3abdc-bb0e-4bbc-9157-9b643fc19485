{".class": "MypyFile", "_fullname": "src.ui.run", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AGENT_REGISTRY": {".class": "SymbolTableNode", "cross_ref": "src.main.AGENT_REGISTRY", "kind": "Gdef"}, "AI_MODEL_CONFIG": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.AI_MODEL_CONFIG", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DEFAULT_MODEL_NAMES": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.DEFAULT_MODEL_NAMES", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExpansionAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.expansion_agent.ExpansionAgent", "kind": "Gdef"}, "IllustrationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.illustration_agent.IllustrationAgent", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NEW_SYSTEM_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.ui.run.NEW_SYSTEM_AVAILABLE", "name": "NEW_SYSTEM_AVAILABLE", "type": "builtins.bool"}}, "NOVEL_CREATION_STATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.ui.run.NOVEL_CREATION_STATE", "name": "NOVEL_CREATION_STATE", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "NovelManager": {".class": "SymbolTableNode", "cross_ref": "src.agents.novel_manager.NovelManager", "kind": "Gdef"}, "OptimizationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.optimization_agent.OptimizationAgent", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PolishingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.polishing_agent.PolishingAgent", "kind": "Gdef"}, "ReviewAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.review_agent.ReviewAgent", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WritingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_agent.WritingAgent", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.run.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.run.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.run.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.run.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.run.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.ui.run.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_custom_model": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.add_custom_model", "kind": "Gdef"}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "continue_novel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["novel_dir", "num_chapters", "progress_callback", "illustration_style", "story_requirements", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.run.continue_novel", "name": "continue_novel", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["novel_dir", "num_chapters", "progress_callback", "illustration_style", "story_requirements", "language"], "arg_types": ["builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "continue_novel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_web_ui": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.run.create_web_ui", "name": "create_web_ui", "type": null}}, "create_writing_pipeline": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.create_writing_pipeline", "kind": "Gdef"}, "current_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.ui.run.current_dir", "name": "current_dir", "type": "builtins.str"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "delete_custom_model": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.delete_custom_model", "kind": "Gdef"}, "get_agents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.run.get_agents", "name": "get_agents", "type": null}}, "get_custom_models": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.get_custom_models", "kind": "Gdef"}, "gr": {".class": "SymbolTableNode", "cross_ref": "gradio", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "launch_simple_reader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["novel_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.run.launch_simple_reader", "name": "launch_simple_reader", "type": null}}, "load_custom_models": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.load_custom_models", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.run.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_arguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.run.parse_arguments", "name": "parse_arguments", "type": null}}, "register_writing_agents": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.register_writing_agents", "kind": "Gdef"}, "root_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.ui.run.root_dir", "name": "root_dir", "type": "builtins.str"}}, "run_pipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["title", "genre", "style", "scene", "characters", "num_chapters", "output_dir", "save_intermediates", "progress", "use_new_system", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.ui.run.run_pipeline", "name": "run_pipeline", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["title", "genre", "style", "scene", "characters", "num_chapters", "output_dir", "save_intermediates", "progress", "use_new_system", "language"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.str", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_pipeline", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_custom_models": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.save_custom_models", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "update_agent_model": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.update_agent_model", "kind": "Gdef"}, "webbrowser": {".class": "SymbolTableNode", "cross_ref": "webbrowser", "kind": "Gdef"}}, "path": "E:\\storymc\\src\\ui\\run.py"}