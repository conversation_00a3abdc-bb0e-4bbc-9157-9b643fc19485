"""
小说生成模块
用于生成完整的小说，包括大纲和章节内容
"""

import os
import re
import json
import time
from typing import Dict, List, Optional, Tuple, Any, Callable

# 导入章节生成器
try:
    from src.chapter_generator import generate_chapter, ChapterGenerator
except ImportError:
    # 如果导入失败，创建简单的替代函数
    def generate_chapter(chapter_num, chapter_outline, prev_chapter_content=None, next_chapter_outline=None, novel_info=None):
        return f"第{chapter_num}章\n\n这是第{chapter_num}章的内容。\n\n章节结束。"

    class ChapterGenerator:
        def extract_outline_elements(self, chapter_outline):
            return {"title": "", "core_events": "", "characters": "", "story_relation": "", "emotional_tone": ""}

# 导入过渡处理模块
try:
    from src.transition_handler import create_transition, ensure_chapter_connection, create_chapter_ending
except ImportError:
    # 如果导入失败，创建简单的替代函数
    def create_transition(prev_chapter_content, next_chapter_outline):
        return ""

    def ensure_chapter_connection(prev_chapter, next_chapter):
        return prev_chapter, next_chapter

    def create_chapter_ending(chapter_content, next_chapter_outline=None):
        return chapter_content

# 导入中国文化元素模块
try:
    from src.chinese_cultural_elements import enrich_novel_with_chinese_elements
except ImportError:
    # 如果导入失败，创建简单的替代函数
    def enrich_novel_with_chinese_elements(chapter_content, genre, chapter_num):
        return chapter_content

class NovelGenerator:
    """小说生成器，用于生成完整的小说"""

    def __init__(self):
        self.chapter_generator = ChapterGenerator()

    def create_outline(self, title: str, genre: str, style: str, scene: str, characters: str, chapters: int) -> str:
        """
        创建小说大纲

        参数:
            title: 小说标题
            genre: 类型
            style: 风格
            scene: 场景
            characters: 角色
            chapters: 章节数

        返回:
            小说大纲
        """
        # 创建基本信息
        outline = f"""# 故事基本情节
《{title}》是一部{genre}作品，采用{style}的写作风格。
故事发生在{scene}。

# 主要人物关系
主角：{characters}

# 核心事件设定
这是一个关于{characters}的故事。
"""

        # 添加章节大纲
        for i in range(1, chapters + 1):
            if i == 1:
                chapter_title = f"开篇章节"
                core_events = f"介绍了{characters}的背景和{scene}的环境。主角{characters}面临着一个重要的选择。"
                chapter_characters = f"{characters}"
                story_relation = f"建立故事背景和主角形象，引入故事的主要冲突。"
                emotional_tone = f"好奇、期待、稳定中带有一丝紧张。"
            elif i % 2 == 0:
                chapter_title = f"冲突与行动"
                core_events = f"{characters}遇到了一个重大的障碍。场景从{scene}转移到了一个新的地点。冲突开始升级，紧张感增加。"
                chapter_characters = f"{characters}"
                story_relation = f"推动情节发展，加深故事冲突，展示主角的成长。"
                emotional_tone = f"紧张、激烈、决然。"
            else:
                chapter_title = f"探索与发现"
                core_events = f"{characters}发现了一个重要的线索。{scene}的环境中隐藏着一个秘密。故事的节奏变得更加缓慢，但充满了悬念。"
                chapter_characters = f"{characters}"
                story_relation = f"揭示故事中的重要秘密，为后续情节做链接。"
                emotional_tone = f"悬疑、好奇、紧张中带有发现的喜悦。"

            outline += f"""
#### **第{i}章：{chapter_title}**
1. **章节标题**：{chapter_title}
2. **本章核心事件**：{core_events}
3. **本章出场角色**：{chapter_characters}
4. **本章与整体故事的关系**：{story_relation}
5. **本章情感基调**：{emotional_tone}
"""

        return outline

    def generate_novel(self, title: str, genre: str, style: str, scene: str, characters: str,
                      chapters: int, output_dir: str, progress_callback: Callable = None) -> Tuple[bool, str]:
        """
        生成完整的小说

        参数:
            title: 小说标题
            genre: 类型
            style: 风格
            scene: 场景
            characters: 角色
            chapters: 章节数
            output_dir: 输出目录
            progress_callback: 进度回调函数

        返回:
            (成功标志, 结果信息)
        """
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)

            # 创建子目录
            chapters_dir = os.path.join(output_dir, "chapters")
            illustrations_dir = os.path.join(output_dir, "illustrations")
            os.makedirs(chapters_dir, exist_ok=True)
            os.makedirs(illustrations_dir, exist_ok=True)

            # 创建小说状态文件
            state_file = os.path.join(output_dir, "novel_state.json")
            novel_state = {
                "title": title,
                "genre": genre,
                "style": style,
                "scene": scene,
                "characters": characters,
                "total_chapters": chapters,
                "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                "chapters_status": {}
            }

            # 创建大纲
            outline = self.create_outline(title, genre, style, scene, characters, chapters)
            outline_file = os.path.join(output_dir, "outline.txt")
            with open(outline_file, "w", encoding="utf-8") as f:
                f.write(outline)

            # 保存小说状态
            with open(state_file, "w", encoding="utf-8") as f:
                json.dump(novel_state, f, ensure_ascii=False, indent=2)

            # 如果有进度回调函数，通知创建完成
            if progress_callback:
                progress_callback(f"小说《{title}》的大纲和状态文件已创建")

            # 生成章节内容
            novel_info = {
                "title": title,
                "genre": genre,
                "style": style,
                "scene": scene,
                "characters": characters
            }

            # 解析大纲，提取章节信息
            chapter_outlines = {}
            chapter_pattern = re.compile(r'####\s*\*\*第(\d+)章[：:](.+?)\*\*\s*([\s\S]+?)(?=####\s*\*\*第\d+章|$)', re.DOTALL)
            matches = chapter_pattern.findall(outline)

            for num, title, content in matches:
                chapter_num = int(num)
                chapter_outlines[chapter_num] = f"#### **第{chapter_num}章：{title}**{content}"

            # 生成每一章内容
            prev_chapter_content = None

            for i in range(1, chapters + 1):
                if progress_callback:
                    progress_callback(f"正在生成第{i}章...")

                # 获取当前章节大纲
                chapter_outline = chapter_outlines.get(i, f"#### **第{i}章**\n1. **章节标题**：第{i}章\n2. **本章核心事件**：这是第{i}章的内容。")

                # 获取下一章大纲（如果有）
                next_chapter_outline = chapter_outlines.get(i+1, None)

                # 生成章节内容
                chapter_content = generate_chapter(
                    chapter_num=i,
                    chapter_outline=chapter_outline,
                    prev_chapter_content=prev_chapter_content,
                    next_chapter_outline=next_chapter_outline,
                    novel_info=novel_info
                )

                # 保存章节内容
                chapter_file = os.path.join(chapters_dir, f"chapter_{i}_final.txt")
                with open(chapter_file, "w", encoding="utf-8") as f:
                    f.write(chapter_content)

                # 更新小说状态
                novel_state["chapters_status"][str(i)] = "completed"
                novel_state["updated_at"] = time.strftime("%Y-%m-%d %H:%M:%S")

                with open(state_file, "w", encoding="utf-8") as f:
                    json.dump(novel_state, f, ensure_ascii=False, indent=2)

                # 保存当前章节内容作为下一章的参考
                prev_chapter_content = chapter_content

                if progress_callback:
                    progress_callback(f"第{i}章已生成并保存")

            # 生成Word文档
            try:
                from src.document_generator import save_novel_to_word
                word_path = save_novel_to_word(output_dir)
                if progress_callback:
                    progress_callback(f"小说已保存为Word文档：{word_path}")
            except ImportError:
                if progress_callback:
                    progress_callback("无法导入文档生成模块，跳过Word文档生成")

            return True, f"小说《{title}》已成功生成，共{chapters}章"

        except Exception as e:
            import traceback
            error_message = f"生成小说时出错：{e}\n{traceback.format_exc()}"
            if progress_callback:
                progress_callback(f"错误：{error_message}")
            return False, error_message

    def continue_novel(self, novel_dir: str, chapters: int, progress_callback: Callable = None, illustration_style: str = "写实风格") -> Tuple[bool, str]:
        """
        继续写作小说

        参数:
            novel_dir: 小说目录
            chapters: 要继续写作的章节数
            progress_callback: 进度回调函数
            illustration_style: 插图风格

        返回:
            (成功标志, 结果信息)
        """
        try:
            # 读取小说状态
            state_file = os.path.join(novel_dir, "novel_state.json")
            if not os.path.exists(state_file):
                return False, f"找不到小说状态文件：{state_file}"

            with open(state_file, "r", encoding="utf-8") as f:
                novel_state = json.load(f)

            # 创建子目录
            chapters_dir = os.path.join(novel_dir, "chapters")
            illustrations_dir = os.path.join(novel_dir, "illustrations")
            os.makedirs(chapters_dir, exist_ok=True)
            os.makedirs(illustrations_dir, exist_ok=True)

            # 读取大纲
            outline_file = os.path.join(novel_dir, "outline.txt")
            if os.path.exists(outline_file):
                with open(outline_file, "r", encoding="utf-8") as f:
                    outline_content = f.read()
            else:
                return False, f"找不到大纲文件：{outline_file}"

            # 解析大纲，提取章节信息
            chapter_outlines = {}
            chapter_pattern = re.compile(r'####\s*\*\*第(\d+)章[：:](.+?)\*\*\s*([\s\S]+?)(?=####\s*\*\*第\d+章|$)', re.DOTALL)
            matches = chapter_pattern.findall(outline_content)

            for num, title, content in matches:
                chapter_num = int(num)
                chapter_outlines[chapter_num] = f"#### **第{chapter_num}章：{title}**{content}"

            # 计算开始章节
            start_chapter = 1
            if "chapters_status" in novel_state:
                completed_chapters = [int(ch) for ch, status in novel_state["chapters_status"].items()
                                     if status == "completed"]
                if completed_chapters:
                    start_chapter = max(completed_chapters) + 1

            # 检查是否需要追加大纲
            max_chapter_num = max(chapter_outlines.keys()) if chapter_outlines else 0
            end_chapter = start_chapter + chapters - 1

            if end_chapter > max_chapter_num:
                # 需要追加大纲
                if progress_callback:
                    progress_callback(f"需要追加大纲，当前最大章节为{max_chapter_num}，需要生成到第{end_chapter}章")

                # 获取小说信息
                title = novel_state.get("title", "未知标题")
                genre = novel_state.get("genre", "现代小说")
                style = novel_state.get("style", "写实主义")
                scene = novel_state.get("scene", "城市")
                characters = novel_state.get("characters", "主角")

                # 追加大纲
                new_outline = outline_content
                for i in range(max_chapter_num + 1, end_chapter + 1):
                    if i % 2 == 0:
                        chapter_title = f"冲突与行动"
                        core_events = f"{characters}遇到了一个重大的障碍。场景从{scene}转移到了一个新的地点。冲突开始升级，紧张感增加。"
                        chapter_characters = f"{characters}"
                        story_relation = f"推动情节发展，加深故事冲突，展示主角的成长。"
                        emotional_tone = f"紧张、激烈、决然。"
                    else:
                        chapter_title = f"探索与发现"
                        core_events = f"{characters}发现了一个重要的线索。{scene}的环境中隐藏着一个秘密。故事的节奏变得更加缓慢，但充满了悬念。"
                        chapter_characters = f"{characters}"
                        story_relation = f"揭示故事中的重要秘密，为后续情节做链接。"
                        emotional_tone = f"悬疑、好奇、紧张中带有发现的喜悦。"

                    new_chapter_outline = f"""
#### **第{i}章：{chapter_title}**
1. **章节标题**：{chapter_title}
2. **本章核心事件**：{core_events}
3. **本章出场角色**：{chapter_characters}
4. **本章与整体故事的关系**：{story_relation}
5. **本章情感基调**：{emotional_tone}
"""
                    new_outline += new_chapter_outline
                    chapter_outlines[i] = f"#### **第{i}章：{chapter_title}**{new_chapter_outline}"

                # 保存更新后的大纲
                with open(outline_file, "w", encoding="utf-8") as f:
                    f.write(new_outline)

                if progress_callback:
                    progress_callback(f"大纲已更新，添加了{end_chapter - max_chapter_num}章")

            # 准备小说信息
            novel_info = {
                "title": novel_state.get("title", "未知标题"),
                "genre": novel_state.get("genre", "现代小说"),
                "style": novel_state.get("style", "写实主义"),
                "scene": novel_state.get("scene", "城市"),
                "characters": novel_state.get("characters", "主角")
            }

            # 获取前一章内容（如果有）
            prev_chapter_content = None
            if start_chapter > 1:
                prev_chapter_file = os.path.join(chapters_dir, f"chapter_{start_chapter-1}_final.txt")
                if os.path.exists(prev_chapter_file):
                    with open(prev_chapter_file, "r", encoding="utf-8") as f:
                        prev_chapter_content = f.read()

            # 生成章节内容
            for i in range(start_chapter, start_chapter + chapters):
                if progress_callback:
                    progress_callback(f"正在生成第{i}章...")

                # 获取当前章节大纲
                chapter_outline = chapter_outlines.get(i, f"#### **第{i}章**\n1. **章节标题**：第{i}章\n2. **本章核心事件**：这是第{i}章的内容。")

                # 获取下一章大纲（如果有）
                next_chapter_outline = chapter_outlines.get(i+1, None)

                # 生成章节内容
                chapter_content = generate_chapter(
                    chapter_num=i,
                    chapter_outline=chapter_outline,
                    prev_chapter_content=prev_chapter_content,
                    next_chapter_outline=next_chapter_outline,
                    novel_info=novel_info
                )

                # 保存章节内容
                chapter_file = os.path.join(chapters_dir, f"chapter_{i}_final.txt")
                with open(chapter_file, "w", encoding="utf-8") as f:
                    f.write(chapter_content)

                # 更新小说状态
                if "chapters_status" not in novel_state:
                    novel_state["chapters_status"] = {}
                novel_state["chapters_status"][str(i)] = "completed"
                novel_state["updated_at"] = time.strftime("%Y-%m-%d %H:%M:%S")

                with open(state_file, "w", encoding="utf-8") as f:
                    json.dump(novel_state, f, ensure_ascii=False, indent=2)

                # 保存当前章节内容作为下一章的参考
                prev_chapter_content = chapter_content

                if progress_callback:
                    progress_callback(f"第{i}章已生成并保存")

            # 生成Word文档
            try:
                from src.document_generator import save_novel_to_word
                word_path = save_novel_to_word(novel_dir)
                if progress_callback:
                    progress_callback(f"小说已保存为Word文档：{word_path}")
            except ImportError:
                if progress_callback:
                    progress_callback("无法导入文档生成模块，跳过Word文档生成")

            return True, f"小说《{novel_state.get('title', '未知标题')}》已成功继续写作，从第{start_chapter}章开始，共{chapters}章"

        except Exception as e:
            import traceback
            error_message = f"继续写作小说时出错：{e}\n{traceback.format_exc()}"
            if progress_callback:
                progress_callback(f"错误：{error_message}")
            return False, error_message

def generate_novel(title: str, genre: str, style: str, scene: str, characters: str,
                  chapters: int, output_dir: str, progress_callback: Callable = None) -> Tuple[bool, str]:
    """
    生成小说的便捷函数

    参数:
        title: 小说标题
        genre: 类型
        style: 风格
        scene: 场景
        characters: 角色
        chapters: 章节数
        output_dir: 输出目录
        progress_callback: 进度回调函数

    返回:
        (成功标志, 结果信息)
    """
    generator = NovelGenerator()
    return generator.generate_novel(title, genre, style, scene, characters, chapters, output_dir, progress_callback)

def continue_novel(novel_dir: str, chapters: int, progress_callback: Callable = None, illustration_style: str = "写实风格") -> Tuple[bool, str]:
    """
    继续写作小说的便捷函数

    参数:
        novel_dir: 小说目录
        chapters: 要继续写作的章节数
        progress_callback: 进度回调函数
        illustration_style: 插图风格

    返回:
        (成功标志, 结果信息)
    """
    generator = NovelGenerator()
    return generator.continue_novel(novel_dir, chapters, progress_callback, illustration_style)

if __name__ == "__main__":
    # 测试代码
    def progress_callback(message):
        print(message)

    # 测试生成小说
    success, message = generate_novel(
        title="测试小说",
        genre="现代都市",
        style="写实主义",
        scene="繁华都市",
        characters="李明",
        chapters=3,
        output_dir="output/测试小说",
        progress_callback=progress_callback
    )

    print(f"生成小说结果：{success}, {message}")
