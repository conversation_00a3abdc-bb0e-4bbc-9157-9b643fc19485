#!/usr/bin/env python3
"""
运行脚本，用于启动中文小说写作流水线
"""

import os
import sys
import argparse
import json
import time
from datetime import datetime
import traceback
import webbrowser
import threading
import gradio as gr
from typing import Dict, List, Any, Optional, Union, Tuple

# 使用新系统，启用高级功能
NEW_SYSTEM_AVAILABLE = True
print("使用新系统生成小说，启用三层记忆管理、一致性检查和叙事控制")

# 小说创建状态，用于在不同函数之间共享小说目录
NOVEL_CREATION_STATE = {"novel_dir": ""}

# 导入旧系统组件
import sys
import os

# 添加项目根目录到Python路径
# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录（当前目录的上两级）
root_dir = os.path.dirname(os.path.dirname(current_dir))
# 将项目根目录添加到Python路径
sys.path.insert(0, root_dir)

try:
    from src.main import AGENT_REGISTRY
except ImportError:
    print("警告: 无法导入AGENT_REGISTRY，使用简单替代")
    from src.agents.writing_agents import AGENT_REGISTRY
from src.agents.writing_agents import (
    register_writing_agents,
    create_writing_pipeline,
    NovelManager,
    WritingAgent,
    PolishingAgent,
    ExpansionAgent,
    ReviewAgent,
    OptimizationAgent,
    IllustrationAgent
)

# 导入模型配置
from src.core.model_config import (
    AI_MODEL_CONFIG,
    DEFAULT_MODEL_NAMES,
    add_custom_model,
    get_custom_models,
    delete_custom_model,
    save_custom_models,
    load_custom_models,
    update_agent_model
)

# 全局变量，用于跟踪小说创作状态
NOVEL_CREATION_STATE = {
    "is_running": False,
    "should_pause": False,
    "novel_dir": "",
    "current_chapter": 0,
    "total_chapters": 0,
    "creation_thread": None
}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="中文小说写作流水线")

    parser.add_argument("--title", type=str, default="无题", help="小说标题")
    parser.add_argument("--genre", type=str, default="现代小说", help="小说类型")
    parser.add_argument("--style", type=str, default="写实主义", help="写作风格")
    parser.add_argument("--scene", type=str, default="城市", help="场景设定")
    parser.add_argument("--characters", type=str, default="一位年轻人", help="角色设定")
    parser.add_argument("--chapters", type=int, default=3, help="章节数量")
    parser.add_argument("--output-dir", type=str, default="output", help="输出目录")
    parser.add_argument("--save-intermediates", action="store_true", help="是否保存中间结果")
    parser.add_argument("--web", action="store_true", help="启动Web界面")

    return parser.parse_args()

def run_pipeline(title: str, genre: str, style: str, scene: str, characters: str,
                num_chapters: int, output_dir: str = "output",
                save_intermediates: bool = False, progress=None, use_new_system: bool = True,
                language: str = "中文"):
    """
    运行写作流水线
    参数:
        title: 标题
        genre: 类型
        style: 风格
        scene: 场景
        characters: 角色
        num_chapters: 章节数量
        output_dir: 输出目录
        save_intermediates: 是否保存中间结果
        progress: 进度回调函数
        use_new_system: 是否使用新系统
        language: 写作语言，如中文、英文等
    返回:
        生成的小说对象和输出目录
    """
    # 加载智能体配置
    config_file = "agent_config.json"

    if os.path.exists(config_file):
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)

            # 更新智能体模型
            update_agent_model("WritingAgent", config.get("writing_model", AI_MODEL_CONFIG["agent_models"]["WritingAgent"]))
            update_agent_model("PolishingAgent", config.get("polishing_model", AI_MODEL_CONFIG["agent_models"]["PolishingAgent"]))
            update_agent_model("IllustrationAgent", config.get("illustration_model", AI_MODEL_CONFIG["agent_models"]["IllustrationAgent"]))

            # 获取插图风格
            new_style = config.get("illustration_style")
            if new_style:
                illustration_style = new_style

            # 更新模型温度
            writing_model = config.get("writing_model")
            writing_temperature = config.get("writing_temperature", 0.7)
            if writing_model and writing_model in AI_MODEL_CONFIG["models"]:
                AI_MODEL_CONFIG["models"][writing_model]["temperature"] = writing_temperature
        except Exception as e:
            print(f"加载智能体配置时出错: {e}")
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    log_messages = []

    def log(message):
        log_messages.append(message)
        if progress:
            progress(message)
        else:
            print(message)

    log(f"=== 中文小说写作流水线启动 ===")
    log(f"标题: {title}")
    log(f"类型: {genre}")
    log(f"风格: {style}")
    log(f"场景: {scene}")
    log(f"角色: {characters}")
    log(f"章节数量: {num_chapters}")
    log(f"输出目录: {output_dir}")
    log(f"保存中间结果: {'是' if save_intermediates else '否'}")
    log(f"使用新系统: {'是' if use_new_system and NEW_SYSTEM_AVAILABLE else '否'}")
    log("=" * 40)

    # 使用新系统生成小说
    if use_new_system and NEW_SYSTEM_AVAILABLE:
        log(f"使用新系统生成小说，启用三层记忆管理、一致性检查和叙事控制...")
        # 初始化MCP客户端
        try:
            from src.utils.mcp_client import get_mcp_client
            mcp_client = get_mcp_client()
            log(f"成功初始化MCP客户端")
        except Exception as e:
            log(f"初始化MCP客户端时出错: {e}")
    else:
        log(f"使用基础系统生成小说...")

    # 注册写作智能体
    register_writing_agents()

    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # 检查是否已经在输出目录中
    if os.path.basename(output_dir) == title or os.path.basename(os.path.dirname(output_dir)) == title:
        # 如果已经在小说目录中，直接使用该目录
        novel_output_dir = output_dir
        log(f"继续在现有目录中写作: {novel_output_dir}")
    else:
        # 否则创建新目录
        novel_output_dir = os.path.join(output_dir, f"{title}_{timestamp}")
        os.makedirs(novel_output_dir, exist_ok=True)
        log(f"创建新目录: {novel_output_dir}")

    # 打印目录结构信息，便于调试
    log(f"当前目录结构: {novel_output_dir}")
    log(f"目录父级: {os.path.dirname(novel_output_dir)}")
    log(f"目录名称: {os.path.basename(novel_output_dir)}")

    try:
        # 创建写作流水线
        pipeline = create_writing_pipeline()

        # 创建章节上下文
        chapter_context = {
            "title": title,
            "genre": genre,
            "style": style,
            "scene": scene,
            "characters": characters,
            "chapter_number": 1,  # 开始从第一章
            "total_chapters": num_chapters,
            "output_dir": novel_output_dir,
            "save_intermediates": save_intermediates,
            "language": language  # 添加语言参数
        }

        # 运行写作流水线
        from src.agents.writing_pipeline import run_pipeline as run_writing_pipeline
        success, content = run_writing_pipeline(pipeline, chapter_context)

        # 创建小说对象
        from src.agents.novel_manager import Novel, Chapter
        novel = Novel(title=title, genre=genre, style=style)

        # 添加章节
        chapter = Chapter(number=1, title=f"第1章", content=content)
        novel.add_chapter(chapter)

        log(f"\n=== 写作流水线完成 ===")
        log(f"小说框架保存在: {os.path.join(novel_output_dir, 'chapter_outline.txt')}")
        log(f"最终文档保存在: {os.path.join(novel_output_dir, f'{title}.docx')}")

        return novel, novel_output_dir, "\n".join(log_messages)
    except Exception as e:
        error_msg = f"写作流水线执行出错: {e}"
        log(error_msg)
        log(traceback.format_exc())
        return None, novel_output_dir, "\n".join(log_messages)

def get_agents():
    """获取所有需要的智能体实例"""
    writing_agent = AGENT_REGISTRY.get_agents_by_type(WritingAgent)[0]
    polishing_agent = AGENT_REGISTRY.get_agents_by_type(PolishingAgent)[0]
    expansion_agent = AGENT_REGISTRY.get_agents_by_type(ExpansionAgent)[0]
    review_agent = AGENT_REGISTRY.get_agents_by_type(ReviewAgent)[0]
    optimization_agent = AGENT_REGISTRY.get_agents_by_type(OptimizationAgent)[0]
    illustration_agent = AGENT_REGISTRY.get_agents_by_type(IllustrationAgent)[0]
    return (
        writing_agent,
        polishing_agent,
        expansion_agent,
        review_agent,
        optimization_agent,
        illustration_agent
    )

def continue_novel(novel_dir: str, num_chapters: int = 1, progress_callback=None, illustration_style="写实风格", story_requirements="", language="中文"):
    """继续写作小说"""
    if not os.path.exists(novel_dir):
        return False, f"错误: 目录不存在: {novel_dir}"

    # 注册写作智能体
    register_writing_agents()

    # 创建写作流水线
    pipeline = create_writing_pipeline()

    # 检查大纲文件
    outline_file = os.path.join(novel_dir, "novel_outline.txt")
    chapter_outline_file = os.path.join(novel_dir, "chapter_outline.txt")

    # 如果在指定目录中找不到大纲文件，尝试在output目录下查找
    if not (os.path.exists(outline_file) or os.path.exists(chapter_outline_file)):
        # 检查novel_dir是否是完整路径还是只是目录名
        if os.path.dirname(novel_dir) == "":
            # 如果只是目录名，尝试在output目录下查找
            alternative_outline_file = os.path.join("output", novel_dir, "novel_outline.txt")
            alternative_chapter_outline_file = os.path.join("output", novel_dir, "chapter_outline.txt")

            if os.path.exists(alternative_outline_file):
                outline_file = alternative_outline_file
                novel_dir = os.path.join("output", novel_dir)
                print(f"在output目录下找到小说大纲文件: {outline_file}")
            elif os.path.exists(alternative_chapter_outline_file):
                chapter_outline_file = alternative_chapter_outline_file
                novel_dir = os.path.join("output", novel_dir)
                print(f"在output目录下找到小说大纲文件: {chapter_outline_file}")

    # 如果大纲文件不存在，返回警告
    if not (os.path.exists(outline_file) or os.path.exists(chapter_outline_file)):
        if progress_callback:
            progress_callback(f"警告: 未找到小说大纲文件，可能导致继续写作出现问题")

    # 尝试从小说目录中获取小说信息
    novel_info_path = os.path.join(novel_dir, "novel_info.json")

    # 如果在指定目录中找不到novel_info.json，尝试在output目录下查找
    if not os.path.exists(novel_info_path):
        # 检查novel_dir是否是完整路径还是只是目录名
        if os.path.dirname(novel_dir) == "":
            # 如果只是目录名，尝试在output目录下查找
            alternative_path = os.path.join("output", novel_dir, "novel_info.json")
            if os.path.exists(alternative_path):
                novel_info_path = alternative_path
                novel_dir = os.path.join("output", novel_dir)
                print(f"在output目录下找到小说信息文件: {novel_info_path}")

    # 如果仍然找不到，返回错误
    if not os.path.exists(novel_info_path):
        return False, f"错误: 无法找到小说信息文件: {novel_info_path}"

    try:
        # 加载小说信息
        with open(novel_info_path, "r", encoding="utf-8") as f:
            novel_info = json.load(f)

        # 获取小说基本信息
        title = novel_info.get("title", "")
        genre = novel_info.get("genre", "")
        style = novel_info.get("style", "")

        # 确定下一章节编号
        # 首先从 novel_info.json 中的 last_chapter_completed 字段获取
        last_chapter_completed = novel_info.get("last_chapter_completed", 0)
        # 获取章节列表，确保在后续代码中可用
        chapters = novel_info.get("chapters", [])

        if last_chapter_completed > 0:
            next_chapter_number = last_chapter_completed + 1
            if progress_callback:
                progress_callback(f"从 novel_info.json 中读取到最后完成的章节: {last_chapter_completed}")
        else:
            # 如果没有 last_chapter_completed 字段，则从 chapters 列表中推断
            next_chapter_number = 1
            if chapters:
                chapter_numbers = [chapter.get("number", 0) for chapter in chapters]
                next_chapter_number = max(chapter_numbers) + 1

        if progress_callback:
            progress_callback(f"小说《{title}》已加载，当前有 {len(chapters)} 章节")
            progress_callback(f"将从第 {next_chapter_number} 章开始继续写作 {num_chapters} 章")

        # 获取前一章节内容（如果有）
        previous_chapter_content = ""
        if next_chapter_number > 1:
            prev_chapter_file = os.path.join(novel_dir, f"chapter_{next_chapter_number-1}_final.txt")
            if os.path.exists(prev_chapter_file):
                try:
                    with open(prev_chapter_file, "r", encoding="utf-8") as f:
                        previous_chapter_content = f.read()
                except Exception as e:
                    if progress_callback:
                        progress_callback(f"警告: 无法读取前一章节内容: {e}")

        # 生成所有请求的章节
        success_count = 0
        for i in range(num_chapters):
            current_chapter = next_chapter_number + i

            if progress_callback:
                progress_callback(f"\n=== 正在生成第 {current_chapter} 章 ===")

            # 使用NovelManager加载小说对象，包含大纲和进度信息
            from src.agents.novel_manager import NovelManager
            novel_manager = NovelManager()
            novel_obj = novel_manager.load_novel(novel_dir)

            if not novel_obj:
                if progress_callback:
                    progress_callback(f"无法加载小说对象，将使用基本信息继续")
            else:
                if progress_callback:
                    progress_callback(f"已成功加载小说对象，当前进度: {novel_obj.writing_progress:.2%}")
                    progress_callback(f"已完成章节: {novel_obj.completed_chapters} 章，当前章节: {novel_obj.current_chapter}")
                    progress_callback(f"大纲中包含的最大章节编号: {novel_obj.max_chapter_in_outline}")

                # 使用小说对象中的大纲
                novel_outline = novel_obj.outline
                max_chapter_in_outline = novel_obj.max_chapter_in_outline

                # 更新下一章节编号
                if novel_obj.last_chapter_completed > 0:
                    next_chapter_number = novel_obj.last_chapter_completed + 1
                    if progress_callback:
                        progress_callback(f"根据小说对象信息，将从第 {next_chapter_number} 章开始继续写作")

            # 如果没有成功加载小说对象或大纲，尝试直接从文件加载大纲
            if not novel_outline:
                if os.path.exists(outline_file):
                    with open(outline_file, "r", encoding="utf-8") as f:
                        novel_outline = f.read()
                    if progress_callback:
                        progress_callback(f"从{outline_file}加载大纲成功")
                elif os.path.exists(chapter_outline_file):
                    with open(chapter_outline_file, "r", encoding="utf-8") as f:
                        novel_outline = f.read()
                    if progress_callback:
                        progress_callback(f"从{chapter_outline_file}加载大纲成功")

                # 如果仍然没有大纲，创建一个基本的大纲模板
                if not novel_outline:
                    if progress_callback:
                        progress_callback(f"未找到大纲文件，将创建新的大纲")

                    # 创建基本的大纲模板
                    novel_outline = f"""《{title}》小说大纲

类型：{genre}
风格：{style}
场景：{novel_info.get('scene', '')}
角色：{novel_info.get('characters', '')}

"""

                    # 保存新创建的大纲，追加到原有文件中
                    # 如果文件不存在，则创建新文件
                    if os.path.exists(outline_file):
                        with open(outline_file, "a", encoding="utf-8") as f:
                            f.write("\n\n" + novel_outline)
                    else:
                        with open(outline_file, "w", encoding="utf-8") as f:
                            f.write(novel_outline)

                    # 同时保存一份chapter_outline.txt（兼容旧版本）
                    if os.path.exists(chapter_outline_file):
                        with open(chapter_outline_file, "a", encoding="utf-8") as f:
                            f.write("\n\n" + novel_outline)
                    else:
                        with open(chapter_outline_file, "w", encoding="utf-8") as f:
                            f.write(novel_outline)

                    if progress_callback:
                        progress_callback(f"已创建新的大纲文件")

                # 如果没有从小说对象中获取到最大章节编号，则分析大纲
                if max_chapter_in_outline == 0:
                    import re
                    # 查找大纲中的最大章节号
                    chapter_headers = re.findall(r'## 第(\d+)章', novel_outline)
                    if chapter_headers:
                        max_chapter_in_outline = max(int(num) for num in chapter_headers)

                    if progress_callback:
                        progress_callback(f"大纲中包含的最大章节号为: {max_chapter_in_outline}")

            # 如果当前章节号大于大纲中的最大章节号，需要追加大纲
            if current_chapter > max_chapter_in_outline:
                    if progress_callback:
                        progress_callback(f"当前章节号({current_chapter})大于大纲中的最大章节号({max_chapter_in_outline})，需要追加大纲")

                    # 获取WritingAgent实例生成新章节大纲
                    from src.agents.writing_agents import WritingAgent
                    writing_agent = WritingAgent("WritingAgent")

                    # 生成新章节大纲的提示词
                    outline_prompt = f"""
你是一位专业的小说大纲规划师。根据以下已有的小说大纲，请为第{current_chapter}章生成一个详细的大纲，确保与前面章节的情节自然衔接。

已有大纲：
{novel_outline}

请为第{current_chapter}章生成大纲，使用与已有大纲相同的格式，包含以下内容：

## 第{current_chapter}章

### 章节标题
[请提供第{current_chapter}章的标题]

### 章节概要
[请描述第{current_chapter}章的主要情节和事件]

### 关键场景
[请描述第{current_chapter}章中的关键场景]

### 角色发展
[请描述第{current_chapter}章中角色的变化和发展]

请只返回第{current_chapter}章的大纲内容，不要包含其他章节或额外的解释。
"""

                    # 生成新章节大纲
                    new_chapter_outline = writing_agent.call_llm(outline_prompt)

                    if progress_callback:
                        progress_callback(f"已生成第{current_chapter}章大纲")

                    # 追加到现有大纲
                    novel_outline += "\n\n" + new_chapter_outline

                    # 保存更新后的大纲，追加到原有文件中
                    if os.path.exists(outline_file):
                        with open(outline_file, "a", encoding="utf-8") as f:
                            f.write("\n\n" + new_chapter_outline)
                    else:
                        with open(outline_file, "w", encoding="utf-8") as f:
                            f.write(novel_outline)

                    # 同时保存一份到chapter_outline.txt（兼容旧版本）
                    if os.path.exists(chapter_outline_file):
                        with open(chapter_outline_file, "a", encoding="utf-8") as f:
                            f.write("\n\n" + new_chapter_outline)
                    else:
                        with open(chapter_outline_file, "w", encoding="utf-8") as f:
                            f.write(novel_outline)

                    if progress_callback:
                        progress_callback(f"已更新大纲文件，添加了第{current_chapter}章大纲")

            # 提取当前章节的大纲
            chapter_outline = ""
            if novel_outline:
                import re
                chapter_pattern = rf"## 第{current_chapter}章[\s\S]*?(?=## 第{current_chapter+1}章|$)"
                chapter_match = re.search(chapter_pattern, novel_outline)
                if chapter_match:
                    chapter_outline = chapter_match.group(0).strip()
                    if progress_callback:
                        progress_callback(f"已提取第{current_chapter}章大纲")

            # 创建章节上下文
            # 计算总章节数，确保 chapters 变量已定义
            total_chapters = len(chapters) + num_chapters

            chapter_context = {
                "title": title,
                "genre": genre,
                "style": style,
                "scene": novel_info.get("scene", ""),
                "characters": novel_info.get("characters", ""),
                "chapter_number": current_chapter,
                "total_chapters": total_chapters,
                "chapter_title": f"第{current_chapter}章",
                "previous_chapter_content": previous_chapter_content,
                "novel_outline": novel_outline,
                "chapter_outline": chapter_outline,
                "output_dir": novel_dir,
                "save_intermediates": True,
                "language": language  # 添加语言参数
            }

            # 如果有用户提供的故事要求，添加到章节上下文中
            if story_requirements:
                chapter_context["story_requirements"] = story_requirements
                if progress_callback:
                    progress_callback(f"已添加用户提供的故事要求：{story_requirements}")

            # 如果有前一章内容，生成过渡提示
            if previous_chapter_content and current_chapter > 1:
                try:
                    # 尝试导入过渡处理模块
                    from src.transition_handler import extract_last_paragraph

                    # 提取前一章的最后一段
                    last_paragraph = extract_last_paragraph(previous_chapter_content)

                    if last_paragraph:
                        # 添加过渡提示到章节上下文
                        transition_prompt = f"""
为了确保章节之间的无缝衔接，请注意以下前一章的结尾内容：

{last_paragraph}

你的新章节必须直接从这个场景或对话继续，不得有任何时间或场景跳跃。如果前一章结尾是对话，新章节应继续该对话或对该对话的直接反应。如果前一章结尾是情节描述，新章节应继续该情节或其直接后果。

请确保新章节的开头与前一章的结尾实现真正的无缝衔接，读者不应感觉到章节切换。
"""
                        chapter_context["transition_prompt"] = transition_prompt

                        if progress_callback:
                            progress_callback(f"已生成章节过渡提示，确保与前一章无缝衔接")
                except ImportError:
                    if progress_callback:
                        progress_callback(f"警告: 无法导入过渡处理模块，章节衔接可能不够自然")
                except Exception as e:
                    if progress_callback:
                        progress_callback(f"生成过渡提示时出错: {e}")

            # 如果启用了高级功能，初始化记忆和叙事控制
            if NEW_SYSTEM_AVAILABLE:
                try:
                    # 初始化MCP客户端
                    from src.utils.mcp_client import get_mcp_client
                    mcp_client = get_mcp_client()
                    if progress_callback:
                        progress_callback(f"成功初始化MCP客户端")

                    # 初始化记忆管理
                    from src.memory.memory_integration import get_memory_manager, clear_short_term_memory
                    memory_manager = get_memory_manager(novel_dir)
                    # 清空短期记忆，准备新章节
                    clear_short_term_memory(novel_dir)
                    if progress_callback:
                        progress_callback(f"成功初始化记忆管理")

                    # 初始化一致性检查
                    from src.consistency.consistency_checker import get_consistency_checker
                    consistency_checker = get_consistency_checker(novel_dir)
                    if progress_callback:
                        progress_callback(f"成功初始化一致性检查")

                    # 初始化叙事控制
                    from src.narrative.narrative_controller import get_narrative_controller
                    narrative_controller = get_narrative_controller(novel_dir)
                    if progress_callback:
                        progress_callback(f"成功初始化叙事控制")
                except Exception as e:
                    if progress_callback:
                        progress_callback(f"初始化高级功能时出错: {e}")
                    traceback.print_exc()

            # 在章节开始时更新进度信息
            if novel_obj:
                # 设置章节状态为进行中
                chapter = novel_obj.get_chapter(current_chapter)
                if not chapter:
                    # 创建新章节
                    from src.agents.novel_manager import Chapter
                    chapter = Chapter(current_chapter)
                    novel_obj.add_chapter(chapter)

                # 设置状态为进行中
                chapter.status = "in_progress"

                # 保存小说状态
                novel_manager.save_novel(novel_obj, novel_dir)

                if progress_callback:
                    progress_callback(f"已更新第 {current_chapter} 章状态为进行中")

            # 运行写作流水线
            from src.agents.writing_pipeline import run_pipeline as run_writing_pipeline
            success, content = run_writing_pipeline(pipeline, chapter_context)

            if success:
                success_count += 1
                # 更新前一章节内容，为下一章做准备
                previous_chapter_content = content

                # 保存章节内容到chapters目录
                chapters_dir = os.path.join(novel_dir, "chapters")
                os.makedirs(chapters_dir, exist_ok=True)

                chapter_file = os.path.join(chapters_dir, f"chapter_{current_chapter}_final.txt")
                with open(chapter_file, "w", encoding="utf-8") as f:
                    f.write(content)

                # 同时保存到根目录（为了兼容性）
                root_chapter_file = os.path.join(novel_dir, f"chapter_{current_chapter}_final.txt")
                with open(root_chapter_file, "w", encoding="utf-8") as f:
                    f.write(content)

                # 在章节结束时更新进度信息
                if novel_obj:
                    # 更新章节内容
                    chapter = novel_obj.get_chapter(current_chapter)
                    if chapter:
                        chapter.content = content

                        # 检查章节是否完整
                        if novel_obj.is_chapter_complete(chapter):
                            chapter.status = "completed"
                            if progress_callback:
                                progress_callback(f"第 {current_chapter} 章生成完成，内容完整")
                        else:
                            chapter.status = "incomplete"
                            if progress_callback:
                                progress_callback(f"第 {current_chapter} 章生成完成，但内容不完整，标记为不完整")

                    # 保存小说状态
                    novel_manager.save_novel(novel_obj, novel_dir)

                    if progress_callback:
                        progress_callback(f"已更新小说信息，当前进度: {novel_obj.writing_progress:.2%}")

                if progress_callback:
                    progress_callback(f"第 {current_chapter} 章生成成功，长度: {len(content)} 字符")
            else:
                if progress_callback:
                    progress_callback(f"第 {current_chapter} 章生成失败: {content}")
                # 如果失败，停止生成后续章节
                break

        # 更新小说信息并生成Word文档
        try:
            from src.agents.novel_manager import Novel, Chapter, NovelManager

            # 加载小说对象，而不是创建新的
            novel = None
            try:
                # 尝试从现有文件加载小说对象
                novel_manager = NovelManager()
                novel = novel_manager.load_novel(novel_dir)
                if progress_callback:
                    progress_callback(f"成功加载小说对象，将更新现有文件")
            except Exception as e:
                if progress_callback:
                    progress_callback(f"加载小说对象时出错: {e}\n将创建新的小说对象")

            # 如果加载失败，创建新的小说对象
            if not novel:
                novel = Novel(title=title, genre=genre, style=style)
                novel.scene = novel_info.get("scene", "")
                novel.characters = novel_info.get("characters", "")
                novel.total_chapters = next_chapter_number + success_count - 1

            # 更新小说状态信息
            novel.status = "in_progress" if success_count < num_chapters else "completed"
            novel.current_chapter = next_chapter_number + success_count - 1
            novel.completed_chapters = next_chapter_number + success_count - 1
            novel.last_writing_time = datetime.now().isoformat()
            novel.updated_at = datetime.now().isoformat()

            # 加载所有章节
            for chapter_num in range(1, next_chapter_number + success_count):
                # 检查是否已经有这个章节
                existing_chapter = novel.get_chapter(chapter_num)
                if existing_chapter:
                    # 如果已经有这个章节，检查是否完整
                    if existing_chapter.status == "incomplete":
                        if progress_callback:
                            progress_callback(f"第 {chapter_num} 章不完整，将重新生成")
                        # 如果章节不完整，则重新生成
                        # 这里不删除现有章节，因为在生成新章节时会自动覆盖
                    else:
                        # 如果章节完整，跳过
                        continue

                # 检查章节文件是否存在
                chapter_file = os.path.join(novel_dir, f"chapter_{chapter_num}_final.txt")
                if os.path.exists(chapter_file):
                    try:
                        with open(chapter_file, "r", encoding="utf-8") as f:
                            chapter_content = f.read()

                        # 创建章节对象
                        chapter = Chapter(number=chapter_num, title=f"第{chapter_num}章", content=chapter_content)

                        # 检查章节是否完整
                        if novel.is_chapter_complete(chapter):
                            chapter.status = "completed"  # 设置状态为已完成
                            if progress_callback:
                                progress_callback(f"第 {chapter_num} 章内容完整，标记为已完成")
                        else:
                            chapter.status = "incomplete"  # 设置状态为不完整
                            if progress_callback:
                                progress_callback(f"第 {chapter_num} 章内容不完整，标记为不完整")

                        # 添加插图路径（如果有）
                        illustrations_dir = os.path.join(novel_dir, "illustrations")
                        if os.path.exists(illustrations_dir):
                            illustration_path = os.path.join(illustrations_dir, f"chapter_{chapter_num}.png")
                            if os.path.exists(illustration_path):
                                chapter.illustrations.append(illustration_path)

                        # 添加章节
                        novel.add_chapter(chapter)
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"警告: 加载第 {chapter_num} 章时出错: {e}")

            # 保存更新后的小说信息
            novel_info_path = os.path.join(novel_dir, "novel_info.json")
            with open(novel_info_path, "w", encoding="utf-8") as f:
                json.dump(novel.to_dict(), f, ensure_ascii=False, indent=2)

            if progress_callback:
                progress_callback(f"已更新小说信息文件: {novel_info_path}")

            # 生成Word文档
            doc_path = os.path.join(novel_dir, f"{title}.docx")
            novel.save_as_document(doc_path)

            if progress_callback:
                progress_callback(f"\n=== 小说文档已生成 ===")
                progress_callback(f"文档保存在: {doc_path}")
        except Exception as e:
            if progress_callback:
                progress_callback(f"生成Word文档时出错: {e}")

        return True, f"成功生成 {success_count} 章内容"
    except Exception as e:
        error_message = f"继续写作时出错: {e}\n{traceback.format_exc()}"
        if progress_callback:
            progress_callback(error_message)
        return False, error_message

def launch_simple_reader(novel_dir=None):
    """启动简易阅读器"""
    try:
        # 使用极简阅读器模块
        from src.reader import launch_reader

        # 获取当前小说目录
        if novel_dir is None and 'novel_dir' in NOVEL_CREATION_STATE:
            novel_dir = NOVEL_CREATION_STATE['novel_dir']

        # 启动阅读器线程
        reader_thread = threading.Thread(target=lambda: launch_reader(novel_dir))
        reader_thread.daemon = True  # 设置为守护线程，不阻塞主程序退出
        reader_thread.start()

        # 等待两秒，给阅读器时间启动
        time.sleep(2)

        # 自动打开浏览器
        try:
            webbrowser.open("http://127.0.0.1:4556")
            print("已打开阅读器网页: http://127.0.0.1:4556")
        except Exception as e:
            print(f"打开浏览器时出错: {e}")
            print("请手动访问 http://127.0.0.1:4556")

        return "\n\n正在启动极简阅读器，浏览器应该已自动打开。\n如果没有，请手动访问 http://127.0.0.1:4556"
    except Exception as e:
        print(f"启动阅读器时出错: {e}")
        traceback.print_exc()
        return f"\n\n启动阅读器时出错: {e}"

def create_web_ui():
    """创建Web用户界面"""
    # 获取预设选项
    presets = AI_MODEL_CONFIG.get("presets", {})

    # 小说类型选项 - 使用新的专业化分类
    genre_options = presets.get("genres", [
        # 专业类型
        "医学小说", "法律小说", "军事小说", "商战小说", "科技小说",
        # 传统类型（细分）
        "都市言情", "古代言情", "现代悬疑", "历史悬疑",
        "硬科幻", "软科幻", "东方奇幻", "西方奇幻",
        "武侠江湖", "仙侠修真", "职场小说", "校园青春",
        "家庭伦理", "心理小说", "社会现实", "网络文学"
    ])

    # 写作风格选项 - 使用新的专业化分类
    style_options = presets.get("styles", [
        # 专业风格
        "医学写实", "法庭辩论", "军事纪实", "商业分析",
        # 传统风格（细分）
        "温情治愈", "悬疑烧脑", "浪漫唯美", "幽默诙谐",
        "深沉哲理", "激情热血", "细腻情感", "紧张刺激",
        # 现代风格
        "网络爽文", "现实主义", "魔幻现实", "意识流",
        "后现代", "极简主义"
    ])

    # 场景设定选项 - 使用新的专业化分类
    scene_options = presets.get("scenes", [
        # 专业场景
        "医院", "法院", "军营", "公司", "实验室", "学校",
        # 传统场景（细分）
        "现代都市", "古代京城", "江湖武林", "仙侠世界",
        "未来星际", "异世大陆", "小镇", "乡村",
        "海岛", "山区", "边疆", "网络世界"
    ])

    # 角色设定选项
    character_options = presets.get("characters", [
        "一位年轻人", "大学生", "白领精英", "创业者", "警察", "医生",
        "教师", "作家", "科学家", "身怀特殊能力的人", "武林高手",
        "修仙者", "特种兵", "黑客", "历史人物", "外星人", "机器人",
        "魔法师", "游戏玩家", "孤儿", "双胞胎", "家庭主妇", "商人"
    ])

    # 获取模型配置
    preset_models = AI_MODEL_CONFIG.get("preset_models", {})
    glm_model_options = preset_models.get("glm_models", ["glm-4-flash"])
    siliconflow_model_options = preset_models.get("siliconflow_models", ["internlm/internlm2_5-7b-chat"])
    cogview_model_options = preset_models.get("image_models", ["cogview-3-flash"])

    # 加载自定义模型
    load_custom_models()

    # 目录选择函数
    def select_directory():
        """打开文件浏览器选择目录"""
        try:
            import tkinter as tk
            from tkinter import filedialog

            # 创建tkinter窗口
            root = tk.Tk()
            root.withdraw()

            # 确保窗口在前台
            root.attributes('-topmost', True)

            # 打开目录选择对话框
            directory = filedialog.askdirectory(title="选择目录", initialdir=os.getcwd())

            # 销毁窗口
            root.destroy()

            if not directory:
                return ""

            # 规范化路径
            directory = os.path.normpath(directory)
            print(f"已选择目录: {directory}")

            return directory
        except Exception as e:
            print(f"选择目录时出错: {e}")
            traceback.print_exc()
            return ""

    # 加载本地Ollama模型
    def load_local_ollama_models():
        """加载本地Ollama模型"""
        try:
            import requests
            models = []
            try:
                response = requests.get("http://localhost:11434/api/tags")
                if response.status_code == 200:
                    data = response.json()
                    if "models" in data:
                        models = [model["name"] for model in data["models"]]
                    else:
                        print("Ollama API返回的数据格式不正确")
            except Exception as e:
                print(f"获取Ollama模型列表失败: {e}")

            return models
        except ImportError:
            print("未安装requests库，无法获取Ollama模型列表")
            return []

    # 加载本地模型
    local_ollama_models = load_local_ollama_models()
    if local_ollama_models:
        print(f"已加载 {len(local_ollama_models)} 个本地Ollama模型")

    # 创建Gradio界面
    with gr.Blocks(title="中文小说写作系统") as demo:
        # 创建状态框，用于显示阅读器状态
        reader_status = gr.Textbox(visible=False)

        with gr.Row():
            gr.Markdown("# 中文小说写作系统")
            advanced_reader_btn = gr.Button("打开阅读器", variant="primary", scale=0)

        with gr.Tab("创建新小说"):
            with gr.Row():
                with gr.Column():
                    title_input = gr.Textbox(
                        label="小说标题",
                        placeholder="请输入小说标题，例如：大都市的车水马龙、星辰大海、归途是故乡"
                    )

                    genre_input = gr.Dropdown(
                        choices=genre_options,
                        label="小说类型",
                        value=genre_options[0] if genre_options else None,
                        allow_custom_value=True,
                        info="🏥 医学小说：专业医疗背景，包含医学报告和临床思维 | 📚 法律小说：法庭辩论和司法程序 | 💼 商战小说：商业策略和职场竞争"
                    )

                    style_input = gr.Dropdown(
                        choices=style_options,
                        label="写作风格",
                        value=style_options[0] if style_options else None,
                        allow_custom_value=True,
                        info="可以选择或输入自定义风格，例如：温情浪漫、幽默诗意、悬疑惊悚"
                    )

                    scene_input = gr.Dropdown(
                        choices=scene_options,
                        label="场景设定",
                        value=scene_options[0] if scene_options else None,
                        allow_custom_value=True,
                        info="可以选择或输入自定义场景，例如：现代大都市、古代官场、未来星球基地"
                    )

                    character_input = gr.Dropdown(
                        choices=character_options,
                        label="角色设定",
                        value=character_options[0] if character_options else None,
                        allow_custom_value=True,
                        info="可以选择或输入自定义角色，例如：年轻律师、退休特工、大学教授、双重身份的商人"
                    )

                    chapters_input = gr.Slider(
                        minimum=1,
                        maximum=20,
                        value=3,
                        step=1,
                        label="本次生成章节数量",
                        info="设置本次生成的章节数量，可以后续继续生成更多章节"
                    )

                    # 插图风格将根据小说风格自动确定
                    illustration_style_info = gr.Markdown("插图风格将根据小说风格自动确定，以确保风格一致性")

                    # 添加写作语言选择
                    language_input = gr.Dropdown(
                        choices=["中文", "英文", "日文", "韩文", "法文", "德文", "西班牙文", "俄文", "意大利文", "葡萄牙文"],
                        label="写作语言",
                        value="中文",
                        info="选择小说的写作语言，将影响生成的内容语言"
                    )

                    create_button = gr.Button("开始创作", variant="primary")

                with gr.Column():
                    # 添加状态指示器
                    with gr.Row():
                        status_indicator = gr.Markdown("状态: 就绪")

                    # 添加进度条
                    progress_bar = gr.Slider(
                        minimum=0,
                        maximum=100,
                        value=0,
                        step=1,
                        label="进度",
                        interactive=False
                    )

                    output_text = gr.Textbox(
                        label="创作进度",
                        lines=20,
                        interactive=False,
                        elem_id="output_log"
                    )
                    output_image = gr.Image(label="生成的插图")

                    with gr.Row():
                        open_reader_button = gr.Button("打开阅读器", variant="secondary")
                        open_folder_button = gr.Button("打开输出文件夹", variant="secondary")

            # 创建小说函数
            def create_novel(title, genre, style, scene, characters, chapters, language="中文"):
                """创建新小说"""
                if not title:
                    return "错误: 请输入小说标题"

                progress_message = f"开始创作《{title}》...\n"
                progress_message += f"类型: {genre}\n"
                progress_message += f"风格: {style}\n"
                progress_message += f"场景: {scene}\n"
                progress_message += f"角色: {characters}\n"
                progress_message += f"章节数量: {chapters}\n"
                progress_message += f"插图风格: 根据小说风格自动确定\n"
                progress_message += f"写作语言: {language}\n"
                progress_message += "=" * 40 + "\n"

                # 创建小说
                try:
                    # 初始化日志消息列表
                    log_messages = []

                    # 加载智能体配置
                    illustration_style = "写实风格"  # 默认插图风格
                    config_file = "agent_config.json"
                    if os.path.exists(config_file):
                        try:
                            with open(config_file, "r", encoding="utf-8") as f:
                                config = json.load(f)

                            # 更新智能体模型
                            # 获取写作模型，所有写作相关智能体使用同一个模型
                            writing_model = config.get("writing_model", AI_MODEL_CONFIG["agent_models"]["WritingAgent"])

                            # 更新所有写作相关智能体模型为同一个模型
                            update_agent_model("WritingAgent", writing_model)
                            update_agent_model("PolishingAgent", writing_model)
                            update_agent_model("ExpansionAgent", writing_model)
                            update_agent_model("ReviewAgent", writing_model)
                            update_agent_model("OptimizationAgent", writing_model)

                            # 更新插图智能体模型
                            update_agent_model("IllustrationAgent", config.get("illustration_model", AI_MODEL_CONFIG["agent_models"]["IllustrationAgent"]))

                            # 获取插图风格
                            illustration_style = config.get("illustration_style", "写实风格")

                            # 更新模型温度
                            writing_model = config.get("writing_model")
                            writing_temperature = config.get("writing_temperature", 0.7)
                            if writing_model and writing_model in AI_MODEL_CONFIG["models"]:
                                AI_MODEL_CONFIG["models"][writing_model]["temperature"] = writing_temperature

                            # 重新注册智能体，使配置生效
                            register_writing_agents()
                            progress_message += f"已加载智能体配置\n"
                            log_messages.append("已加载智能体配置")
                        except Exception as e:
                            print(f"加载智能体配置时出错: {e}")

                    # 创建写作流水线
                    # 插图风格将根据小说风格自动确定
                    pipeline = create_writing_pipeline()

                    # 记录插图风格
                    log_messages.append("插图风格将根据小说风格自动确定")

                    # 创建章节上下文
                    # 在Web界面中始终创建新的目录
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    novel_output_dir = os.path.join("output", f"{title}_{timestamp}")
                    os.makedirs(novel_output_dir, exist_ok=True)
                    log_messages.append(f"创建新的输出目录: {novel_output_dir}")

                    chapter_context = {
                        "title": title,
                        "genre": genre,
                        "style": style,
                        "scene": scene,
                        "characters": characters,
                        "chapter_number": 1,  # 开始从第一章
                        "total_chapters": chapters,
                        "output_dir": novel_output_dir,
                        "save_intermediates": True,
                        "language": language  # 添加语言参数
                    }

                    # 创建小说信息文件
                    from src.agents.novel_manager import Novel
                    novel = Novel(title=title, genre=genre, style=style)
                    novel.scene = scene
                    novel.characters = characters
                    novel.total_chapters = chapters

                    # 保存小说信息
                    novel_info_path = os.path.join(novel_output_dir, "novel_info.json")
                    with open(novel_info_path, "w", encoding="utf-8") as f:
                        json.dump(novel.to_dict(), f, ensure_ascii=False, indent=2)
                    log_messages.append(f"已创建小说信息文件: {novel_info_path}")

                    # 运行写作流水线，生成所有请求的章节
                    from src.agents.writing_pipeline import run_pipeline as run_writing_pipeline
                    from src.agents.novel_manager import Novel, Chapter

                    # 创建小说对象
                    novel = Novel(title=title, genre=genre, style=style)

                    # 生成所有请求的章节
                    success_count = 0
                    previous_chapter_content = ""

                    for chapter_num in range(1, chapters + 1):
                        log_messages.append(f"\n=== 正在生成第 {chapter_num} 章 ===")
                        progress_message += f"\n=== 正在生成第 {chapter_num} 章 ===\n"

                        # 更新章节上下文
                        chapter_context["chapter_number"] = chapter_num
                        chapter_context["previous_chapter_content"] = previous_chapter_content

                        # 如果有前一章内容，生成过渡提示
                        if previous_chapter_content and chapter_num > 1:
                            try:
                                # 尝试导入过渡处理模块
                                from src.transition_handler import extract_last_paragraph, create_transition
                                import re

                                # 提取前一章的最后几个段落
                                last_paragraphs = extract_last_paragraph(previous_chapter_content, num_paragraphs=3)

                                # 获取当前章节的大纲
                                current_chapter_outline = ""
                                if "chapter_outline" in chapter_context:
                                    current_chapter_outline = chapter_context["chapter_outline"]
                                elif "novel_outline" in chapter_context:
                                    # 从小说大纲中提取当前章节的大纲
                                    novel_outline = chapter_context["novel_outline"]
                                    chapter_pattern = rf"\*\*\s*第{chapter_num}章.*?\*\*\s*"
                                    chapter_match = re.search(chapter_pattern, novel_outline, re.DOTALL)
                                    if chapter_match:
                                        # 提取当前章节的大纲部分
                                        start_idx = chapter_match.start()
                                        next_chapter_pattern = rf"\*\*\s*第{chapter_num+1}章.*?\*\*\s*"
                                        next_chapter_match = re.search(next_chapter_pattern, novel_outline, re.DOTALL)
                                        end_idx = next_chapter_match.start() if next_chapter_match else len(novel_outline)
                                        current_chapter_outline = novel_outline[start_idx:end_idx]

                                # 创建过渡提示
                                transition_text = create_transition(previous_chapter_content, current_chapter_outline)

                                if last_paragraphs:
                                    # 添加过渡提示到章节上下文
                                    transition_prompt = f"""
为了确保章节之间的无缝衔接，请注意以下前一章的结尾内容：

{last_paragraphs}

你的新章节必须直接从这个场景或对话继续，不得有任何时间或场景跳跃。如果前一章结尾是对话，新章节应继续该对话或对该对话的直接反应。如果前一章结尾是情节描述，新章节应继续该情节或其直接后果。

请确保新章节的开头与前一章的结尾实现真正的无缝衔接，读者不应感觉到章节切换。

如果适用，可以考虑使用以下过渡文本作为参考：
{transition_text}
"""
                                    chapter_context["transition_prompt"] = transition_prompt
                                    log_messages.append(f"已生成章节过渡提示，确保与前一章无缝衔接")
                            except ImportError:
                                log_messages.append(f"警告: 无法导入过渡处理模块，章节衔接可能不够自然")
                            except Exception as e:
                                log_messages.append(f"生成过渡提示时出错: {e}")

                        # 运行写作流水线生成当前章节
                        success, content = run_writing_pipeline(pipeline, chapter_context)

                        if success and content:
                            # 添加章节
                            chapter = Chapter(number=chapter_num, title=f"第{chapter_num}章", content=content)
                            novel.add_chapter(chapter)

                            # 更新前一章内容，为下一章做准备
                            previous_chapter_content = content
                            success_count += 1

                            log_messages.append(f"第 {chapter_num} 章生成成功，长度: {len(content)} 字符")
                        else:
                            log_messages.append(f"第 {chapter_num} 章生成失败: {content}")
                            # 如果失败，停止生成后续章节
                            break

                    # 收集日志消息
                    log_messages = [f"开始创作《{title}》...",
                                    f"类型: {genre}",
                                    f"风格: {style}",
                                    f"场景: {scene}",
                                    f"角色: {characters}",
                                    f"章节数量: {chapters}",
                                    f"插图风格: 根据小说风格自动确定",
                                    f"写作语言: {language}",
                                    "=" * 40]

                    if success:
                        log_messages.append(f"\n=== 写作流水线完成 ===")
                        log_messages.append(f"小说框架保存在: {os.path.join(novel_output_dir, 'chapter_outline.txt')}")
                        log_messages.append(f"最终文档保存在: {os.path.join(novel_output_dir, f'{title}.docx')}")
                    else:
                        log_messages.append(f"\n=== 写作流水线出错 ===")
                        log_messages.append(f"错误信息: {content}")

                    novel_dir = novel_output_dir

                    # 将日志消息列表转换为字符串并添加到进度消息中
                    progress_message += "\n".join(log_messages)

                    # 查找生成的插图
                    illustration_path = None
                    if novel_dir:
                        illustrations_dir = os.path.join(novel_dir, "illustrations")
                        if os.path.exists(illustrations_dir):
                            for file in os.listdir(illustrations_dir):
                                if file.endswith(".png") or file.endswith(".jpg"):
                                    illustration_path = os.path.join(illustrations_dir, file)
                                    break

                    return progress_message, illustration_path
                except Exception as e:
                    error_message = f"创作小说时出错: {e}\n{traceback.format_exc()}"
                    return progress_message + "\n" + error_message, None

            # 打开阅读器函数
            def open_reader(novel_dir):
                """打开阅读器"""
                if not novel_dir:
                    return "\n\n\u9519\u8bef: \u8bf7\u5148\u521b\u5efa\u6216\u7eed\u5199\u5c0f\u8bf4"

                try:
                    # 检查novel_dir是否是完整路径还是只是目录名
                    if os.path.dirname(novel_dir) == "" and not os.path.exists(novel_dir):
                        # 如果只是目录名，尝试在output目录下查找
                        alternative_dir = os.path.join("output", novel_dir)
                        if os.path.exists(alternative_dir):
                            novel_dir = alternative_dir

                    # 检查目录是否存在
                    if not os.path.exists(novel_dir):
                        return f"\n\n\u9519\u8bef: \u76ee\u5f55\u4e0d\u5b58\u5728: {novel_dir}"

                    # 导入阅读器模块
                    from src.ui.reader import launch_reader

                    # 启动阅读器（在新线程中运行）
                    threading.Thread(target=launch_reader, args=(novel_dir,)).start()

                    return f"\n\n\u6b63\u5728\u6253\u5f00\u9605\u8bfb\u5668\uff0c\u8bf7\u7a0d\u7b49..."
                except Exception as e:
                    return f"\n\n\u6253\u5f00\u9605\u8bfb\u5668\u65f6\u51fa\u9519: {e}"

            # 打开输出文件夹函数
            def open_folder(novel_dir):
                """打开输出文件夹"""
                if not novel_dir:
                    return "\n\n\u9519\u8bef: \u8bf7\u5148\u521b\u5efa\u6216\u7eed\u5199\u5c0f\u8bf4"

                try:
                    # 检查novel_dir是否是完整路径还是只是目录名
                    if os.path.dirname(novel_dir) == "" and not os.path.exists(novel_dir):
                        # 如果只是目录名，尝试在output目录下查找
                        alternative_dir = os.path.join("output", novel_dir)
                        if os.path.exists(alternative_dir):
                            novel_dir = alternative_dir

                    # 检查目录是否存在
                    if not os.path.exists(novel_dir):
                        return f"\n\n\u9519\u8bef: \u76ee\u5f55\u4e0d\u5b58\u5728: {novel_dir}"

                    # 根据不同的操作系统使用不同的方法打开文件夹
                    import platform
                    import subprocess

                    system = platform.system()

                    if system == "Windows":
                        # Windows系统使用startfile
                        os.startfile(novel_dir)
                    elif system == "Darwin":
                        # macOS系统使用open命令
                        subprocess.run(["open", novel_dir])
                    else:
                        # Linux系统使用xdg-open命令
                        subprocess.run(["xdg-open", novel_dir])

                    return f"\n\n\u5df2\u6253\u5f00\u8f93\u51fa\u6587\u4ef6\u5939: {novel_dir}"
                except Exception as e:
                    return f"\n\n\u6253\u5f00\u8f93\u51fa\u6587\u4ef6\u5939\u65f6\u51fa\u9519: {e}"

            # 创建小说函数的包装器，保存novel_dir
            def create_novel_wrapper(*args):
                result = create_novel(*args)
                if isinstance(result, tuple) and len(result) >= 1:
                    # 从输出中提取novel_dir
                    output_text = result[0]
                    lines = output_text.split('\n')
                    for line in lines:
                        if line.startswith('创建新的输出目录:'):
                            global NOVEL_CREATION_STATE
                            NOVEL_CREATION_STATE['novel_dir'] = line.split(':', 1)[1].strip()
                            break
                return result

            # 绑定创建按钮事件
            create_button.click(
                fn=create_novel_wrapper,
                inputs=[title_input, genre_input, style_input, scene_input, character_input, chapters_input, language_input],
                outputs=[output_text, output_image]
            )

            # 绑定打开阅读器按钮事件
            open_reader_button.click(
                fn=lambda: launch_simple_reader(NOVEL_CREATION_STATE.get('novel_dir', '')),
                inputs=[],
                outputs=[output_text]
            )

            # 绑定打开输出文件夹按钮事件
            open_folder_button.click(
                fn=lambda: open_folder(NOVEL_CREATION_STATE.get('novel_dir', '')),
                inputs=[],
                outputs=[output_text]
            )

        with gr.Tab("续写小说"):
            with gr.Row():
                with gr.Column(scale=1):
                    with gr.Row():
                        novel_dir_input = gr.Textbox(
                            label="小说目录",
                            placeholder="请输入小说目录路径，例如: output/我的小说_20240321_123456",
                            scale=4
                        )
                        novel_dir_button = gr.Button("选择目录", scale=1)

                    # 添加大纲显示
                    outline_display = gr.Textbox(
                        label="小说大纲",
                        lines=10,
                        interactive=False
                    )

                    continue_chapters_input = gr.Slider(
                        minimum=1,
                        maximum=10,
                        value=1,
                        step=1,
                        label="续写章节数量",
                        info="建议先生成少量章节测试，满意后再继续生成更多章节"
                    )

                    # 插图风格将根据小说风格自动确定
                    continue_illustration_info = gr.Markdown("插图风格将根据小说风格自动确定，以确保风格一致性")

                    # 添加写作语言选择
                    continue_language_input = gr.Dropdown(
                        choices=["中文", "英文", "日文", "韩文", "法文", "德文", "西班牙文", "俄文", "意大利文", "葡萄牙文"],
                        label="写作语言",
                        value="中文",
                        info="选择续写的语言，建议与原小说保持一致"
                    )

                    # 添加故事要求输入框
                    story_requirements_input = gr.Textbox(
                        label="故事要求",
                        placeholder="输入特定的故事要求，例如：添加一个新角色、增加一个意外转折、探索某个主题等",
                        lines=3,
                        info="在大纲框架下添加特定的故事元素，使小说情节更加丰富"
                    )

                    with gr.Row():
                        load_novel_button = gr.Button("加载小说", scale=1)
                        continue_button = gr.Button("开始续写", scale=1)

                with gr.Column(scale=2):
                    continue_output = gr.Textbox(label="续写进度", lines=20)
                    continue_image = gr.Image(label="生成的插图")

                    with gr.Row():
                        continue_reader_button = gr.Button("打开阅读器", variant="secondary")
                        continue_folder_button = gr.Button("打开输出文件夹", variant="secondary")

            # 加载小说函数
            def load_novel_outline(novel_dir: str):
                """加载小说大纲"""
                try:
                    if not novel_dir:
                        return "错误: 请选择小说目录"

                    # 检查novel_dir是否是完整路径还是只是目录名
                    if os.path.dirname(novel_dir) == "" and not os.path.exists(novel_dir):
                        # 如果只是目录名，尝试在output目录下查找
                        alternative_dir = os.path.join("output", novel_dir)
                        if os.path.exists(alternative_dir):
                            novel_dir = alternative_dir
                            print(f"使用output目录下的小说目录: {novel_dir}")

                    # 检查目录是否存在
                    if not os.path.exists(novel_dir):
                        return f"错误: 目录不存在: {novel_dir}"

                    # 检查大纲文件(优先检查novel_outline.txt，如果不存在则尝试chapter_outline.txt)
                    outline_path = os.path.join(novel_dir, "novel_outline.txt")
                    if not os.path.exists(outline_path):
                        outline_path = os.path.join(novel_dir, "chapter_outline.txt")

                    if os.path.exists(outline_path):
                        with open(outline_path, "r", encoding="utf-8") as f:
                            outline_content = f.read()
                        return outline_content
                    else:
                        return "未找到大纲文件"
                except Exception as e:
                    return f"加载大纲出错: {str(e)}"

            # 续写小说函数
            def continue_novel_ui(novel_dir: str, num_chapters: int, story_requirements: str = "", language: str = "中文"):
                """续写小说UI函数"""
                if not novel_dir:
                    return "错误: 请选择小说目录"

                # 检查novel_dir是否是完整路径还是只是目录名
                if os.path.dirname(novel_dir) == "" and not os.path.exists(novel_dir):
                    # 如果只是目录名，尝试在output目录下查找
                    alternative_dir = os.path.join("output", novel_dir)
                    if os.path.exists(alternative_dir):
                        novel_dir = alternative_dir
                        print(f"使用output目录下的小说目录: {novel_dir}")

                if not os.path.exists(novel_dir):
                    return f"错误: 目录不存在: {novel_dir}"

                # 创建进度回调函数
                progress_output = ""

                # 加载智能体配置
                config_file = "agent_config.json"
                if os.path.exists(config_file):
                    try:
                        with open(config_file, "r", encoding="utf-8") as f:
                            config = json.load(f)

                        # 更新智能体模型
                        # 获取写作模型，所有写作相关智能体使用同一个模型
                        writing_model = config.get("writing_model", AI_MODEL_CONFIG["agent_models"]["WritingAgent"])

                        # 更新所有写作相关智能体模型为同一个模型
                        update_agent_model("WritingAgent", writing_model)
                        update_agent_model("PolishingAgent", writing_model)
                        update_agent_model("ExpansionAgent", writing_model)
                        update_agent_model("ReviewAgent", writing_model)
                        update_agent_model("OptimizationAgent", writing_model)

                        # 更新插图智能体模型
                        update_agent_model("IllustrationAgent", config.get("illustration_model", AI_MODEL_CONFIG["agent_models"]["IllustrationAgent"]))

                        # 更新模型温度
                        writing_model = config.get("writing_model")
                        writing_temperature = config.get("writing_temperature", 0.7)
                        if writing_model and writing_model in AI_MODEL_CONFIG["models"]:
                            AI_MODEL_CONFIG["models"][writing_model]["temperature"] = writing_temperature

                        # 重新注册智能体，使配置生效
                        register_writing_agents()
                        progress_output += f"已加载智能体配置\n"
                    except Exception as e:
                        print(f"加载智能体配置时出错: {e}")
                def update_progress(message, progress=None, status=None):
                    nonlocal progress_output
                    progress_output += message + "\n"

                    outputs = [progress_output]

                    # 更新进度条和状态指示器
                    if progress is not None:
                        outputs.append(gr.update(value=progress))
                    else:
                        outputs.append(gr.update())

                    if status is not None:
                        outputs.append(gr.update(value=f"状态: {status}"))
                    else:
                        outputs.append(gr.update())

                    return outputs[0]  # 为了兼容现有代码，只返回文本

                # 运行续写功能
                # 确保在原有小说文件夹内续写，而不是创建新的文件夹
                try:
                    # 更新状态和进度条
                    update_progress("准备续写...", progress=0, status="初始化")
                    result = continue_novel(novel_dir, num_chapters, progress_callback=update_progress, story_requirements=story_requirements, language=language)
                    update_progress("续写完成!", progress=100, status="完成")
                except Exception as e:
                    update_progress(f"续写过程中出错: {e}", progress=0, status="错误")
                    result = (False, f"续写过程中出错: {e}")

                # 查找生成的插图
                illustration_path = None
                illustrations_dir = os.path.join(novel_dir, "illustrations")
                if os.path.exists(illustrations_dir):
                    for file in os.listdir(illustrations_dir):
                        if file.endswith(".png") or file.endswith(".jpg"):
                            illustration_path = os.path.join(illustrations_dir, file)
                            break

                # 将进度输出和结果组合
                combined_output = progress_output
                if isinstance(result, tuple) and len(result) == 2:
                    success, message = result
                    combined_output += f"\n\n结果: {'成功' if success else '失败'}\n{message}"
                else:
                    combined_output += f"\n\n{result}"

                return combined_output, illustration_path

            # 续写小说函数的包装器，保存novel_dir
            def continue_novel_wrapper(novel_dir, num_chapters, story_requirements, language):
                result = continue_novel_ui(novel_dir, num_chapters, story_requirements=story_requirements, language=language)
                if isinstance(result, tuple) and len(result) >= 1:
                    # 保存novel_dir以便后续使用
                    global NOVEL_CREATION_STATE
                    NOVEL_CREATION_STATE['novel_dir'] = novel_dir
                return result

            # 绑定事件
            load_novel_button.click(
                fn=load_novel_outline,
                inputs=[novel_dir_input],
                outputs=[outline_display]
            )

            continue_button.click(
                fn=continue_novel_wrapper,
                inputs=[novel_dir_input, continue_chapters_input, story_requirements_input, continue_language_input],
                outputs=[continue_output, continue_image]
            )

            novel_dir_button.click(
                fn=select_directory,
                inputs=[],
                outputs=[novel_dir_input]
            )

            # 绑定打开阅读器按钮事件
            continue_reader_button.click(
                fn=lambda: launch_simple_reader(novel_dir_input.value),
                inputs=[],
                outputs=[continue_output]
            )

            # 绑定打开输出文件夹按钮事件
            continue_folder_button.click(
                fn=lambda: open_folder(novel_dir_input.value),
                inputs=[],
                outputs=[continue_output]
            )

        with gr.Tab("智能体配置"):
            with gr.Row():
                with gr.Column(scale=1):
                    gr.Markdown("## 写作智能体配置")
                    gr.Markdown("所有写作相关智能体（创作、润色、扩写、审核、优化）将使用同一个模型，以确保一致性和减少错误。")

                    # 写作模型选择
                    writing_model_dropdown = gr.Dropdown(
                        choices=AI_MODEL_CONFIG["preset_models"]["glm_models"] +
                                AI_MODEL_CONFIG["preset_models"]["siliconflow_models"] +
                                AI_MODEL_CONFIG["preset_models"]["openrouter_models"],
                        label="写作模型",
                        value=AI_MODEL_CONFIG["agent_models"]["WritingAgent"],
                        info="用于所有写作相关任务的统一模型"
                    )

                    # 写作模型创意度
                    writing_temperature_slider = gr.Slider(
                        minimum=0.1,
                        maximum=1.0,
                        value=0.7,
                        step=0.1,
                        label="写作模型创意度",
                        info="设置较高的值会使生成的内容更加多样化和创意"
                    )

                    # 隐藏的润色模型下拉框（与写作模型相同）
                    polishing_model_dropdown = gr.Dropdown(
                        choices=AI_MODEL_CONFIG["preset_models"]["glm_models"] +
                                AI_MODEL_CONFIG["preset_models"]["siliconflow_models"] +
                                AI_MODEL_CONFIG["preset_models"]["openrouter_models"],
                        label="润色模型",
                        value=AI_MODEL_CONFIG["agent_models"]["PolishingAgent"],
                        visible=False
                    )

                    # 隐藏的过渡段模型下拉框（与写作模型相同）
                    transition_model_dropdown = gr.Dropdown(
                        choices=AI_MODEL_CONFIG["preset_models"]["glm_models"] +
                                AI_MODEL_CONFIG["preset_models"]["siliconflow_models"] +
                                AI_MODEL_CONFIG["preset_models"]["openrouter_models"],
                        label="过渡段模型",
                        value=AI_MODEL_CONFIG["agent_models"]["WritingAgent"],
                        visible=False
                    )

                with gr.Column(scale=1):
                    gr.Markdown("## 大纲智能体配置")
                    gr.Markdown("大纲智能体负责生成小说的总纲、卷纲和章节大纲，是小说创作的基础。")

                    # 大纲模型下拉框
                    outline_model_dropdown = gr.Dropdown(
                        choices=AI_MODEL_CONFIG["preset_models"]["glm_models"] +
                                AI_MODEL_CONFIG["preset_models"]["siliconflow_models"] +
                                AI_MODEL_CONFIG["preset_models"]["openrouter_models"],
                        label="大纲模型",
                        value=AI_MODEL_CONFIG["preset_models"]["openrouter_models"][0] if AI_MODEL_CONFIG["preset_models"]["openrouter_models"] else AI_MODEL_CONFIG["agent_models"]["WritingAgent"],
                        info="用于生成小说大纲的模型，可以与写作模型不同"
                    )

                    # 大纲模型创意度滑块
                    outline_temperature_slider = gr.Slider(
                        minimum=0.1,
                        maximum=1.0,
                        value=0.7,
                        step=0.1,
                        label="大纲模型创意度",
                        info="设置较高的值会使生成的大纲更加多样化和创意"
                    )

                with gr.Column(scale=1):
                    gr.Markdown("## 插图智能体配置")
                    gr.Markdown("插图智能体负责生成与小说内容相匹配的插图。")

                    # 插图模型选择
                    illustration_model_dropdown = gr.Dropdown(
                        choices=AI_MODEL_CONFIG["preset_models"]["image_models"],
                        label="插图模型",
                        value=AI_MODEL_CONFIG["agent_models"]["IllustrationAgent"],
                        info="用于生成小说插图的模型"
                    )

                    # 插图风格选择
                    illustration_style_dropdown = gr.Dropdown(
                        choices=["写实风格", "水彩风格", "油画风格", "素描风格", "动漫风格", "正版插画风格", "中国国画风格"],
                        label="插图风格",
                        value="写实风格",
                        info="插图的艺术风格"
                    )

            # 保存按钮
            save_config_button = gr.Button("保存配置", variant="primary")
            config_status = gr.Textbox(label="配置状态", interactive=False)

            # 保存配置函数
            def save_agent_config(writing_model, writing_temperature, polishing_model,
                                 outline_model, outline_temperature, transition_model,
                                 illustration_model, illustration_style):
                try:
                    # 更新所有写作相关智能体模型为同一个模型
                    update_agent_model("WritingAgent", writing_model)
                    update_agent_model("PolishingAgent", writing_model)  # 使用同一个写作模型
                    update_agent_model("ExpansionAgent", writing_model)  # 使用同一个写作模型
                    update_agent_model("ReviewAgent", writing_model)     # 使用同一个写作模型
                    update_agent_model("OptimizationAgent", writing_model) # 使用同一个写作模型

                    # 更新大纲智能体模型
                    update_agent_model("OutlineGeneratorAgent", outline_model)

                    # 更新插图智能体模型
                    update_agent_model("IllustrationAgent", illustration_model)

                    # 更新模型温度
                    models_dict = AI_MODEL_CONFIG["models"]
                    if writing_model in models_dict:
                        models_dict[writing_model]["temperature"] = writing_temperature
                    if outline_model in models_dict:
                        models_dict[outline_model]["temperature"] = outline_temperature

                    # 保存配置到文件
                    config_file = "agent_config.json"
                    config = {
                        "writing_model": writing_model,
                        "writing_temperature": writing_temperature,
                        "polishing_model": writing_model,  # 使用同一个写作模型
                        "outline_model": outline_model,    # 大纲模型可以不同
                        "outline_temperature": outline_temperature,  # 大纲模型创意度
                        "transition_model": writing_model, # 使用同一个写作模型
                        "illustration_model": illustration_model,
                        "illustration_style": illustration_style
                    }

                    with open(config_file, "w", encoding="utf-8") as f:
                        json.dump(config, f, ensure_ascii=False, indent=2)

                    # 重新注册写作智能体，使配置生效
                    register_writing_agents()

                    return f"智能体配置已保存\n\n写作模型: {writing_model} (创意度: {writing_temperature})\n大纲模型: {outline_model} (创意度: {outline_temperature})\n插图模型: {illustration_model} (风格: {illustration_style})\n\n所有写作相关智能体（创作、润色、扩写、审核、优化）均使用同一个模型"
                except Exception as e:
                    return f"保存配置时出错: {e}"

            # 绑定保存按钮事件
            save_config_button.click(
                fn=save_agent_config,
                inputs=[
                    writing_model_dropdown, writing_temperature_slider,
                    polishing_model_dropdown, outline_model_dropdown,
                    outline_temperature_slider, transition_model_dropdown,
                    illustration_model_dropdown, illustration_style_dropdown
                ],
                outputs=[config_status]
            )

            # 加载配置函数
            def load_agent_config():
                config_file = "agent_config.json"
                if os.path.exists(config_file):
                    try:
                        with open(config_file, "r", encoding="utf-8") as f:
                            config = json.load(f)

                        # 获取写作模型，所有写作相关智能体使用同一个模型
                        writing_model = config.get("writing_model", AI_MODEL_CONFIG["agent_models"]["WritingAgent"])
                        writing_temperature = config.get("writing_temperature", 0.7)

                        # 获取大纲模型配置
                        outline_model = config.get("outline_model", AI_MODEL_CONFIG["agent_models"]["OutlineGeneratorAgent"] if "OutlineGeneratorAgent" in AI_MODEL_CONFIG["agent_models"] else writing_model)
                        outline_temperature = config.get("outline_temperature", writing_temperature)

                        # 获取插图模型配置
                        illustration_model = config.get("illustration_model", AI_MODEL_CONFIG["agent_models"]["IllustrationAgent"])
                        illustration_style = config.get("illustration_style", "写实风格")

                        # 为了兼容旧版本的配置文件，返回所有参数
                        return (
                            writing_model,
                            writing_temperature,
                            writing_model,  # 润色模型与写作模型相同
                            outline_model,  # 大纲模型可以不同
                            outline_temperature,  # 大纲模型创意度
                            writing_model,  # 过渡段模型与写作模型相同
                            illustration_model,
                            illustration_style
                        )
                    except Exception as e:
                        print(f"加载配置时出错: {e}")

                # 返回默认值
                default_writing_model = AI_MODEL_CONFIG["agent_models"]["WritingAgent"]
                default_temperature = 0.7
                default_outline_model = AI_MODEL_CONFIG["agent_models"]["OutlineGeneratorAgent"] if "OutlineGeneratorAgent" in AI_MODEL_CONFIG["agent_models"] else default_writing_model
                default_outline_temperature = 0.7
                default_illustration_model = AI_MODEL_CONFIG["agent_models"]["IllustrationAgent"]

                return (
                    default_writing_model,
                    default_temperature,
                    default_writing_model,  # 润色模型与写作模型相同
                    default_outline_model,  # 大纲模型可以不同
                    default_outline_temperature,  # 大纲模型创意度
                    default_writing_model,  # 过渡段模型与写作模型相同
                    default_illustration_model,
                    "写实风格"
                )

            # 页面加载时加载配置
            demo.load(
                fn=load_agent_config,
                inputs=None,
                outputs=[
                    writing_model_dropdown, writing_temperature_slider,
                    polishing_model_dropdown, outline_model_dropdown,
                    outline_temperature_slider, transition_model_dropdown,
                    illustration_model_dropdown, illustration_style_dropdown
                ]
                # 移除 _js 参数，因为它在新版本的 Gradio 中不受支持
                # _js="() => {console.log('Loading agent config...'); return [];}"
            )

            # 绑定简易阅读器按钮事件
            advanced_reader_btn.click(
                fn=lambda: launch_simple_reader(),
                inputs=[],
                outputs=[reader_status]
            )

            # 添加一个加载按钮，以便用户可以手动加载配置
            load_config_button = gr.Button("加载配置")
            load_config_button.click(
                fn=load_agent_config,
                inputs=None,
                outputs=[
                    writing_model_dropdown, writing_temperature_slider,
                    polishing_model_dropdown, outline_model_dropdown,
                    outline_temperature_slider, transition_model_dropdown,
                    illustration_model_dropdown, illustration_style_dropdown
                ]
            )

        with gr.Tab("关于"):
            gr.Markdown("""
    # 中文小说写作系统

    这是一个基于大语言模型的中文小说写作系统，可以根据用户提供的标题、类型、风格等信息，自动生成小说内容。

    ## 功能特点

    1. 支持多种小说类型和写作风格
    2. 多阶段写作流程，包括创作、润色、扩写、审核和优化
    3. 自动生成与内容相匹配的插图
    4. 可导出为Word文档格式

    ## 使用方法

    1. 在"创建新小说"标签页中填写小说的基本信息
    2. 点击"开始创作"按钮，系统将自动生成小说内容
    3. 创作完成后，可以在输出目录中找到生成的小说文件

    ## 高级设置

    在高级设置中，您可以自定义使用的AI模型：
    - 写作模型：用于所有写作相关任务（创作、润色、扩写、审核、优化）
    - 插图模型：用于生成小说插图

    所有写作相关智能体使用同一个模型，以确保一致性和减少错误。

    ## 系统运行逻辑和使用指南

    ### 系统运行逻辑

    #### 初始化阶段：
    - 系统启动时，会注册各种智能体（WritingAgent, PolishingAgent, ExpansionAgent, ReviewAgent, OptimizationAgent, IllustrationAgent, OutlineGeneratorAgent）
    - 每个智能体使用特定的模型配置，可以在model_config.py中设置

    #### 大纲生成阶段：
    - 当创建新小说时，系统会使用OutlineGeneratorAgent生成三级大纲（总纲→卷纲→章节大纲）
    - 大纲生成器会创建novel_info.json文件，记录小说的基本信息和章节状态
    - 大纲会保存在novel_outline.txt和chapter_outline.txt文件中

    #### 写作流程：
    1. 系统会检查novel_info.json文件，确定当前应该写作的章节
    2. 根据大纲，系统会依次执行以下步骤：
       - 初始内容生成（WritingAgent）
       - 内容润色（PolishingAgent）
       - 内容扩展（ExpansionAgent）
       - 内容审核（ReviewAgent）
       - 内容优化（OptimizationAgent）
       - 插图生成（IllustrationAgent）
    3. 每个步骤的结果会保存在对应的文件中
    4. 最终结果会保存在chapter_{number}_final.txt文件中

    #### 进度跟踪：
    - 每完成一个章节，系统会更新novel_info.json文件，记录章节状态
    - 续写时，系统会读取novel_info.json文件，从最后完成的章节的下一章开始写作

    #### 高级功能：
    - 系统集成了记忆管理、一致性检查和叙事控制等高级功能
    - 这些功能可以帮助生成更连贯、更一致的小说内容

    ### 使用指南

    #### 创建新小说：
    - 提供小说标题、类型、风格、场景、角色等基本信息
    - 系统会自动生成大纲和第一章内容

    示例：
    ```
    python main.py --title "星辰大海" --genre "科幻" --style "硬核科幻"
    ```

    #### 续写小说：
    - 指定小说目录，系统会自动从最后完成的章节的下一章开始写作

    示例：
    ```
    python main.py --continue ./novels/星辰大海
    ```

    #### 查看生成的内容：
    - 最终内容保存在chapter_{number}_final.txt文件中
    - 可以使用内置的阅读器查看生成的内容

    #### 导出小说：
    - 系统可以将生成的小说导出为Word文档
    - 可以包含或不包含插图

    ### 注意事项

    1. 如果写作过程中断，可以使用续写功能继续写作，系统会自动从最后完成的章节的下一章开始
    2. 不要手动修改novel_info.json文件，以免导致系统无法正确识别章节状态
    3. 如果需要修改大纲，建议在创建新小说时进行，而不是在写作过程中修改

    ## 文件结构说明

    ### 小说目录结构：
    - novel_info.json：小说基本信息和章节状态
    - novel_outline.txt：小说总大纲
    - chapter_outline.txt：章节大纲
    - chapter_{number}_final.txt：最终章节内容
    - chapter_{number}_{step}.txt：各步骤的中间结果
    - illustrations/：插图目录

    ### 代码结构：
    - src/agents/：各种智能体的实现
    - src/core/：核心功能和配置
    - src/memory/：记忆管理功能
    - src/consistency/：一致性检查功能
    - src/narrative/：叙事控制功能
    - src/utils/：工具函数

    ## 常见问题解答

    **Q: 如何继续写作中断的小说？**
    A: 使用--continue参数并指定小说目录，系统会自动从最后完成的章节的下一章开始写作。

    **Q: 如何修改已生成的章节？**
    A: 目前系统不支持直接修改已生成的章节。如果需要修改，建议手动编辑chapter_{number}_final.txt文件。

    **Q: 如何调整生成内容的风格？**
    A: 在创建小说时指定--style参数，或者修改model_config.py中的模型参数。

    **Q: 系统支持哪些类型的小说？**
    A: 系统支持各种类型的小说，包括科幻、奇幻、悬疑、言情等。只需在创建小说时指定--genre参数。

    **Q: 如何生成更多插图？**
    A: 系统默认为每章生成一张插图。如果需要更多插图，可以修改illustration_generator.py中的相关参数。

    > 通过以上修改和说明，我们解决了novel_info.json未实时更新的问题，并提供了系统的运行逻辑和使用指南。现在，当创作过程意外终止后，重新启动续写功能会从上次中断的地方继续，而不是从第一章开始。
  """)

    return demo

def main():
    """主函数"""
    args = parse_arguments()

    if args.web:
        # 注册写作智能体
        register_writing_agents()

        # 创建Web界面
        demo = create_web_ui()

        # 自动打开浏览器
        webbrowser.open('http://127.0.0.1:4554')

        # 启动Gradio应用
        demo.queue()  # 启用队列功能，改善实时更新

        # 尝试不同的端口，避免端口冲突
        try:
            # 首选端口
            demo.launch(server_name="0.0.0.0", server_port=4554, share=True)
        except OSError:
            try:
                # 备选端口
                print("端口4554被占用，尝试端口4555...")
                demo.launch(server_name="0.0.0.0", server_port=4555, share=True)
            except OSError:
                # 随机端口
                print("端口4555也被占用，使用随机端口...")
                demo.launch(server_name="0.0.0.0", share=True)
    else:
        # 命令行模式
        use_new_system = NEW_SYSTEM_AVAILABLE
        print(f"使用新系统: {'是' if use_new_system else '否'}")

        # 加载智能体配置
        illustration_style = "写实风格"  # 默认插图风格
        config_file = "agent_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)

                # 获取写作模型，所有写作相关智能体使用同一个模型
                writing_model = config.get("writing_model", AI_MODEL_CONFIG["agent_models"]["WritingAgent"])

                # 更新所有写作相关智能体模型为同一个模型
                update_agent_model("WritingAgent", writing_model)
                update_agent_model("PolishingAgent", writing_model)  # 使用同一个写作模型
                update_agent_model("ExpansionAgent", writing_model)  # 使用同一个写作模型
                update_agent_model("ReviewAgent", writing_model)     # 使用同一个写作模型
                update_agent_model("OptimizationAgent", writing_model) # 使用同一个写作模型

                # 更新插图智能体模型
                update_agent_model("IllustrationAgent", config.get("illustration_model", AI_MODEL_CONFIG["agent_models"]["IllustrationAgent"]))

                # 获取插图风格
                illustration_style = config.get("illustration_style", "写实风格")

                # 更新模型温度
                writing_model = config.get("writing_model")
                writing_temperature = config.get("writing_temperature", 0.7)
                if writing_model and writing_model in AI_MODEL_CONFIG["models"]:
                    AI_MODEL_CONFIG["models"][writing_model]["temperature"] = writing_temperature

                # 重新注册智能体，使配置生效
                register_writing_agents()
                print(f"已加载智能体配置")
            except Exception as e:
                print(f"加载智能体配置时出错: {e}")

        run_pipeline(
            title=args.title,
            genre=args.genre,
            style=args.style,
            scene=args.scene,
            characters=args.characters,
            num_chapters=args.chapters,
            output_dir=args.output_dir,
            save_intermediates=args.save_intermediates,
            use_new_system=use_new_system,
            illustration_style=illustration_style
        )

if __name__ == "__main__":
    sys.exit(main())
