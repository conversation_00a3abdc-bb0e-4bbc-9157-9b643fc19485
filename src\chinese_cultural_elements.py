"""
中国文化元素模块
用于在小说中融入中国文化元素，增强小说的文化底蕴和吸引力
"""

import random
from typing import Dict, List, Optional, Tuple

# 中国传统文化元素
class ChineseCulturalElements:
    """中国文化元素类，提供各种中国文化元素的生成和融合功能"""
    
    def __init__(self):
        # 中国传统节日
        self.traditional_festivals = [
            {"name": "春节", "description": "农历新年，象征着新的开始，家人团聚的重要节日"},
            {"name": "元宵节", "description": "正月十五，赏花灯、猜灯谜、吃元宵的传统节日"},
            {"name": "清明节", "description": "祭祖扫墓、踏青的传统节日"},
            {"name": "端午节", "description": "农历五月初五，赛龙舟、吃粽子、挂艾草的传统节日"},
            {"name": "七夕节", "description": "中国传统的情人节，牛郎织女相会的日子"},
            {"name": "中秋节", "description": "农历八月十五，赏月、吃月饼、家人团聚的传统节日"},
            {"name": "重阳节", "description": "农历九月初九，登高、赏菊、敬老的传统节日"}
        ]
        
        # 中国传统美德
        self.traditional_virtues = [
            {"name": "仁", "description": "仁爱之心，关心他人，推己及人"},
            {"name": "义", "description": "正义之心，明辨是非，坚持原则"},
            {"name": "礼", "description": "礼节之心，尊重他人，遵守规范"},
            {"name": "智", "description": "智慧之心，明察秋毫，洞悉事理"},
            {"name": "信", "description": "诚信之心，言而有信，诚实守信"},
            {"name": "忠", "description": "忠诚之心，忠于职守，忠于信念"},
            {"name": "孝", "description": "孝顺之心，尊敬父母，孝敬长辈"},
            {"name": "悌", "description": "友爱之心，兄友弟恭，和睦相处"},
            {"name": "廉", "description": "廉洁之心，清正廉洁，不贪不占"},
            {"name": "耻", "description": "羞耻之心，知耻近乎勇，有所不为"}
        ]
        
        # 中国传统文化符号
        self.cultural_symbols = [
            {"name": "龙", "description": "中华民族的图腾，象征着权威、尊贵、吉祥"},
            {"name": "凤", "description": "百鸟之王，象征着美好、和平、吉祥"},
            {"name": "梅兰竹菊", "description": "四君子，象征着高尚的品格和情操"},
            {"name": "牡丹", "description": "花中之王，象征着富贵、繁荣"},
            {"name": "莲花", "description": "出淤泥而不染，象征着纯洁、高尚"},
            {"name": "太极", "description": "阴阳相生相克，象征着宇宙的运行规律"},
            {"name": "中国结", "description": "象征着团结、和谐、吉祥如意"}
        ]
        
        # 中国传统哲学思想
        self.philosophical_thoughts = [
            {"name": "儒家思想", "description": "以仁为核心，强调道德伦理和人际关系"},
            {"name": "道家思想", "description": "以道为核心，强调自然无为和顺应自然"},
            {"name": "佛家思想", "description": "以禅为核心，强调修身养性和超脱世俗"},
            {"name": "墨家思想", "description": "以兼爱为核心，强调普遍之爱和实用主义"},
            {"name": "法家思想", "description": "以法为核心，强调严刑峻法和中央集权"},
            {"name": "阴阳五行", "description": "解释宇宙万物的生成变化规律"},
            {"name": "天人合一", "description": "人与自然和谐相处，追求天人合一的境界"}
        ]
        
        # 中国传统艺术形式
        self.traditional_arts = [
            {"name": "书法", "description": "汉字书写艺术，讲究笔法、结构和意境"},
            {"name": "国画", "description": "中国传统绘画，讲究意境和笔墨技法"},
            {"name": "京剧", "description": "中国国粹，集唱念做打于一体的综合艺术"},
            {"name": "昆曲", "description": "中国最古老的戏曲之一，被称为'百戏之祖'"},
            {"name": "民乐", "description": "中国传统音乐，包括古琴、琵琶、二胡等乐器"},
            {"name": "剪纸", "description": "中国民间艺术，用剪刀或刻刀在纸上刻出各种图案"},
            {"name": "陶瓷", "description": "中国传统工艺，包括青花瓷、汝窑、龙泉窑等"}
        ]
        
        # 中国传统建筑
        self.traditional_architecture = [
            {"name": "四合院", "description": "北方传统民居，四面房屋围合成院落"},
            {"name": "徽派建筑", "description": "安徽地区传统建筑，以马头墙、雕花为特色"},
            {"name": "园林", "description": "中国传统园林，讲究自然山水和人工造景的结合"},
            {"name": "宝塔", "description": "佛教建筑，多为多层楼阁式建筑"},
            {"name": "牌坊", "description": "表彰功德或标志地点的建筑物"},
            {"name": "长城", "description": "中国古代伟大的防御工程，世界文化遗产"},
            {"name": "故宫", "description": "中国最大的古代宫殿建筑群，世界文化遗产"}
        ]
        
        # 中国传统饮食
        self.traditional_cuisine = [
            {"name": "川菜", "description": "以麻辣为特色，讲究味型多样"},
            {"name": "粤菜", "description": "以清淡为特色，讲究原汁原味"},
            {"name": "鲁菜", "description": "以咸鲜为特色，讲究火候和刀工"},
            {"name": "苏菜", "description": "以甜为特色，讲究精致和美观"},
            {"name": "浙菜", "description": "以鲜为特色，讲究原料的新鲜"},
            {"name": "闽菜", "description": "以海鲜为特色，讲究清鲜"},
            {"name": "湘菜", "description": "以香辣为特色，讲究香型丰富"},
            {"name": "徽菜", "description": "以烹饪山珍为特色，讲究火功"}
        ]
        
        # 中国传统服饰
        self.traditional_clothing = [
            {"name": "汉服", "description": "汉族传统服饰，宽袍大袖，端庄典雅"},
            {"name": "旗袍", "description": "中国传统女性服装，修身显曲线美"},
            {"name": "唐装", "description": "中式上衣，盘扣设计，简洁大方"},
            {"name": "马褂", "description": "清代男子外套，对襟右衽，宽袖紧腕"},
            {"name": "长袍", "description": "传统男子服装，一片式连身衣服"},
            {"name": "云肩", "description": "女子披肩，多为刺绣工艺"},
            {"name": "绣花鞋", "description": "传统手工绣花布鞋，精美别致"}
        ]
        
        # 中国传统文学
        self.traditional_literature = [
            {"name": "诗经", "description": "中国最早的诗歌总集，收录了从西周初年到春秋中叶的诗歌"},
            {"name": "楚辞", "description": "以屈原作品为代表的浪漫主义诗歌"},
            {"name": "汉赋", "description": "汉代盛行的一种文体，铺陈扬厉，辞藻华丽"},
            {"name": "唐诗", "description": "唐代诗歌，中国古典诗歌的巅峰"},
            {"name": "宋词", "description": "宋代流行的一种文学体裁，婉约派和豪放派"},
            {"name": "元曲", "description": "元代流行的一种文学体裁，通俗易懂"},
            {"name": "明清小说", "description": "明清时期的长篇小说，如四大名著"}
        ]
        
        # 中国传统名言
        self.traditional_sayings = [
            {"saying": "己所不欲，勿施于人", "meaning": "自己不想要的，不要强加给别人"},
            {"saying": "知己知彼，百战不殆", "meaning": "了解自己和对手，才能立于不败之地"},
            {"saying": "千里之行，始于足下", "meaning": "再长的路也是从脚下开始"},
            {"saying": "学而不思则罔，思而不学则殆", "meaning": "学习而不思考是浪费，思考而不学习是危险的"},
            {"saying": "三人行，必有我师焉", "meaning": "和别人相处，一定能从中学到东西"},
            {"saying": "吃得苦中苦，方为人上人", "meaning": "能够承受常人难以忍受的痛苦，才能成为出类拔萃的人"},
            {"saying": "书山有路勤为径，学海无涯苦作舟", "meaning": "学习需要勤奋和刻苦作为引导"}
        ]
        
        # 中国传统故事
        self.traditional_stories = [
            {"title": "愚公移山", "moral": "坚持不懈，最终能够克服困难"},
            {"title": "精卫填海", "moral": "不畏艰难，坚持到底"},
            {"title": "孟母三迁", "moral": "环境对人的成长有重要影响"},
            {"title": "司马光砸缸", "moral": "遇到紧急情况要冷静思考，勇敢行动"},
            {"title": "曹冲称象", "moral": "灵活运用知识，解决实际问题"},
            {"title": "负荆请罪", "moral": "勇于认错，真诚道歉"},
            {"title": "卧薪尝胆", "moral": "忍辱负重，为实现目标而努力"}
        ]
        
        # 中国传统节气
        self.traditional_solar_terms = [
            {"name": "立春", "description": "春季的开始，万物复苏"},
            {"name": "惊蛰", "description": "春雷惊醒蛰伏的昆虫，大地开始复苏"},
            {"name": "清明", "description": "气温升高，草木繁茂，是祭祖扫墓的日子"},
            {"name": "立夏", "description": "夏季的开始，万物繁茂"},
            {"name": "芒种", "description": "农作物开始有了芒，可以播种了"},
            {"name": "小暑", "description": "天气开始炎热，但还不是最热的时候"},
            {"name": "立秋", "description": "秋季的开始，天气开始转凉"},
            {"name": "白露", "description": "天气转凉，早晨草木上有白色露珠"},
            {"name": "寒露", "description": "天气更加寒冷，露水将要凝结成霜"},
            {"name": "立冬", "description": "冬季的开始，天气寒冷"},
            {"name": "大雪", "description": "降雪量大，天气寒冷"},
            {"name": "小寒", "description": "天气寒冷，但还不是最冷的时候"}
        ]
    
    def get_random_festival(self) -> Dict:
        """获取随机传统节日"""
        return random.choice(self.traditional_festivals)
    
    def get_random_virtue(self) -> Dict:
        """获取随机传统美德"""
        return random.choice(self.traditional_virtues)
    
    def get_random_symbol(self) -> Dict:
        """获取随机文化符号"""
        return random.choice(self.cultural_symbols)
    
    def get_random_thought(self) -> Dict:
        """获取随机哲学思想"""
        return random.choice(self.philosophical_thoughts)
    
    def get_random_art(self) -> Dict:
        """获取随机传统艺术"""
        return random.choice(self.traditional_arts)
    
    def get_random_architecture(self) -> Dict:
        """获取随机传统建筑"""
        return random.choice(self.traditional_architecture)
    
    def get_random_cuisine(self) -> Dict:
        """获取随机传统美食"""
        return random.choice(self.traditional_cuisine)
    
    def get_random_clothing(self) -> Dict:
        """获取随机传统服饰"""
        return random.choice(self.traditional_clothing)
    
    def get_random_literature(self) -> Dict:
        """获取随机传统文学"""
        return random.choice(self.traditional_literature)
    
    def get_random_saying(self) -> Dict:
        """获取随机传统名言"""
        return random.choice(self.traditional_sayings)
    
    def get_random_story(self) -> Dict:
        """获取随机传统故事"""
        return random.choice(self.traditional_stories)
    
    def get_random_solar_term(self) -> Dict:
        """获取随机传统节气"""
        return random.choice(self.traditional_solar_terms)
    
    def get_cultural_elements_for_genre(self, genre: str) -> List[Dict]:
        """根据小说类型获取适合的文化元素"""
        elements = []
        
        if "历史" in genre or "古代" in genre:
            # 历史小说适合加入传统文化元素
            elements.append(self.get_random_festival())
            elements.append(self.get_random_virtue())
            elements.append(self.get_random_symbol())
            elements.append(self.get_random_thought())
            elements.append(self.get_random_architecture())
            elements.append(self.get_random_clothing())
            elements.append(self.get_random_literature())
            elements.append(self.get_random_saying())
            elements.append(self.get_random_story())
        
        elif "武侠" in genre or "仙侠" in genre:
            # 武侠小说适合加入传统文化元素和哲学思想
            elements.append(self.get_random_virtue())
            elements.append(self.get_random_thought())
            elements.append(self.get_random_saying())
            elements.append(self.get_random_story())
            elements.append(self.get_random_architecture())
        
        elif "现代" in genre or "都市" in genre:
            # 现代小说适合加入传统节日和美食
            elements.append(self.get_random_festival())
            elements.append(self.get_random_cuisine())
            elements.append(self.get_random_saying())
            elements.append(self.get_random_solar_term())
        
        elif "科幻" in genre:
            # 科幻小说适合加入传统哲学思想和文化符号
            elements.append(self.get_random_thought())
            elements.append(self.get_random_symbol())
        
        elif "奇幻" in genre:
            # 奇幻小说适合加入传统文化符号和故事
            elements.append(self.get_random_symbol())
            elements.append(self.get_random_story())
            elements.append(self.get_random_thought())
        
        else:
            # 其他类型小说随机加入一些文化元素
            elements.append(self.get_random_festival())
            elements.append(self.get_random_virtue())
            elements.append(self.get_random_saying())
        
        return elements
    
    def enrich_chapter_with_cultural_elements(self, chapter_content: str, genre: str, chapter_num: int) -> str:
        """使用中国文化元素丰富章节内容"""
        # 获取适合的文化元素
        elements = self.get_cultural_elements_for_genre(genre)
        
        # 如果没有获取到元素，直接返回原内容
        if not elements:
            return chapter_content
        
        # 根据章节号选择不同的元素
        element_index = (chapter_num - 1) % len(elements)
        element = elements[element_index]
        
        # 构建文化元素段落
        cultural_paragraph = ""
        
        if "name" in element and "description" in element:
            # 节日、美德、符号等
            cultural_paragraph = f"\n\n正如{element['name']}所象征的那样，{element['description']}。这让人不禁想起中国传统文化中的智慧。"
        elif "saying" in element and "meaning" in element:
            # 名言
            cultural_paragraph = f"\n\n这让人想起了那句古语："{element['saying']}"，意思是{element['meaning']}。这句话在当下的情境中显得尤为贴切。"
        elif "title" in element and "moral" in element:
            # 故事
            cultural_paragraph = f"\n\n这种情况让人想起了《{element['title']}》的故事，告诉我们{element['moral']}。历史的智慧往往能给现实以启示。"
        
        # 在章节内容的中间位置插入文化元素段落
        lines = chapter_content.split('\n\n')
        if len(lines) > 3:  # 确保章节内容足够长
            insert_position = len(lines) // 2
            lines.insert(insert_position, cultural_paragraph)
            return '\n\n'.join(lines)
        else:
            return chapter_content + cultural_paragraph
    
    def generate_chinese_style_description(self, scene: str, season: str = None) -> str:
        """生成中国风格的场景描述"""
        if not season:
            season = random.choice(["春", "夏", "秋", "冬"])
        
        # 中国风格的场景描述模板
        templates = [
            f"{season}风拂过{scene}，如同一幅淡雅的水墨画徐徐展开。",
            f"{scene}在{season}日的照耀下，宛如一首古老的诗词，韵味悠长。",
            f"{season}色的{scene}，仿佛是一幅工笔重彩，细腻而又生动。",
            f"{scene}沐浴在{season}光中，如同一曲古琴弦音，清幽而深远。",
            f"{season}意渐浓，{scene}如同一幅山水长卷，意境深远。"
        ]
        
        return random.choice(templates)
    
    def generate_chinese_style_emotion(self, emotion: str) -> str:
        """生成中国风格的情感描述"""
        # 中国风格的情感描述模板
        templates = {
            "高兴": [
                "心如明镜，喜悦如春风拂面。",
                "心中欢喜，如同饮下一杯清茶，回味悠长。",
                "喜不自胜，如同登高望远，心旷神怡。"
            ],
            "悲伤": [
                "心如止水，悲伤如秋风落叶。",
                "心中苦楚，如同饮下一杯苦茶，余味绵长。",
                "悲痛欲绝，如同独坐空谷，听风听雨。"
            ],
            "愤怒": [
                "怒火中烧，如同夏日烈阳，灼人心扉。",
                "心中愤懑，如同一把利剑，锋芒毕露。",
                "怒不可遏，如同山洪爆发，势不可挡。"
            ],
            "恐惧": [
                "心惊胆战，如同秋日落叶，飘摇不定。",
                "心中恐惧，如同置身悬崖，进退维谷。",
                "惊恐万状，如同暴风雨中的一叶小舟，随波逐流。"
            ],
            "期待": [
                "满怀期待，如同春日播种，静待花开。",
                "心中期许，如同仰望星空，憧憬未来。",
                "翘首以盼，如同久旱盼甘霖，渴望已久。"
            ]
        }
        
        # 如果没有对应的情感模板，返回通用描述
        if emotion not in templates:
            return "心中百感交集，如同四季更替，变化万千。"
        
        return random.choice(templates[emotion])

def enrich_novel_with_chinese_elements(chapter_content: str, genre: str, chapter_num: int) -> str:
    """使用中国文化元素丰富小说内容"""
    cultural_elements = ChineseCulturalElements()
    return cultural_elements.enrich_chapter_with_cultural_elements(chapter_content, genre, chapter_num)

if __name__ == "__main__":
    # 测试代码
    cultural_elements = ChineseCulturalElements()
    
    # 测试获取随机文化元素
    print("随机传统节日:", cultural_elements.get_random_festival())
    print("随机传统美德:", cultural_elements.get_random_virtue())
    print("随机文化符号:", cultural_elements.get_random_symbol())
    
    # 测试根据小说类型获取文化元素
    print("\n历史小说的文化元素:")
    for element in cultural_elements.get_cultural_elements_for_genre("历史小说"):
        print(element)
    
    print("\n现代小说的文化元素:")
    for element in cultural_elements.get_cultural_elements_for_genre("现代都市"):
        print(element)
    
    # 测试丰富章节内容
    test_chapter = """第1章：开篇

这是第一章的内容。
主角李明站在高楼上，俯瞰着整个城市。
夜幕降临，城市的灯光如同繁星点点。
他深吸一口气，知道自己的决定将改变一切。

第1章结束。
"""
    
    enriched_chapter = cultural_elements.enrich_chapter_with_cultural_elements(test_chapter, "现代都市", 1)
    print("\n丰富后的章节内容:")
    print(enriched_chapter)
    
    # 测试生成中国风格的描述
    print("\n中国风格的场景描述:")
    print(cultural_elements.generate_chinese_style_description("山间小路", "秋"))
    
    print("\n中国风格的情感描述:")
    print(cultural_elements.generate_chinese_style_emotion("高兴"))
