"""
Novel Core - Core components for the novel generation system
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
from abc import ABC, abstractmethod

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', f'novel_generator_{datetime.now().strftime("%Y%m%d")}.log'))
    ]
)

logger = logging.getLogger('novel_core')

class Chapter:
    """Class representing a novel chapter"""

    def __init__(self, number: int, title: str):
        """
        Initialize a chapter

        Args:
            number: Chapter number
            title: Chapter title
        """
        self.number = number
        self.title = title
        self.content = ""
        self.illustrations: List[str] = []
        self.status = "pending"  # pending, in_progress, completed, error
        self.metadata: Dict[str, Any] = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        try:
            return {
                "number": self.number,
                "title": self.title,
                "content": self.content,
                "illustrations": self.illustrations[:] if self.illustrations else [],
                "status": self.status,
                "metadata": dict(self.metadata)
            }
        except Exception as e:
            logger.error(f"Error converting chapter to dict: {e}")
            return {
                "number": self.number,
                "title": self.title,
                "content": "",
                "illustrations": [],
                "status": "error",
                "metadata": {"error": str(e)}
            }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Chapter':
        """Create from dictionary"""
        try:
            chapter = cls(
                number=data.get("number", 0),
                title=data.get("title", "未命名章节")
            )
            chapter.content = data.get("content", "")
            chapter.illustrations = data.get("illustrations", [])[:]
            chapter.status = data.get("status", "pending")
            chapter.metadata = dict(data.get("metadata", {}))
            return chapter
        except Exception as e:
            logger.error(f"Error creating chapter from dict: {e}")
            chapter = cls(0, "错误章节")
            chapter.status = "error"
            chapter.metadata = {"error": str(e)}
            return chapter

class NovelState:
    """Class for managing novel state"""

    def __init__(self, title: str, output_dir: str):
        """
        Initialize novel state

        Args:
            title: Novel title
            output_dir: Output directory
        """
        self.title = title
        self.output_dir = output_dir
        self.chapters: List[Chapter] = []
        self.metadata: Dict[str, Any] = {}
        self.outline = ""
        self.state_file = os.path.join(output_dir, "novel_state.json")

        os.makedirs(output_dir, exist_ok=True)

    def save_state(self) -> bool:
        """
        Save the current state to a file

        Returns:
            True if successful, False otherwise
        """
        try:
            state = {
                "title": self.title,
                "chapters": [chapter.to_dict() for chapter in self.chapters],
                "metadata": self.metadata,
                "outline": self.outline,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # 保存状态文件
            with open(self.state_file, "w", encoding="utf-8") as f:
                json.dump(state, f, ensure_ascii=False, indent=2)

            # 同时保存大纲到novel_outline.txt文件
            if self.outline:
                outline_file = os.path.join(self.output_dir, "novel_outline.txt")
                with open(outline_file, "w", encoding="utf-8") as f:
                    f.write(self.outline)
                logger.info(f"Outline saved to: {outline_file}")

            logger.info(f"State saved to: {self.state_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving state: {e}")
            return False

    def load_state(self) -> bool:
        """
        Load state from a file

        Returns:
            True if successful, False otherwise
        """
        try:
            # Find novel files in the directory structure
            files = find_novel_files(self.output_dir)

            # Update state file path if found
            if files["state_file"]:
                self.state_file = files["state_file"]
                # Update output_dir to the directory containing the state file
                self.output_dir = os.path.dirname(self.state_file)
                logger.info(f"Updated output directory to: {self.output_dir}")

            # Load state from file
            if os.path.exists(self.state_file):
                with open(self.state_file, "r", encoding="utf-8") as f:
                    state = json.load(f)

                self.title = state.get("title", self.title)
                self.chapters = [Chapter.from_dict(ch) for ch in state.get("chapters", [])]
                self.metadata = state.get("metadata", {})
                self.outline = state.get("outline", "")

                logger.info(f"State loaded from: {self.state_file}")

                # 如果我们没有大纲但找到了novel_outline.txt，加载它
                outline_file = os.path.join(self.output_dir, "novel_outline.txt")
                if not self.outline and os.path.exists(outline_file):
                    try:
                        with open(outline_file, "r", encoding="utf-8") as f:
                            self.outline = f.read()
                        logger.info(f"Outline loaded from: {outline_file}")
                    except Exception as outline_error:
                        logger.error(f"Error loading outline: {outline_error}")

                return True

            # If we couldn't load the state file, try to reconstruct from chapter files
            if files["chapter_files"]:
                logger.info(f"No state file found, attempting to reconstruct from {len(files['chapter_files'])} chapter files")

                # Sort chapter files by number
                chapter_files = sorted(files["chapter_files"],
                                      key=lambda x: int(os.path.basename(x).split("_")[1].split(".")[0])
                                      if os.path.basename(x).split("_")[1].split(".")[0].isdigit() else 0)

                # Create chapters from files
                for file_path in chapter_files:
                    try:
                        # Extract chapter number from filename
                        filename = os.path.basename(file_path)
                        chapter_num = int(filename.split("_")[1].split(".")[0])

                        # Create chapter
                        chapter = self.add_chapter(chapter_num, f"第{chapter_num}章")

                        # Load content
                        with open(file_path, "r", encoding="utf-8") as f:
                            chapter.content = f.read()

                        # Set status
                        chapter.status = "completed"

                        # Check for illustration
                        if files["illustration_dir"]:
                            illustration_path = os.path.join(files["illustration_dir"], f"chapter_{chapter_num}.png")
                            if os.path.exists(illustration_path):
                                chapter.illustrations = [illustration_path]

                        logger.info(f"Reconstructed chapter {chapter_num} from {file_path}")
                    except Exception as chapter_error:
                        logger.error(f"Error reconstructing chapter from {file_path}: {chapter_error}")

                # If we have chapters, save the reconstructed state
                if self.chapters:
                    self.save_state()
                    logger.info(f"Saved reconstructed state to {self.state_file}")
                    return True

            logger.error(f"Could not load or reconstruct state from {self.output_dir}")
            return False
        except Exception as e:
            logger.error(f"Error loading state: {e}")
            return False

    def add_chapter(self, title_or_number: Union[str, int], title: Optional[str] = None) -> Chapter:
        """
        Add a new chapter or get an existing one

        Args:
            title_or_number: Chapter title or number
            title: Chapter title (if first argument is number)

        Returns:
            The new or existing chapter
        """
        try:
            # Handle different calling conventions
            if title is None:
                # Called as add_chapter(title)
                chapter_title = str(title_or_number)
                # Try to extract chapter number from title
                import re
                chapter_num_match = re.search(r'第(\d+)章', chapter_title)
                if chapter_num_match:
                    number = int(chapter_num_match.group(1))
                else:
                    number = len(self.chapters) + 1
            else:
                # Called as add_chapter(number, title)
                number = int(title_or_number)
                chapter_title = title

            # Check if chapter already exists
            existing_chapter = self.get_chapter(number)
            if existing_chapter:
                return existing_chapter

            # Create new chapter
            chapter = Chapter(number, chapter_title)
            self.chapters.append(chapter)

            # Sort chapters by number
            self.chapters.sort(key=lambda ch: ch.number)

            # 确保大纲文件存在并保存到novel_outline.txt
            if self.outline:
                outline_file = os.path.join(self.output_dir, "novel_outline.txt")
                if not os.path.exists(outline_file):
                    try:
                        with open(outline_file, "w", encoding="utf-8") as f:
                            f.write(self.outline)
                        logger.info(f"Created outline file: {outline_file}")
                    except Exception as e:
                        logger.error(f"Error creating outline file: {e}")

            self.save_state()
            return chapter
        except Exception as e:
            logger.error(f"Error adding chapter: {e}")
            # Create a dummy chapter as fallback
            chapter = Chapter(len(self.chapters) + 1, "Error Chapter")
            chapter.status = "error"
            chapter.metadata["error"] = str(e)
            self.chapters.append(chapter)
            self.save_state()
            return chapter

    def get_chapter(self, number: int) -> Optional[Chapter]:
        """
        Get a chapter by number

        Args:
            number: Chapter number

        Returns:
            The chapter, or None if not found
        """
        try:
            for chapter in self.chapters:
                if chapter.number == number:
                    return chapter
            return None
        except Exception as e:
            logger.error(f"Error getting chapter: {e}")
            return None

class LLMInterface(ABC):
    """Abstract interface for language model interactions"""

    @abstractmethod
    def generate_text(self, prompt: str, max_tokens: int = 4000) -> str:
        """
        Generate text using a language model

        Args:
            prompt: The prompt to send to the model
            max_tokens: Maximum number of tokens to generate

        Returns:
            Generated text
        """
        pass

    @abstractmethod
    def generate_image(self, prompt: str, output_path: str) -> str:
        """
        Generate an image using an image generation model

        Args:
            prompt: The prompt to send to the model
            output_path: Path to save the generated image

        Returns:
            Path to the generated image
        """
        pass

class ProgressCallback:
    """Class for handling progress callbacks"""

    def __init__(self, callback_fn: Optional[Callable[[str], None]] = None):
        """
        Initialize progress callback

        Args:
            callback_fn: Function to call with progress updates
        """
        self.callback_fn = callback_fn

    def update(self, message: str):
        """
        Update progress

        Args:
            message: Progress message
        """
        # Log the message
        logger.info(message)

        # Call the callback function if provided
        if self.callback_fn:
            self.callback_fn(message)

def find_novel_files(directory: str) -> Dict[str, Any]:
    """
    Find novel-related files in a directory structure

    Args:
        directory: Base directory to search in

    Returns:
        Dictionary mapping file types to file paths
    """
    result: Dict[str, Any] = {
        "state_file": None,
        "outline_file": None,
        "chapter_files": [],
        "illustration_dir": None
    }

    # Check if the directory exists
    if not os.path.exists(directory) or not os.path.isdir(directory):
        logger.error(f"Directory does not exist: {directory}")
        return result

    # First, check the current directory
    state_file = os.path.join(directory, "novel_state.json")
    if os.path.exists(state_file):
        result["state_file"] = state_file

    # 只检查novel_outline.txt作为大纲文件
    outline_file = os.path.join(directory, "novel_outline.txt")
    if os.path.exists(outline_file):
        result["outline_file"] = outline_file

    # Check for illustration directory
    illustration_dir = os.path.join(directory, "illustrations")
    if os.path.exists(illustration_dir) and os.path.isdir(illustration_dir):
        result["illustration_dir"] = illustration_dir

    # Find chapter files
    for file in os.listdir(directory):
        if file.startswith("chapter_") and file.endswith(".txt"):
            result["chapter_files"].append(os.path.join(directory, file))

    # If we didn't find the state file, check subdirectories
    if not result["state_file"]:
        for item in os.listdir(directory):
            item_path = os.path.join(directory, item)
            if os.path.isdir(item_path):
                # Check if this subdirectory has the state file
                sub_state_file = os.path.join(item_path, "novel_state.json")
                if os.path.exists(sub_state_file):
                    result["state_file"] = sub_state_file

                    # 只检查novel_outline.txt作为大纲文件
                    outline_file = os.path.join(item_path, "novel_outline.txt")
                    if os.path.exists(outline_file):
                        result["outline_file"] = outline_file

                    # Check for illustration directory
                    sub_illustration_dir = os.path.join(item_path, "illustrations")
                    if os.path.exists(sub_illustration_dir) and os.path.isdir(sub_illustration_dir):
                        result["illustration_dir"] = sub_illustration_dir

                    # Find chapter files
                    for file in os.listdir(item_path):
                        if file.startswith("chapter_") and file.endswith(".txt"):
                            result["chapter_files"].append(os.path.join(item_path, file))

                    # We found the state file, so we can stop searching
                    break

    # Log what we found
    logger.info(f"Found state file: {result['state_file']}")
    logger.info(f"Found outline file: {result['outline_file']}")
    logger.info(f"Found {len(result['chapter_files'])} chapter files")
    logger.info(f"Found illustration directory: {result['illustration_dir']}")

    return result

def create_timestamp_dir(base_dir: str, prefix: str) -> str:
    """
    Create a directory with a timestamp

    Args:
        base_dir: Base directory
        prefix: Directory name prefix

    Returns:
        Path to the created directory
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    dir_path = os.path.join(base_dir, f"{prefix}_{timestamp}")
    os.makedirs(dir_path, exist_ok=True)
    return dir_path


def ensure_outline_has_basics(novel_state: NovelState, llm_interface: LLMInterface) -> bool:
    """
    确保大纲包含故事基本情节、人物关系和事件设定

    Args:
        novel_state: 小说状态
        llm_interface: 语言模型接口

    Returns:
        bool: 是否成功添加基本信息
    """
    # 检查大纲是否存在
    if not novel_state.outline:
        logger.error("No outline found to enhance")
        return False

    # 检查大纲是否已经包含基本信息
    if "# 故事基本情节" in novel_state.outline and \
       "# 主要人物关系" in novel_state.outline and \
       "# 核心事件设定" in novel_state.outline:
        logger.info("Outline already has story basics")
        return True

    try:
        # 构建提示词，要求LLM生成基本信息
        prompt = f"""
请基于以下小说大纲，生成故事基本情节、主要人物关系和核心事件设定。

小说大纲：
{novel_state.outline}

请生成以下内容：
1. 故事基本情节：简要描述整个故事的主要情节发展
2. 主要人物关系：详细说明主要角色之间的关系
3. 核心事件设定：列出故事中的关键事件和转折点

请使用以下格式：

# 故事基本情节
[详细描述]

# 主要人物关系
[详细描述]

# 核心事件设定
[详细描述]
"""

        # 调用LLM生成基本信息
        basics_content = llm_interface.generate_text(prompt)

        # 确保生成的内容包含所需的标记
        if "# 故事基本情节" not in basics_content or \
           "# 主要人物关系" not in basics_content or \
           "# 核心事件设定" not in basics_content:
            logger.error("Generated content does not contain all required sections")
            return False

        # 将基本信息添加到大纲开头
        novel_state.outline = f"{basics_content}\n\n{novel_state.outline}"

        # 保存更新后的大纲
        outline_file = os.path.join(novel_state.output_dir, "novel_outline.txt")
        with open(outline_file, "w", encoding="utf-8") as f:
            f.write(novel_state.outline)

        # 保存状态
        novel_state.save_state()

        logger.info("Successfully added story basics to outline")
        return True
    except Exception as e:
        logger.error(f"Error adding story basics to outline: {e}")
        return False
