2025-03-16 21:06:58,522 - novel_creation - INFO - Logging initialized
2025-03-16 21:06:58,578 - novel_creation - DEBUG - Debug mode enabled
2025-03-16 21:06:58,789 - mcp_client - INFO - Initialized MCP client with session ID: novel_session_1742130418
2025-03-16 21:06:58,789 - novel_creation - INFO - Setting up the multi-agent system...
2025-03-16 21:06:58,790 - novel_creation - INFO - Starting novel generation...
2025-03-16 21:06:58,792 - mcp_client - INFO - Calling LLM model gpt-4 with prompt: 
    You are a creative director tasked with creat...
2025-03-16 21:06:59,300 - mcp_client - INFO - Calling LLM model gpt-4 with prompt: 
    You are a plot architect expanding a novel ou...
2025-03-16 21:06:59,805 - novel_creation - ERROR - An error occurred: 'NoneType' object is not subscriptable
Traceback (most recent call last):
  File "E:\storyteller\run.py", line 83, in main
    generate_novel(agents, args)
  File "E:\storyteller\example.py", line 91, in generate_novel
    agents["director"].handle_message({
  File "E:\storyteller\main.py", line 64, in handle_message
    self.send_message(
  File "E:\storyteller\main.py", line 22, in send_message
    agent.handle_message(message)
  File "E:\storyteller\main.py", line 97, in handle_message
    self.send_message(
  File "E:\storyteller\main.py", line 22, in send_message
    agent.handle_message(message)
  File "E:\storyteller\main.py", line 192, in handle_message
    self._start_generation()
  File "E:\storyteller\main.py", line 206, in _start_generation
    self.generate_chapter(chapter)
  File "E:\storyteller\main.py", line 219, in generate_chapter
    chapter_text = self._call_llm(chapter_info)
  File "E:\storyteller\main.py", line 228, in _call_llm
    protagonist = self.characters['protagonists'][0]['name'] if self.characters['protagonists'] else "Protagonist"
TypeError: 'NoneType' object is not subscriptable
2025-03-16 21:08:21,719 - novel_creation - INFO - Logging initialized
2025-03-16 21:08:21,754 - novel_creation - DEBUG - Debug mode enabled
2025-03-16 21:08:22,031 - mcp_client - INFO - Initialized MCP client with session ID: novel_session_1742130502
2025-03-16 21:08:22,031 - novel_creation - INFO - Setting up the multi-agent system...
2025-03-16 21:08:22,034 - novel_creation - INFO - Starting novel generation...
2025-03-16 21:08:22,035 - mcp_client - INFO - Calling LLM model gpt-4 with prompt: 
    You are a creative director tasked with creat...
2025-03-16 21:08:22,545 - mcp_client - INFO - Calling LLM model gpt-4 with prompt: 
    You are a plot architect expanding a novel ou...
2025-03-16 21:08:23,062 - mcp_client - INFO - Calling LLM model claude-3-sonnet with prompt: 
    You are a character designer creating charact...
2025-03-16 21:08:23,568 - mcp_client - INFO - Calling LLM model claude-3-opus with prompt: 
    You are writing Chapter 1: Chapter 1 for a no...
2025-03-16 21:08:24,079 - mcp_client - INFO - Calling LLM model claude-3-opus with prompt: 
    You are writing Chapter 2: Chapter 2 for a no...
2025-03-16 21:08:24,585 - mcp_client - INFO - Calling LLM model claude-3-opus with prompt: 
    You are writing Chapter 3: Chapter 3 for a no...
2025-03-16 21:08:25,096 - mcp_client - INFO - Calling LLM model gpt-4 with prompt: 
    You are an editor reviewing a chapter of a no...
2025-03-16 21:08:25,602 - mcp_client - INFO - Calling LLM model gpt-4 with prompt: 
    You are an editor reviewing a chapter of a no...
2025-03-16 21:08:26,107 - mcp_client - INFO - Calling LLM model gpt-4 with prompt: 
    You are an editor reviewing a chapter of a no...
