"""
日志模块
用于记录系统运行日志，提供专业的进程跟踪和错误报告
"""

import os
import sys
import time
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime

# 创建日志目录
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "logs")
os.makedirs(log_dir, exist_ok=True)

# 创建不同类型的日志文件
system_log_file = os.path.join(log_dir, "system.log")
process_log_file = os.path.join(log_dir, "process.log")
error_log_file = os.path.join(log_dir, "error.log")

# 配置日志格式
detailed_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
simple_format = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
process_format = logging.Formatter('%(asctime)s - PROCESS - %(message)s')

# 创建系统日志处理器
system_handler = RotatingFileHandler(
    system_log_file,
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
system_handler.setFormatter(detailed_format)
system_handler.setLevel(logging.INFO)

# 创建进程日志处理器
process_handler = RotatingFileHandler(
    process_log_file,
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
process_handler.setFormatter(process_format)
process_handler.setLevel(logging.INFO)

# 创建错误日志处理器
error_handler = RotatingFileHandler(
    error_log_file,
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
error_handler.setFormatter(detailed_format)
error_handler.setLevel(logging.ERROR)

# 创建控制台处理器 - 只显示重要信息
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(simple_format)
console_handler.setLevel(logging.INFO)

# 创建主日志记录器
logger = logging.getLogger("novel_system")
logger.setLevel(logging.INFO)
# 防止日志重复
logger.propagate = False
# 清除现有处理器
for handler in logger.handlers[:]:
    logger.removeHandler(handler)
logger.addHandler(system_handler)
logger.addHandler(error_handler)
logger.addHandler(console_handler)

# 创建进程日志记录器 - 仅记录进程流程，不输出内容
process_logger = logging.getLogger("novel_process")
process_logger.setLevel(logging.INFO)
# 防止日志重复
process_logger.propagate = False
# 清除现有处理器
for handler in process_logger.handlers[:]:
    process_logger.removeHandler(handler)
process_logger.addHandler(process_handler)

def get_logger():
    """获取主日志记录器"""
    return logger

def get_process_logger():
    """获取进程日志记录器"""
    return process_logger

def log_process_start(process_name):
    """记录进程开始"""
    process_logger.info(f"START: {process_name}")
    return time.time()

def log_process_end(process_name, start_time=None):
    """记录进程结束"""
    if start_time:
        elapsed = time.time() - start_time
        process_logger.info(f"END: {process_name} - 耗时: {elapsed:.2f}秒")
    else:
        process_logger.info(f"END: {process_name}")

def log_process_step(step_name, details=None):
    """记录进程步骤"""
    if details:
        process_logger.info(f"STEP: {step_name} - {details}")
    else:
        process_logger.info(f"STEP: {step_name}")

def log_error(error_message, exception=None):
    """记录错误"""
    if exception:
        logger.error(f"{error_message}: {str(exception)}")
    else:
        logger.error(error_message)

def log_model_usage(model_name, tokens_in, tokens_out, duration_ms, success=True):
    """记录模型使用情况

    参数:
        model_name: 模型名称
        tokens_in: 输入token数
        tokens_out: 输出token数
        duration_ms: 耗时（毫秒）
        success: 是否成功
    """
    # 创建模型使用日志文件
    usage_log_file = os.path.join(log_dir, "model_usage.log")

    # 记录模型使用情况
    try:
        with open(usage_log_file, "a", encoding="utf-8") as f:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            status = "SUCCESS" if success else "FAILED"
            f.write(f"{timestamp}\t{model_name}\t{tokens_in}\t{tokens_out}\t{duration_ms}\t{status}\n")
    except Exception as e:
        logger.error(f"记录模型使用情况时出错: {e}")

def log_chapter_generation(chapter_num, chapter_title, tokens, duration_ms, success=True):
    """记录章节生成情况

    参数:
        chapter_num: 章节号
        chapter_title: 章节标题
        tokens: 生成的token数
        duration_ms: 耗时（毫秒）
        success: 是否成功
    """
    # 创建章节生成日志文件
    chapter_log_file = os.path.join(log_dir, "chapter_generation.log")

    # 记录章节生成情况
    try:
        with open(chapter_log_file, "a", encoding="utf-8") as f:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            status = "SUCCESS" if success else "FAILED"
            f.write(f"{timestamp}\t第{chapter_num}章\t{chapter_title}\t{tokens}\t{duration_ms}\t{status}\n")
    except Exception as e:
        logger.error(f"记录章节生成情况时出错: {e}")

def setup_chapter_logger(novel_title, chapter_num):
    """设置章节日志记录器

    参数:
        novel_title: 小说标题
        chapter_num: 章节号

    返回:
        章节日志记录器
    """
    # 创建章节日志目录
    chapter_log_dir = os.path.join(log_dir, "chapters")
    os.makedirs(chapter_log_dir, exist_ok=True)

    # 创建章节日志文件
    chapter_log_file = os.path.join(chapter_log_dir, f"{novel_title}_chapter_{chapter_num}.log")

    # 创建章节日志记录器
    chapter_logger = logging.getLogger(f"chapter_{novel_title}_{chapter_num}")
    chapter_logger.setLevel(logging.INFO)

    # 清除现有处理器
    for handler in chapter_logger.handlers[:]:
        chapter_logger.removeHandler(handler)

    # 创建文件处理器
    file_handler = logging.FileHandler(chapter_log_file, encoding="utf-8")
    file_handler.setFormatter(detailed_format)
    chapter_logger.addHandler(file_handler)

    # 添加控制台处理器
    chapter_logger.addHandler(console_handler)

    return chapter_logger

# 导出日志记录器和函数
__all__ = ["logger", "process_logger", "get_logger", "get_process_logger",
           "log_process_start", "log_process_end", "log_process_step", "log_error",
           "log_model_usage", "log_chapter_generation", "setup_chapter_logger"]
