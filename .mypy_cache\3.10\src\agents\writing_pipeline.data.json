{".class": "MypyFile", "_fullname": "src.agents.writing_pipeline", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ADVANCED_FEATURES_ENABLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.agents.writing_pipeline.ADVANCED_FEATURES_ENABLED", "name": "ADVANCED_FEATURES_ENABLED", "type": "builtins.bool"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExpansionAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.expansion_agent.ExpansionAgent", "kind": "Gdef"}, "IllustrationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.illustration_agent.IllustrationAgent", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "OptimizationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.optimization_agent.OptimizationAgent", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PolishingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.polishing_agent.PolishingAgent", "kind": "Gdef"}, "ReviewAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.review_agent.ReviewAgent", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WritingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_agent.WritingAgent", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_pipeline.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_pipeline.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_pipeline.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_pipeline.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_pipeline.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_pipeline.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_segment_to_memory": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.add_segment_to_memory", "kind": "Gdef"}, "calculate_similarity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["str1", "str2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_pipeline.calculate_similarity", "name": "calculate_similarity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["str1", "str2"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calculate_similarity", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "check_chapter_exists": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["novel_dir", "chapter_number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_pipeline.check_chapter_exists", "name": "check_chapter_exists", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["novel_dir", "chapter_number"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_chapter_exists", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_outline_status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["novel_dir", "chapter_number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_pipeline.check_outline_status", "name": "check_outline_status", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["novel_dir", "chapter_number"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_outline_status", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_short_term_memory": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.clear_short_term_memory", "kind": "Gdef"}, "consistency_check_consistency": {".class": "SymbolTableNode", "cross_ref": "src.consistency.consistency_checker.check_consistency", "kind": "Gdef"}, "create_writing_pipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_pipeline.create_writing_pipeline", "name": "create_writing_pipeline", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_writing_pipeline", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "determine_illustration_style": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["novel_style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_pipeline.determine_illustration_style", "name": "determine_illustration_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["novel_style"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "determine_illustration_style", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.writing_pipeline.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "enhance_prompt_with_memory": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.enhance_prompt_with_memory", "kind": "Gdef"}, "generate_branch_options": {".class": "SymbolTableNode", "cross_ref": "src.narrative.narrative_controller.generate_branch_options", "kind": "Gdef"}, "generate_next_chapter_outline": {".class": "SymbolTableNode", "cross_ref": "src.narrative.narrative_controller.generate_next_chapter_outline", "kind": "Gdef"}, "get_consistency_checker": {".class": "SymbolTableNode", "cross_ref": "src.consistency.consistency_checker.get_consistency_checker", "kind": "Gdef"}, "get_memory_manager": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.get_memory_manager", "kind": "Gdef"}, "get_narrative_controller": {".class": "SymbolTableNode", "cross_ref": "src.narrative.narrative_controller.get_narrative_controller", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.agents.writing_pipeline.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "memory_check_consistency": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.check_consistency", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "register_writing_agents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_pipeline.register_writing_agents", "name": "register_writing_agents", "type": null}}, "remove_duplicate_paragraphs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_pipeline.remove_duplicate_paragraphs", "name": "remove_duplicate_paragraphs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_duplicate_paragraphs", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_pipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pipeline", "chapter_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_pipeline.run_pipeline", "name": "run_pipeline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pipeline", "chapter_context"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_pipeline", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_consistency_log": {".class": "SymbolTableNode", "cross_ref": "src.consistency.consistency_checker.save_consistency_log", "kind": "Gdef"}, "select_optimal_path": {".class": "SymbolTableNode", "cross_ref": "src.narrative.narrative_controller.select_optimal_path", "kind": "Gdef"}, "setup_logging": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_pipeline.setup_logging", "name": "setup_logging", "type": null}}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "update_memory_from_chapter": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.update_memory_from_chapter", "kind": "Gdef"}}, "path": "E:\\storymc\\src\\agents\\writing_pipeline.py"}