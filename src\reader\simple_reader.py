import os
import re
import glob
import logging
import gradio as gr
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SimpleReader")

def get_chapter_files(novel_dir):
    """获取小说目录中的所有章节文件"""
    if not novel_dir or not os.path.exists(novel_dir):
        return []

    # 查找所有章节文件
    chapter_files = glob.glob(os.path.join(novel_dir, "chapter_*_final.txt"))

    # 如果没有找到final文件，尝试查找其他章节文件
    if not chapter_files:
        chapter_files = glob.glob(os.path.join(novel_dir, "chapter_*.txt"))

    # 排序章节文件
    chapter_files.sort(key=lambda x: int(re.search(r'chapter_(\d+)', os.path.basename(x)).group(1)))

    return chapter_files

def get_chapter_title(chapter_file):
    """从章节文件名中提取章节标题"""
    match = re.search(r'chapter_(\d+)', os.path.basename(chapter_file))
    if match:
        chapter_num = match.group(1)
        return f"第{chapter_num}章"
    return os.path.basename(chapter_file)

def get_illustration_for_chapter(novel_dir, chapter_num):
    """获取章节对应的插图"""
    if not novel_dir or not os.path.exists(novel_dir):
        return None

    # 查找插图目录
    illustrations_dir = os.path.join(novel_dir, "illustrations")
    if not os.path.exists(illustrations_dir):
        return None

    # 查找章节对应的插图
    illustration_files = [
        os.path.join(illustrations_dir, f)
        for f in os.listdir(illustrations_dir)
        if f.startswith(f"chapter_{chapter_num}") and (f.endswith(".png") or f.endswith(".jpg"))
    ]

    # 如果找不到精确匹配的插图，返回任何可能的插图
    if not illustration_files:
        illustration_files = [
            os.path.join(illustrations_dir, f)
            for f in os.listdir(illustrations_dir)
            if f.endswith(".png") or f.endswith(".jpg")
        ]

    return illustration_files[0] if illustration_files else None

def read_chapter_content(chapter_file):
    """读取章节内容"""
    try:
        with open(chapter_file, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.error(f"读取章节内容时出错: {e}")
        return f"读取章节内容时出错: {e}"

def get_novel_info(novel_dir):
    """获取小说信息"""
    if not novel_dir or not os.path.exists(novel_dir):
        return {"title": "未知小说", "author": "未知作者", "genre": "未知类型", "style": "未知风格"}

    # 尝试从novel_info.json读取小说信息
    import json
    novel_info_file = os.path.join(novel_dir, "novel_info.json")
    if os.path.exists(novel_info_file):
        try:
            with open(novel_info_file, "r", encoding="utf-8") as f:
                novel_info = json.load(f)
            return {
                "title": novel_info.get("title", "未知小说"),
                "author": novel_info.get("author", "AI作家"),
                "genre": novel_info.get("genre", "未知类型"),
                "style": novel_info.get("style", "未知风格")
            }
        except Exception as e:
            logger.error(f"读取小说信息时出错: {e}")

    # 如果无法从novel_info.json读取，尝试从目录名称提取
    novel_name = os.path.basename(novel_dir)
    return {
        "title": novel_name,
        "author": "AI作家",
        "genre": "未知类型",
        "style": "未知风格"
    }

def get_subdirectories(parent_dir):
    """获取指定目录下的所有子目录"""
    if not parent_dir or not os.path.exists(parent_dir):
        return []

    try:
        subdirs = [d for d in os.listdir(parent_dir) if os.path.isdir(os.path.join(parent_dir, d))]
        return subdirs
    except Exception as e:
        logger.error(f"获取子目录时出错: {e}")
        return []

def launch_reader(novel_dir=None):
    """启动阅读器"""
    # 确保output目录存在
    output_dir = "output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    # 如果没有提供小说目录，默认使用output目录
    if not novel_dir:
        novel_dir = output_dir

    with gr.Blocks(css=custom_css()) as reader:
        # 初始化阅读设置状态
        reading_settings = {
            "font_size": "medium",  # small, medium, large, xlarge
            "line_height": "comfortable",  # normal, comfortable, relaxed
            "theme": "light"  # light, dark
        }

        gr.Markdown("# 📚 小说阅读器")

        with gr.Row():
            with gr.Column(scale=1, elem_classes="sidebar-column"):
                # 目录浏览器
                parent_dir = gr.Textbox(label="父目录", value=os.path.abspath(output_dir))
                refresh_parent_btn = gr.Button("刷新目录列表")

                # 子目录列表
                subdirs = get_subdirectories(parent_dir.value)
                subdir_dropdown = gr.Dropdown(
                    label="选择小说目录",
                    choices=subdirs,
                    interactive=True,
                    allow_custom_value=True
                )

                # 加载按钮
                load_novel_btn = gr.Button("加载选中的小说", variant="primary")

                # 小说信息
                novel_info_md = gr.Markdown("### 小说信息\n\n请选择小说目录")

                # 阅读设置面板
                with gr.Group(elem_classes="reading-settings"):
                    gr.Markdown("### 阅读设置")

                    # 主题切换
                    theme_toggle = gr.Radio(
                        choices=["日间模式", "夜间模式"],
                        label="阅读主题",
                        value="日间模式",
                        interactive=True
                    )

                    # 字体大小
                    font_size = gr.Radio(
                        choices=["小", "中", "大", "超大"],
                        label="字体大小",
                        value="中",
                        interactive=True
                    )

                    # 行距
                    line_height = gr.Radio(
                        choices=["紧凑", "舒适", "宽松"],
                        label="行距",
                        value="舒适",
                        interactive=True
                    )

                # 章节列表
                gr.Markdown("### 章节列表")
                chapter_list = gr.Dropdown(label="选择章节", choices=[], interactive=True)

            with gr.Column(scale=3, elem_classes="content-column"):
                # 章节内容
                chapter_title_md = gr.Markdown("## 请选择章节")

                # 导航按钮
                with gr.Row():
                    prev_chapter_button = gr.Button("上一章", elem_classes="nav-button")
                    next_chapter_button = gr.Button("下一章", elem_classes="nav-button")

                # 插图
                chapter_illustration = gr.Image(label="章节插图", type="filepath", interactive=False)

                # 章节内容
                chapter_content = gr.Textbox(
                    label="章节内容",
                    lines=25,
                    max_lines=50,
                    interactive=False,
                    elem_id="chapter-content-textbox",
                    elem_classes="font-size-medium line-height-comfortable"
                )

        # 刷新父目录函数
        def refresh_parent_directory(parent_dir):
            subdirs = get_subdirectories(parent_dir)
            return gr.Dropdown(choices=subdirs)

        # 加载小说函数
        def load_novel(parent_dir, subdir):
            if not subdir:
                return [], "### 小说信息\n\n请选择小说目录"

            # 构建完整的小说目录路径
            novel_dir = os.path.join(parent_dir, subdir)
            if not os.path.exists(novel_dir):
                return [], f"### 小说信息\n\n错误: 目录不存在: {novel_dir}"

            # 获取章节文件
            chapter_files = get_chapter_files(novel_dir)
            chapter_choices = [(get_chapter_title(f), f) for f in chapter_files]

            # 获取小说信息
            novel_info = get_novel_info(novel_dir)
            info_md = f"### 小说信息\n\n**标题**: {novel_info['title']}\n\n**作者**: {novel_info['author']}\n\n**类型**: {novel_info['genre']}\n\n**风格**: {novel_info['style']}\n\n**章节数**: {len(chapter_files)}"

            return chapter_choices, info_md

        # 加载章节内容函数
        def load_chapter_content(chapter_file):
            if not chapter_file or not os.path.exists(chapter_file):
                return "## 请选择章节", "请选择有效的章节", None

            chapter_title = get_chapter_title(chapter_file)
            chapter_content = read_chapter_content(chapter_file)

            # 提取章节编号
            match = re.search(r'chapter_(\d+)', os.path.basename(chapter_file))
            chapter_num = match.group(1) if match else "0"

            # 获取插图
            novel_dir = os.path.dirname(chapter_file)
            illustration = get_illustration_for_chapter(novel_dir, chapter_num)

            return f"## {chapter_title}", chapter_content, illustration

        # 导航函数
        def navigate_chapter(direction, current_chapter, chapter_choices):
            if not chapter_choices:
                return current_chapter, "## 请选择章节", "请先选择小说目录", None

            # 找到当前章节的索引
            current_index = -1
            for i, (title, file) in enumerate(chapter_choices):
                if file == current_chapter:
                    current_index = i
                    break

            # 计算新的索引
            if direction == "next":
                new_index = min(current_index + 1, len(chapter_choices) - 1)
            else:  # prev
                new_index = max(current_index - 1, 0)

            # 如果没有找到当前章节或者没有变化，返回第一章
            if current_index == -1 or new_index == current_index:
                if direction == "next" and current_index == -1:
                    new_index = 0
                elif direction == "prev" and current_index == -1:
                    new_index = 0
                else:
                    return current_chapter, "## 已经是最" + ("后" if direction == "next" else "前") + "一章", chapter_content.value, chapter_illustration.value

            # 加载新章节
            new_chapter_file = chapter_choices[new_index][1]
            chapter_title, content, illustration = load_chapter_content(new_chapter_file)

            return new_chapter_file, chapter_title, content, illustration

        # 定义阅读设置函数
        def update_theme(theme_value):
            if theme_value == "夜间模式":
                return "dark-mode"
            else:
                return ""

        def update_font_size(size_value):
            if size_value == "小":
                return "font-size-small"
            elif size_value == "大":
                return "font-size-large"
            elif size_value == "超大":
                return "font-size-xlarge"
            else:  # 中
                return "font-size-medium"

        def update_line_height(height_value):
            if height_value == "紧凑":
                return "line-height-normal"
            elif height_value == "宽松":
                return "line-height-relaxed"
            else:  # 舒适
                return "line-height-comfortable"

        def apply_reading_settings(theme_value, size_value, height_value):
            # 更新主题
            theme_class = update_theme(theme_value)
            # 更新字体大小
            font_size_class = update_font_size(size_value)
            # 更新行距
            line_height_class = update_line_height(height_value)

            # 返回 JavaScript 代码来更新页面样式
            js_code = f"""
            (function() {{
                // 更新主题
                document.body.className = '{theme_class}';

                // 更新字体大小和行距
                var contentBox = document.getElementById('chapter-content-textbox');
                if (contentBox) {{
                    // 移除所有字体大小类
                    contentBox.classList.remove('font-size-small', 'font-size-medium', 'font-size-large', 'font-size-xlarge');
                    // 移除所有行距类
                    contentBox.classList.remove('line-height-normal', 'line-height-comfortable', 'line-height-relaxed');
                    // 添加新的类
                    contentBox.classList.add('{font_size_class}', '{line_height_class}');
                }}

                // 添加淡入效果
                document.body.classList.add('fade-in');
                setTimeout(function() {{
                    document.body.classList.remove('fade-in');
                }}, 500);
            }})();
            """

            return js_code

        # 绑定事件
        refresh_parent_btn.click(
            fn=refresh_parent_directory,
            inputs=[parent_dir],
            outputs=[subdir_dropdown]
        )

        load_novel_btn.click(
            fn=load_novel,
            inputs=[parent_dir, subdir_dropdown],
            outputs=[chapter_list, novel_info_md]
        )

        chapter_list.change(
            fn=load_chapter_content,
            inputs=[chapter_list],
            outputs=[chapter_title_md, chapter_content, chapter_illustration]
        )

        prev_chapter_button.click(
            fn=lambda ch, choices: navigate_chapter("prev", ch, choices),
            inputs=[chapter_list, chapter_list],
            outputs=[chapter_list, chapter_title_md, chapter_content, chapter_illustration]
        )

        next_chapter_button.click(
            fn=lambda ch, choices: navigate_chapter("next", ch, choices),
            inputs=[chapter_list, chapter_list],
            outputs=[chapter_list, chapter_title_md, chapter_content, chapter_illustration]
        )

        # 绑定阅读设置事件
        try:
            # 尝试使用 _js 参数
            theme_toggle.change(
                fn=apply_reading_settings,
                inputs=[theme_toggle, font_size, line_height],
                outputs=[],
                _js=apply_reading_settings
            )

            font_size.change(
                fn=apply_reading_settings,
                inputs=[theme_toggle, font_size, line_height],
                outputs=[],
                _js=apply_reading_settings
            )

            line_height.change(
                fn=apply_reading_settings,
                inputs=[theme_toggle, font_size, line_height],
                outputs=[],
                _js=apply_reading_settings
            )
        except Exception as e:
            # 如果出错，尝试使用兼容方式
            logger.warning(f"使用 _js 参数绑定事件时出错: {e}")
            try:
                # 尝试使用兼容方式
                theme_toggle.change(
                    fn=apply_reading_settings,
                    inputs=[theme_toggle, font_size, line_height],
                    outputs=[],
                    js=apply_reading_settings()
                )

                font_size.change(
                    fn=apply_reading_settings,
                    inputs=[theme_toggle, font_size, line_height],
                    outputs=[],
                    js=apply_reading_settings()
                )

                line_height.change(
                    fn=apply_reading_settings,
                    inputs=[theme_toggle, font_size, line_height],
                    outputs=[],
                    js=apply_reading_settings()
                )
            except Exception as e2:
                # 如果仍然出错，尝试不使用 JavaScript
                logger.error(f"使用兼容方式绑定事件时出错: {e2}")
                # 只绑定 Python 函数，不使用 JavaScript
                theme_toggle.change(
                    fn=apply_reading_settings,
                    inputs=[theme_toggle, font_size, line_height],
                    outputs=[]
                )

                font_size.change(
                    fn=apply_reading_settings,
                    inputs=[theme_toggle, font_size, line_height],
                    outputs=[]
                )

                line_height.change(
                    fn=apply_reading_settings,
                    inputs=[theme_toggle, font_size, line_height],
                    outputs=[]
                )

        # 初始化页面设置
        def init_page_settings():
            # 返回初始化页面的JavaScript代码
            return """
            (function() {
                // 初始化页面设置
                document.body.classList.add('fade-in');

                // 设置内容框的初始样式
                var contentBox = document.getElementById('chapter-content-textbox');
                if (contentBox) {
                    contentBox.classList.add('font-size-medium', 'line-height-comfortable');
                }

                // 添加自定义字体
                var fontLink = document.createElement('link');
                fontLink.rel = 'stylesheet';
                fontLink.href = 'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=Noto+Serif+SC:wght@400;600;700&display=swap';
                document.head.appendChild(fontLink);

                // 添加图标
                var iconLink = document.createElement('link');
                iconLink.rel = 'stylesheet';
                iconLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
                document.head.appendChild(iconLink);

                // 添加页面标题
                document.title = '小说阅读器 - 专业版';

                // 移除淡入效果
                setTimeout(function() {
                    document.body.classList.remove('fade-in');
                }, 500);
            })();
            """

        # 如果提供了小说目录，自动加载章节列表
        if novel_dir and os.path.exists(novel_dir) and novel_dir != output_dir:
            # 获取相对路径
            rel_path = os.path.relpath(novel_dir, output_dir)
            if rel_path == ".":
                # 如果是output目录本身，不自动加载
                pass
            elif os.path.dirname(rel_path) == "":
                # 如果是output的直接子目录
                reader.load(
                    fn=lambda: (rel_path, get_subdirectories(output_dir)),
                    inputs=None,
                    outputs=[subdir_dropdown, subdir_dropdown]
                ).then(
                    fn=load_novel,
                    inputs=[parent_dir, subdir_dropdown],
                    outputs=[chapter_list, novel_info_md]
                )
            else:
                # 如果是更深层次的目录，不自动加载
                pass

        # 初始化页面设置
        try:
            # 尝试使用 _js 参数
            reader.load(_js=init_page_settings)
        except Exception as e:
            # 如果出错，尝试使用兼容方式
            logger.warning(f"使用 _js 参数加载初始化脚本时出错: {e}")
            try:
                # 尝试使用兼容方式
                reader.load(fn=lambda: None, inputs=None, outputs=None, js=init_page_settings())
            except Exception as e2:
                logger.error(f"使用兼容方式加载初始化脚本时出错: {e2}")

    # 启动阅读器
    reader.queue(max_size=10).launch(server_name="127.0.0.1", server_port=4556, share=False, prevent_thread_lock=True)

    # 返回阅读器URL
    return "http://127.0.0.1:4556"

def custom_css():
    """自定义CSS样式"""
    return """
    /* 全局变量 */
    :root {
        --primary-color: #3498db;
        --secondary-color: #2c3e50;
        --background-light: #f8f9fa;
        --background-dark: #2c3e50;
        --text-light: #333;
        --text-dark: #ecf0f1;
        --accent-color: #e74c3c;
        --border-radius: 8px;
        --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        --transition: all 0.3s ease;
        --font-sans: 'Noto Sans SC', 'Microsoft YaHei', 'PingFang SC', sans-serif;
        --font-serif: 'Noto Serif SC', 'SimSun', 'STSong', serif;
        --font-mono: 'Source Code Pro', 'Consolas', monospace;
    }

    /* 整体样式 */
    body {
        font-family: var(--font-sans);
        background-color: var(--background-light);
        color: var(--text-light);
        transition: var(--transition);
    }

    /* 标题样式 */
    h1, h2, h3 {
        font-family: var(--font-serif);
        color: var(--secondary-color);
        letter-spacing: 0.02em;
        font-weight: 600;
    }

    h1 {
        font-size: 2.2rem;
        margin-bottom: 1.5rem;
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 0.5rem;
        display: inline-block;
    }

    h2 {
        font-size: 1.8rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }

    h3 {
        font-size: 1.4rem;
        margin-bottom: 0.8rem;
    }

    /* 容器样式 */
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    /* 卡片样式 */
    .gr-box, .gr-panel {
        border-radius: var(--border-radius) !important;
        box-shadow: var(--box-shadow) !important;
        overflow: hidden;
        transition: var(--transition);
        border: none !important;
    }

    .gr-box:hover, .gr-panel:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15) !important;
    }

    /* 按钮样式 */
    button, .gr-button {
        border-radius: var(--border-radius) !important;
        font-weight: 500 !important;
        transition: var(--transition) !important;
        text-transform: none !important;
        letter-spacing: 0.02em !important;
        padding: 0.6rem 1.2rem !important;
    }

    button:hover, .gr-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    button[variant="primary"], .gr-button.gr-button-primary {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    button[variant="secondary"], .gr-button.gr-button-secondary {
        background-color: var(--secondary-color) !important;
        color: white !important;
    }

    /* 导航按钮样式 */
    .nav-button {
        min-width: 120px;
        font-size: 1.1rem !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 下拉菜单样式 */
    .gr-dropdown {
        border-radius: var(--border-radius) !important;
        box-shadow: var(--box-shadow) !important;
    }

    /* 文本框样式 */
    textarea, .gr-text-input, .gr-textbox {
        font-family: var(--font-sans) !important;
        font-size: 18px !important;
        line-height: 1.8 !important;
        padding: 20px !important;
        border-radius: var(--border-radius) !important;
        border: 1px solid #e0e0e0 !important;
        transition: var(--transition) !important;
    }

    textarea:focus, .gr-text-input:focus, .gr-textbox:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2) !important;
    }

    /* 章节内容样式 */
    .chapter-content {
        font-size: 18px;
        line-height: 1.8;
        letter-spacing: 0.05em;
        text-align: justify;
        padding: 30px;
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        max-width: 800px;
        margin: 0 auto;
    }

    /* 章节内容文本框特殊样式 */
    #chapter-content-textbox {
        font-family: var(--font-serif) !important;
        font-size: 18px !important;
        line-height: 1.8 !important;
        letter-spacing: 0.05em !important;
        text-align: justify !important;
        padding: 30px !important;
        background-color: white !important;
        border: none !important;
        box-shadow: none !important;
    }

    /* 章节列表样式 */
    .chapter-list {
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    /* 小说信息样式 */
    .novel-info {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 20px;
        box-shadow: var(--box-shadow);
        margin-bottom: 20px;
    }

    /* 插图样式 */
    .illustration {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--box-shadow);
        margin: 20px auto;
        max-width: 100%;
        display: block;
    }

    /* 自定义滚动条 */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* 夜间模式 */
    body.dark-mode {
        background-color: var(--background-dark);
        color: var(--text-dark);
    }

    body.dark-mode h1, body.dark-mode h2, body.dark-mode h3 {
        color: #ecf0f1;
    }

    body.dark-mode .gr-box, body.dark-mode .gr-panel {
        background-color: #34495e !important;
        color: #ecf0f1 !important;
    }

    body.dark-mode textarea, body.dark-mode .gr-text-input, body.dark-mode .gr-textbox {
        background-color: #2c3e50 !important;
        color: #ecf0f1 !important;
        border-color: #4a6278 !important;
    }

    body.dark-mode #chapter-content-textbox {
        background-color: #2c3e50 !important;
        color: #ecf0f1 !important;
    }

    body.dark-mode .chapter-content {
        background-color: #2c3e50;
        color: #ecf0f1;
    }

    /* 布局调整 */
    .content-column {
        padding: 0 20px;
    }

    .sidebar-column {
        padding: 0 10px;
    }

    /* 阅读设置面板 */
    .reading-settings {
        background-color: white;
        border-radius: var(--border-radius);
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: var(--box-shadow);
        border: 1px solid #e0e0e0;
    }

    body.dark-mode .reading-settings {
        background-color: #34495e;
        border-color: #4a6278;
    }

    /* 字体大小选择器 */
    .font-size-selector {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    /* 常见字体大小 */
    .font-size-small {
        font-size: 16px !important;
    }

    .font-size-medium {
        font-size: 18px !important;
    }

    .font-size-large {
        font-size: 20px !important;
    }

    .font-size-xlarge {
        font-size: 22px !important;
    }

    /* 行距调整 */
    .line-height-normal {
        line-height: 1.6 !important;
    }

    .line-height-comfortable {
        line-height: 1.8 !important;
    }

    .line-height-relaxed {
        line-height: 2.0 !important;
    }

    /* 页面过渡效果 */
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .chapter-content {
            padding: 15px;
            font-size: 16px;
        }

        h1 {
            font-size: 1.8rem;
        }

        h2 {
            font-size: 1.5rem;
        }
    }
    """

if __name__ == "__main__":
    # 如果直接运行此脚本，启动阅读器
    launch_reader()
