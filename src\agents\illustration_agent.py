"""
插图智能体模块
负责生成小说插图
"""

import os
import json
import time
import requests  # 用于下载图片和调用API
from typing import Dict, Any, Optional, cast
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont

try:
    from src.core.agent import Agent, AGENT_REGISTRY
except ImportError:
    # 如果导入失败，创建简单的Agent类
    class Agent:
        def __init__(self, name, model=None):
            self.name = name
            self.model = model

    # 创建简单的AGENT_REGISTRY类
    class AgentRegistry:
        def __init__(self):
            self.agents = {}

        def register(self, agent):
            self.agents[agent.name] = agent
            return agent

        def get_agent(self, name):
            return self.agents.get(name)

    AGENT_REGISTRY = AgentRegistry()

try:
    from src.core.prompt_manager import PromptManager
except ImportError:
    # 如果导入失败，创建简单的PromptManager类
    class PromptManager:
        def get_prompt(self, prompt_name, **kwargs):
            return f"提示词 {prompt_name} 示例内容"

try:
    from src.core.model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES
except ImportError:
    # 如果导入失败，创建简单的配置
    AI_MODEL_CONFIG = {
        "agent_models": {},
        "default_model": "glm-4-flash"
    }
    DEFAULT_MODEL_NAMES = {"glm": "glm-4-flash"}

# 初始化提示词管理器
prompt_manager = PromptManager()

class IllustrationAgent(Agent):
    """
    插图智能体，负责生成小说插图
    """

    def __init__(self, name: str, model: Optional[str] = None):
        """初始化插图智能体"""
        agent_models = cast(Dict[str, Any], AI_MODEL_CONFIG.get("agent_models", {}))
        default_model = cast(str, AI_MODEL_CONFIG.get("default_model", ""))

        model = model or agent_models.get("IllustrationAgent", "cogview-3-flash")
        super().__init__(name, model)
        self.prompt_manager = prompt_manager

    def handle_message(self, msg):
        """处理接收到的消息"""
        if msg['type'] == 'generate_illustration':
            print(f"插图智能体接收到生成请求...")
            illustration_path = self.generate_illustration(
                scene_description=msg.get('scene_description', ''),
                style=msg.get('style', '写实风格'),
                output_path=msg.get('output_path', None)
            )

            # 发送结果给下一个智能体（如果指定了）
            if 'next_agent' in msg:
                self.send_message(
                    msg['next_agent'],
                    {
                        'type': 'illustration_result',
                        'illustration_path': illustration_path,
                        'metadata': msg.get('metadata', {})
                    }
                )

            return illustration_path

    def extract_landscape_description(self, content: str, max_length: int = 500) -> str:
        """
        从内容中提取风景描述，确保不包含人物

        参数:
            content: 原始内容
            max_length: 最大长度

        返回:
            风景描述
        """
        if not content:
            return "一幅美丽的自然风景画，没有人物"

        # 使用LLM提取风景描述
        prompt = f"""
提取以下小说章节中最生动、最具视觉冲击力的风景或场景描述。
只关注自然风景、建筑、环境等元素，完全不要包含任何人物或角色描述。
提取的内容应该适合生成一幅风景插图。

小说内容:
{content[:2000]}...

请只返回纯粹的风景/场景描述，不要包含任何解释、人物描述或额外内容。
描述应该详细、生动，突出视觉元素如颜色、光线、形状等。
"""
        try:
            landscape_description = self.call_llm(prompt)
            if landscape_description and len(landscape_description) > 50:
                # 确保不包含人物描述
                no_character_prompt = f"""
修改以下场景描述，确保完全移除所有人物、角色和人类元素的描述，只保留纯粹的风景、建筑和环境描述：

{landscape_description}

请返回修改后的纯风景描述，不要包含任何解释。
"""
                landscape_description = self.call_llm(no_character_prompt)

                # 截断到最大长度
                if len(landscape_description) > max_length:
                    landscape_description = landscape_description[:max_length]

                return landscape_description
        except Exception as e:
            print(f"提取风景描述时出错: {e}")

        # 如果提取失败，返回默认描述
        return "一幅美丽的自然风景画，展现小说中的场景，没有人物"

    def generate_illustration(self, scene_description: str, style: str = "写实风格", output_path: Optional[str] = None) -> str:
        """
        生成小说插图

        参数:
            scene_description: 场景描述
            style: 风格
            output_path: 输出路径

        返回:
            生成的插图路径
        """
        print(f"正在生成插图，风格: {style}")

        # 确保场景描述不包含人物
        landscape_description = self.extract_landscape_description(scene_description)
        print(f"提取的风景描述 ({len(landscape_description)} 字符): {landscape_description[:100]}...")

        # 如果没有提供输出路径，创建一个默认路径
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = "output/illustrations"
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"illustration_{timestamp}.png")

        # 构建增强的提示词，确保生成风景而非人物
        enhanced_prompt = f"""
{landscape_description}

风格: {style}
高质量、详细的中国小说风景插图。
纯自然风景，无人物，无角色，无人类元素。
美丽的风景摄影风格，电影级光照，全景视角。
山川、河流、森林、建筑、城市、自然、天气效果。
"""

        # 添加负面提示词，确保不生成人物
        negative_prompt = "人物, 人脸, 人像, 人体, 人类, 手, 脚, 手指, 人形, 人影, 人群, 人物特写, 人物肖像, 人物表情, 人物动作, 人物姿态, 人物身体部位, 人物衣物, 人物面容, 人物头部, 人物脸部, 人物身体, 人物转头, 人物肩膀, 人物手臂, 人物脚部, 人物身体特写"

        try:
            # 获取生成插图的提示词
            prompt = self.prompt_manager.get_prompt(
                "illustration_agent",
                scene_description=landscape_description,
                style=style,
                negative_prompt=negative_prompt
            )

            # 调用CogView-3生成图片
            print(f"调用模型 {self.model} 生成图片...")

            # 确保output_path的目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 从模型配置中获取API密钥和基础URL
            try:
                from src.core.model_config import AI_MODEL_CONFIG
            except ImportError:
                # 如果导入失败，使用已定义的AI_MODEL_CONFIG
                pass
            models_config = AI_MODEL_CONFIG.get("models", {})

            # 根据模型类型选择不同的API调用方式
            if "cogview" in self.model.lower() or self.model.lower() == "cogview-3-flash":
                # 使用zhipuai库调用CogView-3 API
                try:
                    # 导入zhipuai库
                    import zhipuai
                    from zhipuai import ZhipuAI

                    # 创建客户端
                    api_key = models_config.get(self.model, {}).get("api_key", os.environ.get("ZHIPUAI_API_KEY", ""))
                    if not api_key:
                        print("警告: 未找到智谱API密钥，无法生成插图")
                        return self._create_fallback_image(landscape_description, style, output_path)

                    client = ZhipuAI(api_key=api_key)

                    # 调用API生成图片
                    # 确保使用正确的模型名称
                    model_name = "cogview-3-flash"  # 使用固定的模型名称，避免使用可能不存在的模型
                    print(f"使用模型: {model_name} 生成图片...")
                    print(f"提示词: {enhanced_prompt[:100]}...")

                    # 设置重试参数
                    max_retries = 3
                    retry_delay = 5  # 秒

                    # 重试机制
                    for retry in range(max_retries):
                        try:
                            print(f"调用CogView-3 API，提示词: {enhanced_prompt[:100]}...，尝试次数: {retry + 1}/{max_retries}")
                            # 根据官方示例调用API，不使用negative_prompt参数
                            response = client.images.generations(
                                model=model_name,
                                prompt=enhanced_prompt,
                                n=1,
                                size="1024x1024"
                                # 注意：cogview-3-flash不支持negative_prompt参数
                            )
                            # 如果请求成功，跳出重试循环
                            break
                        except Exception as api_error:
                            # 处理API错误
                            error_message = str(api_error)
                            print(f"调用CogView-3 API时出错: {error_message}，尝试次数: {retry + 1}/{max_retries}")

                            # 如果是“requests”变量相关的错误，尝试使用其他方式
                            if "local variable 'requests' referenced before assignment" in error_message:
                                print("检测到requests变量错误，尝试使用其他方式下载图片")
                                try:
                                    # 尝试使用urllib下载图片
                                    import urllib.request
                                    response = client.images.generations(
                                        model=model_name,
                                        prompt=enhanced_prompt,
                                        n=1,
                                        size="1024x1024"
                                    )
                                    # 如果请求成功，跳出重试循环
                                    break
                                except Exception as e:
                                    print(f"尝试替代方法也失败: {e}")

                            # 如果是negative_prompt参数错误，已经在上面处理了，这里不需要重复处理

                            # 如果是最后一次重试，返回错误
                            if retry == max_retries - 1:
                                print(f"调用CogView-3 API失败，所有重试均失败")
                                raise

                            # 等待后重试
                            time.sleep(retry_delay * (retry + 1))

                    # 处理响应
                    if response and hasattr(response, "data") and len(response.data) > 0:
                        image_url = response.data[0].url
                        if image_url:
                            try:
                                # 尝试使用requests下载图片
                                import requests as req  # 在这里局部导入requests库
                                image_response = req.get(image_url)
                                if image_response.status_code == 200:
                                    with open(output_path, "wb") as f:
                                        f.write(image_response.content)
                                    print(f"插图已保存到: {output_path}")
                                    return output_path
                                else:
                                    print(f"下载图片失败，状态码: {image_response.status_code}")
                            except Exception as download_error:
                                # 如果使用requests失败，尝试使用urllib
                                print(f"使用requests下载图片失败: {download_error}，尝试使用urllib")
                                try:
                                    import urllib.request
                                    urllib.request.urlretrieve(image_url, output_path)
                                    print(f"使用urllib下载插图成功，已保存到: {output_path}")
                                    return output_path
                                except Exception as urllib_error:
                                    print(f"使用urllib下载图片也失败: {urllib_error}")
                        else:
                            print("API返回的图片URL为空")
                    else:
                        print("API响应无效")

                except Exception as e:
                    print(f"调用CogView-3 API时出错: {e}")

            elif "kolors" in self.model.lower() or self.model == "Kwai-Kolors/Kolors":
                # 使用requests库调用硅基流动的Kolors API
                try:
                    # 使用全局导入的requests库

                    # 获取API密钥
                    api_key = models_config.get(self.model, {}).get("api_key", os.environ.get("SILICONFLOW_API_KEY", ""))
                    if not api_key:
                        print("警告: 未找到硅基流动API密钥，无法生成插图")
                        return self._create_fallback_image(landscape_description, style, output_path)

                    # 准备请求头和请求体
                    headers = {
                        "Authorization": f"Bearer {api_key}",
                        "Content-Type": "application/json"
                    }

                    # 根据官方示例准备请求体
                    data = {
                        "model": "Kwai-Kolors/Kolors",
                        "prompt": enhanced_prompt,
                        "negative_prompt": negative_prompt,
                        "image_size": "1024x1024",  # 正确的参数名称
                        "batch_size": 1,  # 正确的参数名称
                        "num_inference_steps": 20,
                        "guidance_scale": 7.5,
                        "seed": int(time.time()) % 1000000000  # 添加随机种子数
                    }

                    print(f"使用模型: Kwai-Kolors/Kolors 生成图片...")
                    print(f"请求URL: https://api.siliconflow.cn/v1/images/generations")
                    print(f"提示词: {enhanced_prompt[:100]}...")

                    # 设置重试参数
                    max_retries = 3
                    retry_delay = 5  # 秒
                    timeout = 120  # 超时时间

                    # 重试机制
                    for retry in range(max_retries):
                        try:
                            print(f"调用Kolors API，尝试次数: {retry + 1}/{max_retries}")
                            # 发送请求，使用官方示例的方式
                            response = requests.request(
                                "POST",
                                "https://api.siliconflow.cn/v1/images/generations",
                                headers=headers,
                                json=data,
                                timeout=timeout
                            )
                            # 如果请求成功，跳出重试循环
                            break
                        except requests.exceptions.Timeout:
                            print(f"请求超时，尝试次数: {retry + 1}/{max_retries}")
                            if retry == max_retries - 1:  # 如果是最后一次重试
                                print("请求超时，所有重试均失败")
                                return self._create_fallback_image(landscape_description, style, output_path)
                            time.sleep(retry_delay * (retry + 1))  # 等待后重试，每次等待时间增加
                        except Exception as e:
                            print(f"请求失败: {e}，尝试次数: {retry + 1}/{max_retries}")
                            if retry == max_retries - 1:  # 如果是最后一次重试
                                print(f"请求失败，所有重试均失败: {e}")
                                return self._create_fallback_image(landscape_description, style, output_path)
                            time.sleep(retry_delay * (retry + 1))  # 等待后重试

                    # 处理响应
                    if response.status_code == 200:
                        result = response.json()
                        print(f"响应结果: {result}")

                        # 根据官方示例处理响应
                        if "images" in result and len(result["images"]) > 0:
                            image_url = result["images"][0].get("url")
                            if image_url:
                                try:
                                    # 下载图片
                                    image_response = requests.get(image_url)
                                    if image_response.status_code == 200:
                                        with open(output_path, "wb") as f:
                                            f.write(image_response.content)
                                        print(f"插图已保存到: {output_path}")
                                        return output_path
                                    else:
                                        print(f"下载图片失败，状态码: {image_response.status_code}")
                                except Exception as download_error:
                                    # 如果使用requests失败，尝试使用urllib
                                    print(f"使用requests下载图片失败: {download_error}，尝试使用urllib")
                                    try:
                                        import urllib.request
                                        urllib.request.urlretrieve(image_url, output_path)
                                        print(f"使用urllib下载插图成功，已保存到: {output_path}")
                                        return output_path
                                    except Exception as urllib_error:
                                        print(f"使用urllib下载图片也失败: {urllib_error}")
                            else:
                                print("API返回的图片URL为空")
                        else:
                            print("API响应中没有图片数据")
                    else:
                        print(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

                except Exception as e:
                    print(f"调用Kolors API时出错: {e}")

            else:
                # 使用通用方法调用API
                try:
                    # 尝试导入通用图像生成模块
                    from src.illustration import generate_novel_illustration

                    # 调用通用图像生成函数
                    success, result = generate_novel_illustration(
                        title="小说插图",
                        scene=landscape_description,
                        style=style,
                        output_path=output_path,
                        model_name=self.model
                    )

                    if success and os.path.exists(result):
                        print(f"插图已生成: {result}")
                        return result
                    else:
                        print(f"生成插图失败: {result}")

                except ImportError:
                    print("未找到通用图像生成模块")
                except Exception as e:
                    print(f"调用通用图像生成函数时出错: {e}")

            # 如果所有方法都失败，创建一个后备图像
            return self._create_fallback_image(landscape_description, style, output_path)

        except Exception as e:
            print(f"生成插图时出错: {e}")
            return self._create_fallback_image(landscape_description, style, output_path)

    def _create_fallback_image(self, scene_description: str, style: str, output_path: str) -> str:
        """
        创建后备图像

        参数:
            scene_description: 场景描述
            style: 风格
            output_path: 输出路径

        返回:
            图像路径
        """
        try:
            # 创建一个空白图像
            width, height = 1024, 768  # 横向风景比例
            img = Image.new('RGB', (width, height), color=(240, 248, 255))  # 淡蓝色背景
            draw = ImageDraw.Draw(img)

            # 尝试加载字体
            try:
                title_font = ImageFont.truetype("simhei.ttf", 30)
                font = ImageFont.truetype("simhei.ttf", 20)
            except IOError:
                title_font = ImageFont.load_default()
                font = ImageFont.load_default()

            # 添加标题
            draw.text((width//2, 50), "小说风景插图", fill=(0, 0, 0), font=title_font, anchor="mm")

            # 添加场景描述
            scene_text = scene_description[:200] + "..." if len(scene_description) > 200 else scene_description
            draw.text((20, 100), f"场景: {scene_text}", fill=(0, 0, 0), font=font)
            draw.text((20, 150), f"风格: {style}", fill=(0, 0, 0), font=font)
            draw.text((20, 200), "这是模拟生成的风景插图", fill=(0, 0, 0), font=font)

            # 绘制简单的风景
            # 天空
            for y in range(0, height//2):
                # 从顶部的深蓝色渐变到地平线的浅蓝色
                color = (135 - y//10, 206 - y//10, 235)
                draw.line([(0, y), (width, y)], fill=color)

            # 山脉
            mountain_color = (34, 139, 34)  # 森林绿
            mountain_points = [(0, height//2), (width//4, height//3), (width//2, height//2-50),
                              (3*width//4, height//3+20), (width, height//2)]
            draw.polygon(mountain_points, fill=mountain_color)

            # 湖泊
            lake_color = (65, 105, 225)  # 皇家蓝
            lake_points = [(0, height//2+100), (width//3, height//2+50), (2*width//3, height//2+70),
                          (width, height//2+120), (width, height), (0, height)]
            draw.polygon(lake_points, fill=lake_color)

            # 添加时间戳
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            draw.text((20, height-40), f"生成时间: {timestamp}", fill=(100, 100, 100), font=font)

            # 保存图片
            img.save(output_path)
            print(f"后备风景插图已保存到: {output_path}")

            return output_path
        except Exception as e:
            print(f"创建后备图像时出错: {e}")
            return ""
