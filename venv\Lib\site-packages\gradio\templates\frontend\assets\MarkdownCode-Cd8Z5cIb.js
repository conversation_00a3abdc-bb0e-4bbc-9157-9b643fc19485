import{k as v,A as P,c as q}from"./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js";var H=function(e,r,t){for(var a=t,l=0,s=e.length;a<r.length;){var o=r[a];if(l<=0&&r.slice(a,a+s)===e)return a;o==="\\"?a++:o==="{"?l++:o==="}"&&l--,a++}return-1},N=function(e){return e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")},S=/^\\begin{/,X=function(e,r){for(var t,a=[],l=new RegExp("("+r.map(f=>N(f.left)).join("|")+")");t=e.search(l),t!==-1;){t>0&&(a.push({type:"text",data:e.slice(0,t)}),e=e.slice(t));var s=r.findIndex(f=>e.startsWith(f.left));if(t=H(r[s].right,e,r[s].left.length),t===-1)break;var o=e.slice(0,t+r[s].right.length),d=S.test(o)?o:e.slice(r[s].left.length,t);a.push({type:"math",data:d,rawData:o,display:r[s].display}),e=e.slice(t+r[s].right.length)}return e!==""&&a.push({type:"text",data:e}),a},B=function(e,r){var t=X(e,r.delimiters);if(t.length===1&&t[0].type==="text")return null;for(var a=document.createDocumentFragment(),l=0;l<t.length;l++)if(t[l].type==="text")a.appendChild(document.createTextNode(t[l].data));else{var s=document.createElement("span"),o=t[l].data;r.displayMode=t[l].display;try{r.preProcess&&(o=r.preProcess(o)),v.render(o,s,r)}catch(d){if(!(d instanceof v.ParseError))throw d;r.errorCallback("KaTeX auto-render: Failed to parse `"+t[l].data+"` with ",d),a.appendChild(document.createTextNode(t[l].rawData));continue}a.appendChild(s)}return a},F=function i(e,r){for(var t=0;t<e.childNodes.length;t++){var a=e.childNodes[t];if(a.nodeType===3){for(var l=a.textContent,s=a.nextSibling,o=0;s&&s.nodeType===Node.TEXT_NODE;)l+=s.textContent,s=s.nextSibling,o++;var d=B(l,r);if(d){for(var f=0;f<o;f++)a.nextSibling.remove();t+=d.childNodes.length-1,e.replaceChild(d,a)}else t+=o}else a.nodeType===1&&function(){var w=" "+a.className+" ",b=r.ignoredTags.indexOf(a.nodeName.toLowerCase())===-1&&r.ignoredClasses.every(_=>w.indexOf(" "+_+" ")===-1);b&&i(a,r)}()}},K=function(e,r){if(!e)throw new Error("No element provided to render");var t={};for(var a in r)r.hasOwnProperty(a)&&(t[a]=r[a]);t.delimiters=t.delimiters||[{left:"$$",right:"$$",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"\\begin{equation}",right:"\\end{equation}",display:!0},{left:"\\begin{align}",right:"\\end{align}",display:!0},{left:"\\begin{alignat}",right:"\\end{alignat}",display:!0},{left:"\\begin{gather}",right:"\\end{gather}",display:!0},{left:"\\begin{CD}",right:"\\end{CD}",display:!0},{left:"\\[",right:"\\]",display:!0}],t.ignoredTags=t.ignoredTags||["script","noscript","style","textarea","pre","code","option"],t.ignoredClasses=t.ignoredClasses||[],t.errorCallback=t.errorCallback||console.error,t.macros=t.macros||{},F(e,t)};const U=(i,e)=>{try{return!!i&&new URL(i).origin!==new URL(e).origin}catch{return!1}};function y(i,e){const r=new P,t=new DOMParser().parseFromString(i,"text/html");return L(t.body,"A",a=>{a instanceof HTMLElement&&"target"in a&&U(a.getAttribute("href"),e)&&(a.setAttribute("target","_blank"),a.setAttribute("rel","noopener noreferrer"))}),r.sanitize(t).body.innerHTML}function L(i,e,r){i&&(i.nodeName===e||typeof e=="function")&&r(i);const t=i?.childNodes||[];for(let a=0;a<t.length;a++)L(t[a],e,r)}const Z=["!--","!doctype","a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","big","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","link","main","map","mark","menu","meta","meter","nav","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","search","section","select","small","source","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr"],{SvelteComponent:j,attr:V,binding_callbacks:W,detach:G,element:J,flush:m,init:Q,insert:Y,noop:E,safe_not_equal:$,toggle_class:k}=window.__gradio__svelte__internal,{afterUpdate:ee,tick:ie}=window.__gradio__svelte__internal;function te(i){let e;return{c(){e=J("span"),V(e,"class","md svelte-7ddecg"),k(e,"chatbot",i[0]),k(e,"prose",i[1])},m(r,t){Y(r,e,t),e.innerHTML=i[3],i[11](e)},p(r,[t]){t&8&&(e.innerHTML=r[3]),t&1&&k(e,"chatbot",r[0]),t&2&&k(e,"prose",r[1])},i:E,o:E,d(r){r&&G(e),i[11](null)}}}function T(i){return i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function re(i,e,r){let{chatbot:t=!0}=e,{message:a}=e,{sanitize_html:l=!0}=e,{latex_delimiters:s=[]}=e,{render_markdown:o=!0}=e,{line_breaks:d=!0}=e,{header_links:f=!1}=e,{root:w}=e,{allow_tags:b=!1}=e,_,x;const M=q({header_links:f,line_breaks:d,latex_delimiters:s||[]});function A(n,c){if(c===!0){const h=/<\/?([a-zA-Z][a-zA-Z0-9-]*)([\s>])/g;return n.replace(h,(g,u,p)=>Z.includes(u.toLowerCase())?g:g.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}if(Array.isArray(c)){const h=c.map(u=>({open:new RegExp(`<(${u})(\\s+[^>]*)?>`,"gi"),close:new RegExp(`</(${u})>`,"gi")}));let g=n;return h.forEach(u=>{g=g.replace(u.open,p=>p.replace(/</g,"&lt;").replace(/>/g,"&gt;")),g=g.replace(u.close,p=>p.replace(/</g,"&lt;").replace(/>/g,"&gt;"))}),g}return n}function C(n){let c=n;if(o){const h=[];s.forEach((g,u)=>{const p=T(g.left),O=T(g.right),z=new RegExp(`${p}([\\s\\S]+?)${O}`,"g");c=c.replace(z,(I,ae)=>(h.push(I),`%%%LATEX_BLOCK_${h.length-1}%%%`))}),c=M.parse(c),c=c.replace(/%%%LATEX_BLOCK_(\d+)%%%/g,(g,u)=>h[parseInt(u,10)])}return b&&(c=A(c,b)),l&&y&&(c=y(c,w)),c}async function R(n){s.length>0&&n&&s.some(h=>n.includes(h.left)&&n.includes(h.right))&&K(_,{delimiters:s,throwOnError:!1})}ee(async()=>{_&&document.body.contains(_)?await R(a):console.error("Element is not in the DOM")});function D(n){W[n?"unshift":"push"](()=>{_=n,r(2,_)})}return i.$$set=n=>{"chatbot"in n&&r(0,t=n.chatbot),"message"in n&&r(4,a=n.message),"sanitize_html"in n&&r(5,l=n.sanitize_html),"latex_delimiters"in n&&r(6,s=n.latex_delimiters),"render_markdown"in n&&r(1,o=n.render_markdown),"line_breaks"in n&&r(7,d=n.line_breaks),"header_links"in n&&r(8,f=n.header_links),"root"in n&&r(9,w=n.root),"allow_tags"in n&&r(10,b=n.allow_tags)},i.$$.update=()=>{i.$$.dirty&16&&(a&&a.trim()?r(3,x=C(a)):r(3,x=""))},[t,o,_,x,a,l,s,d,f,w,b,D]}class se extends j{constructor(e){super(),Q(this,e,re,te,$,{chatbot:0,message:4,sanitize_html:5,latex_delimiters:6,render_markdown:1,line_breaks:7,header_links:8,root:9,allow_tags:10})}get chatbot(){return this.$$.ctx[0]}set chatbot(e){this.$$set({chatbot:e}),m()}get message(){return this.$$.ctx[4]}set message(e){this.$$set({message:e}),m()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),m()}get latex_delimiters(){return this.$$.ctx[6]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),m()}get render_markdown(){return this.$$.ctx[1]}set render_markdown(e){this.$$set({render_markdown:e}),m()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),m()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),m()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),m()}get allow_tags(){return this.$$.ctx[10]}set allow_tags(e){this.$$set({allow_tags:e}),m()}}export{se as M};
//# sourceMappingURL=MarkdownCode-Cd8Z5cIb.js.map
