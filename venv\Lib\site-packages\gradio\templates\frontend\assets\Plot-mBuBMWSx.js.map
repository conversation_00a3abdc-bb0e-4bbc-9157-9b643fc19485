{"version": 3, "mappings": ";gtCAAAA,EAoBKC,EAAAC,EAAAC,CAAA,EATJC,EAAkDF,EAAAG,CAAA,EAClDD,EAAkDF,EAAAI,CAAA,EAClDF,EAAmDF,EAAAK,CAAA,EACnDH,EAAkDF,EAAAM,CAAA,EAClDJ,EAAmDF,EAAAO,CAAA,EACnDL,EAGCF,EAAAQ,CAAA,6cCbqC,+FA8EjB,+QAnBfC,EAAG,2FAAHA,EAAG,sWAEDA,EAAa,qbAAbA,EAAa,utBAHjB,OAAAA,MAASA,EAAa,+TAxDf,UAAAC,CAAA,EAAAC,EACPC,EACO,QAAAC,EAAA,IAAAF,EACA,YAAAG,CAAA,EAAAH,EACA,YAAAI,CAAA,EAAAJ,EACA,SAAAK,CAAA,EAAAL,EACA,eAAAM,CAAA,EAAAN,EACA,qBAAAO,CAAA,EAAAP,EACA,QAAAQ,CAAA,EAAAR,GAGA,MAAAS,EAAiC,MAAAT,EACjC,aAAAU,CAAA,EAAAV,EAEPW,EAAqB,KACrBC,EAAQb,GAAO,KACfc,EAAoB,SAElBC,EAAWC,KAIXC,EAAA,CACL,WAAAC,EAAA,WAAqB,0BAAgC,4CACrD,UAAAA,EAAA,WAAoB,yBAA+B,0CACnD,WAAAA,EAAA,WAAqB,0BAAgC,0DACrD,eAAAA,EAAA,WAAyB,8BAAoC,6CAG1D,IAAAC,EAAA,GAEE,MAAAC,EAAA,OAAoB,OAAW,QACjCC,EAAM,ggBAEHrB,IAAUE,EAAA,MAChBmB,GAAO,GACH,IAAAC,EAAOtB,GAAO,KACdsB,IAAST,QACZD,EAAgB,MAEbU,GAAQA,KAAQL,GAAmBG,IAClCD,EAAsBG,CAAI,EAC7BC,EAAA,GAAAX,EAAgBO,EAAsBG,CAAI,GAE1CL,EAAgBK,CAAI,IAAI,KAAME,GAAA,CAC7BD,EAAA,GAAAX,EAAgBY,EAAO,SACvBD,EAAA,GAAAJ,EAAsBG,CAAI,EAAIV,EAAAO,CAAA,UAIjCjB,EAASF,CAAA,OACTa,EAAQS,CAAA,EACRP,EAAS,QAAQ", "names": ["insert", "target", "svg", "anchor", "append", "circle0", "circle1", "circle2", "circle3", "circle4", "path", "ctx", "value", "$$props", "_value", "colors", "show_label", "theme_mode", "caption", "bokeh_version", "show_actions_button", "gradio", "x_lim", "_selectable", "PlotComponent", "_type", "loaded_plotly_css", "dispatch", "createEventDispatcher", "plotTypeMapping", "__vitePreload", "loadedPlotTypeMapping", "is_browser", "key", "type", "$$invalidate", "module"], "ignoreList": [], "sources": ["../../../../js/icons/src/Plot.svelte", "../../../../js/plot/shared/Plot.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<circle cx=\"20\" cy=\"4\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"8\" cy=\"16\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"28\" cy=\"12\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"11\" cy=\"7\" r=\"2\" fill=\"currentColor\" />\n\t<circle cx=\"16\" cy=\"24\" r=\"2\" fill=\"currentColor\" />\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M30 3.413L28.586 2L4 26.585V2H2v26a2 2 0 0 0 2 2h26v-2H5.413Z\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\t//@ts-nocheck\n\timport { Plot as PlotIcon } from \"@gradio/icons\";\n\timport { Empty } from \"@gradio/atoms\";\n\timport type { ThemeMode } from \"js/core/src/components/types\";\n\timport type { Gradio, SelectData } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let value;\n\tlet _value;\n\texport let colors: string[] = [];\n\texport let show_label: boolean;\n\texport let theme_mode: ThemeMode;\n\texport let caption: string;\n\texport let bokeh_version: string | null;\n\texport let show_actions_button: bool;\n\texport let gradio: Gradio<{\n\t\tselect: SelectData;\n\t}>;\n\texport let x_lim: [number, number] | null = null;\n\texport let _selectable: boolean;\n\n\tlet PlotComponent: any = null;\n\tlet _type = value?.type;\n\tlet loaded_plotly_css = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t}>();\n\n\tconst plotTypeMapping = {\n\t\tplotly: () => import(\"./plot_types/PlotlyPlot.svelte\"),\n\t\tbokeh: () => import(\"./plot_types/BokehPlot.svelte\"),\n\t\taltair: () => import(\"./plot_types/AltairPlot.svelte\"),\n\t\tmatplotlib: () => import(\"./plot_types/MatplotlibPlot.svelte\")\n\t};\n\n\tlet loadedPlotTypeMapping = {};\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tlet key = 0;\n\n\t$: if (value !== _value) {\n\t\tkey += 1;\n\t\tlet type = value?.type;\n\t\tif (type !== _type) {\n\t\t\tPlotComponent = null;\n\t\t}\n\t\tif (type && type in plotTypeMapping && is_browser) {\n\t\t\tif (loadedPlotTypeMapping[type]) {\n\t\t\t\tPlotComponent = loadedPlotTypeMapping[type];\n\t\t\t} else {\n\t\t\t\tplotTypeMapping[type]().then((module) => {\n\t\t\t\t\tPlotComponent = module.default;\n\t\t\t\t\tloadedPlotTypeMapping[type] = PlotComponent;\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\t_value = value;\n\t\t_type = type;\n\t\tdispatch(\"change\");\n\t}\n</script>\n\n{#if value && PlotComponent}\n\t{#key key}\n\t\t<svelte:component\n\t\t\tthis={PlotComponent}\n\t\t\t{value}\n\t\t\t{colors}\n\t\t\t{theme_mode}\n\t\t\t{show_label}\n\t\t\t{caption}\n\t\t\t{bokeh_version}\n\t\t\t{show_actions_button}\n\t\t\t{gradio}\n\t\t\t{_selectable}\n\t\t\t{x_lim}\n\t\t\tbind:loaded_plotly_css\n\t\t\ton:load\n\t\t\ton:select\n\t\t/>\n\t{/key}\n{:else}\n\t<Empty unpadded_box={true} size=\"large\"><PlotIcon /></Empty>\n{/if}\n"], "file": "assets/Plot-mBuBMWSx.js"}