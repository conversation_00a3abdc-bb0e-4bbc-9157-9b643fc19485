import"./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js";import{c as j,a as U}from"./utils-BsGrhMNe.js";import{C as H}from"./Check-BiRlaMNo.js";import{C as J}from"./Copy-CxQ9EyK2.js";import{M as V}from"./MarkdownCode-Cd8Z5cIb.js";import{I as X}from"./IconButton-DbC-jsk_.js";import{I as Y}from"./IconButtonWrapper-DrWC4NJv.js";import{S as Z}from"./index-CWG4El0O.js";import"./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js";import{B as x}from"./Block-CMfAaXj9.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js";import{default as Re}from"./Example-CxAuiy_R.js";import"./index-V1UhiEW8.js";import"./svelte/svelte.js";import"./Clear-By3xiIwg.js";import"./prism-python-DO0H4w6Q.js";const{SvelteComponent:$,action_destroyer:p,append:ee,attr:w,check_outros:te,create_component:A,destroy_component:F,detach:ie,element:se,flush:b,group_outros:ne,init:le,insert:ae,mount_component:G,safe_not_equal:_e,set_style:K,space:oe,toggle_class:L,transition_in:C,transition_out:M}=window.__gradio__svelte__internal,{createEventDispatcher:re}=window.__gradio__svelte__internal;function N(t){let e,s;return e=new Y({props:{$$slots:{default:[he]},$$scope:{ctx:t}}}),{c(){A(e.$$.fragment)},m(i,a){G(e,i,a),s=!0},p(i,a){const o={};a&270336&&(o.$$scope={dirty:a,ctx:i}),e.$set(o)},i(i){s||(C(e.$$.fragment,i),s=!0)},o(i){M(e.$$.fragment,i),s=!1},d(i){F(e,i)}}}function he(t){let e,s;return e=new X({props:{Icon:t[13]?H:J,label:t[13]?"Copied conversation":"Copy conversation"}}),e.$on("click",t[14]),{c(){A(e.$$.fragment)},m(i,a){G(e,i,a),s=!0},p(i,a){const o={};a&8192&&(o.Icon=i[13]?H:J),a&8192&&(o.label=i[13]?"Copied conversation":"Copy conversation"),e.$set(o)},i(i){s||(C(e.$$.fragment,i),s=!0)},o(i){M(e.$$.fragment,i),s=!1},d(i){F(e,i)}}}function ue(t){let e,s,i,a,o,c,f,l,r,u=t[10]&&N(t);return i=new V({props:{message:t[2],latex_delimiters:t[7],sanitize_html:t[5],line_breaks:t[6],chatbot:!1,header_links:t[8],root:t[11]}}),{c(){e=se("div"),u&&u.c(),s=oe(),A(i.$$.fragment),w(e,"class",a="prose "+(t[0]?.join(" ")||"")+" svelte-lag733"),w(e,"data-testid","markdown"),w(e,"dir",o=t[4]?"rtl":"ltr"),w(e,"style",c=t[9]?`max-height: ${j(t[9])}; overflow-y: auto;`:""),L(e,"hide",!t[1]),K(e,"min-height",t[3]&&t[12]?.status!=="pending"?j(t[3]):void 0)},m(n,m){ae(n,e,m),u&&u.m(e,null),ee(e,s),G(i,e,null),f=!0,l||(r=p(U.call(null,e)),l=!0)},p(n,[m]){n[10]?u?(u.p(n,m),m&1024&&C(u,1)):(u=N(n),u.c(),C(u,1),u.m(e,s)):u&&(ne(),M(u,1,1,()=>{u=null}),te());const d={};m&4&&(d.message=n[2]),m&128&&(d.latex_delimiters=n[7]),m&32&&(d.sanitize_html=n[5]),m&64&&(d.line_breaks=n[6]),m&256&&(d.header_links=n[8]),m&2048&&(d.root=n[11]),i.$set(d),(!f||m&1&&a!==(a="prose "+(n[0]?.join(" ")||"")+" svelte-lag733"))&&w(e,"class",a),(!f||m&16&&o!==(o=n[4]?"rtl":"ltr"))&&w(e,"dir",o),(!f||m&512&&c!==(c=n[9]?`max-height: ${j(n[9])}; overflow-y: auto;`:""))&&w(e,"style",c),(!f||m&3)&&L(e,"hide",!n[1]);const k=m&512;(m&4616||k)&&K(e,"min-height",n[3]&&n[12]?.status!=="pending"?j(n[3]):void 0)},i(n){f||(C(u),C(i.$$.fragment,n),f=!0)},o(n){M(u),M(i.$$.fragment,n),f=!1},d(n){n&&ie(e),u&&u.d(),F(i),l=!1,r()}}}function me(t,e,s){let{elem_classes:i=[]}=e,{visible:a=!0}=e,{value:o}=e,{min_height:c=void 0}=e,{rtl:f=!1}=e,{sanitize_html:l=!0}=e,{line_breaks:r=!1}=e,{latex_delimiters:u}=e,{header_links:n=!1}=e,{height:m=void 0}=e,{show_copy_button:d=!1}=e,{root:k}=e,{loading_status:I=void 0}=e,v=!1,z;const B=re();async function S(){"clipboard"in navigator&&(await navigator.clipboard.writeText(o),B("copy",{value:o}),q())}function q(){s(13,v=!0),z&&clearTimeout(z),z=setTimeout(()=>{s(13,v=!1)},1e3)}return t.$$set=h=>{"elem_classes"in h&&s(0,i=h.elem_classes),"visible"in h&&s(1,a=h.visible),"value"in h&&s(2,o=h.value),"min_height"in h&&s(3,c=h.min_height),"rtl"in h&&s(4,f=h.rtl),"sanitize_html"in h&&s(5,l=h.sanitize_html),"line_breaks"in h&&s(6,r=h.line_breaks),"latex_delimiters"in h&&s(7,u=h.latex_delimiters),"header_links"in h&&s(8,n=h.header_links),"height"in h&&s(9,m=h.height),"show_copy_button"in h&&s(10,d=h.show_copy_button),"root"in h&&s(11,k=h.root),"loading_status"in h&&s(12,I=h.loading_status)},t.$$.update=()=>{t.$$.dirty&4&&B("change")},[i,a,o,c,f,l,r,u,n,m,d,k,I,v,S]}class fe extends ${constructor(e){super(),le(this,e,me,ue,_e,{elem_classes:0,visible:1,value:2,min_height:3,rtl:4,sanitize_html:5,line_breaks:6,latex_delimiters:7,header_links:8,height:9,show_copy_button:10,root:11,loading_status:12})}get elem_classes(){return this.$$.ctx[0]}set elem_classes(e){this.$$set({elem_classes:e}),b()}get visible(){return this.$$.ctx[1]}set visible(e){this.$$set({visible:e}),b()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),b()}get min_height(){return this.$$.ctx[3]}set min_height(e){this.$$set({min_height:e}),b()}get rtl(){return this.$$.ctx[4]}set rtl(e){this.$$set({rtl:e}),b()}get sanitize_html(){return this.$$.ctx[5]}set sanitize_html(e){this.$$set({sanitize_html:e}),b()}get line_breaks(){return this.$$.ctx[6]}set line_breaks(e){this.$$set({line_breaks:e}),b()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),b()}get header_links(){return this.$$.ctx[8]}set header_links(e){this.$$set({header_links:e}),b()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),b()}get show_copy_button(){return this.$$.ctx[10]}set show_copy_button(e){this.$$set({show_copy_button:e}),b()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),b()}get loading_status(){return this.$$.ctx[12]}set loading_status(e){this.$$set({loading_status:e}),b()}}const ge=fe,{SvelteComponent:ce,assign:de,attr:be,create_component:T,destroy_component:E,detach:O,element:ke,flush:g,get_spread_object:we,get_spread_update:ve,init:ze,insert:P,mount_component:D,safe_not_equal:Ce,space:Ie,toggle_class:Q,transition_in:W,transition_out:y}=window.__gradio__svelte__internal;function Be(t){let e,s,i,a,o;const c=[{autoscroll:t[8].autoscroll},{i18n:t[8].i18n},t[4],{variant:"center"}];let f={};for(let l=0;l<c.length;l+=1)f=de(f,c[l]);return e=new Z({props:f}),e.$on("clear_status",t[17]),a=new ge({props:{value:t[3],elem_classes:t[1],visible:t[2],rtl:t[5],latex_delimiters:t[9],sanitize_html:t[6],line_breaks:t[7],header_links:t[10],show_copy_button:t[14],root:t[8].root,loading_status:t[4]}}),a.$on("change",t[18]),a.$on("copy",t[19]),{c(){T(e.$$.fragment),s=Ie(),i=ke("div"),T(a.$$.fragment),be(i,"class","svelte-1ed2p3z"),Q(i,"pending",t[4]?.status==="pending")},m(l,r){D(e,l,r),P(l,s,r),P(l,i,r),D(a,i,null),o=!0},p(l,r){const u=r&272?ve(c,[r&256&&{autoscroll:l[8].autoscroll},r&256&&{i18n:l[8].i18n},r&16&&we(l[4]),c[3]]):{};e.$set(u);const n={};r&8&&(n.value=l[3]),r&2&&(n.elem_classes=l[1]),r&4&&(n.visible=l[2]),r&32&&(n.rtl=l[5]),r&512&&(n.latex_delimiters=l[9]),r&64&&(n.sanitize_html=l[6]),r&128&&(n.line_breaks=l[7]),r&1024&&(n.header_links=l[10]),r&16384&&(n.show_copy_button=l[14]),r&256&&(n.root=l[8].root),r&16&&(n.loading_status=l[4]),a.$set(n),(!o||r&16)&&Q(i,"pending",l[4]?.status==="pending")},i(l){o||(W(e.$$.fragment,l),W(a.$$.fragment,l),o=!0)},o(l){y(e.$$.fragment,l),y(a.$$.fragment,l),o=!1},d(l){l&&(O(s),O(i)),E(e,l),E(a)}}}function Me(t){let e,s;return e=new x({props:{visible:t[2],elem_id:t[0],elem_classes:t[1],container:t[15],allow_overflow:!0,overflow_behavior:"auto",height:t[11],min_height:t[12],max_height:t[13],$$slots:{default:[Be]},$$scope:{ctx:t}}}),{c(){T(e.$$.fragment)},m(i,a){D(e,i,a),s=!0},p(i,[a]){const o={};a&4&&(o.visible=i[2]),a&1&&(o.elem_id=i[0]),a&2&&(o.elem_classes=i[1]),a&32768&&(o.container=i[15]),a&2048&&(o.height=i[11]),a&4096&&(o.min_height=i[12]),a&8192&&(o.max_height=i[13]),a&1067006&&(o.$$scope={dirty:a,ctx:i}),e.$set(o)},i(i){s||(W(e.$$.fragment,i),s=!0)},o(i){y(e.$$.fragment,i),s=!1},d(i){E(e,i)}}}function Se(t,e,s){let{label:i}=e,{elem_id:a=""}=e,{elem_classes:o=[]}=e,{visible:c=!0}=e,{value:f=""}=e,{loading_status:l}=e,{rtl:r=!1}=e,{sanitize_html:u=!0}=e,{line_breaks:n=!1}=e,{gradio:m}=e,{latex_delimiters:d}=e,{header_links:k=!1}=e,{height:I}=e,{min_height:v}=e,{max_height:z}=e,{show_copy_button:B=!1}=e,{container:S=!1}=e;const q=()=>m.dispatch("clear_status",l),h=()=>m.dispatch("change"),R=_=>m.dispatch("copy",_.detail);return t.$$set=_=>{"label"in _&&s(16,i=_.label),"elem_id"in _&&s(0,a=_.elem_id),"elem_classes"in _&&s(1,o=_.elem_classes),"visible"in _&&s(2,c=_.visible),"value"in _&&s(3,f=_.value),"loading_status"in _&&s(4,l=_.loading_status),"rtl"in _&&s(5,r=_.rtl),"sanitize_html"in _&&s(6,u=_.sanitize_html),"line_breaks"in _&&s(7,n=_.line_breaks),"gradio"in _&&s(8,m=_.gradio),"latex_delimiters"in _&&s(9,d=_.latex_delimiters),"header_links"in _&&s(10,k=_.header_links),"height"in _&&s(11,I=_.height),"min_height"in _&&s(12,v=_.min_height),"max_height"in _&&s(13,z=_.max_height),"show_copy_button"in _&&s(14,B=_.show_copy_button),"container"in _&&s(15,S=_.container)},t.$$.update=()=>{t.$$.dirty&65792&&m.dispatch("change")},[a,o,c,f,l,r,u,n,m,d,k,I,v,z,B,S,i,q,h,R]}class Oe extends ce{constructor(e){super(),ze(this,e,Se,Me,Ce,{label:16,elem_id:0,elem_classes:1,visible:2,value:3,loading_status:4,rtl:5,sanitize_html:6,line_breaks:7,gradio:8,latex_delimiters:9,header_links:10,height:11,min_height:12,max_height:13,show_copy_button:14,container:15})}get label(){return this.$$.ctx[16]}set label(e){this.$$set({label:e}),g()}get elem_id(){return this.$$.ctx[0]}set elem_id(e){this.$$set({elem_id:e}),g()}get elem_classes(){return this.$$.ctx[1]}set elem_classes(e){this.$$set({elem_classes:e}),g()}get visible(){return this.$$.ctx[2]}set visible(e){this.$$set({visible:e}),g()}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),g()}get loading_status(){return this.$$.ctx[4]}set loading_status(e){this.$$set({loading_status:e}),g()}get rtl(){return this.$$.ctx[5]}set rtl(e){this.$$set({rtl:e}),g()}get sanitize_html(){return this.$$.ctx[6]}set sanitize_html(e){this.$$set({sanitize_html:e}),g()}get line_breaks(){return this.$$.ctx[7]}set line_breaks(e){this.$$set({line_breaks:e}),g()}get gradio(){return this.$$.ctx[8]}set gradio(e){this.$$set({gradio:e}),g()}get latex_delimiters(){return this.$$.ctx[9]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),g()}get header_links(){return this.$$.ctx[10]}set header_links(e){this.$$set({header_links:e}),g()}get height(){return this.$$.ctx[11]}set height(e){this.$$set({height:e}),g()}get min_height(){return this.$$.ctx[12]}set min_height(e){this.$$set({min_height:e}),g()}get max_height(){return this.$$.ctx[13]}set max_height(e){this.$$set({max_height:e}),g()}get show_copy_button(){return this.$$.ctx[14]}set show_copy_button(e){this.$$set({show_copy_button:e}),g()}get container(){return this.$$.ctx[15]}set container(e){this.$$set({container:e}),g()}}export{Re as BaseExample,ge as BaseMarkdown,Oe as default};
//# sourceMappingURL=Index-VHcFswDo.js.map
