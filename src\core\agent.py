"""
智能体基类模块
定义了所有智能体的基类和智能体注册表
"""

import os
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Type, TypeVar, cast

# 定义Agent类型变量
T = TypeVar('T', bound='Agent')

class Agent:
    """
    智能体基类，所有智能体都应继承自此类
    """

    def __init__(self, name: str, model: str):
        """
        初始化智能体

        参数:
            name: 智能体名称
            model: 使用的模型名称
        """
        self.name = name
        self.model = model
        self.messages: List[Dict[str, Any]] = []
        self.id = str(uuid.uuid4())

    def call_llm(self, prompt: str) -> str:
        """
        调用大语言模型

        参数:
            prompt: 提示词

        返回:
            模型生成的内容
        """
        print(f"调用模型 {self.model} 生成内容...")
        
        # 导入MCP客户端
        try:
            from src.utils.mcp_client import call_llm as mcp_call_llm
            return mcp_call_llm(self.model, prompt)
        except ImportError:
            return f"LLM调用示例响应: {prompt[:50]}..."

    def handle_message(self, message: Dict[str, Any]) -> Any:
        """
        处理接收到的消息

        参数:
            message: 消息内容

        返回:
            处理结果
        """
        # 默认实现，子类应该重写此方法
        print(f"智能体 {self.name} 收到消息: {message}")
        return {"status": "received", "agent": self.name}


class AgentRegistry:
    """
    智能体注册表，用于管理所有智能体
    """

    def __init__(self):
        """初始化智能体注册表"""
        self.agents: Dict[str, Agent] = {}

    def register(self, agent: Agent) -> Agent:
        """
        注册智能体

        参数:
            agent: 要注册的智能体

        返回:
            注册的智能体实例
        """
        self.agents[agent.name] = agent
        print(f"智能体 {agent.name} 已注册，使用模型: {agent.model}")
        return agent

    def get_agent(self, name: str) -> Optional[Agent]:
        """
        获取指定名称的智能体

        参数:
            name: 智能体名称

        返回:
            智能体实例，如果不存在则返回None
        """
        return self.agents.get(name)

    def get_agents_by_type(self, agent_type: Type[T]) -> List[T]:
        """
        获取指定类型的所有智能体

        参数:
            agent_type: 智能体类型

        返回:
            指定类型的智能体列表
        """
        return [agent for agent in self.agents.values() if isinstance(agent, agent_type)]

    def dispatch_message(self, sender: str, recipient: str, message: Dict[str, Any]):
        """
        分发消息给指定的智能体

        参数:
            sender: 发送者名称
            recipient: 接收者名称
            message: 消息内容
        """
        recipient_agent = self.get_agent(recipient)
        if recipient_agent:
            # 添加发送者信息
            message['sender'] = sender

            # 处理消息
            recipient_agent.handle_message(message)
        else:
            print(f"错误: 找不到智能体 {recipient}")


# 创建全局智能体注册表
AGENT_REGISTRY = AgentRegistry()
