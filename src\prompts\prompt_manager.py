from typing import Dict, Any, Optional
import os
import json


class PromptManager:
    """
    提示词管理器，负责管理和生成各种写作相关的提示词
    """

    def __init__(self, prompt_dir: str = "prompts"):
        """
        初始化提示词管理器

        参数:
            prompt_dir: 提示词文件存储目录
        """
        self.prompt_dir = prompt_dir
        self.prompts = self._load_prompts()

        # 如果没有找到提示词文件，创建默认提示词
        if not self.prompts:
            self._create_default_prompts()
            self._save_prompts()

    def _load_prompts(self) -> Dict[str, str]:
        """
        从文件加载提示词

        返回:
            提示词字典
        """
        prompts = {}

        # 确保提示词目录存在
        os.makedirs(self.prompt_dir, exist_ok=True)

        # 尝试加载提示词文件
        prompt_file = os.path.join(self.prompt_dir, "writing_prompts.json")
        if os.path.exists(prompt_file):
            try:
                with open(prompt_file, "r", encoding="utf-8") as f:
                    prompts = json.load(f)
                print(f"已从 {prompt_file} 加载 {len(prompts)} 个提示词模板")
            except Exception as e:
                print(f"加载提示词文件时出错: {e}")

        return prompts

    def _save_prompts(self):
        """保存提示词到文件"""
        prompt_file = os.path.join(self.prompt_dir, "writing_prompts.json")
        try:
            with open(prompt_file, "w", encoding="utf-8") as f:
                json.dump(self.prompts, f, ensure_ascii=False, indent=2)
            print(f"已将 {len(self.prompts)} 个提示词模板保存到 {prompt_file}")
        except Exception as e:
            print(f"保存提示词文件时出错: {e}")

    def _create_default_prompts(self) -> Dict[str, str]:
        """创建默认提示词模板"""
        self.prompts = {
            "writing_agent_outline": """
你是一位专业的中文小说创作者，请为以下小说创作一个详细的内容大纲：

标题：{title}
类型：{genre}
风格：{style}
场景：{scene}
角色：{characters}

请提供一个包含5-7个段落的详细大纲，每个段落描述一个场景或情节发展。大纲应该包含故事的开端、发展、高潮和结局。
请确保大纲具有连贯性和逻辑性，能够形成一个完整的故事。
""",

            "writing_agent_paragraph": """
你是一位专业的中文小说创作者，请根据以下大纲和要求，创作一个精彩的段落：

标题：{title}
类型：{genre}
风格：{style}
场景：{scene}
角色：{characters}

整体大纲：
{outline}

当前段落大纲：{paragraph}

这是第{paragraph_number}段，共{total_paragraphs}段。请创作一个生动、详细的段落，包含丰富的描写和情感表达。
段落应当至少包含300字，最多800字，要有细节描写、环境氛围和人物心理活动。
请根据小说风格和类型，使用适当的语言和表达方式。

请直接给出段落内容，不需要额外的解释或说明。
""",

            "writing_agent_polish": """
你是一位专业的中文小说编辑，请对以下小说内容进行润色和完善：

标题：{title}
类型：{genre}
风格：{style}

原始内容：
{content}

请对内容进行润色，使其更加流畅、生动、有吸引力。请注意：
1. 保持原有的故事情节和人物设定不变
2. 改进语言表达，使其更符合{style}风格
3. 增强段落之间的连贯性和过渡
4. 修正任何语法错误或表达不清的地方
5. 确保故事有一个完整的开端、发展和结局

请直接给出润色后的内容，不需要额外的解释或说明。
""",

            "writing_agent": """
你是一位专业的中文小说创作者，擅长创作各种风格的小说内容。请根据以下信息创作一段精彩的小说内容：

标题：{title}
类型：{genre}
风格：{style}
场景：{scene}
角色：{characters}
上一章节：{final_content}
创作要求：
1. 上下文：如果存在上文，请紧密续写上文最后一段开始创作。
2. 段落结构：请使用合理的段落结构，段落长短适中，避免过长段落影响阅读体验。
3. 对话：对话要自然流畅，符合角色身份和性格特点，使用恰当的对话标点。
4. 环境描写：通过细节描写营造场景氛围，使用感官描写（视觉、听觉、嗅觉等）增强沉浸感。
5. 角色心理：适当展现角色内心活动，通过心理描写展示角色性格和情感变化。
6. 情节发展：情节要有起承转合，保持读者的阅读兴趣，适当设置悬念和冲突。
7. 意外元素：加入一些出人意料的元素，避免情节过于平淡或可预测。

请直接开始创作，不需要额外的解释或说明。创作的内容应当完整、连贯，能够独立成篇。
""",

            "polishing_agent": """
你是一位专业的中文文学编辑，擅长对小说内容进行润色和优化。请对以下小说内容进行全面润色：

原文：
{original_text}

润色要求：
1. 语言表达：提升语言的优美度和流畅性，修正不自然或生硬的表达。
2. 词汇选择：使用更加精准、生动的词汇，避免重复用词，增强表现力。
3. 句式多样：调整句式结构，使长短句搭配合理，增加语言的节奏感和韵律感。
4. 修辞手法：适当增加比喻、拟人、排比等修辞手法，使文字更加生动形象。
5. 语法修正：纠正语法错误，确保文本在语法上的准确性。
6. 标点使用：规范标点符号的使用，使标点符号能够准确表达语气和停顿。

请直接给出润色后的内容，不需要解释修改原因。润色应当保持原文的核心内容和风格，同时提升文学性和可读性。
""",

            "expansion_agent": """
你是一位专业的中文小说扩写专家，擅长对小说内容进行合理的扩展和丰富。请对以下小说内容进行扩写：

原文：
{original_text}

扩写要求：
1. 场景细节：增加场景的细节描写，包括环境、气氛、光线、声音等元素，使场景更加立体。
2. 角色刻画：深化角色形象，增加角色的外貌、动作、表情、心理活动等描写，使角色更加丰满。
3. 情感表达：丰富情感层次，通过细腻的描写展现角色的情感变化和内心冲突。
4. 对话扩展：适当扩展对话内容，通过对话展现角色性格和推动情节发展。
5. 背景补充：适当补充故事背景和世界观设定，增强故事的深度和可信度。
6. 情节丰富：在不改变主要情节走向的前提下，增加合理的情节支线或细节。
7. 字数要求：字数要求为4000字以上。
8. 避免重复和出现提示词内容，整体一致。
请直接给出扩写后的内容，不需要解释扩写思路。扩写应当自然融入原文，保持风格一致，避免生硬的拼接感。
""",

            "review_agent": """
你是一位专业的文学评论家和编辑，擅长对小说内容进行全面评估和修复，请对内容进行系统性完善：

原文：
{original_text}

核心要求：
内容：确保故事完整闭环，补全逻辑断点
结构：三幕式节奏控制（铺垫30%/发展40%/高潮30%）
人物：强化特征标签（动机/行为/关系三维度）
场景：五感沉浸构建（视觉/听觉必含）
文本：文学性检测（修辞≥15%+风格方差≤0.3），扩写≥20%
细节：细节完善度≥90%（人物/场景/情节）
语言：语法准确度≥95%，词汇多样性≥80%
节奏：阅读节奏控制（段落平均长度±10%）
情感：情感共鸣度≥80%（情感标签匹配度）
字数要求：字数要求为8000字以上。
请直接输出修复完善后的内容。这个版本将作为后续优化的基础，需要确保内容完整、逻辑严密、避免出现提示词。
""",

            "optimization_agent": """
你是一位专业的中文小说优化专家，擅长对小说内容进行全面优化和提升。请对以下小说内容进行优化：

原文：
{original_text}

优化要求：
1. 整体结构：优化故事结构，确保情节发展合理，起承转合清晰，节奏感强。
2. 情节连贯：修正情节中的不连贯或矛盾之处，确保故事逻辑自洽。
3. 角色一致：保持角色形象和性格的一致性，修正角色行为与性格不符的地方。
4. 语言提升：提升语言表达的文学性和感染力，使用更加精准、生动的词汇和句式。
5. 细节完善：扩写和完善故事细节，增强真实感和沉浸感，修正不合理或模糊的细节。
6. 主题深化：扩写和强化作品的主题表达，使思想内涵更加深刻，引人思考。
7. 章节结尾处理：章节结尾必须留有悬念，不要完全解决所有冲突，为下一章节留下铺垫。不要在结尾描述角色的整个生命历程或最终命运。
8. 悬念设计：在章节结尾处添加意外的转折、新的问题或神秘的线索，引发读者的好奇心，让读者想要继续阅读下一章。
9. 字数要求：字数要求为10000字以上。
10. 避免重复和出现提示词内容，整体一致。
请直接给出优化后的内容，不需要解释优化思路。优化应当全面提升作品质量，同时保持原作的风格和核心内容。
""",

            "illustration_agent": """
你是一位专业的插画艺术家，擅长为儿童读物和青少年小说创作精美的简笔插图。请根据以下场景描述创作一幅插图：

场景描述：
{scene_description}

风格要求：
{style}

场景构建要求：
主题 全景自然景观 无人物介入
核心要素 地形地貌完整性95以上 生态元素密度3类每平方公里
光学规范 晨间柔光 色温4500K正负5 能见度等级A加
构图参数 黄金螺旋布局 景深梯度0.8至无限 轴线偏移容差3以内

视觉安全要求：
元素过滤 自动替换尖锐物 阴影区大于30 非自然色相
色彩矩阵 Pantone L82至88明度带 饱和度阀值S15以内
动态平衡 湍流系数0.2以内 枝叶摆幅8以内

技术规格要求：
分辨率 7680乘4320像素 色深12bit
纹理等级 纹理指数4.5以上 ISO13655标准
风格锚定 自然写实主义 风格偏移量0.12以内

情绪传达要求：
活力参数 光合作用指数PHI7.2正负0.3
宁静系数 声波模拟值20dB等效以内
色彩心理学 蓝绿主频480至520nm占比60以上

执行验证要求：
双通道校验 形态合规性α通道 光谱安全性β通道
容错机制 实时渲染误差修正 每帧3次以上采样

输出格式要求： PNG 无损压缩 ICC配置文件 sRGB IEC61966减2.1
""",

            "serial_novel_chapter": """
你是一个擅长创作长篇连载小说的专业的独特风格的超级作家。当前需要生成第{chapter_number}章的内容。为了确保章节之间故事连贯，请严格按照以下要求生成：

1. **前章上下文**：
   - 前一章（第{previous_chapter_number}章）的结尾是：“{previous_chapter_ending}”
   - 前一章的关键事件：“{previous_key_event}”
   - 当前时间：“{current_time}”
   - 当前地点：“{current_location}”
   - 主要人物状态：“{character_status}”

2. **本章生成要求**：
   - 从前一章结尾的场景直接开始，确保时间、地点、人物的连续性。
   - 基于前一章的关键事件推进故事，例如：“{plot_development}”
   - 本章的主要目标或冲突是：“{chapter_goal}”
   - 语气与风格：“{style}”
   - 本章字数控制在：“{word_count}”

3. **连贯性约束**：
   - 不得随意跳跃时间或地点，除非故事逻辑明确需要，并在文中合理过渡并解释。
   - 保持人物行为和动机与前文一致，不得出现矛盾。
   - 本章开头必须自然衔接前章结尾，避免突兀感。
   - 如果需要引入新角色或事件，必须与前文有合理关联。

4. **输出格式**：
   - 以小说正文形式输出，不包含额外注释或说明。

请根据以上信息生成第{chapter_number}章的内容。
"""
        }

        return self.prompts

    def get_prompt(self, prompt_type: str, **kwargs) -> str:
        """
        获取指定类型的提示词，并填充参数

        参数:
            prompt_type: 提示词类型
            **kwargs: 提示词中需要填充的参数

        返回:
            填充参数后的提示词
        """
        if prompt_type not in self.prompts:
            raise ValueError(f"未找到类型为 '{prompt_type}' 的提示词")

        # 获取提示词模板
        prompt_template = self.prompts[prompt_type]

        # 填充参数
        try:
            prompt = prompt_template.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"提示词参数错误: 缺少参数 {e}")

        return prompt

    def add_prompt(self, prompt_type: str, prompt_template: str):
        """
        添加新的提示词模板

        参数:
            prompt_type: 提示词类型
            prompt_template: 提示词模板
        """
        self.prompts[prompt_type] = prompt_template
        self._save_prompts()
        print(f"已添加类型为 '{prompt_type}' 的提示词模板")

    def update_prompt(self, prompt_type: str, prompt_template: str):
        """
        更新现有的提示词模板

        参数:
            prompt_type: 提示词类型
            prompt_template: 新的提示词模板
        """
        if prompt_type not in self.prompts:
            raise ValueError(f"未找到类型为 '{prompt_type}' 的提示词，无法更新")

        self.prompts[prompt_type] = prompt_template
        self._save_prompts()
        print(f"已更新类型为 '{prompt_type}' 的提示词模板")

    def delete_prompt(self, prompt_type: str):
        """
        删除提示词模板

        参数:
            prompt_type: 要删除的提示词类型
        """
        if prompt_type not in self.prompts:
            raise ValueError(f"未找到类型为 '{prompt_type}' 的提示词，无法删除")

        del self.prompts[prompt_type]
        self._save_prompts()
        print(f"已删除类型为 '{prompt_type}' 的提示词模板")

    def list_prompts(self) -> Dict[str, str]:
        """
        列出所有可用的提示词模板

        返回:
            提示词模板字典
        """
        return self.prompts
