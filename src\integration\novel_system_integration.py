"""
Novel System Integration Module

This module integrates all the advanced components of the novel writing system:
1. Memory Management
2. Consistency Checking
3. Clue Management
4. Narrative Control

It provides a simple interface for the main novel writing pipeline to use these components.
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
import traceback

# Import memory management components
try:
    from src.memory.memory_manager import MemoryManager
except ImportError:
    print("Warning: Memory management components not found")
    MemoryManager = None

# Import consistency checking components
try:
    from src.consistency.consistency_checker import get_consistency_checker, check_consistency
except ImportError:
    print("Warning: Consistency checking components not found")
    get_consistency_checker = None
    check_consistency = None

# Import clue management components
try:
    from src.clues.clue_manager import get_clue_manager, update_clues_from_text, get_unused_clues
except ImportError:
    print("Warning: Clue management components not found")
    get_clue_manager = None
    update_clues_from_text = None
    get_unused_clues = None

# Import narrative control components
try:
    from src.narrative.narrative_controller import get_narrative_controller, generate_next_chapter_outline
except ImportError:
    print("Warning: Narrative control components not found")
    get_narrative_controller = None
    generate_next_chapter_outline = None

# Import MCP client for LLM calls
try:
    from src.utils.mcp_client import call_llm
except ImportError:
    print("Warning: MCP client not found")
    def call_llm(model, prompt, **kwargs):
        print(f"Would call model {model} with prompt: {prompt[:100]}...")
        return f"LLM调用示例响应: {prompt[:50]}..."

class NovelSystemIntegration:
    """
    Novel System Integration class that coordinates all advanced components
    """
    
    def __init__(self, novel_dir: str, model: str = "glm-4-flash"):
        """
        Initialize the novel system integration
        
        Args:
            novel_dir: Novel directory
            model: Model to use for LLM calls
        """
        self.novel_dir = novel_dir
        self.model = model
        
        # Create necessary directories
        os.makedirs(novel_dir, exist_ok=True)
        os.makedirs(os.path.join(novel_dir, "memory"), exist_ok=True)
        os.makedirs(os.path.join(novel_dir, "consistency"), exist_ok=True)
        os.makedirs(os.path.join(novel_dir, "clues"), exist_ok=True)
        os.makedirs(os.path.join(novel_dir, "narrative"), exist_ok=True)
        
        # Initialize components
        self.memory_manager = MemoryManager(novel_dir) if MemoryManager else None
        
        # Initialize other components through their getter functions
        if get_consistency_checker:
            self.consistency_checker = get_consistency_checker(novel_dir)
        else:
            self.consistency_checker = None
            
        if get_clue_manager:
            self.clue_manager = get_clue_manager(novel_dir)
        else:
            self.clue_manager = None
            
        if get_narrative_controller:
            self.narrative_controller = get_narrative_controller(novel_dir)
        else:
            self.narrative_controller = None
        
        print(f"Novel system integration initialized for {novel_dir}")
        
    def call_llm(self, prompt: str, **kwargs) -> str:
        """
        Call LLM with the given prompt
        
        Args:
            prompt: Prompt to send to the LLM
            **kwargs: Additional arguments to pass to the LLM
            
        Returns:
            LLM response
        """
        return call_llm(self.model, prompt, **kwargs)
    
    def enhance_prompt_with_memory(self, prompt: str) -> str:
        """
        Enhance a prompt with memory context
        
        Args:
            prompt: Original prompt
            
        Returns:
            Enhanced prompt
        """
        if not self.memory_manager:
            return prompt
            
        context = self.memory_manager.get_context_for_generation(self.call_llm)
        
        # Add global summary if available
        if context.get("global_summary"):
            prompt += f"\n\n故事总结（请确保与此保持一致）：\n{context['global_summary']}\n"
        
        # Add recent content if available
        if context.get("recent_content"):
            prompt += f"\n\n最近内容（请确保连贯性）：\n{context['recent_content']}\n"
        
        # Add active clues if available
        active_clues = context.get("active_clues", [])
        if active_clues:
            clues_text = "\n".join([f"- {clue['description']}" for clue in active_clues])
            prompt += f"\n\n活跃线索（请在本章中使用或发展这些线索）：\n{clues_text}\n"
        
        return prompt
    
    def update_memory_from_chapter(self, chapter_content: str, chapter_num: int) -> None:
        """
        Update memory with chapter content
        
        Args:
            chapter_content: Chapter content
            chapter_num: Chapter number
        """
        if not self.memory_manager:
            return
            
        # Extract entities
        self.memory_manager.extract_entities_from_text(chapter_content, self.call_llm)
        
        # Summarize chapter
        self.memory_manager.summarize_chapter(chapter_num, chapter_content, self.call_llm)
        
        # Save memory
        self.memory_manager.save_memory()
        
        print(f"Updated memory for chapter {chapter_num}")
    
    def check_consistency(self, chapter_content: str, chapter_num: int) -> Tuple[bool, str]:
        """
        Check consistency of chapter content
        
        Args:
            chapter_content: Chapter content
            chapter_num: Chapter number
            
        Returns:
            (is_consistent, consistency_report)
        """
        if not check_consistency:
            return True, "Consistency checking not available"
            
        return check_consistency(chapter_content, chapter_num, self.novel_dir, self.call_llm)
    
    def update_clues(self, chapter_content: str) -> int:
        """
        Update clues based on chapter content
        
        Args:
            chapter_content: Chapter content
            
        Returns:
            Number of clues updated
        """
        if not update_clues_from_text:
            return 0
            
        return update_clues_from_text(chapter_content, self.novel_dir, self.call_llm)
    
    def get_clue_suggestions(self, chapter_outline: str) -> str:
        """
        Get suggestions for integrating unused clues
        
        Args:
            chapter_outline: Chapter outline
            
        Returns:
            Suggestions text
        """
        if not get_unused_clues or not self.clue_manager:
            return ""
            
        unused_clues = get_unused_clues(self.novel_dir)
        if not unused_clues:
            return ""
            
        # Create clue integration prompt
        clues_text = "\n".join([f"- {clue['description']}" for clue in unused_clues])
        
        prompt = f"""
你是一位小说顾问，帮助作家将未使用的线索整合到故事中。请提出如何将以下未使用的线索整合到即将创作的章节中。

未使用的线索：
{clues_text}

章节大纲：
{chapter_outline}

请为每个线索提出具体的整合方式。你的建议应该：
1. 自然而不勉强
2. 符合章节大纲
3. 推动情节或角色发展
4. 不要过早揭示太多

请以建议列表的形式回答，每个线索一条建议。
"""
        
        return self.call_llm(prompt)
    
    def generate_next_chapter_outline(self, chapter_num: int, novel_outline: str) -> str:
        """
        Generate outline for the next chapter
        
        Args:
            chapter_num: Current chapter number
            novel_outline: Novel outline
            
        Returns:
            Chapter outline
        """
        if not generate_next_chapter_outline:
            return f"第{chapter_num + 1}章大纲 - 故事的延续"
            
        return generate_next_chapter_outline(chapter_num, novel_outline, self.novel_dir, self.call_llm)
    
    def add_segment_to_memory(self, segment: str) -> None:
        """
        Add a segment to short-term memory
        
        Args:
            segment: Text segment
        """
        if not self.memory_manager:
            return
            
        self.memory_manager.add_segment(segment)
    
    def clear_short_term_memory(self) -> None:
        """
        Clear short-term memory
        """
        if not self.memory_manager:
            return
            
        self.memory_manager.clear_short_term()

# Global novel system integration instance
_novel_system = None

def get_novel_system(novel_dir: str, model: str = "glm-4-flash") -> NovelSystemIntegration:
    """
    Get or create a novel system integration for the given novel directory
    
    Args:
        novel_dir: Novel directory
        model: Model to use for LLM calls
        
    Returns:
        Novel system integration instance
    """
    global _novel_system
    
    if _novel_system is None or _novel_system.novel_dir != novel_dir:
        _novel_system = NovelSystemIntegration(novel_dir, model)
    
    return _novel_system

def enhance_prompt_with_memory(prompt: str, novel_dir: str) -> str:
    """
    Enhance a prompt with memory context
    
    Args:
        prompt: Original prompt
        novel_dir: Novel directory
        
    Returns:
        Enhanced prompt
    """
    system = get_novel_system(novel_dir)
    return system.enhance_prompt_with_memory(prompt)

def update_memory_from_chapter(chapter_content: str, chapter_num: int, novel_dir: str) -> None:
    """
    Update memory with chapter content
    
    Args:
        chapter_content: Chapter content
        chapter_num: Chapter number
        novel_dir: Novel directory
    """
    system = get_novel_system(novel_dir)
    system.update_memory_from_chapter(chapter_content, chapter_num)

def check_chapter_consistency(chapter_content: str, chapter_num: int, novel_dir: str) -> Tuple[bool, str]:
    """
    Check consistency of chapter content
    
    Args:
        chapter_content: Chapter content
        chapter_num: Chapter number
        novel_dir: Novel directory
        
    Returns:
        (is_consistent, consistency_report)
    """
    system = get_novel_system(novel_dir)
    return system.check_consistency(chapter_content, chapter_num)

def update_chapter_clues(chapter_content: str, novel_dir: str) -> int:
    """
    Update clues based on chapter content
    
    Args:
        chapter_content: Chapter content
        novel_dir: Novel directory
        
    Returns:
        Number of clues updated
    """
    system = get_novel_system(novel_dir)
    return system.update_clues(chapter_content)

def get_clue_integration_suggestions(chapter_outline: str, novel_dir: str) -> str:
    """
    Get suggestions for integrating unused clues
    
    Args:
        chapter_outline: Chapter outline
        novel_dir: Novel directory
        
    Returns:
        Suggestions text
    """
    system = get_novel_system(novel_dir)
    return system.get_clue_suggestions(chapter_outline)

def generate_chapter_outline(chapter_num: int, novel_outline: str, novel_dir: str) -> str:
    """
    Generate outline for the next chapter
    
    Args:
        chapter_num: Current chapter number
        novel_outline: Novel outline
        novel_dir: Novel directory
        
    Returns:
        Chapter outline
    """
    system = get_novel_system(novel_dir)
    return system.generate_next_chapter_outline(chapter_num, novel_outline)
