"""
简易文档阅读器模块

提供简单易用的阅读器界面，支持TXT、Markdown和Word格式。
"""

import os
import json
import gradio as gr
import markdown
import webbrowser
import threading
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# 尝试导入Word处理库
try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("Warning: python-docx not available. Word documents will not be supported.")

# 支持的文件类型
SUPPORTED_EXTENSIONS = {
    ".txt": "text",
    ".md": "markdown",
    ".docx": "docx" if DOCX_AVAILABLE else "unsupported",
    ".doc": "docx" if DOCX_AVAILABLE else "unsupported",
}

# 历史记录存储路径
HISTORY_FILE = os.path.join(os.path.dirname(__file__), "reader_history.json")

class SimpleReader:
    """简易文档阅读器，支持多种格式"""

    def __init__(self):
        """初始化阅读器"""
        self.current_file = None
        self.current_content = None
        self.current_format = None
        self.history = self.load_history()

    def load_history(self) -> List[str]:
        """加载历史记录"""
        if os.path.exists(HISTORY_FILE):
            try:
                with open(HISTORY_FILE, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载历史记录时出错: {e}")
        return []

    def save_history(self) -> None:
        """保存历史记录"""
        try:
            with open(HISTORY_FILE, "w", encoding="utf-8") as f:
                json.dump(self.history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存历史记录时出错: {e}")

    def add_to_history(self, file_path: str) -> None:
        """
        添加文件到历史记录

        参数:
            file_path: 文件路径
        """
        # 如果已在历史记录中，先移除
        if file_path in self.history:
            self.history.remove(file_path)

        # 添加到开头
        self.history.insert(0, file_path)

        # 只保留最近10条记录
        self.history = self.history[:10]

        # 保存历史记录
        self.save_history()

    def get_history(self) -> List[str]:
        """
        获取历史记录

        返回:
            文件路径列表
        """
        return self.history

    def read_text_file(self, file_path: str) -> str:
        """
        读取文本文件

        参数:
            file_path: 文件路径

        返回:
            文件内容
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return f.read()
        except UnicodeDecodeError:
            # 尝试不同的编码
            try:
                with open(file_path, "r", encoding="gbk") as f:
                    return f.read()
            except UnicodeDecodeError:
                try:
                    with open(file_path, "r", encoding="latin-1") as f:
                        return f.read()
                except Exception as e:
                    return f"读取文件时出错: {e}"
        except Exception as e:
            return f"读取文件时出错: {e}"

    def read_markdown_file(self, file_path: str) -> str:
        """
        读取Markdown文件

        参数:
            file_path: 文件路径

        返回:
            文件内容(HTML)
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 转换Markdown为HTML
            html = markdown.markdown(content, extensions=['tables', 'fenced_code'])

            # 添加CSS样式
            styled_html = f"""
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }}
                h1, h2, h3, h4, h5, h6 {{ color: #333; margin-top: 24px; margin-bottom: 16px; }}
                h1 {{ font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }}
                h2 {{ font-size: 1.5em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }}
                p {{ margin-top: 0; margin-bottom: 16px; }}
                code {{ font-family: Consolas, monospace; background-color: #f6f8fa; padding: 0.2em 0.4em; border-radius: 3px; }}
                pre {{ background-color: #f6f8fa; border-radius: 3px; padding: 16px; overflow: auto; }}
                pre code {{ background-color: transparent; padding: 0; }}
                blockquote {{ margin: 0; padding: 0 1em; color: #6a737d; border-left: 0.25em solid #dfe2e5; }}
                table {{ border-collapse: collapse; width: 100%; }}
                table th, table td {{ border: 1px solid #ddd; padding: 6px 13px; }}
                table tr {{ background-color: #fff; border-top: 1px solid #c6cbd1; }}
                table tr:nth-child(2n) {{ background-color: #f6f8fa; }}
                img {{ max-width: 100%; }}
            </style>
            {html}
            """

            return styled_html
        except Exception as e:
            return f"读取Markdown文件时出错: {e}"

    def read_docx_file(self, file_path: str) -> str:
        """
        读取Word文档

        参数:
            file_path: 文件路径

        返回:
            文件内容(HTML)
        """
        if not DOCX_AVAILABLE:
            return "Word文档支持不可用。请安装python-docx库。"

        try:
            doc = Document(file_path)

            # 提取带格式的文本
            html_parts = ["<div class='word-document'>"]

            # 处理段落
            for para in doc.paragraphs:
                if not para.text.strip():
                    html_parts.append("<p>&nbsp;</p>")
                    continue

                # 检查段落样式
                style = para.style.name.lower()
                if "heading" in style:
                    level = int(style.replace("heading", "").strip()) if style != "heading" else 1
                    level = max(1, min(level, 6))  # 确保级别在1-6之间
                    html_parts.append(f"<h{level}>{para.text}</h{level}>")
                else:
                    html_parts.append(f"<p>{para.text}</p>")

            # 处理表格
            for table in doc.tables:
                table_html = ["<table border='1' cellpadding='3'>"]
                for row in table.rows:
                    table_html.append("<tr>")
                    for cell in row.cells:
                        table_html.append(f"<td>{cell.text}</td>")
                    table_html.append("</tr>")
                table_html.append("</table>")
                html_parts.append("".join(table_html))

            html_parts.append("</div>")

            # 添加CSS样式
            styled_html = f"""
            <style>
                .word-document {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }}
                h1, h2, h3, h4, h5, h6 {{ color: #333; margin-top: 24px; margin-bottom: 16px; }}
                h1 {{ font-size: 2em; }}
                h2 {{ font-size: 1.5em; }}
                p {{ margin-top: 0; margin-bottom: 16px; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 16px; }}
                table th, table td {{ border: 1px solid #ddd; padding: 6px 13px; }}
                table tr {{ background-color: #fff; }}
            </style>
            {"".join(html_parts)}
            """

            return styled_html
        except Exception as e:
            return f"读取Word文档时出错: {e}"

    def open_file(self, file_path: str) -> Dict[str, Any]:
        """
        打开文件

        参数:
            file_path: 文件路径

        返回:
            包含文件信息的字典
        """
        if not os.path.exists(file_path):
            return {
                "success": False,
                "error": f"文件未找到: {file_path}",
                "content": f"错误: 文件未找到: {file_path}",
                "format": None
            }

        # 获取文件扩展名
        ext = os.path.splitext(file_path)[1].lower()

        # 检查是否支持
        if ext not in SUPPORTED_EXTENSIONS:
            return {
                "success": False,
                "error": f"不支持的文件格式: {ext}",
                "content": f"错误: 不支持的文件格式: {ext}",
                "format": None
            }

        # 根据格式读取文件
        format_type = SUPPORTED_EXTENSIONS[ext]
        content = None

        if format_type == "text":
            content = self.read_text_file(file_path)
        elif format_type == "markdown":
            content = self.read_markdown_file(file_path)
        elif format_type == "docx":
            content = self.read_docx_file(file_path)

        # 更新当前文件
        self.current_file = file_path
        self.current_content = content
        self.current_format = format_type

        # 添加到历史记录
        self.add_to_history(file_path)

        return {
            "success": True,
            "content": content,
            "format": format_type,
            "file_path": file_path,
            "file_name": os.path.basename(file_path)
        }

def create_reader_ui() -> gr.Blocks:
    """
    创建阅读器UI

    返回:
        Gradio Blocks界面
    """
    reader = SimpleReader()

    with gr.Blocks(title="简易文档阅读器", theme=gr.themes.Soft()) as ui:
        # 创建状态框，用于显示日志
        log_output = gr.Textbox(label="日志", visible=False, lines=3)

        with gr.Row():
            gr.Markdown("# 简易文档阅读器")

        with gr.Row():
            gr.Markdown("支持TXT、Markdown和Word格式的本地文档阅读器。")

        with gr.Row():
            with gr.Column(scale=1):
                file_input = gr.File(label="选择文件", file_types=[".txt", ".md", ".docx", ".doc"])

                with gr.Accordion("最近打开的文件", open=True):
                    history_list = gr.Dropdown(
                        choices=[],
                        label="历史记录",
                        interactive=True
                    )

                with gr.Accordion("阅读设置", open=True):
                    theme_selector = gr.Radio(
                        choices=["浅色", "深色", "护眼模式"],
                        label="主题",
                        value="浅色"
                    )
                    font_size = gr.Slider(
                        minimum=12,
                        maximum=24,
                        value=16,
                        step=1,
                        label="字体大小"
                    )

            with gr.Column(scale=3):
                file_info = gr.Markdown("## 请选择一个文件开始阅读")

                # 文档内容显示
                content_display = gr.HTML(
                    label="文档内容",
                    visible=True
                )

        # 隐藏状态
        current_file_path = gr.State("")

        # 文件上传处理函数
        def file_upload_handler(file_obj):
            if file_obj is None:
                return (
                    "## 请选择一个文件",
                    gr.update(value="", visible=True),
                    ""
                )

            file_path = file_obj.name
            result = reader.open_file(file_path)

            if not result["success"]:
                return (
                    f"## 错误: {result['error']}",
                    gr.update(value=result["content"], visible=True),
                    ""
                )

            # 更新历史记录下拉框
            update_history_dropdown()

            return (
                f"## {result['file_name']} ({result['format']})",
                gr.update(value=result["content"], visible=True),
                file_path
            )

        # 历史记录变更处理函数
        def history_change_handler(selected_path):
            if not selected_path:
                return (
                    "## 请选择一个文件",
                    gr.update(value="", visible=True),
                    ""
                )

            result = reader.open_file(selected_path)

            if not result["success"]:
                return (
                    f"## 错误: {result['error']}",
                    gr.update(value=result["content"], visible=True),
                    ""
                )

            return (
                f"## {result['file_name']} ({result['format']})",
                gr.update(value=result["content"], visible=True),
                selected_path
            )

        # 更新内容显示处理函数
        def update_content_display(theme, font_size):
            if not reader.current_content:
                return gr.update(value="", visible=True)

            # 根据主题添加CSS
            theme_css = ""
            if theme == "深色":
                theme_css = """
                body { background-color: #222; color: #eee; }
                h1, h2, h3, h4, h5, h6 { color: #ccc; }
                a { color: #58a6ff; }
                code { background-color: #333; }
                pre { background-color: #333; }
                blockquote { color: #aaa; border-left-color: #444; }
                table tr { background-color: #222; }
                table tr:nth-child(2n) { background-color: #333; }
                table th, table td { border-color: #444; }
                """
            elif theme == "护眼模式":
                theme_css = """
                body { background-color: #f0f0e0; color: #333; }
                pre, code { background-color: #e0e0d0; }
                """

            # 添加字体大小CSS
            font_css = f"body {{ font-size: {font_size}px; }}"

            # 如果是HTML内容，插入CSS
            if reader.current_format in ["markdown", "docx"]:
                content = reader.current_content
                # 查找</style>标签的位置
                style_end_pos = content.find("</style>")
                if style_end_pos > 0:
                    # 在</style>前插入主题CSS
                    content = content[:style_end_pos] + theme_css + font_css + content[style_end_pos:]
                else:
                    # 如果没有</style>标签，添加一个新的style标签
                    content = f"<style>{theme_css}{font_css}</style>" + content
                return gr.update(value=content, visible=True)
            else:
                # 纯文本内容，包装在pre标签中
                return gr.update(
                    value=f"""
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; {font_css[5:-1]} }}
                        pre {{ white-space: pre-wrap; word-wrap: break-word; padding: 15px; }}
                        {theme_css}
                    </style>
                    <pre>{reader.current_content}</pre>
                    """,
                    visible=True
                )

        # 更新历史记录下拉框
        def update_history_dropdown():
            history = reader.get_history()
            history_options = []
            for path in history:
                if os.path.exists(path):
                    history_options.append(path)
            return gr.update(choices=history_options)

        # 绑定事件
        file_input.upload(
            fn=file_upload_handler,
            inputs=[file_input],
            outputs=[file_info, content_display, current_file_path]
        )

        history_list.change(
            fn=history_change_handler,
            inputs=[history_list],
            outputs=[file_info, content_display, current_file_path]
        )

        theme_selector.change(
            fn=update_content_display,
            inputs=[theme_selector, font_size],
            outputs=[content_display]
        )

        font_size.change(
            fn=update_content_display,
            inputs=[theme_selector, font_size],
            outputs=[content_display]
        )

        # 初始化加载历史记录
        ui.load(
            fn=update_history_dropdown,
            inputs=None,
            outputs=[history_list]
        )

    return ui

def launch_simple_reader():
    """启动简易阅读器"""
    try:
        # 创建阅读器UI
        ui = create_reader_ui()

        # 尝试不同的端口，避免端口冲突
        try:
            # 首选端口
            ui.queue().launch(server_name="127.0.0.1", server_port=4556, share=False, prevent_thread_lock=True)
        except OSError:
            try:
                # 备选端口
                print("端口4556被占用，尝试端口4557...")
                ui.queue().launch(server_name="127.0.0.1", server_port=4557, share=False, prevent_thread_lock=True)
            except OSError:
                # 随机端口
                print("端口4557也被占用，使用随机端口...")
                ui.queue().launch(server_name="127.0.0.1", share=False, prevent_thread_lock=True)
    except Exception as e:
        print(f"启动简易阅读器时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    launch_simple_reader()
