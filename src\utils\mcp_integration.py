"""
MCP Integration Module

This module provides integration with the Multi-agent Cooperation Protocol (MCP) framework.
It allows the novel writing system to use MCP for agent cooperation.
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
import traceback

try:
    from src.utils.mcp_client import call_llm, generate_illustration
except ImportError:
    # Create dummy functions if MCP client is not available
    def call_llm(model_name, prompt, temperature=0.7, max_tokens=2000):
        print(f"[MCP] Would call model {model_name} with prompt: {prompt[:100]}...")
        return f"LLM调用示例响应: {prompt[:50]}..."
    
    def generate_illustration(title, prompt, style=None):
        print(f"[MCP] Would generate illustration for {title} with prompt: {prompt[:100]}...")
        return None

class MCPAgentCoordinator:
    """
    Coordinator for MCP-based agent cooperation
    """
    
    def __init__(self, novel_dir: str):
        """
        Initialize MCP agent coordinator
        
        Args:
            novel_dir: Novel directory
        """
        self.novel_dir = novel_dir
        self.agents = {}
        self.default_model = "glm-4-flash"
    
    def register_agent(self, agent_id: str, agent_type: str, model_name: str = None):
        """
        Register an agent
        
        Args:
            agent_id: Agent identifier
            agent_type: Agent type
            model_name: Model name to use
        """
        self.agents[agent_id] = {
            "type": agent_type,
            "model": model_name or self.default_model
        }
        print(f"Registered agent: {agent_id} ({agent_type}) using model {model_name or self.default_model}")
    
    def call_agent(self, agent_id: str, prompt: str, temperature: float = 0.7, max_tokens: int = 2000) -> str:
        """
        Call an agent
        
        Args:
            agent_id: Agent identifier
            prompt: Prompt to send
            temperature: Temperature parameter
            max_tokens: Maximum tokens to generate
            
        Returns:
            Agent response
        """
        if agent_id not in self.agents:
            print(f"Warning: Agent {agent_id} not registered, using default model")
            model_name = self.default_model
        else:
            model_name = self.agents[agent_id]["model"]
        
        try:
            return call_llm(model_name, prompt, temperature, max_tokens)
        except Exception as e:
            print(f"Error calling agent {agent_id}: {e}")
            traceback.print_exc()
            return f"Error: {str(e)}"
    
    def generate_illustration(self, title: str, prompt: str, style: str = None) -> Optional[str]:
        """
        Generate an illustration
        
        Args:
            title: Illustration title
            prompt: Illustration prompt
            style: Illustration style
            
        Returns:
            Path to generated illustration or None
        """
        try:
            return generate_illustration(title, prompt, style)
        except Exception as e:
            print(f"Error generating illustration: {e}")
            traceback.print_exc()
            return None

# Global MCP agent coordinator instance
_mcp_coordinator = None

def get_mcp_coordinator(novel_dir: str) -> MCPAgentCoordinator:
    """
    Get or create an MCP agent coordinator for the given novel directory
    
    Args:
        novel_dir: Novel directory
        
    Returns:
        MCP agent coordinator instance
    """
    global _mcp_coordinator
    
    if _mcp_coordinator is None or _mcp_coordinator.novel_dir != novel_dir:
        _mcp_coordinator = MCPAgentCoordinator(novel_dir)
        print(f"Created new MCP agent coordinator for {novel_dir}")
        
        # Register default agents
        _mcp_coordinator.register_agent("writing", "WritingAgent", "glm-4-flash")
        _mcp_coordinator.register_agent("polishing", "PolishingAgent", "glm-4-flash")
        _mcp_coordinator.register_agent("expansion", "ExpansionAgent", "internlm/internlm2_5-7b-chat")
        _mcp_coordinator.register_agent("review", "ReviewAgent", "glm-4-flash")
        _mcp_coordinator.register_agent("optimization", "OptimizationAgent", "internlm/internlm2_5-7b-chat")
        _mcp_coordinator.register_agent("illustration", "IllustrationAgent", "cogview-3-flash")
        _mcp_coordinator.register_agent("consistency", "ConsistencyAgent", "glm-4-flash")
        _mcp_coordinator.register_agent("narrative", "NarrativeAgent", "glm-4-flash")
        _mcp_coordinator.register_agent("memory", "MemoryAgent", "glm-4-flash")
        _mcp_coordinator.register_agent("clue", "ClueAgent", "glm-4-flash")
    
    return _mcp_coordinator

def call_agent(agent_id: str, prompt: str, novel_dir: str, temperature: float = 0.7, max_tokens: int = 2000) -> str:
    """
    Call an agent
    
    Args:
        agent_id: Agent identifier
        prompt: Prompt to send
        novel_dir: Novel directory
        temperature: Temperature parameter
        max_tokens: Maximum tokens to generate
        
    Returns:
        Agent response
    """
    coordinator = get_mcp_coordinator(novel_dir)
    return coordinator.call_agent(agent_id, prompt, temperature, max_tokens)

def generate_illustration(title: str, prompt: str, novel_dir: str, style: str = None) -> Optional[str]:
    """
    Generate an illustration
    
    Args:
        title: Illustration title
        prompt: Illustration prompt
        novel_dir: Novel directory
        style: Illustration style
        
    Returns:
        Path to generated illustration or None
    """
    coordinator = get_mcp_coordinator(novel_dir)
    return coordinator.generate_illustration(title, prompt, style)
