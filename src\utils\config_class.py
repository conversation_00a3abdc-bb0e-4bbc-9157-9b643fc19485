"""
配置类模块
用于加载和管理配置
"""

import os
import json
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class Config:
    """配置类，用于加载和管理配置"""
    
    def __init__(self, config_file=None):
        """初始化配置类
        
        参数:
            config_file: 配置文件路径，如果为None，则使用默认配置
        """
        self.config_file = config_file
        self.config = {}
        self.load()
        
    def load(self):
        """加载配置"""
        # 默认配置
        self.config = {
            "novel": {
                "title": "未知标题",
                "genre": "现代小说",
                "style": "写实主义",
                "scene": "城市",
                "characters": "一位年轻人"
            },
            "agents": {
                "writing_model": "glm-4-flash",
                "writing_temperature": 0.7,
                "polish_model": "glm-4-flash",
                "outline_model": "glm-4-flash",
                "outline_temperature": 0.7,
                "transition_model": "glm-4-flash",
                "illustration_model": "cogview-3-flash",
                "illustration_style": "写实风格"
            },
            "system": {
                "output_dir": "output",
                "log_dir": "logs",
                "port": 4554,
                "debug": False
            }
        }
        
        # 如果配置文件存在，则加载配置文件
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    loaded_config = json.load(f)
                    # 更新配置
                    self._update_dict(self.config, loaded_config)
                    logger.info(f"已加载配置文件: {self.config_file}")
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                
    def save(self, config_file=None):
        """保存配置
        
        参数:
            config_file: 配置文件路径，如果为None，则使用初始化时的配置文件路径
        """
        save_file = config_file or self.config_file
        if not save_file:
            logger.warning("未指定配置文件路径，无法保存配置")
            return False
            
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(save_file), exist_ok=True)
            
            # 保存配置
            with open(save_file, "w", encoding="utf-8") as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
                
            logger.info(f"已保存配置到: {save_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False
            
    def get(self, key, default=None):
        """获取配置值
        
        参数:
            key: 配置键，可以是点分隔的路径，如"novel.title"
            default: 默认值，如果配置不存在，则返回默认值
            
        返回:
            配置值
        """
        if "." in key:
            # 处理嵌套配置
            parts = key.split(".")
            value = self.config
            for part in parts:
                if isinstance(value, dict) and part in value:
                    value = value[part]
                else:
                    return default
            return value
        else:
            # 处理顶级配置
            return self.config.get(key, default)
            
    def set(self, key, value):
        """设置配置值
        
        参数:
            key: 配置键，可以是点分隔的路径，如"novel.title"
            value: 配置值
            
        返回:
            是否设置成功
        """
        if "." in key:
            # 处理嵌套配置
            parts = key.split(".")
            config = self.config
            for i, part in enumerate(parts[:-1]):
                if part not in config:
                    config[part] = {}
                elif not isinstance(config[part], dict):
                    config[part] = {}
                config = config[part]
            config[parts[-1]] = value
            return True
        else:
            # 处理顶级配置
            self.config[key] = value
            return True
            
    def _update_dict(self, target, source):
        """递归更新字典
        
        参数:
            target: 目标字典
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_dict(target[key], value)
            else:
                target[key] = value
                
    def __getitem__(self, key):
        """获取配置值
        
        参数:
            key: 配置键
            
        返回:
            配置值
        """
        return self.get(key)
        
    def __setitem__(self, key, value):
        """设置配置值
        
        参数:
            key: 配置键
            value: 配置值
        """
        self.set(key, value)
        
    def __contains__(self, key):
        """检查配置是否存在
        
        参数:
            key: 配置键
            
        返回:
            配置是否存在
        """
        return self.get(key) is not None
        
    def __str__(self):
        """返回配置的字符串表示
        
        返回:
            配置的字符串表示
        """
        return json.dumps(self.config, ensure_ascii=False, indent=4)
        
# 创建全局配置实例
config = Config()

# 导出
__all__ = ["Config", "config"]
