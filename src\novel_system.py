"""
Novel System - Main entry point for the novel generation system
"""

import os
import argparse
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime

# Import improved logging system
from src.utils.logger import (
    logger, process_logger,
    log_process_start, log_process_end, log_process_step, log_error
)

# Check if the new system is available
try:
    from novel_integration import generate_novel as generate_novel_new
    NEW_SYSTEM_AVAILABLE = True
    logger.info("New novel generation system is available")
except ImportError:
    NEW_SYSTEM_AVAILABLE = False
    logger.warning("New novel generation system is not available, falling back to old system")

# Import the old system if needed
if not NEW_SYSTEM_AVAILABLE:
    try:
        from writing_agents import create_writing_pipeline, register_writing_agents
        logger.info("Old novel generation system is available")
        OLD_SYSTEM_AVAILABLE = True
    except ImportError:
        OLD_SYSTEM_AVAILABLE = False
        logger.error("Neither new nor old novel generation system is available")

def generate_novel(
    title: str,
    genre: str = "",
    style: str = "",
    scene: str = "",
    characters: str = "",
    num_chapters: int = 10,
    output_dir: str = "output",
    save_intermediates: bool = False,
    batch_size: int = 3,
    progress_callback: Optional[Callable[[str], None]] = None,
    use_new_system: bool = True
) -> str:
    """
    Generate a complete novel

    Args:
        title: Novel title
        genre: Novel genre
        style: Writing style
        scene: Main scene
        characters: Main characters
        num_chapters: Number of chapters
        output_dir: Output directory
        save_intermediates: Whether to save intermediate files
        batch_size: Number of chapters to process in each batch
        progress_callback: Function to call with progress updates
        use_new_system: Whether to use the new system (if available)

    Returns:
        Path to the generated document
    """
    # Start process tracking
    process_id = f"novel_generation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    start_time = log_process_start(f"Novel Generation: {title}")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    log_process_step("Created output directory", output_dir)

    # Define a wrapper for the progress callback that also logs to process log
    def progress_wrapper(message):
        if progress_callback:
            progress_callback(message)
        # Only log non-content messages to avoid cluttering the logs
        if not message.startswith("第") and not "：" in message[:20] and len(message) < 200:
            log_process_step("Progress", message)

    # Use the new system if available and requested
    if NEW_SYSTEM_AVAILABLE and use_new_system:
        logger.info("Using new novel generation system")
        log_process_step("System Selection", "Using new novel generation system")
        try:
            doc_path = generate_novel_new(
                title=title,
                genre=genre,
                style=style,
                scene=scene,
                characters=characters,
                num_chapters=num_chapters,
                output_dir=output_dir,
                save_intermediates=save_intermediates,
                batch_size=batch_size,
                progress_callback=progress_wrapper
            )
            if doc_path:
                log_process_end(f"Novel Generation: {title}", start_time)
                log_process_step("Document Generated", doc_path)
                return doc_path
            else:
                log_error(f"Failed to generate novel with new system")
                log_process_step("Generation Failed", "New system returned empty path")
                return ""
        except Exception as e:
            log_error(f"Error generating novel with new system", e)
            log_process_step("Generation Error", str(e))
            return ""

    # Fall back to the old system if available
    elif OLD_SYSTEM_AVAILABLE:
        logger.info("Using old novel generation system")
        log_process_step("System Selection", "Using old novel generation system")

        try:
            # Register writing agents
            register_writing_agents()
            log_process_step("Initialization", "Registered writing agents")

            # Create timestamp directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            novel_output_dir = os.path.join(output_dir, f"{title}_{timestamp}")
            os.makedirs(novel_output_dir, exist_ok=True)
            log_process_step("Directory Creation", novel_output_dir)

            # Run the writing pipeline
            log_process_step("Pipeline Start", f"Starting writing pipeline for {title}")
            pipeline_start = log_process_start("Writing Pipeline")

            novel = create_writing_pipeline(
                title=title,
                genre=genre,
                style=style,
                scene=scene,
                characters=characters,
                num_chapters=num_chapters,
                output_dir=novel_output_dir,
                save_intermediates=save_intermediates,
                progress=progress_wrapper
            )

            log_process_end("Writing Pipeline", pipeline_start)

            # Return the path to the Word document
            doc_path = os.path.join(novel_output_dir, f"{title}.docx")
            log_process_end(f"Novel Generation: {title}", start_time)
            log_process_step("Document Generated", doc_path)
            return doc_path

        except Exception as e:
            log_error(f"Error generating novel with old system", e)
            log_process_step("Generation Error", str(e))
            log_process_end(f"Novel Generation: {title}", start_time)
            return ""

    # Neither system is available
    else:
        log_error("No novel generation system is available")
        if progress_callback:
            progress_callback("No novel generation system is available")
        log_process_end(f"Novel Generation: {title}", start_time)
        return ""

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Generate a novel')

    parser.add_argument('--title', type=str, default='山河无恙', help='Novel title')
    parser.add_argument('--genre', type=str, default='历史', help='Novel genre')
    parser.add_argument('--style', type=str, default='写实', help='Writing style')
    parser.add_argument('--scene', type=str, default='古代中国', help='Main scene')
    parser.add_argument('--characters', type=str, default='李明、王芳', help='Main characters')
    parser.add_argument('--chapters', type=int, default=10, help='Number of chapters')
    parser.add_argument('--output-dir', type=str, default='output', help='Output directory')
    parser.add_argument('--batch-size', type=int, default=3, help='Batch size for chapter processing')
    parser.add_argument('--save-intermediates', action='store_true', help='Save intermediate files')
    parser.add_argument('--use-old-system', action='store_true', help='Use the old system even if the new one is available')

    return parser.parse_args()

def print_progress(message):
    """Print progress message"""
    # Only print non-content messages to the console
    if not message.startswith("第") and not "：" in message[:20] and len(message) < 200:
        print(f"进度: {message}")

def main():
    """Main function"""
    # Start process tracking
    main_start = log_process_start("Novel System Main Function")

    # Create necessary directories
    os.makedirs('output', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    log_process_step("Directory Setup", "Created output and logs directories")

    # Parse arguments
    args = parse_arguments()
    log_process_step("Arguments Parsed", f"Title: {args.title}, Chapters: {args.chapters}")

    # Log configuration
    logger.info(f"Starting novel generation for '{args.title}'")
    logger.info(f"Configuration: {args.genre} genre, {args.style} style, {args.chapters} chapters")

    # Display professional startup message
    print("\n" + "=" * 60)
    print(f"  中文小说生成系统 - 开始创作《{args.title}》")
    print("=" * 60)
    print(f"  类型: {args.genre}")
    print(f"  风格: {args.style}")
    print(f"  场景: {args.scene}")
    print(f"  角色: {args.characters}")
    print(f"  章节: {args.chapters} 章")
    print("=" * 60 + "\n")

    # Generate novel
    log_process_step("Generation Start", f"Starting generation for '{args.title}'")
    doc_path = generate_novel(
        title=args.title,
        genre=args.genre,
        style=args.style,
        scene=args.scene,
        characters=args.characters,
        num_chapters=args.chapters,
        output_dir=args.output_dir,
        save_intermediates=args.save_intermediates,
        batch_size=args.batch_size,
        progress_callback=print_progress,
        use_new_system=not args.use_old_system
    )

    if doc_path:
        log_process_step("Generation Complete", f"Document saved to {doc_path}")
        log_process_end("Novel System Main Function", main_start)

        # Display success message
        print("\n" + "=" * 60)
        print(f"  小说《{args.title}》生成成功！")
        print(f"  文档保存在: {doc_path}")
        print("=" * 60 + "\n")
        return 0
    else:
        log_error("Novel generation failed")
        log_process_end("Novel System Main Function", main_start)

        # Display error message
        print("\n" + "=" * 60)
        print(f"  错误: 小说《{args.title}》生成失败！")
        print("  请检查日志文件了解详细错误信息。")
        print("=" * 60 + "\n")
        return 1

if __name__ == '__main__':
    import sys
    sys.exit(main())
