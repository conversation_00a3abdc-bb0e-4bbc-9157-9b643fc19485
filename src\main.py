"""
主程序模块，包含Agent基类和智能体注册表
"""

import os
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Type, TypeVar, cast, TYPE_CHECKING
from abc import ABC, abstractmethod
from datetime import datetime

if TYPE_CHECKING:
    import requests
else:
    pass

# 定义Agent类型变量
T = TypeVar('T', bound='Agent')

class Agent(ABC):
    """
    智能体基类，所有智能体都应继承自此类
    """

    def __init__(self, name: str, model: str):
        """
        初始化智能体

        参数:
            name: 智能体名称
            model: 使用的模型名称
        """
        self.name = name
        self.model = model
        self.messages: List[Dict[str, Any]] = []
        self.id = str(uuid.uuid4())

    def call_llm(self, prompt: str) -> str:
        """
        调用大语言模型

        参数:
            prompt: 提示词

        返回:
            模型生成的内容
        """
        print(f"调用模型 {self.model} 生成内容...")

        try:
            # 导入模型配置
            from model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES

            # 获取模型配置
            models_config = AI_MODEL_CONFIG.get("models", {})
            if not isinstance(models_config, dict):
                print(f"警告: 模型配置不是字典类型，将使用模拟响应")
                return self._mock_llm_response(prompt)

            model_config = models_config.get(self.model, {})
            if not isinstance(model_config, dict) or not model_config:
                print(f"警告: 未找到模型 {self.model} 的配置，将使用模拟响应")
                return self._mock_llm_response(prompt)

            # 如果没有找到模型配置，尝试使用默认模型
            if not model_config:
                default_model = cast(str, AI_MODEL_CONFIG.get("default_model", ""))
                model_config = models_config.get(default_model, {})
                if not model_config:
                    print(f"警告: 未找到模型 {self.model} 的配置，将使用模拟响应")
                    return self._mock_llm_response(prompt)

            # 获取API密钥和基础URL
            api_key = model_config.get("api_key", "")
            base_url = model_config.get("base_url", "")
            api_type = model_config.get("api_type", "")

            if not base_url:
                print(f"警告: 模型 {self.model} 缺少基础URL，将使用模拟响应")
                return self._mock_llm_response(prompt)

            # 根据模型类型选择不同的API调用方式
            if api_type == "zhipu" or self.model == DEFAULT_MODEL_NAMES["glm"] or self.model.startswith("glm-"):
                if not api_key:
                    print(f"警告: 模型 {self.model} 缺少API密钥，将使用模拟响应")
                    return self._mock_llm_response(prompt)
                return self._call_glm_api(prompt, api_key, base_url, model_config)
            elif api_type == "siliconflow" or self.model == DEFAULT_MODEL_NAMES["siliconflow"] or self.model.startswith("internlm/"):
                if not api_key:
                    print(f"警告: 模型 {self.model} 缺少API密钥，将使用模拟响应")
                    return self._mock_llm_response(prompt)
                return self._call_siliconflow_api(prompt, api_key, base_url, model_config)
            elif api_type == "ollama" or self.model == DEFAULT_MODEL_NAMES["ollama"]:
                return self._call_ollama_api(prompt, base_url, model_config)
            elif api_type == "openrouter" or self.model == DEFAULT_MODEL_NAMES["openrouter"] or ":free" in self.model:
                if not api_key:
                    print(f"警告: 模型 {self.model} 缺少API密钥，将使用模拟响应")
                    return self._mock_llm_response(prompt)
                return self._call_openrouter_api(prompt, api_key, base_url, model_config)
            else:
                print(f"警告: 不支持的模型类型 {self.model}，将使用模拟响应")
                return self._mock_llm_response(prompt)

        except Exception as e:
            print(f"调用LLM时出错: {e}")
            return self._mock_llm_response(prompt)

    def _mock_llm_response(self, prompt: str) -> str:
        """生成模拟的LLM响应"""
        time.sleep(1)  # 模拟API调用延迟
        return f"这是由{self.model}生成的内容。基于提示词：{prompt[:50]}..."

    def _call_glm_api(self, prompt: str, api_key: str, base_url: str, model_config: Dict[str, Any]) -> str:
        """调用智谱AI的GLM模型API"""
        import requests
        import json

        # 准备请求头和请求体
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # 使用原始模型名称，不做修改
        model_name = self.model

        # 如果是glm-4，强制使用glm-4-flash
        if model_name == "glm-4":
            model_name = "glm-4-flash"
            print(f"注意: 将glm-4更改为glm-4-flash")

        data = {
            "model": model_name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": model_config.get("temperature", 0.7),
            "max_tokens": model_config.get("max_tokens", 1024)
        }

        # 发送请求
        try:
            # 确保base_url不以斜杠结尾，然后添加路径
            if base_url.endswith('/'):
                base_url = base_url[:-1]

            api_endpoint = f"{base_url}/chat/completions"
            print(f"发送请求到 {api_endpoint}")
            print(f"使用模型: {model_name}")
            # 不显示API密钥，保护隐私和安全
            print(f"API密钥: [已隐藏]")

            response = requests.post(api_endpoint, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                else:
                    print(f"API响应格式不正确: {result}")
                    return self._mock_llm_response(prompt)
            else:
                print(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return self._mock_llm_response(prompt)
        except Exception as e:
            print(f"调用GLM API时出错: {e}")
            return self._mock_llm_response(prompt)

    def _call_siliconflow_api(self, prompt: str, api_key: str, base_url: str, model_config: Dict[str, Any]) -> str:
        """调用硅基流动的API（支持DeepSeek、InternLM、Qwen等模型）"""
        import requests
        import json

        # 准备请求头和请求体
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # 限制max_tokens不超过4096
        max_tokens = min(model_config.get("max_tokens", 1024), 4096)

        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": model_config.get("temperature", 0.7),
            "max_tokens": max_tokens
        }

        # 发送请求
        try:
            # 确保base_url不以斜杠结尾，然后添加路径
            if base_url.endswith('/'):
                base_url = base_url[:-1]

            api_endpoint = f"{base_url}/chat/completions"
            print(f"发送请求到 {api_endpoint}")
            print(f"使用模型: {self.model}")
            # 不显示API密钥，保护隐私和安全
            print(f"API密钥: [已隐藏]")
            print(f"最大生成token数: {max_tokens}")

            response = requests.post(api_endpoint, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                else:
                    print(f"API响应格式不正确: {result}")
                    return self._mock_llm_response(prompt)
            else:
                print(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return self._mock_llm_response(prompt)
        except Exception as e:
            print(f"调用硅基流动API时出错: {e}")
            return self._mock_llm_response(prompt)

    def _call_ollama_api(self, prompt: str, base_url: str, model_config: Dict[str, Any]) -> str:
        """调用Ollama本地模型API"""
        import requests
        import json

        # 准备请求头和请求体
        headers = {
            "Content-Type": "application/json"
        }

        # 从模型名称中提取实际的模型名称
        model_name = self.model
        if model_name == "gemma2:2b" or model_name == "llama3:8b":
            # 这些是预设的模型名称
            pass
        elif "ollama" in model_name.lower():
            # 如果是自定义的ollama模型，使用DEFAULT_MODEL_NAMES中的值
            from model_config import DEFAULT_MODEL_NAMES
            model_name = DEFAULT_MODEL_NAMES["ollama"]

        data = {
            "model": model_name,
            "prompt": prompt,
            "stream": False,
            "temperature": model_config.get("temperature", 0.7),
            "num_predict": model_config.get("max_tokens", 2048)
        }

        # 发送请求
        try:
            # 确保base_url不以斜杠结尾，然后添加路径
            if base_url.endswith('/'):
                base_url = base_url[:-1]

            api_endpoint = f"{base_url}/generate"
            print(f"发送请求到 {api_endpoint}")
            print(f"使用模型: {model_name}")

            response = requests.post(api_endpoint, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                if "response" in result:
                    return result["response"]
                else:
                    print(f"API响应格式不正确: {result}")
                    return self._mock_llm_response(prompt)
            else:
                print(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return self._mock_llm_response(prompt)
        except Exception as e:
            print(f"调用Ollama API时出错: {e}")
            return self._mock_llm_response(prompt)

    def _call_openrouter_api(self, prompt: str, api_key: str, base_url: str, model_config: Dict[str, Any]) -> str:
        """调用OpenRouter API（支持各种免费模型）"""
        import requests
        import json

        # 准备请求头和请求体
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://storyteller.ai",  # 可选，用于OpenRouter排名
            "X-Title": "Storyteller AI"  # 可选，用于OpenRouter排名
        }

        # 如果模型配置中有额外的头部信息，添加它们
        extra_headers = model_config.get("extra_headers", {})
        if extra_headers and isinstance(extra_headers, dict):
            headers.update(extra_headers)

        # 限制max_tokens不超过4096
        max_tokens = min(model_config.get("max_tokens", 1024), 4096)

        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": model_config.get("temperature", 0.7),
            "max_tokens": max_tokens
        }

        # 发送请求
        try:
            # 确保base_url不以斜杠结尾，然后添加路径
            if base_url.endswith('/'):
                base_url = base_url[:-1]

            api_endpoint = f"{base_url}/chat/completions"
            print(f"发送请求到 {api_endpoint}")
            print(f"使用模型: {self.model}")
            # 不显示API密钥，保护隐私和安全
            print(f"API密钥: [已隐藏]")
            print(f"最大生成token数: {max_tokens}")

            response = requests.post(api_endpoint, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                else:
                    print(f"API响应格式不正确: {result}")
                    return self._mock_llm_response(prompt)
            else:
                print(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return self._mock_llm_response(prompt)
        except Exception as e:
            print(f"调用OpenRouter API时出错: {e}")
            return self._mock_llm_response(prompt)

    def send_message(self, recipient: str, message: Dict[str, Any]):
        """
        发送消息给另一个智能体

        参数:
            recipient: 接收者名称
            message: 消息内容
        """
        # 将消息添加到消息队列
        self.messages.append({
            'sender': self.name,
            'recipient': recipient,
            'content': message,
            'timestamp': time.time()
        })

        # 在实际应用中，这里应该将消息发送给接收者
        # 这里只是打印消息
        print(f"{self.name} 发送消息给 {recipient}")

        # 通知AGENT_REGISTRY处理消息
        AGENT_REGISTRY.dispatch_message(self.name, recipient, message)

    @abstractmethod
    def handle_message(self, message: Dict[str, Any]):
        """
        处理接收到的消息

        参数:
            message: 消息内容
        """
        pass


class AgentRegistry:
    """
    智能体注册表，用于管理所有智能体
    """

    def __init__(self):
        """初始化智能体注册表"""
        self.agents: Dict[str, Agent] = {}

    def register(self, agent: Agent) -> Agent:
        """
        注册智能体

        参数:
            agent: 要注册的智能体

        返回:
            注册的智能体实例
        """
        self.agents[agent.name] = agent
        print(f"智能体 {agent.name} 已注册")
        return agent

    def get_agent(self, name: str) -> Optional[Agent]:
        """
        获取指定名称的智能体

        参数:
            name: 智能体名称

        返回:
            智能体实例，如果不存在则返回None
        """
        return self.agents.get(name)

    def get_agents_by_type(self, agent_type: Type[T]) -> List[T]:
        """
        获取指定类型的所有智能体

        参数:
            agent_type: 智能体类型

        返回:
            指定类型的智能体列表
        """
        return [agent for agent in self.agents.values() if isinstance(agent, agent_type)]

    def dispatch_message(self, sender: str, recipient: str, message: Dict[str, Any]):
        """
        分发消息给指定的智能体

        参数:
            sender: 发送者名称
            recipient: 接收者名称
            message: 消息内容
        """
        recipient_agent = self.get_agent(recipient)
        if recipient_agent:
            # 添加发送者信息
            message['sender'] = sender

            # 处理消息
            recipient_agent.handle_message(message)
        else:
            print(f"错误: 找不到智能体 {recipient}")


# 创建全局智能体注册表
AGENT_REGISTRY = AgentRegistry()

# 示例：发布者智能体
class PublisherAgent(Agent):
    def __init__(self, name: str, model: str, output_dir: Optional[str] = None):
        super().__init__(name, model)
        self.output_dir = output_dir or "output"
        os.makedirs(self.output_dir, exist_ok=True)

    def handle_message(self, msg: Dict[str, Any]):
        if msg['type'] == 'publish':
            print(f"Publisher received {len(msg['chapters'])} edited chapters...")
            self._upload_to_storage(msg['chapters'])
            self._notify_user(msg['chapters'])

    def _upload_to_storage(self, chapters: List[Dict[str, Any]]):
        # Store to distributed file system
        for chapter in chapters:
            filename = f"{self.output_dir}/chapter_{chapter['number']:03d}_{chapter['title'].replace(' ', '_')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(chapter['text'])
            print(f"Saved chapter to {filename}")

    def _notify_user(self, chapters: List[Dict[str, Any]]):
        # Notify user of generated chapters
        chapter_numbers = [c['number'] for c in chapters]
        print(f"Notification: Chapters {min(chapter_numbers)} to {max(chapter_numbers)} have been published!")

# 示例：仪表盘智能体
class DashboardAgent(Agent):
    def __init__(self, name: str, model: str):
        super().__init__(name, model)
        self.progress_db: Dict[int, datetime] = {}

    def handle_message(self, msg: Dict[str, Any]):
        if msg['type'] == 'progress_update':
            self.update_progress(msg['chapter_number'])

    def update_progress(self, chapter_number: int):
        self.progress_db[chapter_number] = datetime.now()
        self._update_graph()

    def _update_graph(self):
        # Generate real-time progress chart
        total_chapters = max(self.progress_db.keys()) if self.progress_db else 0
        completed_chapters = len(self.progress_db)
        if total_chapters > 0:
            print(f"Progress: {completed_chapters}/{total_chapters} chapters completed ({completed_chapters/total_chapters*100:.1f}% complete)")
        else:
            print("No chapters completed yet")

# 主函数
def main():
    # 创建必要的目录
    os.makedirs("output", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    os.makedirs("temp", exist_ok=True)

    # 初始化智能体
    publisher = AGENT_REGISTRY.register(PublisherAgent("Publisher", "gpt-3.5-turbo"))
    dashboard = AGENT_REGISTRY.register(DashboardAgent("Dashboard", "gpt-3.5-turbo"))

    print("小说生成系统已初始化。")

if __name__ == "__main__":
    main()
