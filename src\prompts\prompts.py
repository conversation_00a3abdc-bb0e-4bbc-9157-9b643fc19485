"""
提示词管理模块
用于集中管理系统中使用的各种提示词
"""

class WritingPrompts:
    """写作相关提示词"""

    @staticmethod
    def chapter_writing_prompt(chapter_num, title, genre, style, scene, characters,
                              chapter_title, chapter_outline, previous_chapters_summary,
                              previous_chapter_content):
        """生成章节写作提示词"""
        return f"""
我需要你创作一部高质量的中文小说第{chapter_num}章的内容。这是一部专业水准的小说，需要丰富的细节、生动的描写和深刻的情感表达。

小说基本信息:
- 标题：《{title}》
- 类型：{genre}
- 风格：{style}
- 场景设定：{scene}
- 主要角色：{characters}

章节信息:
- 章节标题：{chapter_title}
- 章节大纲：{chapter_outline}

前面章节摘要：
{previous_chapters_summary}

前一章节结尾（最后1000字）：
{previous_chapter_content[-1000:] if previous_chapter_content else ""}

写作要求（必须严格遵循）：
1. 必须严格按照大纲创作，不得有任何偏离或遗漏大纲中的关键情节点
2. 必须使用大纲中指定的角色名称，不得更改或添加不在大纲中的主要角色
3. 必须与前一章节的结尾实现真正的无缝衔接，确保情节和对话的自然过渡
4. 必须保持角色性格、关系和背景设定的一致性，不得与前面章节冲突
5. 必须将大纲中的所有关键情节点都包含在章节内容中，不得遗漏

无缝衔接要求（非常重要）：
1. 本章开头必须直接从前一章结尾的场景、对话或情节继续，不得出现时间或场景跳跃
2. 如果前一章结尾是对话，本章开头应继续该对话或对该对话的直接反应
3. 如果前一章结尾是情节描述，本章开头应继续该情节或其直接后果
4. 不得重复前一章结尾的内容，而是要自然地继续发展

写作技巧要求：
1. 字数要求：本章至少5000字，确保内容丰富充实且完整
2. 章节结构：开头必须与前一章结尾无缝衔接；中间部分展开本章核心事件；结尾为下一章埋下伏笔
3. 场景描写：使用丰富的感官描写（视觉、听觉、嗅觉、触觉、味觉）创造沉浸式体验
4. 人物塑造：通过对话、行动和内心独白展现人物性格和情感变化
5. 文学性：使用恰当的修辞手法和多样化的句式，避免重复和平淡的表达
6. 中国元素：适当融入中国传统文化元素，增强文化底蕴
7. 情感深度：确保情节中包含情感起伏，引发读者共鸣
8. 细节丰富：添加小细节使故事更加真实可信
9. 对话生动：创造自然、有个性的对话，展现人物关系和冲突
10. 节奏控制：根据情节需要调整叙事节奏，关键场景放慢，过渡场景加快
11. 悬念设置：在章节结尾设置悬念，吸引读者继续阅读

请直接给出完整的章节内容，不需要任何解释。确保内容符合专业小说的质量标准，能够吸引读者持续阅读。
"""

    @staticmethod
    def chapter_validation_prompt(current_chapter_outline, previous_chapter_content, content):
        """生成章节验证提示词"""
        return f"""
请作为严格的小说质量检查员，评估以下小说章节内容。你的任务是确保章节内容严格遵循大纲，并与前一章实现无缝衔接。

章节大纲：
{current_chapter_outline}

前一章结尾（最后500字）：
{previous_chapter_content[-500:] if previous_chapter_content else ""}

生成的章节内容（前1000字）：
{content[:1000]}...

请严格评估以下方面：
1. 大纲遵循度：内容是否严格遵循大纲中的情节设定和要求？是否有遗漏大纲中的关键情节点？
2. 角色一致性：是否使用了大纲中指定的角色，且角色设定与前文一致？
3. 无缝衔接：本章开头是否与前一章结尾实现了真正的无缝衔接？是否有时间或场景跳跃？
4. 内容质量：是否有内容重复、短语重复或与大纲无关的内容？

请给出详细的评估结果，并指出是否需要重新生成。如果需要重新生成，请给出具体原因和改进建议。

最后，请以下列格式给出结论：
结论: [需要重新生成] 或 [不需要重新生成]
理由: [简要理由]
"""

    @staticmethod
    def chapter_regeneration_prompt(validation_result):
        """生成章节重新生成提示词"""
        return f"""
请注意，之前的生成内容存在以下问题：
{validation_result}

请重新生成内容，并特别注意：
1. 必须严格按照大纲创作，不得有任何偏离或遗漏大纲中的关键情节点
2. 必须使用大纲中指定的角色名称，不得更改或添加不在大纲中的主要角色
3. 必须与前一章的结尾实现真正的无缝衔接，确保情节和对话的自然过渡
4. 本章开头必须直接从前一章结尾的场景、对话或情节继续，不得出现时间或场景跳跃
5. 不得重复前一章结尾的内容，而是要自然地继续发展
请直接给出章节内容，不需要额外的解释。
"""

    @staticmethod
    def chapter_summary_prompt(content):
        """生成章节摘要提示词"""
        return f"""
请为以下小说章节提供一个简短的摘要（不超过150字）:

{content[:1000]}...

请只提供摘要，不要有任何其他文字。
"""

    @staticmethod
    def transition_generation_prompt(previous_chapter_ending, next_chapter_outline, chapter_num, chapter_title):
        """生成章节过渡段提示词"""
        return f"""
请为小说创建一个无缝衔接的过渡段，确保前一章结尾与下一章开头之间的自然过渡。

前一章结尾内容（最后一段）：
{previous_chapter_ending}

下一章信息：
- 章节号: 第{chapter_num}章
- 章节标题: {chapter_title}
- 章节大纲: {next_chapter_outline}

请生成一个过渡段，包含两部分：
1. 前一章结尾的最后几句话（可以适当修改以便更好地衔接）
2. 下一章开头的前几句话（必须与下一章大纲内容一致）

要求：
1. 两部分之间必须实现真正的无缝衔接，读者不应感觉到章节切换
2. 不要使用"第X章"等章节标记，只需提供内容
3. 过渡必须自然流畅，保持情节、场景和对话的连续性
4. 如果前一章结尾是对话，下一章开头应继续该对话或对该对话的直接反应
5. 如果前一章结尾是情节描述，下一章开头应继续该情节或其直接后果
6. 总长度控制在300-500字之间

请直接给出过渡段内容，不需要任何解释。
"""


class OutlinePrompts:
    """大纲相关提示词"""

    @staticmethod
    def outline_generation_prompt(title, genre, style, scene, characters, num_chapters):
        """生成小说大纲提示词"""
        return f"""
请为一部小说创建详细的章节大纲。

小说基本信息:
- 标题：{title}
- 类型：{genre}
- 风格：{style}
- 场景设定：{scene}
- 主要角色：{characters}
- 计划章节数：{num_chapters}章

请在大纲开头提供以下内容：
1. 故事基本情节：简要描述整个故事的主要情节发展
2. 主要人物关系：详细说明主要角色之间的关系
3. 核心事件设定：列出故事中的关键事件和转折点

然后，请为每一章提供详细的大纲，包括：
1. 章节标题：简洁而有吸引力
2. 核心事件：本章发生的主要事件
3. 出场角色：本章中出现的角色
4. 与整体故事的关系：本章如何推动整体故事发展
5. 情感基调：本章的主要情感色彩

请确保章节之间有合理的连贯性和递进关系，整体故事有明确的起承转合。
每章大纲应包含足够的细节，以便能够据此写出完整的章节内容。

请使用以下格式：

# 故事基本情节
[详细描述]

# 主要人物关系
[详细描述]

# 核心事件设定
[详细描述]

# 第1章：[章节标题]
[详细大纲]

# 第2章：[章节标题]
[详细大纲]

...以此类推
"""

    @staticmethod
    def outline_continuation_prompt(existing_outline, current_chapters, additional_chapters):
        """生成大纲续写提示词"""
        return f"""
请基于现有的小说大纲，为后续章节创建详细的大纲。

现有大纲：
{existing_outline}

当前已完成的章节数：{current_chapters}章
需要新增的章节数：{additional_chapters}章

请为第{current_chapters+1}章到第{current_chapters+additional_chapters}章创建详细的大纲，确保与现有大纲的风格和内容保持一致，并自然地延续故事情节。

每一章的大纲应包括：
1. 章节标题：简洁而有吸引力
2. 核心事件：本章发生的主要事件
3. 出场角色：本章中出现的角色
4. 与整体故事的关系：本章如何推动整体故事发展
5. 情感基调：本章的主要情感色彩

请确保新增章节与已有章节之间有合理的连贯性和递进关系，并且能够自然地推动故事向结局发展。

请使用与现有大纲相同的格式：

# 第{current_chapters+1}章：[章节标题]
[详细大纲]

# 第{current_chapters+2}章：[章节标题]
[详细大纲]

...以此类推
"""


class PolishingPrompts:
    """润色相关提示词"""

    @staticmethod
    def polish_content_prompt(content):
        """生成内容润色提示词"""
        return f"""
请对以下小说章节内容进行润色，提升文学性和可读性，但不要改变原有的情节和人物设定。

原始内容：
{content}

润色要求：
1. 改进语言表达，使其更加生动、优美
2. 增强描写的细节和感染力
3. 优化对话，使其更加自然、生动
4. 调整段落结构，提高阅读流畅度
5. 修正可能存在的语法错误和表达不当
6. 保持原有的情节、人物和核心内容不变

请直接给出润色后的完整内容，不需要解释或标注修改之处。
"""

    @staticmethod
    def expand_content_prompt(content):
        """生成内容扩写提示词"""
        return f"""
请对以下小说章节内容进行扩写，使其更加丰富、详实，但不要改变原有的情节和人物设定。

原始内容：
{content}

扩写要求：
1. 增加场景描写的细节，使读者能够更好地想象场景
2. 丰富人物的心理活动和情感变化
3. 适当增加对话内容，展现人物性格和关系
4. 在关键情节处增加更多细节描写
5. 保持原有的情节发展和人物设定不变
6. 扩写后的内容应比原内容增加约30%的篇幅

请直接给出扩写后的完整内容，不需要解释或标注修改之处。
"""


class ReviewPrompts:
    """审核相关提示词"""

    @staticmethod
    def review_content_prompt(content):
        """生成内容审核提示词"""
        return f"""
请对以下小说章节内容进行全面审核，检查是否存在问题，并给出改进建议。

内容：
{content[:2000]}...（内容较长，仅展示部分）

审核要求：
1. 检查情节是否连贯、合理
2. 检查人物形象是否一致、立体
3. 检查语言表达是否生动、准确
4. 检查是否存在逻辑矛盾或情节漏洞
5. 检查是否有不必要的重复或冗余内容
6. 检查是否有不恰当的内容或表达

请给出详细的审核意见，包括发现的问题和具体的改进建议。
"""

    @staticmethod
    def optimize_content_prompt(content):
        """生成内容优化提示词"""
        return f"""
请对以下小说章节内容进行优化，提升整体质量，但不要改变原有的核心情节。

内容：
{content[:2000]}...（内容较长，仅展示部分）

优化要求：
1. 提升情节的紧凑性和吸引力
2. 增强人物形象的鲜明度和立体感
3. 优化语言表达，使其更加生动、优美
4. 修正可能存在的逻辑矛盾或情节漏洞
5. 删减不必要的重复或冗余内容
6. 调整段落结构，提高阅读流畅度
7. 保持原有的核心情节和人物设定不变

请直接给出优化后的完整内容，不需要解释或标注修改之处。
"""


class IllustrationPrompts:
    """插图相关提示词"""

    @staticmethod
    def illustration_generation_prompt(scene_description, style):
        """生成插图生成提示词"""
        return f"""
请根据以下场景描述生成一幅高质量的小说插图。

场景描述：
{scene_description}

风格要求：
{style}

插图要求：
1. 必须是纯风景插图，不得包含任何人物、人脸、人形或人类身体部位
2. 必须是高质量、高清晰度的插图，具有丰富的细节和气氛
3. 必须准确表现场景描述中的关键元素和环境
4. 必须符合指定的风格要求，体现小说的整体氛围
5. 如果是中国背景的小说，应当融入中国传统元素和美学

请生成一幅高质量的风景插图，能够准确表现场景内容和氛围，符合指定的风格要求，但不包含任何人物。
"""
