"""
Illustration Generator Module - Handles the generation of novel illustrations
"""

import os
import requests
from typing import Dict, Any, Optional
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
from io import BytesIO

class IllustrationGenerator:
    """
    Handles the generation of illustrations for novel chapters
    """

    def __init__(self, llm_caller, api_caller, output_dir: str):
        """
        Initialize the illustration generator

        Args:
            llm_caller: Function to call the language model
            api_caller: Function to call the image generation API
            output_dir: Directory to save output files
        """
        self.llm_caller = llm_caller
        self.api_caller = api_caller
        self.output_dir = output_dir
        self.illustrations_dir = os.path.join(output_dir, "illustrations")
        os.makedirs(self.illustrations_dir, exist_ok=True)

    def extract_scene_description(self, chapter_content: str, max_length: int = 500) -> str:
        """
        Extract a scene description from chapter content for illustration

        Args:
            chapter_content: The chapter content
            max_length: Maximum length of the scene description

        Returns:
            Scene description suitable for illustration
        """
        if not chapter_content:
            return "A beautiful landscape scene from a novel"

        # Try to extract a meaningful scene using LLM, focusing on landscapes only
        prompt = f"""
Extract a vivid, visual LANDSCAPE or SCENERY description from this novel chapter that would make a good illustration.
Focus ONLY on natural scenery, landscapes, buildings, or environments - NO CHARACTERS OR PEOPLE.
Describe mountains, rivers, cities, buildings, forests, weather, time of day, etc.
Keep the description under 300 words and make it suitable for landscape image generation:

{chapter_content[:2000]}...

Return only the landscape/scenery description without any explanations or characters.
DO NOT include any people, characters, or human figures in your description.
"""
        try:
            scene_description = self.llm_caller(prompt)
            if scene_description and len(scene_description) > 50:
                # Truncate if too long
                if len(scene_description) > max_length:
                    scene_description = scene_description[:max_length]
                return scene_description
        except Exception as e:
            print(f"Error extracting scene description: {e}")

        # Fallback: use the beginning of the chapter
        return chapter_content[:max_length] if len(chapter_content) > max_length else chapter_content

    def generate_illustration(self,
                             chapter_num: int,
                             chapter_content: str,
                             style: str = "写实风格") -> str:
        """
        Generate an illustration for a chapter

        Args:
            chapter_num: Chapter number
            chapter_content: Content of the chapter
            style: Illustration style

        Returns:
            Path to the generated illustration
        """
        print(f"\n--- Generating illustration for Chapter {chapter_num} ---")

        # Extract scene description
        scene_description = self.extract_scene_description(chapter_content)
        print(f"Using scene description ({len(scene_description)} characters)")

        # Create output path
        output_path = os.path.join(self.illustrations_dir, f"chapter_{chapter_num}.png")

        # Enhance the prompt for better image generation, ensuring landscape only
        enhanced_prompt = f"""
{scene_description}

Style: {style}
High quality, detailed landscape illustration for a Chinese novel.
Natural scenery, no people, no characters, no human figures.
Beautiful landscape photography style, cinematic lighting, panoramic view.
Mountains, rivers, forests, buildings, cities, nature, weather effects.
"""

        try:
            # Call the image generation API
            print("Calling image generation API...")
            image_url = self.api_caller(enhanced_prompt)

            if image_url:
                # Download the image
                response = requests.get(image_url)
                if response.status_code == 200:
                    with open(output_path, "wb") as f:
                        f.write(response.content)
                    print(f"Illustration saved to: {output_path}")
                    return output_path
                else:
                    print(f"Failed to download image, status code: {response.status_code}")
            else:
                print("No image URL returned from API")

        except Exception as e:
            print(f"Error generating illustration: {e}")

        # Create a fallback image if API call fails
        return self._create_fallback_image(scene_description, style, output_path)

    def _create_fallback_image(self, scene_description: str, style: str, output_path: str) -> str:
        """
        Create a fallback image when API generation fails

        Args:
            scene_description: Scene description
            style: Illustration style
            output_path: Output path for the image

        Returns:
            Path to the created image
        """
        try:
            # Create a blank image
            img = Image.new('RGB', (1024, 1024), color='white')
            draw = ImageDraw.Draw(img)

            # Try to load a font, use default if not available
            try:
                font = ImageFont.truetype("simhei.ttf", 20)
            except IOError:
                font = ImageFont.load_default()

            # Add text to the image
            draw.text((20, 20), f"场景: {scene_description[:100]}...", fill="black", font=font)
            draw.text((20, 50), f"风格: {style}", fill="black", font=font)
            draw.text((20, 80), "自动生成的插图（API调用失败）", fill="black", font=font)

            # Save the image
            img.save(output_path)
            print(f"Fallback illustration saved to: {output_path}")
            return output_path

        except Exception as e:
            print(f"Error creating fallback image: {e}")
            return ""
