"""
写作流程模块
负责创建和运行写作流程
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union, Tuple, Callable, cast
import traceback
from datetime import datetime

# 初始化日志系统
def setup_logging():
    """设置日志系统"""
    # 设置基本日志格式
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 创建写作流程日志器
    logger = logging.getLogger("WritingPipeline")

    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)

    # 检查是否已经添加了处理器
    handlers_exist = False
    for handler in logger.handlers:
        if isinstance(handler, logging.StreamHandler):
            handlers_exist = True
            break

    if not handlers_exist:
        logger.addHandler(console_handler)
        logger.info("写作流程日志系统初始化完成")

    return logger

# 初始化日志
logger = setup_logging()

# 导入记忆管理、一致性检查和叙事控制模块
try:
    from src.memory.memory_integration import (
        get_memory_manager, enhance_prompt_with_memory,
        update_memory_from_chapter, add_segment_to_memory,
        clear_short_term_memory, check_consistency as memory_check_consistency
    )
    from src.consistency.consistency_checker import (
        get_consistency_checker, check_consistency as consistency_check_consistency,
        save_consistency_log
    )
    from src.narrative.narrative_controller import (
        get_narrative_controller, generate_branch_options,
        select_optimal_path, generate_next_chapter_outline
    )
    ADVANCED_FEATURES_ENABLED = True
    print("高级功能已启用: 三层记忆管理、一致性检查和叙事控制")
except ImportError as e:
    print(f"警告: 无法导入高级功能模块: {e}")
    ADVANCED_FEATURES_ENABLED = False

try:
    # 导入智能体类
    from src.agents.writing_agent import WritingAgent
    from src.agents.polishing_agent import PolishingAgent
    from src.agents.expansion_agent import ExpansionAgent
    from src.agents.review_agent import ReviewAgent
    from src.agents.optimization_agent import OptimizationAgent
    from src.agents.illustration_agent import IllustrationAgent
except ImportError:
    # 如果导入失败，尝试从writing_agents.py导入
    try:
        from src.agents.writing_agents import (
            WritingAgent, PolishingAgent, ExpansionAgent,
            ReviewAgent, OptimizationAgent, IllustrationAgent
        )
    except ImportError:
        print("警告: 无法导入智能体类，写作流程将无法正常工作")

def create_writing_pipeline() -> List[Dict[str, Any]]:
    """
    创建写作流程

    参数:
        illustration_style: 插图风格

    返回:
        写作流程配置
    """
    # 创建写作流程
    pipeline = [
        {
            "name": "writing",
            "agent_class": WritingAgent,
            "agent_name": "WritingAgent",
            "output_suffix": "1_initial",
            "description": "初始内容生成"
        },
        {
            "name": "polishing",
            "agent_class": PolishingAgent,
            "agent_name": "PolishingAgent",
            "output_suffix": "2_polished",
            "description": "内容润色"
        },
        {
            "name": "expansion",
            "agent_class": ExpansionAgent,
            "agent_name": "ExpansionAgent",
            "output_suffix": "3_expanded",
            "description": "内容扩展"
        },
        {
            "name": "review",
            "agent_class": ReviewAgent,
            "agent_name": "ReviewAgent",
            "output_suffix": "4_reviewed",
            "description": "内容审核"
        },
        {
            "name": "optimization",
            "agent_class": OptimizationAgent,
            "agent_name": "OptimizationAgent",
            "output_suffix": "5_optimized",
            "description": "内容优化"
        },
        {
            "name": "illustration",
            "agent_class": IllustrationAgent,
            "agent_name": "IllustrationAgent",
            "output_suffix": "illustration",
            "description": "插图生成",
            "params": {
                "style": "写实风格"  # 默认插图风格，将根据小说风格自动调整
            }
        }
    ]

    return pipeline

def run_pipeline(pipeline: List[Dict[str, Any]], chapter_context: Dict[str, Any]) -> Tuple[bool, str]:
    """
    运行写作流程

    参数:
        pipeline: 写作流程配置
        chapter_context: 章节上下文

    返回:
        (成功标志, 结果内容或错误信息)
    """
    # 获取全局日志器
    global logger

    # 记录写作流程启动信息
    logger.info(f"[写作流程] 开始运行写作流程")

    try:
        # 获取章节信息
        chapter_number = chapter_context.get("chapter_number", 1)
        output_dir = chapter_context.get("output_dir", "output")
        novel_title = chapter_context.get("title", "")
        genre = chapter_context.get("genre", "")
        style = chapter_context.get("style", "")
        scene = chapter_context.get("scene", "")
        characters = chapter_context.get("characters", "")
        total_chapters = chapter_context.get("total_chapters", 1)

        # 记录章节信息
        logger.info(f"[写作流程] 章节信息: 小说标题={novel_title}, 章节编号={chapter_number}, 总章节数={total_chapters}")
        logger.info(f"[写作流程] 小说属性: 类型={genre}, 风格={style}, 场景={scene}, 角色={characters}")

        # 使用传入的output_dir作为小说目录，不创建嵌套目录
        novel_dir = output_dir

        # 检查目录是否存在
        if not os.path.exists(novel_dir):
            os.makedirs(novel_dir, exist_ok=True)
            print(f"创建小说目录: {novel_dir}")
        else:
            print(f"使用现有小说目录: {novel_dir}")

        # 如果是继续写作模式，检查novel_info.json文件以确定当前章节
        novel_info_file = os.path.join(novel_dir, "novel_info.json")
        if os.path.exists(novel_info_file):
            try:
                with open(novel_info_file, "r", encoding="utf-8") as f:
                    novel_info = json.load(f)

                # 检查已完成的章节
                completed_chapters = []
                for ch in novel_info.get("chapters", []):
                    if ch.get("status") == "completed":
                        completed_chapters.append(ch.get("number"))

                if completed_chapters:
                    # 找到最大的已完成章节
                    last_completed = max(completed_chapters)
                    # 如果当前章节小于等于最后完成章节，则设置为最后完成章节的下一章
                    if chapter_number <= last_completed:
                        chapter_number = last_completed + 1
                        logger.info(f"[写作流程] 检测到已完成章节: {completed_chapters}")
                        logger.info(f"[写作流程] 继续写作从第{chapter_number}章开始")
                        print(f"检测到已完成章节: {completed_chapters}")
                        print(f"继续写作从第{chapter_number}章开始")

                        # 更新章节上下文
                        chapter_context["chapter_number"] = chapter_number
            except Exception as e:
                logger.error(f"[写作流程] 读取novel_info.json时出错: {e}")
                print(f"读取小说信息文件时出错: {e}")

        # 生成并保存小说大纲（如果是第一章且大纲不存在）
        outline_file = os.path.join(novel_dir, "novel_outline.txt")
        chapter_outline_file = os.path.join(novel_dir, "chapter_outline.txt")
        novel_info_file = os.path.join(novel_dir, "novel_info.json")

        # 创建小说信息文件（如果不存在）
        if chapter_number == 1 and not os.path.exists(novel_info_file):
            from src.agents.novel_manager import Novel
            novel = Novel(title=novel_title, genre=genre, style=style)
            novel.scene = scene
            novel.characters = characters
            novel.total_chapters = total_chapters

            # 保存小说信息
            with open(novel_info_file, "w", encoding="utf-8") as f:
                json.dump(novel.to_dict(), f, ensure_ascii=False, indent=2)
            print(f"已创建小说信息文件: {novel_info_file}")

        # 初始化记忆管理、一致性检查和叙事控制
        if ADVANCED_FEATURES_ENABLED:
            # 初始化记忆管理
            memory_manager = get_memory_manager(novel_dir)
            # 初始化一致性检查
            consistency_checker = get_consistency_checker(novel_dir)
            # 初始化叙事控制
            narrative_controller = get_narrative_controller(novel_dir)
            print("高级功能已初始化")

            # 清空短期记忆，准备新章节
            clear_short_term_memory(novel_dir)

        # 检查大纲状态
        logger.info(f"[写作流程] 开始运行大纲状态检查: 小说目录={novel_dir}, 章节编号={chapter_number}")

        outline_status = check_outline_status(novel_dir, chapter_number)
        logger.info(f"[写作流程] 大纲状态检查结果: {outline_status}")

        if outline_status.get("need_new_outline", False):
            logger.info(f"[写作流程] 需要生成新的大纲: {outline_status.get('message', '')}")
            print(f"大纲状态检查: {outline_status.get('message', '')}")
            print("需要生成新的大纲")
        else:
            logger.info(f"[写作流程] 大纲状态正常: {outline_status.get('message', '')}")
            logger.info(f"[写作流程] 当前阶段: {outline_status.get('current_phase', '')}, 剩余章节: {outline_status.get('remaining_chapters', 0)}")
            print(f"大纲状态检查: {outline_status.get('message', '')}")
            print(f"当前阶段: {outline_status.get('current_phase', '')}, 剩余章节: {outline_status.get('remaining_chapters', 0)}")

        # 生成大纲（如果是第一章且大纲不存在，或者需要新的大纲）
        if (chapter_number == 1 and not (os.path.exists(outline_file) or os.path.exists(chapter_outline_file))) or outline_status.get("need_new_outline", False):
            print("正在生成小说大纲...")
            # 获取WritingAgent实例
            writing_agent = None
            for step in pipeline:
                if step.get("name") == "writing":
                    agent_class = step.get("agent_class")
                    agent_name = step.get("agent_name", "")
                    writing_agent = agent_class(agent_name)
                    break

            if writing_agent:
                # 使用大纲生成器生成小说大纲
                try:
                    # 尝试使用大纲生成器
                    print(f"尝试使用大纲生成器生成《{novel_title}》的大纲")
                    outline_file = writing_agent.generate_outline(
                        novel_title=novel_title,
                        novel_genre=genre,
                        novel_style=style,
                        novel_dir=novel_dir
                    )

                    # 读取生成的大纲
                    with open(outline_file, "r", encoding="utf-8") as f:
                        outline = f.read()
                    print(f"已使用大纲生成器生成大纲，长度: {len(outline)} 字符")

                except Exception as e:
                    print(f"使用大纲生成器时出错: {e}")
                    print("回退到使用原始方法生成大纲")

                    # 生成小说大纲
                    outline_prompt = f"""
你是一位专业的小说大纲规划师，请为以下小说创作一个详细的大纲：

标题：{novel_title}
类型：{genre}
风格：{style}
场景：{scene}
角色：{characters}
章节数：{total_chapters}

请提供以下内容：

# 小说整体大纲

## 核心主题
[请描述小说的核心主题和中心思想]

## 主要角色
[请描述小说中的主要角色，包括他们的性格、背景和目标]

## 故事背景
[请描述小说的背景设定，包括时代、地点和重要的背景信息]

## 故事结构
[请描述小说的整体结构，包括开端、发展、高潮和结局]

# 各章节大纲

"""

                    # 生成各章节大纲
                    for i in range(1, total_chapters + 1):
                        outline_prompt += f"""
## 第{i}章

### 章节标题
[请提供第{i}章的标题]

### 章节概要
[请描述第{i}章的主要情节和事件]

### 关键场景
[请描述第{i}章中的关键场景]

### 角色发展
[请描述第{i}章中角色的变化和发展]

"""

                    # 生成大纲
                    outline = writing_agent.call_llm(outline_prompt)

                # 保存大纲
                with open(outline_file, "w", encoding="utf-8") as f:
                    f.write(outline)
                print(f"已生成并保存小说大纲: {outline_file}")

                # 同时保存一份到chapter_outline.txt（兼容旧版本）
                with open(chapter_outline_file, "w", encoding="utf-8") as f:
                    f.write(outline)

                # 将大纲添加到章节上下文
                chapter_context["novel_outline"] = outline

                # 提取当前章节的大纲
                import re
                chapter_pattern = rf"## 第{chapter_number}章[\s\S]*?(?=## 第{chapter_number+1}章|$)"
                chapter_match = re.search(chapter_pattern, outline)
                if chapter_match:
                    chapter_outline = chapter_match.group(0).strip()
                    chapter_context["chapter_outline"] = chapter_outline
                    print(f"已提取第{chapter_number}章大纲")

        # 如果大纲已存在，读取大纲
        elif os.path.exists(outline_file) or os.path.exists(chapter_outline_file):
            # 优先读取novel_outline.txt
            if os.path.exists(outline_file):
                with open(outline_file, "r", encoding="utf-8") as f:
                    outline = f.read()
            # 如果不存在，读取chapter_outline.txt
            elif os.path.exists(chapter_outline_file):
                with open(chapter_outline_file, "r", encoding="utf-8") as f:
                    outline = f.read()

            # 将大纲添加到章节上下文
            chapter_context["novel_outline"] = outline

            # 提取当前章节的大纲
            import re
            chapter_pattern = rf"## 第{chapter_number}章[\s\S]*?(?=## 第{chapter_number+1}章|$)"
            chapter_match = re.search(chapter_pattern, outline)
            if chapter_match:
                chapter_outline = chapter_match.group(0).strip()
                chapter_context["chapter_outline"] = chapter_outline
                print(f"已提取第{chapter_number}章大纲")

        # 初始化内容
        content = ""

        # 运行写作流程
        for step in pipeline:
            step_name = step.get("name", "")
            agent_class = step.get("agent_class")
            agent_name = step.get("agent_name", "")
            output_suffix = step.get("output_suffix", "")
            description = step.get("description", "")
            params = step.get("params", {})

            print(f"\n=== 步骤: {description} ===")

            # 创建智能体
            agent = agent_class(agent_name)

            # 根据步骤类型执行不同的操作
            if step_name == "writing":
                # 如果启用了高级功能，使用叙事控制和记忆增强
                if ADVANCED_FEATURES_ENABLED and chapter_number > 1:
                    try:
                        # 获取前一章内容
                        prev_chapter_file = os.path.join(novel_dir, f"chapter_{chapter_number-1}_final.txt")
                        if os.path.exists(prev_chapter_file):
                            with open(prev_chapter_file, "r", encoding="utf-8") as f:
                                prev_chapter_content = f.read()

                            # 生成分支选项
                            print("正在生成叙事分支选项...")
                            branches = generate_branch_options(
                                chapter_number-1,
                                prev_chapter_content,
                                novel_dir,
                                agent.call_llm
                            )

                            if branches:
                                print(f"生成了 {len(branches)} 个叙事分支选项")

                                # 选择最佳路径
                                print("正在选择最佳叙事路径...")
                                selected_branch = select_optimal_path(
                                    chapter_number-1,
                                    chapter_context.get("novel_outline", ""),
                                    novel_dir,
                                    agent.call_llm
                                )

                                if selected_branch:
                                    print(f"选择了叙事分支: {selected_branch.get('title', '')}")

                                    # 生成章节大纲
                                    chapter_outline = generate_next_chapter_outline(
                                        chapter_number-1,
                                        chapter_context.get("novel_outline", ""),
                                        novel_dir,
                                        agent.call_llm
                                    )

                                    # 更新章节上下文
                                    chapter_context["chapter_outline"] = chapter_outline
                                    print("使用叙事控制生成的章节大纲")
                    except Exception as e:
                        print(f"使用叙事控制时出错: {e}")
                        traceback.print_exc()

                # 使用记忆增强提示词
                if ADVANCED_FEATURES_ENABLED:
                    try:
                        # 获取原始提示词
                        original_prompt = chapter_context.get("prompt", "")
                        if not original_prompt:
                            # 如果没有提示词，创建一个基本的提示词
                            original_prompt = f"请根据大纲写作第{chapter_number}章的内容。"

                        # 增强提示词
                        enhanced_prompt = enhance_prompt_with_memory(
                            original_prompt,
                            novel_dir,
                            agent.call_llm
                        )

                        # 更新章节上下文
                        chapter_context["prompt"] = enhanced_prompt
                        print("使用记忆增强的提示词")
                    except Exception as e:
                        print(f"增强提示词时出错: {e}")
                        traceback.print_exc()

                # 写作步骤
                # 处理语言和故事要求
                # 首先检查是否指定了语言
                language = chapter_context.get("language", "中文")
                print(f"将使用 {language} 进行写作")

                # 初始化提示词，如果还没有
                if "prompt" not in chapter_context or not chapter_context["prompt"]:
                    chapter_context["prompt"] = f"请根据大纲写作第{chapter_number}章的内容。"

                # 添加语言要求
                chapter_context["prompt"] += f"\n\n请使用 {language} 写作整个内容，确保所有文本都是 {language}。"

                # 如果有用户提供的故事要求，将其添加到提示词中
                if "story_requirements" in chapter_context and chapter_context["story_requirements"]:
                    story_req = chapter_context["story_requirements"]
                    print(f"将用户提供的故事要求添加到写作提示中: {story_req}")
                    chapter_context["prompt"] += f"\n\n特别要求: {story_req}"

                content = agent.write_chapter(chapter_context)

                # 检查是否有特殊消息
                if content and content.startswith("PREVIOUS_CHAPTER_MISSING:"):
                    # 提取缺失的章节编号
                    missing_chapter = content.split(":")[1]
                    return False, f"需要先创建第 {missing_chapter} 章"

                # 如果启用了高级功能，将内容添加到短期记忆
                if ADVANCED_FEATURES_ENABLED and content:
                    try:
                        # 将内容分段添加到短期记忆
                        paragraphs = content.split('\n\n')
                        for i, para in enumerate(paragraphs):
                            if para.strip() and len(para) > 100:  # 只添加非空且足够长的段落
                                add_segment_to_memory(para, novel_dir)
                                if i % 5 == 0:  # 每5个段落打印一次日志
                                    print(f"已添加 {i+1}/{len(paragraphs)} 个段落到短期记忆")
                    except Exception as e:
                        print(f"添加内容到短期记忆时出错: {e}")
                        traceback.print_exc()

            elif step_name == "polishing":
                # 润色步骤
                content = agent.polish_content(content)

                # 润色后检测并删除重复内容
                content = remove_duplicate_paragraphs(content)

            elif step_name == "expansion":
                # 扩展步骤
                # 如果内容已经超过3000字，直接跳过扩写步骤
                if len(content) >= 3000:
                    print(f"跳过扩展步骤（内容已超过3000字，当前{len(content)}字）")
                else:
                    content = agent.expand_content(content)
                    # 扩展后检测并删除重复内容
                    content = remove_duplicate_paragraphs(content)
                    print("扩展后再次检测并删除重复内容")

            elif step_name == "review":
                # 审核步骤
                content = agent.review_content(content)

                # 审核后再次检测并删除重复内容
                content = remove_duplicate_paragraphs(content)
                print("审核后再次检测并删除重复内容")

            elif step_name == "optimization":
                # 优化步骤
                content = agent.optimize_content(content)

                # 优化后再次检测并删除重复内容
                content = remove_duplicate_paragraphs(content)
                print("优化后再次检测并删除重复内容")

            elif step_name == "illustration":
                # 插图步骤
                # 创建插图目录
                illustrations_dir = os.path.join(novel_dir, "illustrations")
                os.makedirs(illustrations_dir, exist_ok=True)

                try:
                    # 生成插图
                    print("正在生成章节插图...")

                    # 使用LLM提取风景描述，而不是直接使用内容
                    # 这样可以避免生成人物图像
                    landscape_prompt = f"""
从以下小说章节中提取最生动、最具视觉冲击力的风景或场景描述。
只关注自然风景、建筑、环境等元素，完全不要包含任何人物或角色描述。
提取的内容应该适合生成一幅风景插图。

小说内容:
{content[:2000]}...

请只返回纯粹的风景/场景描述，不要包含任何解释、人物描述或额外内容。
描述应该详细、生动，突出视觉元素如颜色、光线、形状等。
"""

                    # 使用写作智能体生成风景描述
                    from src.agents.writing_agents import WritingAgent
                    writing_agent = WritingAgent("WritingAgent")
                    landscape_description = writing_agent.call_llm(landscape_prompt)

                    print(f"提取的风景描述 ({len(landscape_description)} 字符): {landscape_description[:100]}...")

                    # 根据小说风格自动确定插图风格
                    novel_style = chapter_context.get("style", "")
                    illustration_style = determine_illustration_style(novel_style)
                    print(f"根据小说风格 '{novel_style}' 自动确定插图风格: {illustration_style}")

                    # 生成插图
                    illustration_path = agent.generate_illustration(
                        scene_description=landscape_description,  # 使用提取的风景描述
                        style=illustration_style,
                        output_path=os.path.join(illustrations_dir, f"chapter_{chapter_number}.png")
                    )

                    # 更新章节上下文
                    if "illustrations" not in chapter_context:
                        chapter_context["illustrations"] = []

                    if illustration_path:
                        chapter_context["illustrations"].append(illustration_path)
                        print(f"插图生成成功: {illustration_path}")
                    else:
                        print("插图生成失败，使用后备图像")

                except Exception as e:
                    print(f"生成插图时出错: {e}")
                    print("继续写作流程，跳过插图生成")

            # 保存步骤结果
            if content and step_name != "illustration":
                # 检查内容是否是API错误消息
                error_indicators = [
                    "API请求失败",
                    "调用API超时",
                    "调用API出错",
                    "生成内容失败",
                    "Read timed out",
                    "HTTPSConnectionPool",
                    "Connection refused",
                    "API响应中没有生成内容"
                ]

                # 检查内容是否包含错误指示符
                is_error_message = any(indicator in content for indicator in error_indicators)

                # 检查内容是否过短（可能是错误消息）
                is_too_short = len(content.strip()) < 200

                if not is_error_message and not is_too_short:
                    # 创建chapters目录（如果不存在）
                    chapters_dir = os.path.join(novel_dir, "chapters")
                    os.makedirs(chapters_dir, exist_ok=True)

                    # 保存到chapters目录
                    output_path = os.path.join(chapters_dir, f"chapter_{chapter_number}_{output_suffix}.txt")
                    with open(output_path, "w", encoding="utf-8") as f:
                        f.write(content)
                    print(f"保存步骤结果: {output_path}")

                    # 同时保存到根目录（为了兼容性）
                    root_output_path = os.path.join(novel_dir, f"chapter_{chapter_number}_{output_suffix}.txt")
                    with open(root_output_path, "w", encoding="utf-8") as f:
                        f.write(content)
                else:
                    print(f"检测到可能的错误消息，不保存步骤结果")
                    if is_error_message:
                        print(f"错误消息: {content[:100]}...")
                    if is_too_short:
                        print(f"内容过短: 只有 {len(content.strip())} 字符")

        # 保存最终结果
        if content:
            # 检查内容是否是API错误消息
            error_indicators = [
                "API请求失败",
                "调用API超时",
                "调用API出错",
                "生成内容失败",
                "Read timed out",
                "HTTPSConnectionPool",
                "Connection refused",
                "API响应中没有生成内容"
            ]

            # 检查内容是否包含错误指示符
            is_error_message = any(indicator in content for indicator in error_indicators)

            # 检查内容是否过短（可能是错误消息）
            is_too_short = len(content.strip()) < 200

            if not is_error_message and not is_too_short:
                # 检测并删除重复段落
                content = remove_duplicate_paragraphs(content)

                # 如果启用了高级功能，进行一致性检查
                is_consistent = True
                consistency_report = ""
                if ADVANCED_FEATURES_ENABLED:
                    try:
                        print("正在进行一致性检查...")
                        # 使用一致性检查器
                        is_consistent, consistency_report = consistency_check_consistency(
                            content,
                            chapter_number,
                            novel_dir,
                            agent.call_llm
                        )

                        # 保存一致性检查日志
                        save_consistency_log(novel_dir)

                        if not is_consistent:
                            print(f"检测到一致性问题:\n{consistency_report}")
                            # 如果有严重的一致性问题，可以尝试修复
                            # 这里我们只记录问题，不阻止生成过程
                        else:
                            print("一致性检查通过")
                    except Exception as e:
                        print(f"进行一致性检查时出错: {e}")
                        traceback.print_exc()

                # 创建chapters目录（如果不存在）
                chapters_dir = os.path.join(novel_dir, "chapters")
                os.makedirs(chapters_dir, exist_ok=True)

                # 保存最终文件到chapters目录
                final_path = os.path.join(chapters_dir, f"chapter_{chapter_number}_final.txt")
                with open(final_path, "w", encoding="utf-8") as f:
                    f.write(content)

                # 同时保存到根目录（为了兼容性）
                root_final_path = os.path.join(novel_dir, f"chapter_{chapter_number}_final.txt")
                with open(root_final_path, "w", encoding="utf-8") as f:
                    f.write(content)

                # 记录完成信息
                logger.info(f"[写作流程] 章节生成完成: 第{chapter_number}章, 内容长度={len(content)}字符")
                logger.info(f"[写作流程] 最终结果保存到: {final_path}")
                logger.info(f"[写作流程] 兼容性副本保存到: {root_final_path}")
                print(f"保存最终结果: {final_path}")
                print(f"保存兼容性副本: {root_final_path}")

                # 更新novel_info.json文件
                try:
                    novel_info_file = os.path.join(novel_dir, "novel_info.json")
                    if os.path.exists(novel_info_file):
                        # 读取现有信息
                        with open(novel_info_file, "r", encoding="utf-8") as f:
                            novel_info = json.load(f)

                        # 更新章节状态
                        if "chapters" not in novel_info:
                            novel_info["chapters"] = []

                        # 检查章节是否已存在
                        chapter_exists = False
                        for i, ch in enumerate(novel_info.get("chapters", [])):
                            if ch.get("number") == chapter_number:
                                # 更新现有章节
                                novel_info["chapters"][i]["status"] = "completed"
                                novel_info["chapters"][i]["updated_at"] = datetime.now().isoformat()
                                chapter_exists = True
                                break

                        if not chapter_exists:
                            # 添加新章节
                            novel_info["chapters"].append({
                                "number": chapter_number,
                                "title": f"第{chapter_number}章",
                                "status": "completed",
                                "created_at": datetime.now().isoformat(),
                                "updated_at": datetime.now().isoformat()
                            })

                        # 更新小说状态信息
                        novel_info["current_chapter"] = chapter_number
                        novel_info["completed_chapters"] = len([ch for ch in novel_info.get("chapters", []) if ch.get("status") == "completed"])
                        novel_info["last_chapter_completed"] = chapter_number
                        novel_info["updated_at"] = datetime.now().isoformat()

                        # 计算写作进度
                        if novel_info.get("total_chapters", 0) > 0:
                            novel_info["writing_progress"] = novel_info["completed_chapters"] / novel_info["total_chapters"]

                        # 保存更新后的信息
                        with open(novel_info_file, "w", encoding="utf-8") as f:
                            json.dump(novel_info, f, ensure_ascii=False, indent=2)

                        logger.info(f"[写作流程] 已更新novel_info.json: 当前章节={chapter_number}, 已完成章节={novel_info['completed_chapters']}")
                        print(f"已更新小说信息文件: {novel_info_file}")
                except Exception as e:
                    logger.error(f"[写作流程] 更新novel_info.json时出错: {e}")
                    print(f"更新小说信息文件时出错: {e}")

                # 如果启用了高级功能，更新记忆
                if ADVANCED_FEATURES_ENABLED:
                    try:
                        print("正在更新记忆...")
                        # 从章节内容提取实体并更新记忆
                        update_memory_from_chapter(
                            content,
                            chapter_number,
                            novel_dir,
                            agent.call_llm
                        )
                        print("记忆更新完成")
                    except Exception as e:
                        print(f"更新记忆时出错: {e}")
                        traceback.print_exc()
            else:
                print(f"检测到可能的错误消息，不保存最终结果")
                if is_error_message:
                    print(f"错误消息: {content[:100]}...")
                if is_too_short:
                    print(f"内容过短: 只有 {len(content.strip())} 字符")
                return False, "生成内容失败，请重试"

            # 保存插图信息
            if "illustrations" in chapter_context and chapter_context["illustrations"]:
                illustrations_info_path = os.path.join(novel_dir, f"chapter_{chapter_number}_illustrations.json")
                with open(illustrations_info_path, "w", encoding="utf-8") as f:
                    json.dump(chapter_context["illustrations"], f, ensure_ascii=False, indent=2)
                print(f"保存插图信息: {illustrations_info_path}")

        return True, content

    except Exception as e:
        print(f"运行写作流程时出错: {e}")
        traceback.print_exc()
        return False, f"运行写作流程时出错: {e}"

def remove_duplicate_paragraphs(text: str) -> str:
    """
    增强版的重复内容检测和删除函数，可以检测完全相同和高度相似的段落

    参数:
        text: 原始文本

    返回:
        去除重复后的文本
    """
    # 按段落分割文本
    paragraphs = text.split('\n\n')

    # 如果段落数量太少，不处理
    if len(paragraphs) < 5:
        return text

    # 创建一个列表存储不重复的段落
    unique_paragraphs = []
    # 存储段落的指纹(简化内容)用于相似度检测
    paragraph_fingerprints = []
    # 存储已见过的段落内容（用于完全相同的检测）
    seen_paragraphs = set()

    # 统计重复段落
    duplicate_count = 0
    similar_count = 0

    for para in paragraphs:
        # 忽略空段落
        if not para.strip():
            unique_paragraphs.append(para)
            continue

        # 如果段落太短，直接保留
        if len(para) < 50:
            unique_paragraphs.append(para)
            continue

        # 检查是否与已有段落完全相同
        if para.strip() in seen_paragraphs:
            duplicate_count += 1
            continue

        # 创建段落的指纹 - 提取关键词和句子结构
        # 简化版：取段落中的关键词组合
        words = para.strip().split()
        if len(words) > 10:
            # 取段落中的部分关键词作为指纹
            fingerprint = ' '.join(sorted([w for w in words if len(w) > 1])[:20])
        else:
            fingerprint = para.strip()

        # 检查是否与已有段落相似
        is_similar = False
        for existing_fp in paragraph_fingerprints:
            # 简单的相似度检测
            similarity = calculate_similarity(fingerprint, existing_fp)
            if similarity > 0.7:  # 相似度阈值
                is_similar = True
                similar_count += 1
                break

        if not is_similar:
            # 添加到不重复段落列表
            unique_paragraphs.append(para)
            paragraph_fingerprints.append(fingerprint)
            seen_paragraphs.add(para.strip())

    # 如果检测到重复段落，打印日志
    if duplicate_count > 0 or similar_count > 0:
        print(f"检测到并删除了 {duplicate_count} 个完全重复的段落和 {similar_count} 个高度相似的段落")

    # 重新组合文本
    return '\n\n'.join(unique_paragraphs)

def calculate_similarity(str1: str, str2: str) -> float:
    """
    计算两个字符串的相似度

    参数:
        str1: 第一个字符串
        str2: 第二个字符串

    返回:
        相似度，范围0-1
    """
    # 简单的Jaccard相似度
    set1 = set(str1.split())
    set2 = set(str2.split())

    if not set1 or not set2:
        return 0.0

    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))

    return intersection / union if union > 0 else 0.0

def check_outline_status(novel_dir: str, chapter_number: int) -> Dict[str, Any]:
    """
    检查大纲状态，判断是否需要生成新的章节大纲

    参数:
        novel_dir: 小说目录
        chapter_number: 章节编号

    返回:
        状态信息字典
    """
    import logging
    logger = logging.getLogger("WritingPipeline")
    logger.info(f"[写作流程] 开始检查大纲状态: 小说目录={novel_dir}, 章节编号={chapter_number}")

    try:
        # 导入必要的模块
        from src.core.agent import AGENT_REGISTRY

        # 获取大纲生成器智能体
        outline_agent = AGENT_REGISTRY.get_agent("OutlineGeneratorAgent")
        logger.info(f"[写作流程] 大纲生成器智能体状态: {outline_agent is not None}")

        if not outline_agent:
            logger.warning("[写作流程] 大纲生成器智能体未注册")
            return {
                "need_new_outline": True,
                "message": "大纲生成器智能体未注册"
            }

        # 记录大纲生成器智能体信息
        logger.info(f"[写作流程] 大纲生成器智能体信息: 名称={outline_agent.name}, 模型={outline_agent.model}")

        # 发送检查大纲状态的消息
        logger.info(f"[写作流程] 发送检查大纲状态消息给大纲生成器智能体")
        # 直接调用大纲生成器智能体的方法，而不是发送消息
        # 因为 writing_pipeline 不是一个注册的智能体，无法接收响应
        try:
            # 直接调用大纲生成器的方法
            if hasattr(outline_agent, 'outline_generator') and outline_agent.outline_generator:
                status_info = outline_agent.outline_generator.check_outline_status(chapter_number)
                logger.info(f"[写作流程] 直接调用大纲生成器方法获取状态信息")
            else:
                # 如果大纲生成器未初始化，使用默认状态
                status_info = {
                    "need_new_outline": False,
                    "current_phase": "当前阶段",
                    "remaining_chapters": 10,
                    "message": "大纲状态正常"
                }
                logger.info(f"[写作流程] 大纲生成器未初始化，使用默认状态")
        except Exception as e:
            # 如果出错，使用默认状态
            status_info = {
                "need_new_outline": False,
                "current_phase": "当前阶段",
                "remaining_chapters": 10,
                "message": "大纲状态正常"
            }
            logger.info(f"[写作流程] 检查大纲状态时出错: {e}")

        # 使用从大纲生成器获取的状态信息
        result = status_info
        logger.info(f"[写作流程] 大纲状态检查结果: {result}")
        return result
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"[写作流程] 检查大纲状态时出错: {e}")
        logger.error(f"[写作流程] 错误详情:\n{error_trace}")
        print(f"检查大纲状态时出错: {e}")
        return {
            "need_new_outline": True,
            "message": f"检查大纲状态时出错: {e}"
        }

def check_chapter_exists(novel_dir: str, chapter_number: int) -> bool:
    """
    检查章节是否存在

    参数:
        novel_dir: 小说目录
        chapter_number: 章节编号

    返回:
        章节是否存在
    """
    # 检查最终文件是否存在
    final_path = os.path.join(novel_dir, f"chapter_{chapter_number}_final.txt")
    if os.path.exists(final_path):
        # 检查文件内容是否有效
        try:
            with open(final_path, "r", encoding="utf-8") as f:
                content = f.read()
                if len(content.strip()) > 1000:  # 确保内容不是空的或者太短
                    return True
        except Exception:
            pass

    # 检查其他可能的文件
    file_patterns = [
        f"chapter_{chapter_number}_completed.txt",
        f"chapter_{chapter_number}_optimized.txt",
        f"chapter_{chapter_number}_5_optimized.txt",
        f"chapter_{chapter_number}_4_reviewed.txt",
        f"chapter_{chapter_number}_3_expanded.txt",
        f"chapter_{chapter_number}_2_polished.txt",
        f"chapter_{chapter_number}_1_initial.txt"
    ]

    for pattern in file_patterns:
        file_path = os.path.join(novel_dir, pattern)
        if os.path.exists(file_path):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    if len(content.strip()) > 1000:  # 确保内容不是空的或者太短
                        return True
            except Exception:
                pass

    return False


def determine_illustration_style(novel_style: str) -> str:
    """
    根据小说风格自动确定插图风格

    参数:
        novel_style: 小说风格

    返回:
        插图风格
    """
    # 将小说风格映射到插图风格
    style_mapping = {
        # 现代/写实类风格
        "写实主义": "写实风格",
        "现代主义": "写实风格",
        "现实主义": "写实风格",
        "都市": "写实风格",
        "社会": "写实风格",
        "职场": "写实风格",
        "军事": "写实风格",
        "历史": "写实风格",
        "纪实": "写实风格",
        "情节紧凑": "写实风格",

        # 浪漫/艺术类风格
        "浪漫主义": "水彩风格",
        "言情": "水彩风格",
        "爱情": "水彩风格",
        "青春": "水彩风格",
        "诗意": "水彩风格",
        "抒情": "水彩风格",
        "唯美": "水彩风格",

        # 古典/传统类风格
        "古典": "中国国画风格",
        "古风": "中国国画风格",
        "武侠": "中国国画风格",
        "仙侠": "中国国画风格",
        "传统": "中国国画风格",
        "东方": "中国国画风格",

        # 奇幻/科幻类风格
        "奇幻": "正版插画风格",
        "魔幻": "正版插画风格",
        "玄幻": "正版插画风格",
        "科幻": "科幻风格",
        "未来": "科幻风格",
        "太空": "科幻风格",
        "异世界": "正版插画风格",
        "游戏": "正版插画风格",

        # 动漫/轻小说风格
        "轻小说": "动漫风格",
        "校园": "动漫风格",
        "搞笑": "动漫风格",
        "日系": "动漫风格",
        "二次元": "动漫风格",

        # 艺术/表现类风格
        "表现主义": "油画风格",
        "象征主义": "油画风格",
        "后现代": "油画风格",
        "实验派": "油画风格",
        "哲理": "油画风格",
        "意识流": "油画风格",

        # 黑暗/悬疑类风格
        "悬疑": "素描风格",
        "推理": "素描风格",
        "恐怖": "素描风格",
        "惊悚": "素描风格",
        "黑色": "素描风格",
        "犯罪": "素描风格",
        "黑暗": "素描风格"
    }

    # 默认风格
    default_style = "写实风格"

    # 如果小说风格为空，返回默认风格
    if not novel_style:
        return default_style

    # 将小说风格转换为小写，便于匹配
    novel_style_lower = novel_style.lower()

    # 遍历风格映射，查找匹配项
    for key, value in style_mapping.items():
        if key.lower() in novel_style_lower:
            return value

    # 如果没有匹配项，返回默认风格
    return default_style

def register_writing_agents():
    """
    注册所有写作智能体

    这个函数会创建并注册所有写作相关的智能体，包括：
    - WritingAgent: 负责初始内容生成
    - PolishingAgent: 负责润色内容
    - ExpansionAgent: 负责扩展内容
    - ReviewAgent: 负责审核内容
    - OptimizationAgent: 负责优化内容
    - IllustrationAgent: 负责生成插图

    返回:
        None
    """
    try:
        # 导入必要的模块
        from src.core.agent import AGENT_REGISTRY
        from src.core.model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES

        # 导入智能体类
        from src.agents.writing_agent import WritingAgent
        from src.agents.polishing_agent import PolishingAgent
        from src.agents.expansion_agent import ExpansionAgent
        from src.agents.review_agent import ReviewAgent
        from src.agents.optimization_agent import OptimizationAgent
        from src.agents.illustration_agent import IllustrationAgent

        # 获取模型配置
        agent_models = AI_MODEL_CONFIG.get("agent_models", {})
        default_model = AI_MODEL_CONFIG.get("default_model", "glm-4-flash")

        # 获取写作模型，所有写作相关智能体使用同一个模型
        writing_model = agent_models.get("WritingAgent", default_model)

        # 注册智能体，确保所有写作相关智能体使用同一个模型
        AGENT_REGISTRY.register(WritingAgent("WritingAgent", writing_model))
        AGENT_REGISTRY.register(PolishingAgent("PolishingAgent", writing_model))
        AGENT_REGISTRY.register(ExpansionAgent("ExpansionAgent", writing_model))
        AGENT_REGISTRY.register(ReviewAgent("ReviewAgent", writing_model))
        AGENT_REGISTRY.register(OptimizationAgent("OptimizationAgent", writing_model))

        # 注册插图智能体，支持两种图像模型
        illustration_model = agent_models.get("IllustrationAgent", DEFAULT_MODEL_NAMES["cogview"])
        print(f"插图模型: {illustration_model}")
        AGENT_REGISTRY.register(IllustrationAgent("IllustrationAgent", illustration_model))

        # 注册大纲生成器智能体
        try:
            from src.agents.outline_generator import register_outline_generator_agent
            register_outline_generator_agent()
        except Exception as e:
            print(f"注册大纲生成器智能体时出错: {e}")

        print("步骤: 智能体初始化 - 已注册所有写作智能体")
    except Exception as e:
        print(f"注册写作智能体时出错: {e}")
        import traceback
        traceback.print_exc()
