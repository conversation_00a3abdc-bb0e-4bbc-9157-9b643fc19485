#!/usr/bin/env python3
"""
Novel Generation Script - Simple script to generate a novel using the new system
"""

import os
import sys
import argparse
import logging
from datetime import datetime

from novel_integration import generate_novel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', f'novel_generation_{datetime.now().strftime("%Y%m%d")}.log'))
    ]
)

logger = logging.getLogger('generate_novel')

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Generate a novel using the new system')
    
    parser.add_argument('--title', type=str, default='山河无恙', help='Novel title')
    parser.add_argument('--genre', type=str, default='历史', help='Novel genre')
    parser.add_argument('--style', type=str, default='写实', help='Writing style')
    parser.add_argument('--scene', type=str, default='古代中国', help='Main scene')
    parser.add_argument('--characters', type=str, default='李明、王芳', help='Main characters')
    parser.add_argument('--chapters', type=int, default=10, help='Number of chapters')
    parser.add_argument('--output-dir', type=str, default='output', help='Output directory')
    parser.add_argument('--batch-size', type=int, default=3, help='Batch size for chapter processing')
    parser.add_argument('--save-intermediates', action='store_true', help='Save intermediate files')
    
    return parser.parse_args()

def print_progress(message):
    """Print progress message"""
    print(f"进度: {message}")
    logger.info(message)

def main():
    """Main function"""
    # Create necessary directories
    os.makedirs('output', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # Parse arguments
    args = parse_arguments()
    
    print(f"开始生成小说《{args.title}》...")
    print(f"类型: {args.genre}")
    print(f"风格: {args.style}")
    print(f"场景: {args.scene}")
    print(f"角色: {args.characters}")
    print(f"章节数: {args.chapters}")
    print(f"输出目录: {args.output_dir}")
    print(f"批处理大小: {args.batch_size}")
    print(f"保存中间文件: {args.save_intermediates}")
    print()
    
    # Generate novel
    doc_path = generate_novel(
        title=args.title,
        genre=args.genre,
        style=args.style,
        scene=args.scene,
        characters=args.characters,
        num_chapters=args.chapters,
        output_dir=args.output_dir,
        save_intermediates=args.save_intermediates,
        batch_size=args.batch_size,
        progress_callback=print_progress
    )
    
    if doc_path:
        print(f"\n小说生成成功！")
        print(f"文档保存在: {doc_path}")
        return 0
    else:
        print(f"\n小说生成失败！")
        return 1

if __name__ == '__main__':
    sys.exit(main())
