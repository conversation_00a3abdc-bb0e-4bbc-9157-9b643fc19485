{"data_mtime": **********, "dep_lines": [14, 268, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["src.core.medical_report_generator", "src.core.specialized_prompts", "os", "re", "json", "traceback", "typing", "datetime", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "io", "typing_extensions"], "hash": "638562fa760245983e5e6994d874116821d871b2", "id": "src.agents.chapter_generator", "ignore_all": false, "interface_hash": "cb3b20fa0b92ca97c344fa4b994c5aefa2ab158a", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\storymc\\src\\agents\\chapter_generator.py", "plugin_data": null, "size": 19609, "suppressed": [], "version_id": "1.15.0"}