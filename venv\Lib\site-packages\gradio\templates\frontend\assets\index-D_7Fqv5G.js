import{a as Me}from"./Upload--7Xp8CSk.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js";import{B as Qe}from"./BlockLabel-JbMBN4MZ.js";import{V as Re}from"./Video-fsmLZWjA.js";import{S as Xe}from"./SelectSource-D4OOPOCG.js";import"./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js";import"./Image-CnqB5dbD.js";/* empty css                                                   */import{W as Ye}from"./ImageUploader-Dxn_iV-T.js";import"./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js";import"./index-V1UhiEW8.js";/* empty css                                              */import{p as ge,a as Ze}from"./Video-C-llMUaJ.js";import{l as jl}from"./Video-C-llMUaJ.js";import{P as ye,V as $e}from"./VideoPreview-Bmn4fgzX.js";import{default as Pl}from"./Example-97koPlcW.js";import{B as ve}from"./Block-CMfAaXj9.js";import{U as xe}from"./UploadText-Dp_ycz35.js";import{S as ze}from"./index-CWG4El0O.js";/* empty css                                             */import"./prism-python-DO0H4w6Q.js";import"./file-url-DoxvUUVV.js";import"./IconButton-DbC-jsk_.js";import"./Clear-By3xiIwg.js";import"./FullscreenButton-B-slkwu9.js";import"./IconButtonWrapper-DrWC4NJv.js";import"./utils-Gtzs_Zla.js";import"./DropdownArrow-DuA1Ifms.js";import"./Square-oAGqOwsh.js";import"./index-CQUs-3jT.js";import"./StreamingBar-BU9S4hA7.js";import"./svelte/svelte.js";import"./hls-CnVhpNcu.js";import"./Empty-BgOmVBFg.js";import"./ShareButton-BfWeO6Sd.js";import"./Community-Dw1micSV.js";import"./utils-BsGrhMNe.js";import"./Download-DVtk-Jv3.js";import"./DownloadLink-QIttOhoR.js";import"./Trim-JQYgj7Jd.js";import"./Play-B0Q0U1Qz.js";import"./Undo-DCjBnnSO.js";import"./ModifyUpload-DAmzHHEC.js";import"./Edit-BpRIf5rU.js";const{SvelteComponent:et,add_flush_callback:ae,append:re,attr:Q,bind:ue,binding_callbacks:_e,bubble:E,check_outros:he,create_component:R,create_slot:tt,destroy_component:X,detach:J,element:$,empty:lt,flush:p,get_all_dirty_from_scope:nt,get_slot_changes:it,group_outros:de,init:ot,insert:O,mount_component:Y,noop:fe,safe_not_equal:Se,set_data:we,space:ce,text:pe,transition_in:z,transition_out:S,update_slot_base:st}=window.__gradio__svelte__internal,{createEventDispatcher:at}=window.__gradio__svelte__internal;function rt(l){let e,n=(l[0].orig_name||l[0].url)+"",t,i,o,u=ge(l[0].size)+"",f;return{c(){e=$("div"),t=pe(n),i=ce(),o=$("div"),f=pe(u),Q(e,"class","file-name svelte-14jis2k"),Q(o,"class","file-size svelte-14jis2k")},m(s,c){O(s,e,c),re(e,t),O(s,i,c),O(s,o,c),re(o,f)},p(s,c){c[0]&1&&n!==(n=(s[0].orig_name||s[0].url)+"")&&we(t,n),c[0]&1&&u!==(u=ge(s[0].size)+"")&&we(f,u)},i:fe,o:fe,d(s){s&&(J(e),J(i),J(o))}}}function ut(l){let e=l[0]?.url,n,t,i=ke(l);return{c(){i.c(),n=lt()},m(o,u){i.m(o,u),O(o,n,u),t=!0},p(o,u){u[0]&1&&Se(e,e=o[0]?.url)?(de(),S(i,1,1,fe),he(),i=ke(o),i.c(),z(i,1),i.m(n.parentNode,n)):i.p(o,u)},i(o){t||(z(i),t=!0)},o(o){S(i),t=!1},d(o){o&&J(n),i.d(o)}}}function _t(l){let e,n,t,i;const o=[ct,ft],u=[];function f(s,c){return s[1]==="upload"?0:s[1]==="webcam"?1:-1}return~(n=f(l))&&(t=u[n]=o[n](l)),{c(){e=$("div"),t&&t.c(),Q(e,"class","upload-container svelte-14jis2k")},m(s,c){O(s,e,c),~n&&u[n].m(e,null),i=!0},p(s,c){let a=n;n=f(s),n===a?~n&&u[n].p(s,c):(t&&(de(),S(u[a],1,1,()=>{u[a]=null}),he()),~n?(t=u[n],t?t.p(s,c):(t=u[n]=o[n](s),t.c()),z(t,1),t.m(e,null)):t=null)},i(s){i||(z(t),i=!0)},o(s){S(t),i=!1},d(s){s&&J(e),~n&&u[n].d()}}}function ke(l){let e,n;return e=new ye({props:{upload:l[15],root:l[11],interactive:!0,autoplay:l[10],src:l[0].url,subtitle:l[3]?.url,is_stream:!1,mirror:l[8]&&l[1]==="webcam",label:l[5],handle_change:l[24],handle_reset_value:l[13],loop:l[17],value:l[0],i18n:l[12],show_download_button:l[6],handle_clear:l[23],has_change_history:l[20]}}),e.$on("play",l[33]),e.$on("pause",l[34]),e.$on("stop",l[35]),e.$on("end",l[36]),{c(){R(e.$$.fragment)},m(t,i){Y(e,t,i),n=!0},p(t,i){const o={};i[0]&32768&&(o.upload=t[15]),i[0]&2048&&(o.root=t[11]),i[0]&1024&&(o.autoplay=t[10]),i[0]&1&&(o.src=t[0].url),i[0]&8&&(o.subtitle=t[3]?.url),i[0]&258&&(o.mirror=t[8]&&t[1]==="webcam"),i[0]&32&&(o.label=t[5]),i[0]&8192&&(o.handle_reset_value=t[13]),i[0]&131072&&(o.loop=t[17]),i[0]&1&&(o.value=t[0]),i[0]&4096&&(o.i18n=t[12]),i[0]&64&&(o.show_download_button=t[6]),i[0]&1048576&&(o.has_change_history=t[20]),e.$set(o)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){S(e.$$.fragment,t),n=!1},d(t){X(e,t)}}}function ft(l){let e,n;return e=new Ye({props:{root:l[11],mirror_webcam:l[8],include_audio:l[9],webcam_constraints:l[18],mode:"video",i18n:l[12],upload:l[15],stream_every:1}}),e.$on("error",l[30]),e.$on("capture",l[25]),e.$on("start_recording",l[31]),e.$on("stop_recording",l[32]),{c(){R(e.$$.fragment)},m(t,i){Y(e,t,i),n=!0},p(t,i){const o={};i[0]&2048&&(o.root=t[11]),i[0]&256&&(o.mirror_webcam=t[8]),i[0]&512&&(o.include_audio=t[9]),i[0]&262144&&(o.webcam_constraints=t[18]),i[0]&4096&&(o.i18n=t[12]),i[0]&32768&&(o.upload=t[15]),e.$set(o)},i(t){n||(z(e.$$.fragment,t),n=!0)},o(t){S(e.$$.fragment,t),n=!1},d(t){X(e,t)}}}function ct(l){let e,n,t,i;function o(s){l[27](s)}function u(s){l[28](s)}let f={filetype:"video/x-m4v,video/*",max_file_size:l[14],root:l[11],upload:l[15],stream_handler:l[16],aria_label:l[12]("video.drop_to_upload"),$$slots:{default:[ht]},$$scope:{ctx:l}};return l[19]!==void 0&&(f.dragging=l[19]),l[2]!==void 0&&(f.uploading=l[2]),e=new Me({props:f}),_e.push(()=>ue(e,"dragging",o)),_e.push(()=>ue(e,"uploading",u)),e.$on("load",l[22]),e.$on("error",l[29]),{c(){R(e.$$.fragment)},m(s,c){Y(e,s,c),i=!0},p(s,c){const a={};c[0]&16384&&(a.max_file_size=s[14]),c[0]&2048&&(a.root=s[11]),c[0]&32768&&(a.upload=s[15]),c[0]&65536&&(a.stream_handler=s[16]),c[0]&4096&&(a.aria_label=s[12]("video.drop_to_upload")),c[1]&128&&(a.$$scope={dirty:c,ctx:s}),!n&&c[0]&524288&&(n=!0,a.dragging=s[19],ae(()=>n=!1)),!t&&c[0]&4&&(t=!0,a.uploading=s[2],ae(()=>t=!1)),e.$set(a)},i(s){i||(z(e.$$.fragment,s),i=!0)},o(s){S(e.$$.fragment,s),i=!1},d(s){X(e,s)}}}function ht(l){let e;const n=l[26].default,t=tt(n,l,l[38],null);return{c(){t&&t.c()},m(i,o){t&&t.m(i,o),e=!0},p(i,o){t&&t.p&&(!e||o[1]&128)&&st(t,n,i,i[38],e?it(n,i[38],o,null):nt(i[38]),null)},i(i){e||(z(t,i),e=!0)},o(i){S(t,i),e=!1},d(i){t&&t.d(i)}}}function dt(l){let e,n,t,i,o,u,f,s,c,a;e=new Qe({props:{show_label:l[7],Icon:Re,label:l[5]||"Video"}});const h=[_t,ut,rt],g=[];function m(d,k){return d[0]===null||d[0].url===void 0?0:(i==null&&(i=!!Ze()),i?1:d[0].size?2:-1)}~(o=m(l))&&(u=g[o]=h[o](l));function U(d){l[37](d)}let P={sources:l[4],handle_clear:l[23]};return l[1]!==void 0&&(P.active_source=l[1]),s=new Xe({props:P}),_e.push(()=>ue(s,"active_source",U)),{c(){R(e.$$.fragment),n=ce(),t=$("div"),u&&u.c(),f=ce(),R(s.$$.fragment),Q(t,"data-testid","video"),Q(t,"class","video-container svelte-14jis2k")},m(d,k){Y(e,d,k),O(d,n,k),O(d,t,k),~o&&g[o].m(t,null),re(t,f),Y(s,t,null),a=!0},p(d,k){const V={};k[0]&128&&(V.show_label=d[7]),k[0]&32&&(V.label=d[5]||"Video"),e.$set(V);let B=o;o=m(d),o===B?~o&&g[o].p(d,k):(u&&(de(),S(g[B],1,1,()=>{g[B]=null}),he()),~o?(u=g[o],u?u.p(d,k):(u=g[o]=h[o](d),u.c()),z(u,1),u.m(t,f)):u=null);const I={};k[0]&16&&(I.sources=d[4]),!c&&k[0]&2&&(c=!0,I.active_source=d[1],ae(()=>c=!1)),s.$set(I)},i(d){a||(z(e.$$.fragment,d),z(u),z(s.$$.fragment,d),a=!0)},o(d){S(e.$$.fragment,d),S(u),S(s.$$.fragment,d),a=!1},d(d){d&&(J(n),J(t)),X(e,d),~o&&g[o].d(),X(s)}}}function mt(l,e,n){let{$$slots:t={},$$scope:i}=e,{value:o=null}=e,{subtitle:u=null}=e,{sources:f=["webcam","upload"]}=e,{label:s=void 0}=e,{show_download_button:c=!1}=e,{show_label:a=!0}=e,{mirror_webcam:h=!1}=e,{include_audio:g}=e,{autoplay:m}=e,{root:U}=e,{i18n:P}=e,{active_source:d="webcam"}=e,{handle_reset_value:k=()=>{}}=e,{max_file_size:V=null}=e,{upload:B}=e,{stream_handler:I}=e,{loop:A}=e,{uploading:b=!1}=e,{webcam_constraints:F=null}=e,G=!1;const v=at();function Z({detail:r}){n(0,o=r),v("change",r),v("upload",r)}function H(){n(0,o=null),v("change",null),v("clear")}function T(r){n(20,G=!0),v("change",r)}function K({detail:r}){v("change",r)}let q=!1;function M(r){q=r,n(19,q)}function C(r){b=r,n(2,b)}const x=({detail:r})=>v("error",r);function y(r){E.call(this,l,r)}function ee(r){E.call(this,l,r)}function te(r){E.call(this,l,r)}function le(r){E.call(this,l,r)}function ne(r){E.call(this,l,r)}function ie(r){E.call(this,l,r)}function oe(r){E.call(this,l,r)}function se(r){d=r,n(1,d)}return l.$$set=r=>{"value"in r&&n(0,o=r.value),"subtitle"in r&&n(3,u=r.subtitle),"sources"in r&&n(4,f=r.sources),"label"in r&&n(5,s=r.label),"show_download_button"in r&&n(6,c=r.show_download_button),"show_label"in r&&n(7,a=r.show_label),"mirror_webcam"in r&&n(8,h=r.mirror_webcam),"include_audio"in r&&n(9,g=r.include_audio),"autoplay"in r&&n(10,m=r.autoplay),"root"in r&&n(11,U=r.root),"i18n"in r&&n(12,P=r.i18n),"active_source"in r&&n(1,d=r.active_source),"handle_reset_value"in r&&n(13,k=r.handle_reset_value),"max_file_size"in r&&n(14,V=r.max_file_size),"upload"in r&&n(15,B=r.upload),"stream_handler"in r&&n(16,I=r.stream_handler),"loop"in r&&n(17,A=r.loop),"uploading"in r&&n(2,b=r.uploading),"webcam_constraints"in r&&n(18,F=r.webcam_constraints),"$$scope"in r&&n(38,i=r.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&524288&&v("drag",q)},[o,d,b,u,f,s,c,a,h,g,m,U,P,k,V,B,I,A,F,q,G,v,Z,H,T,K,t,M,C,x,y,ee,te,le,ne,ie,oe,se,i]}class bt extends et{constructor(e){super(),ot(this,e,mt,dt,Se,{value:0,subtitle:3,sources:4,label:5,show_download_button:6,show_label:7,mirror_webcam:8,include_audio:9,autoplay:10,root:11,i18n:12,active_source:1,handle_reset_value:13,max_file_size:14,upload:15,stream_handler:16,loop:17,uploading:2,webcam_constraints:18},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),p()}get subtitle(){return this.$$.ctx[3]}set subtitle(e){this.$$set({subtitle:e}),p()}get sources(){return this.$$.ctx[4]}set sources(e){this.$$set({sources:e}),p()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),p()}get show_download_button(){return this.$$.ctx[6]}set show_download_button(e){this.$$set({show_download_button:e}),p()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),p()}get mirror_webcam(){return this.$$.ctx[8]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),p()}get include_audio(){return this.$$.ctx[9]}set include_audio(e){this.$$set({include_audio:e}),p()}get autoplay(){return this.$$.ctx[10]}set autoplay(e){this.$$set({autoplay:e}),p()}get root(){return this.$$.ctx[11]}set root(e){this.$$set({root:e}),p()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),p()}get active_source(){return this.$$.ctx[1]}set active_source(e){this.$$set({active_source:e}),p()}get handle_reset_value(){return this.$$.ctx[13]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),p()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),p()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),p()}get stream_handler(){return this.$$.ctx[16]}set stream_handler(e){this.$$set({stream_handler:e}),p()}get loop(){return this.$$.ctx[17]}set loop(e){this.$$set({loop:e}),p()}get uploading(){return this.$$.ctx[2]}set uploading(e){this.$$set({uploading:e}),p()}get webcam_constraints(){return this.$$.ctx[18]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),p()}}const gt=bt,{SvelteComponent:wt,add_flush_callback:pt,assign:Ve,bind:kt,binding_callbacks:vt,check_outros:zt,create_component:W,destroy_component:D,detach:me,empty:St,flush:w,get_spread_object:Be,get_spread_update:Ie,group_outros:Vt,init:Bt,insert:be,mount_component:L,safe_not_equal:It,space:je,transition_in:j,transition_out:N}=window.__gradio__svelte__internal;function jt(l){let e,n;return e=new ve({props:{visible:l[4],variant:l[0]===null&&l[24]==="upload"?"dashed":"solid",border_mode:l[27]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[12],scale:l[13],min_width:l[14],allow_overflow:!1,$$slots:{default:[qt]},$$scope:{ctx:l}}}),{c(){W(e.$$.fragment)},m(t,i){L(e,t,i),n=!0},p(t,i){const o={};i[0]&16&&(o.visible=t[4]),i[0]&16777217&&(o.variant=t[0]===null&&t[24]==="upload"?"dashed":"solid"),i[0]&134217728&&(o.border_mode=t[27]?"focus":"base"),i[0]&4&&(o.elem_id=t[2]),i[0]&8&&(o.elem_classes=t[3]),i[0]&512&&(o.height=t[9]),i[0]&1024&&(o.width=t[10]),i[0]&4096&&(o.container=t[12]),i[0]&8192&&(o.scale=t[13]),i[0]&16384&&(o.min_width=t[14]),i[0]&267815394|i[1]&16777216&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(j(e.$$.fragment,t),n=!0)},o(t){N(e.$$.fragment,t),n=!1},d(t){D(e,t)}}}function Nt(l){let e,n;return e=new ve({props:{visible:l[4],variant:l[0]===null&&l[24]==="upload"?"dashed":"solid",border_mode:l[27]?"focus":"base",padding:!1,elem_id:l[2],elem_classes:l[3],height:l[9],width:l[10],container:l[12],scale:l[13],min_width:l[14],allow_overflow:!1,$$slots:{default:[Ct]},$$scope:{ctx:l}}}),{c(){W(e.$$.fragment)},m(t,i){L(e,t,i),n=!0},p(t,i){const o={};i[0]&16&&(o.visible=t[4]),i[0]&16777217&&(o.variant=t[0]===null&&t[24]==="upload"?"dashed":"solid"),i[0]&134217728&&(o.border_mode=t[27]?"focus":"base"),i[0]&4&&(o.elem_id=t[2]),i[0]&8&&(o.elem_classes=t[3]),i[0]&512&&(o.height=t[9]),i[0]&1024&&(o.width=t[10]),i[0]&4096&&(o.container=t[12]),i[0]&8192&&(o.scale=t[13]),i[0]&16384&&(o.min_width=t[14]),i[0]&105349410|i[1]&16777216&&(o.$$scope={dirty:i,ctx:t}),e.$set(o)},i(t){n||(j(e.$$.fragment,t),n=!0)},o(t){N(e.$$.fragment,t),n=!1},d(t){D(e,t)}}}function Pt(l){let e,n;return e=new xe({props:{i18n:l[18].i18n,type:"video"}}),{c(){W(e.$$.fragment)},m(t,i){L(e,t,i),n=!0},p(t,i){const o={};i[0]&262144&&(o.i18n=t[18].i18n),e.$set(o)},i(t){n||(j(e.$$.fragment,t),n=!0)},o(t){N(e.$$.fragment,t),n=!1},d(t){D(e,t)}}}function qt(l){let e,n,t,i,o;const u=[{autoscroll:l[18].autoscroll},{i18n:l[18].i18n},l[1]];let f={};for(let a=0;a<u.length;a+=1)f=Ve(f,u[a]);e=new ze({props:f}),e.$on("clear_status",l[42]);function s(a){l[45](a)}let c={value:l[25],subtitle:l[26],label:l[5],show_label:l[8],show_download_button:l[17],sources:l[6],active_source:l[24],mirror_webcam:l[20],include_audio:l[21],autoplay:l[15],root:l[7],loop:l[22],webcam_constraints:l[11],handle_reset_value:l[28],i18n:l[18].i18n,max_file_size:l[18].max_file_size,upload:l[43],stream_handler:l[44],$$slots:{default:[Pt]},$$scope:{ctx:l}};return l[23]!==void 0&&(c.uploading=l[23]),t=new gt({props:c}),vt.push(()=>kt(t,"uploading",s)),t.$on("change",l[29]),t.$on("drag",l[46]),t.$on("error",l[30]),t.$on("clear",l[47]),t.$on("play",l[48]),t.$on("pause",l[49]),t.$on("upload",l[50]),t.$on("stop",l[51]),t.$on("end",l[52]),t.$on("start_recording",l[53]),t.$on("stop_recording",l[54]),{c(){W(e.$$.fragment),n=je(),W(t.$$.fragment)},m(a,h){L(e,a,h),be(a,n,h),L(t,a,h),o=!0},p(a,h){const g=h[0]&262146?Ie(u,[h[0]&262144&&{autoscroll:a[18].autoscroll},h[0]&262144&&{i18n:a[18].i18n},h[0]&2&&Be(a[1])]):{};e.$set(g);const m={};h[0]&33554432&&(m.value=a[25]),h[0]&67108864&&(m.subtitle=a[26]),h[0]&32&&(m.label=a[5]),h[0]&256&&(m.show_label=a[8]),h[0]&131072&&(m.show_download_button=a[17]),h[0]&64&&(m.sources=a[6]),h[0]&16777216&&(m.active_source=a[24]),h[0]&1048576&&(m.mirror_webcam=a[20]),h[0]&2097152&&(m.include_audio=a[21]),h[0]&32768&&(m.autoplay=a[15]),h[0]&128&&(m.root=a[7]),h[0]&4194304&&(m.loop=a[22]),h[0]&2048&&(m.webcam_constraints=a[11]),h[0]&262144&&(m.i18n=a[18].i18n),h[0]&262144&&(m.max_file_size=a[18].max_file_size),h[0]&262144&&(m.upload=a[43]),h[0]&262144&&(m.stream_handler=a[44]),h[0]&262144|h[1]&16777216&&(m.$$scope={dirty:h,ctx:a}),!i&&h[0]&8388608&&(i=!0,m.uploading=a[23],pt(()=>i=!1)),t.$set(m)},i(a){o||(j(e.$$.fragment,a),j(t.$$.fragment,a),o=!0)},o(a){N(e.$$.fragment,a),N(t.$$.fragment,a),o=!1},d(a){a&&me(n),D(e,a),D(t,a)}}}function Ct(l){let e,n,t,i;const o=[{autoscroll:l[18].autoscroll},{i18n:l[18].i18n},l[1]];let u={};for(let f=0;f<o.length;f+=1)u=Ve(u,o[f]);return e=new ze({props:u}),e.$on("clear_status",l[34]),t=new $e({props:{value:l[25],subtitle:l[26],label:l[5],show_label:l[8],autoplay:l[15],loop:l[22],show_share_button:l[16],show_download_button:l[17],i18n:l[18].i18n,upload:l[35]}}),t.$on("play",l[36]),t.$on("pause",l[37]),t.$on("stop",l[38]),t.$on("end",l[39]),t.$on("share",l[40]),t.$on("error",l[41]),{c(){W(e.$$.fragment),n=je(),W(t.$$.fragment)},m(f,s){L(e,f,s),be(f,n,s),L(t,f,s),i=!0},p(f,s){const c=s[0]&262146?Ie(o,[s[0]&262144&&{autoscroll:f[18].autoscroll},s[0]&262144&&{i18n:f[18].i18n},s[0]&2&&Be(f[1])]):{};e.$set(c);const a={};s[0]&33554432&&(a.value=f[25]),s[0]&67108864&&(a.subtitle=f[26]),s[0]&32&&(a.label=f[5]),s[0]&256&&(a.show_label=f[8]),s[0]&32768&&(a.autoplay=f[15]),s[0]&4194304&&(a.loop=f[22]),s[0]&65536&&(a.show_share_button=f[16]),s[0]&131072&&(a.show_download_button=f[17]),s[0]&262144&&(a.i18n=f[18].i18n),s[0]&262144&&(a.upload=f[35]),t.$set(a)},i(f){i||(j(e.$$.fragment,f),j(t.$$.fragment,f),i=!0)},o(f){N(e.$$.fragment,f),N(t.$$.fragment,f),i=!1},d(f){f&&me(n),D(e,f),D(t,f)}}}function Ut(l){let e,n,t,i;const o=[Nt,jt],u=[];function f(s,c){return s[19]?1:0}return e=f(l),n=u[e]=o[e](l),{c(){n.c(),t=St()},m(s,c){u[e].m(s,c),be(s,t,c),i=!0},p(s,c){let a=e;e=f(s),e===a?u[e].p(s,c):(Vt(),N(u[a],1,1,()=>{u[a]=null}),zt(),n=u[e],n?n.p(s,c):(n=u[e]=o[e](s),n.c()),j(n,1),n.m(t.parentNode,t))},i(s){i||(j(n),i=!0)},o(s){N(n),i=!1},d(s){s&&me(t),u[e].d(s)}}}function Et(l,e,n){let{elem_id:t=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{value:u=null}=e,f=null,{label:s}=e,{sources:c}=e,{root:a}=e,{show_label:h}=e,{loading_status:g}=e,{height:m}=e,{width:U}=e,{webcam_constraints:P=null}=e,{container:d=!1}=e,{scale:k=null}=e,{min_width:V=void 0}=e,{autoplay:B=!1}=e,{show_share_button:I=!0}=e,{show_download_button:A}=e,{gradio:b}=e,{interactive:F}=e,{mirror_webcam:G}=e,{include_audio:v}=e,{loop:Z=!1}=e,{input_ready:H}=e,T=!1,K=null,q=null,M,C=u;const x=()=>{C===null||u===C||n(0,u=C)};let y=!1;function ee({detail:_}){_!=null?n(0,u={video:_,subtitles:null}):n(0,u=null)}function te({detail:_}){const[He,Ke]=_.includes("Invalid file type")?["warning","complete"]:["error","error"];n(1,g=g||{}),n(1,g.status=Ke,g),n(1,g.message=_,g),b.dispatch(He,_)}const le=()=>b.dispatch("clear_status",g),ne=(..._)=>b.client.upload(..._),ie=()=>b.dispatch("play"),oe=()=>b.dispatch("pause"),se=()=>b.dispatch("stop"),r=()=>b.dispatch("end"),Ne=({detail:_})=>b.dispatch("share",_),Pe=({detail:_})=>b.dispatch("error",_),qe=()=>b.dispatch("clear_status",g),Ce=(..._)=>b.client.upload(..._),Ue=(..._)=>b.client.stream(..._);function Ee(_){T=_,n(23,T)}const Je=({detail:_})=>n(27,y=_),Oe=()=>b.dispatch("clear"),We=()=>b.dispatch("play"),De=()=>b.dispatch("pause"),Le=()=>b.dispatch("upload"),Te=()=>b.dispatch("stop"),Ae=()=>b.dispatch("end"),Fe=()=>b.dispatch("start_recording"),Ge=()=>b.dispatch("stop_recording");return l.$$set=_=>{"elem_id"in _&&n(2,t=_.elem_id),"elem_classes"in _&&n(3,i=_.elem_classes),"visible"in _&&n(4,o=_.visible),"value"in _&&n(0,u=_.value),"label"in _&&n(5,s=_.label),"sources"in _&&n(6,c=_.sources),"root"in _&&n(7,a=_.root),"show_label"in _&&n(8,h=_.show_label),"loading_status"in _&&n(1,g=_.loading_status),"height"in _&&n(9,m=_.height),"width"in _&&n(10,U=_.width),"webcam_constraints"in _&&n(11,P=_.webcam_constraints),"container"in _&&n(12,d=_.container),"scale"in _&&n(13,k=_.scale),"min_width"in _&&n(14,V=_.min_width),"autoplay"in _&&n(15,B=_.autoplay),"show_share_button"in _&&n(16,I=_.show_share_button),"show_download_button"in _&&n(17,A=_.show_download_button),"gradio"in _&&n(18,b=_.gradio),"interactive"in _&&n(19,F=_.interactive),"mirror_webcam"in _&&n(20,G=_.mirror_webcam),"include_audio"in _&&n(21,v=_.include_audio),"loop"in _&&n(22,Z=_.loop),"input_ready"in _&&n(31,H=_.input_ready)},l.$$.update=()=>{l.$$.dirty[0]&8388608&&n(31,H=!T),l.$$.dirty[0]&1|l.$$.dirty[1]&4&&u&&C===null&&n(33,C=u),l.$$.dirty[0]&16777280&&c&&!M&&n(24,M=c[0]),l.$$.dirty[0]&1&&(u!=null?(n(25,K=u.video),n(26,q=u.subtitles)):(n(25,K=null),n(26,q=null))),l.$$.dirty[0]&262145|l.$$.dirty[1]&2&&JSON.stringify(u)!==JSON.stringify(f)&&(n(32,f=u),b.dispatch("change"))},[u,g,t,i,o,s,c,a,h,m,U,P,d,k,V,B,I,A,b,F,G,v,Z,T,M,K,q,y,x,ee,te,H,f,C,le,ne,ie,oe,se,r,Ne,Pe,qe,Ce,Ue,Ee,Je,Oe,We,De,Le,Te,Ae,Fe,Ge]}class Jt extends wt{constructor(e){super(),Bt(this,e,Et,Ut,It,{elem_id:2,elem_classes:3,visible:4,value:0,label:5,sources:6,root:7,show_label:8,loading_status:1,height:9,width:10,webcam_constraints:11,container:12,scale:13,min_width:14,autoplay:15,show_share_button:16,show_download_button:17,gradio:18,interactive:19,mirror_webcam:20,include_audio:21,loop:22,input_ready:31},null,[-1,-1])}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),w()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),w()}get visible(){return this.$$.ctx[4]}set visible(e){this.$$set({visible:e}),w()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),w()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),w()}get sources(){return this.$$.ctx[6]}set sources(e){this.$$set({sources:e}),w()}get root(){return this.$$.ctx[7]}set root(e){this.$$set({root:e}),w()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),w()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),w()}get height(){return this.$$.ctx[9]}set height(e){this.$$set({height:e}),w()}get width(){return this.$$.ctx[10]}set width(e){this.$$set({width:e}),w()}get webcam_constraints(){return this.$$.ctx[11]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),w()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),w()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),w()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),w()}get autoplay(){return this.$$.ctx[15]}set autoplay(e){this.$$set({autoplay:e}),w()}get show_share_button(){return this.$$.ctx[16]}set show_share_button(e){this.$$set({show_share_button:e}),w()}get show_download_button(){return this.$$.ctx[17]}set show_download_button(e){this.$$set({show_download_button:e}),w()}get gradio(){return this.$$.ctx[18]}set gradio(e){this.$$set({gradio:e}),w()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),w()}get mirror_webcam(){return this.$$.ctx[20]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),w()}get include_audio(){return this.$$.ctx[21]}set include_audio(e){this.$$set({include_audio:e}),w()}get loop(){return this.$$.ctx[22]}set loop(e){this.$$set({loop:e}),w()}get input_ready(){return this.$$.ctx[31]}set input_ready(e){this.$$set({input_ready:e}),w()}}const Vl=Jt;export{Pl as BaseExample,gt as BaseInteractiveVideo,ye as BasePlayer,$e as BaseStaticVideo,Vl as default,jl as loaded,Ze as playable,ge as prettyBytes};
//# sourceMappingURL=index-D_7Fqv5G.js.map
