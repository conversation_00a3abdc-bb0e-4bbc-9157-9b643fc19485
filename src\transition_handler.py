"""
过渡处理模块
用于处理章节之间的无缝过渡，确保前一章的结尾与下一章的开头自然衔接
"""

import re
from typing import Optional, Tuple

def extract_last_paragraph(content: str, num_paragraphs: int = 3) -> str:
    """
    提取章节内容的最后几个段落
    
    参数:
        content: 章节内容
        num_paragraphs: 要提取的段落数量，默认为3
        
    返回:
        最后几个段落组合成的字符串
    """
    paragraphs = content.split('\n\n')
    # 过滤掉空段落
    paragraphs = [p for p in paragraphs if p.strip()]
    
    # 如果没有段落，返回空字符串
    if not paragraphs:
        return ""
    
    # 过滤掉可能的"章节结束"标记
    filtered_paragraphs = []
    for i in range(len(paragraphs) - 1, -1, -1):
        paragraph = paragraphs[i].strip()
        # 跳过章节结束标记
        if "章结束" in paragraph or "章完" in paragraph or "未完待续" in paragraph:
            continue
        filtered_paragraphs.insert(0, paragraph)
        if len(filtered_paragraphs) >= num_paragraphs:
            break
    
    # 如果没有有效段落，返回原始最后一个段落
    if not filtered_paragraphs:
        return paragraphs[-1] if paragraphs else ""
    
    # 将最后几个段落组合成一个字符串
    return "\n\n".join(filtered_paragraphs)

def create_transition(prev_chapter_content: str, next_chapter_outline: str) -> str:
    """
    创建从前一章到下一章的过渡段落
    
    参数:
        prev_chapter_content: 前一章的内容
        next_chapter_outline: 下一章的大纲
        
    返回:
        过渡段落
    """
    # 提取前一章的最后几个段落
    last_paragraphs = extract_last_paragraph(prev_chapter_content)
    
    # 如果没有提取到有效的段落，返回空字符串
    if not last_paragraphs:
        return ""
    
    # 提取关键信息
    # 1. 人物名称
    character_names = re.findall(r'[\u4e00-\u9fa5]{2,3}', last_paragraphs)
    character_names = [name for name in character_names if len(name) >= 2]
    
    # 2. 情感状态
    emotions = []
    emotion_keywords = ["高兴", "兴奋", "愤怒", "悲伤", "恐惧", "惊讶", "厌恶", "期待", 
                        "焦虑", "担忧", "满足", "失望", "困惑", "好奇", "紧张", "放松"]
    for emotion in emotion_keywords:
        if emotion in last_paragraphs:
            emotions.append(emotion)
    
    # 3. 场景描述
    scenes = re.findall(r'在[\u4e00-\u9fa5]{2,10}[里中内]', last_paragraphs)
    
    # 4. 时间描述
    times = re.findall(r'[早中晚][晨上午]|[黎破]明|黄昏|傍晚|深夜|凌晨', last_paragraphs)
    
    # 构建过渡段落
    # 使用最后一个段落的前100个字符作为过渡的开始
    last_paragraph = last_paragraphs.split('\n\n')[-1] if '\n\n' in last_paragraphs else last_paragraphs
    transition = last_paragraph[:100] + "..."
    
    # 根据下一章大纲添加过渡提示
    if "本章核心事件" in next_chapter_outline:
        core_events_match = re.search(r'本章核心事件\*\*：(.+?)(?:\n|$)', next_chapter_outline)
        if core_events_match:
            core_events = core_events_match.group(1).strip()
            transition += f"\n\n这一刻的决定，将引领他们走向{core_events[:20]}..."
    
    return transition

def ensure_chapter_connection(prev_chapter: str, next_chapter: str) -> Tuple[str, str]:
    """
    确保两个章节之间的连接自然流畅
    
    参数:
        prev_chapter: 前一章的内容
        next_chapter: 下一章的内容
        
    返回:
        修改后的前一章和下一章内容
    """
    # 提取前一章的最后几个段落
    last_paragraphs = extract_last_paragraph(prev_chapter)
    last_paragraph = last_paragraphs.split('\n\n')[-1] if '\n\n' in last_paragraphs else last_paragraphs
    
    # 提取下一章的第一个段落（跳过标题）
    next_lines = next_chapter.split('\n\n')
    first_paragraph = ""
    for line in next_lines[1:]:  # 跳过标题
        if line.strip():
            first_paragraph = line
            break
    
    # 如果没有提取到有效的段落，直接返回原内容
    if not last_paragraph or not first_paragraph:
        return prev_chapter, next_chapter
    
    # 检查是否需要添加过渡段落
    needs_transition = True
    
    # 1. 检查时间连续性
    time_patterns = {
        "前一章结束时间": r'([早中晚][晨上午]|[黎破]明|黄昏|傍晚|深夜|凌晨)',
        "下一章开始时间": r'([早中晚][晨上午]|[黎破]明|黄昏|傍晚|深夜|凌晨)'
    }
    
    prev_time = re.search(time_patterns["前一章结束时间"], last_paragraph)
    next_time = re.search(time_patterns["下一章开始时间"], first_paragraph)
    
    if prev_time and next_time and prev_time.group(1) == next_time.group(1):
        needs_transition = False
    
    # 2. 检查场景连续性
    scene_patterns = {
        "前一章结束场景": r'在([\u4e00-\u9fa5]{2,10}[里中内])',
        "下一章开始场景": r'在([\u4e00-\u9fa5]{2,10}[里中内])'
    }
    
    prev_scene = re.search(scene_patterns["前一章结束场景"], last_paragraph)
    next_scene = re.search(scene_patterns["下一章开始场景"], first_paragraph)
    
    if prev_scene and next_scene and prev_scene.group(1) == next_scene.group(1):
        needs_transition = False
    
    # 3. 检查人物连续性
    character_pattern = r'([\u4e00-\u9fa5]{2,3})'
    prev_characters = set(re.findall(character_pattern, last_paragraph))
    next_characters = set(re.findall(character_pattern, first_paragraph))
    
    if prev_characters and next_characters and prev_characters.intersection(next_characters):
        needs_transition = False
    
    # 如果需要添加过渡段落
    if needs_transition:
        # 创建过渡段落，使用最后一段的前50个字符
        transition_paragraph = f"\n\n{last_paragraph[:50]}...\n\n"
        
        # 修改下一章的开头，添加过渡段落
        next_chapter_parts = next_chapter.split('\n\n', 1)
        if len(next_chapter_parts) > 1:
            next_chapter = next_chapter_parts[0] + transition_paragraph + next_chapter_parts[1]
        else:
            next_chapter = next_chapter + transition_paragraph
    
    return prev_chapter, next_chapter

def create_chapter_ending(chapter_content: str, next_chapter_outline: Optional[str] = None) -> str:
    """
    创建章节结尾，使读者期待下一章
    
    参数:
        chapter_content: 章节内容
        next_chapter_outline: 下一章的大纲（如果有）
        
    返回:
        添加了结尾的章节内容
    """
    # 检查章节是否已经有结尾
    if "章结束" in chapter_content or "章完" in chapter_content:
        return chapter_content
    
    # 提取章节标题
    title_match = re.search(r'^第\d+章[：:](.*?)$', chapter_content, re.MULTILINE)
    chapter_title = title_match.group(1).strip() if title_match else "未知标题"
    
    # 创建基本结尾
    ending = f"\n\n这一章的故事暂告一段落，但{chapter_title}的旅程才刚刚开始。"
    
    # 如果有下一章大纲，添加悬念
    if next_chapter_outline:
        if "本章核心事件" in next_chapter_outline:
            core_events_match = re.search(r'本章核心事件\*\*：(.+?)(?:\n|$)', next_chapter_outline)
            if core_events_match:
                core_events = core_events_match.group(1).strip()
                ending += f"接下来，{core_events[:30]}...这将会是怎样的发展？请继续阅读下一章。"
        else:
            ending += "接下来会发生什么？请继续阅读下一章。"
    
    return chapter_content + ending

if __name__ == "__main__":
    # 测试代码
    prev_content = """第1章：开篇

这是第一章的内容。
主角李明站在高楼上，俯瞰着整个城市。
夜幕降临，城市的灯光如同繁星点点。
他深吸一口气，知道自己的决定将改变一切。

第1章结束。
"""

    next_outline = """#### **第2章：冲突与行动**
1. **章节标题**：冲突与行动
2. **本章核心事件**：李明遇到了一个重大的障碍。场景从城市转移到了一个新的地点。冲突开始升级，紧张感增加。
3. **本章出场角色**：李明
4. **本章与整体故事的关系**：推动情节发展，加深故事冲突，展示主角的成长。
5. **本章情感基调**：紧张、激烈、决然。
"""

    transition = create_transition(prev_content, next_outline)
    print("生成的过渡段落:")
    print(transition)
    
    next_content = """第2章：冲突与行动

清晨，阳光洒进窗户，李明早早地起床了。
昨晚的决定让他辗转难眠，但他知道自己必须行动起来。
他收拾好行李，准备前往那个神秘的地点。

第2章结束。
"""
    
    prev_modified, next_modified = ensure_chapter_connection(prev_content, next_content)
    print("\n修改后的章节连接:")
    print("前一章结尾:")
    print(prev_modified[-200:])
    print("\n下一章开头:")
    print(next_modified[:200])
    
    chapter_with_ending = create_chapter_ending(prev_content, next_outline)
    print("\n添加结尾后的章节:")
    print(chapter_with_ending[-200:])
