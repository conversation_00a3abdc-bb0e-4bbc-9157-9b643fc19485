"""
Advanced Document Reader Module

This module provides a professional reader interface for various document formats.
"""

import os
import sys
import json
import gradio as gr
import markdown
import tempfile
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path

# 尝试导入docx库，用于解析Word文档
try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("Warning: python-docx not available. Word documents will not be supported.")

# 尝试导入PDF库，用于解析PDF文档
try:
    import fitz  # PyMuPDF
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("Warning: PyMuPDF not available. PDF documents will not be supported.")

# 支持的文件类型
SUPPORTED_EXTENSIONS = {
    ".txt": "text",
    ".md": "markdown",
    ".json": "json",
    ".docx": "docx" if DOCX_AVAILABLE else "unsupported",
    ".doc": "docx" if DOCX_AVAILABLE else "unsupported",
    ".pdf": "pdf" if PDF_AVAILABLE else "unsupported",
    ".py": "code",
    ".js": "code",
    ".html": "code",
    ".css": "code",
    ".java": "code",
    ".c": "code",
    ".cpp": "code",
    ".h": "code",
    ".hpp": "code",
    ".cs": "code",
    ".go": "code",
    ".rs": "code",
    ".swift": "code",
    ".kt": "code",
    ".ts": "code",
    ".jsx": "code",
    ".tsx": "code",
}

# 书签存储路径
BOOKMARKS_FILE = os.path.join(os.path.dirname(__file__), "reader_bookmarks.json")

class AdvancedReader:
    """Advanced document reader with support for multiple formats"""

    def __init__(self):
        """Initialize the reader"""
        self.current_file = None
        self.current_content = None
        self.current_format = None
        self.bookmarks = self.load_bookmarks()
        self.history = []

    def load_bookmarks(self) -> Dict[str, Dict[str, Any]]:
        """Load bookmarks from file"""
        if os.path.exists(BOOKMARKS_FILE):
            try:
                with open(BOOKMARKS_FILE, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading bookmarks: {e}")
        return {}

    def save_bookmarks(self) -> None:
        """Save bookmarks to file"""
        try:
            with open(BOOKMARKS_FILE, "w", encoding="utf-8") as f:
                json.dump(self.bookmarks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving bookmarks: {e}")

    def add_bookmark(self, file_path: str, position: int, name: Optional[str] = None) -> None:
        """
        Add a bookmark

        Args:
            file_path: Path to the file
            position: Position in the file (line number or page number)
            name: Bookmark name (optional)
        """
        if not name:
            # Generate a name based on the file name and position
            file_name = os.path.basename(file_path)
            name = f"{file_name} - Position {position}"

        # Add to bookmarks
        if file_path not in self.bookmarks:
            self.bookmarks[file_path] = {"positions": {}}

        self.bookmarks[file_path]["positions"][str(position)] = {
            "name": name,
            "added_at": import_datetime().now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # Save bookmarks
        self.save_bookmarks()

    def remove_bookmark(self, file_path: str, position: int) -> None:
        """
        Remove a bookmark

        Args:
            file_path: Path to the file
            position: Position in the file
        """
        if file_path in self.bookmarks and str(position) in self.bookmarks[file_path]["positions"]:
            del self.bookmarks[file_path]["positions"][str(position)]

            # If no more positions, remove the file entry
            if not self.bookmarks[file_path]["positions"]:
                del self.bookmarks[file_path]

            # Save bookmarks
            self.save_bookmarks()

    def get_bookmarks(self, file_path: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get bookmarks

        Args:
            file_path: Path to the file (optional, if None, return all bookmarks)

        Returns:
            List of bookmarks
        """
        if file_path:
            if file_path in self.bookmarks:
                return [
                    {
                        "file_path": file_path,
                        "position": int(position),
                        "name": data["name"],
                        "added_at": data["added_at"]
                    }
                    for position, data in self.bookmarks[file_path]["positions"].items()
                ]
            return []

        # Return all bookmarks
        all_bookmarks = []
        for file_path, data in self.bookmarks.items():
            for position, bookmark_data in data["positions"].items():
                all_bookmarks.append({
                    "file_path": file_path,
                    "position": int(position),
                    "name": bookmark_data["name"],
                    "added_at": bookmark_data["added_at"]
                })

        # Sort by added_at (newest first)
        all_bookmarks.sort(key=lambda x: x["added_at"], reverse=True)
        return all_bookmarks

    def add_to_history(self, file_path: str) -> None:
        """
        Add a file to history

        Args:
            file_path: Path to the file
        """
        # Remove if already in history
        if file_path in self.history:
            self.history.remove(file_path)

        # Add to the beginning
        self.history.insert(0, file_path)

        # Keep only the last 10 items
        self.history = self.history[:10]

    def get_history(self) -> List[str]:
        """
        Get history

        Returns:
            List of file paths
        """
        return self.history

    def read_text_file(self, file_path: str) -> str:
        """
        Read a text file

        Args:
            file_path: Path to the file

        Returns:
            File content
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encodings
            try:
                with open(file_path, "r", encoding="gbk") as f:
                    return f.read()
            except UnicodeDecodeError:
                try:
                    with open(file_path, "r", encoding="latin-1") as f:
                        return f.read()
                except Exception as e:
                    return f"Error reading file: {e}"
        except Exception as e:
            return f"Error reading file: {e}"

    def read_markdown_file(self, file_path: str) -> str:
        """
        Read a Markdown file

        Args:
            file_path: Path to the file

        Returns:
            File content (HTML)
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Convert Markdown to HTML
            html = markdown.markdown(content, extensions=['tables', 'fenced_code'])

            # Add CSS for better styling
            styled_html = f"""
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }}
                h1, h2, h3, h4, h5, h6 {{ color: #333; margin-top: 24px; margin-bottom: 16px; }}
                h1 {{ font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }}
                h2 {{ font-size: 1.5em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }}
                p {{ margin-top: 0; margin-bottom: 16px; }}
                code {{ font-family: Consolas, monospace; background-color: #f6f8fa; padding: 0.2em 0.4em; border-radius: 3px; }}
                pre {{ background-color: #f6f8fa; border-radius: 3px; padding: 16px; overflow: auto; }}
                pre code {{ background-color: transparent; padding: 0; }}
                blockquote {{ margin: 0; padding: 0 1em; color: #6a737d; border-left: 0.25em solid #dfe2e5; }}
                table {{ border-collapse: collapse; width: 100%; }}
                table th, table td {{ border: 1px solid #ddd; padding: 6px 13px; }}
                table tr {{ background-color: #fff; border-top: 1px solid #c6cbd1; }}
                table tr:nth-child(2n) {{ background-color: #f6f8fa; }}
                img {{ max-width: 100%; }}
            </style>
            {html}
            """

            return styled_html
        except Exception as e:
            return f"Error reading Markdown file: {e}"

    def read_docx_file(self, file_path: str) -> str:
        """
        Read a Word document

        Args:
            file_path: Path to the file

        Returns:
            File content (HTML)
        """
        if not DOCX_AVAILABLE:
            return "Word document support is not available. Please install python-docx."

        try:
            doc = Document(file_path)

            # Extract text with formatting
            html_parts = ["<div class='word-document'>"]

            # Process paragraphs
            for para in doc.paragraphs:
                if not para.text.strip():
                    html_parts.append("<p>&nbsp;</p>")
                    continue

                # Check paragraph style
                style = para.style.name.lower()
                if "heading" in style:
                    level = int(style.replace("heading", "").strip()) if style != "heading" else 1
                    level = max(1, min(level, 6))  # Ensure level is between 1 and 6
                    html_parts.append(f"<h{level}>{para.text}</h{level}>")
                else:
                    html_parts.append(f"<p>{para.text}</p>")

            # Process tables
            for table in doc.tables:
                table_html = ["<table border='1' cellpadding='3'>"]
                for row in table.rows:
                    table_html.append("<tr>")
                    for cell in row.cells:
                        table_html.append(f"<td>{cell.text}</td>")
                    table_html.append("</tr>")
                table_html.append("</table>")
                html_parts.append("".join(table_html))

            html_parts.append("</div>")

            # Add CSS for better styling
            styled_html = f"""
            <style>
                .word-document {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }}
                h1, h2, h3, h4, h5, h6 {{ color: #333; margin-top: 24px; margin-bottom: 16px; }}
                h1 {{ font-size: 2em; }}
                h2 {{ font-size: 1.5em; }}
                p {{ margin-top: 0; margin-bottom: 16px; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 16px; }}
                table th, table td {{ border: 1px solid #ddd; padding: 6px 13px; }}
                table tr {{ background-color: #fff; }}
            </style>
            {"".join(html_parts)}
            """

            return styled_html
        except Exception as e:
            return f"Error reading Word document: {e}"

    def read_pdf_file(self, file_path: str) -> Tuple[str, int]:
        """
        Read a PDF document

        Args:
            file_path: Path to the file

        Returns:
            Tuple of (HTML content, total pages)
        """
        if not PDF_AVAILABLE:
            return "PDF support is not available. Please install PyMuPDF (fitz).", 0

        try:
            doc = fitz.open(file_path)
            total_pages = len(doc)

            # Extract text from first page as a preview
            text = doc[0].get_text()

            # Create a simple HTML preview
            html = f"""
            <div class="pdf-preview">
                <h2>PDF Document Preview</h2>
                <p>Total pages: {total_pages}</p>
                <div class="pdf-content">
                    <pre>{text}</pre>
                </div>
            </div>
            """

            return html, total_pages
        except Exception as e:
            return f"Error reading PDF file: {e}", 0

    def open_file(self, file_path: str) -> Dict[str, Any]:
        """
        Open a file

        Args:
            file_path: Path to the file

        Returns:
            Dictionary with file information
        """
        if not os.path.exists(file_path):
            return {
                "success": False,
                "error": f"File not found: {file_path}",
                "content": f"Error: File not found: {file_path}",
                "format": None
            }

        # Get file extension
        ext = os.path.splitext(file_path)[1].lower()

        # Check if supported
        if ext not in SUPPORTED_EXTENSIONS:
            return {
                "success": False,
                "error": f"Unsupported file format: {ext}",
                "content": f"Error: Unsupported file format: {ext}",
                "format": None
            }

        # Read file based on format
        format_type = SUPPORTED_EXTENSIONS[ext]
        content = None
        total_pages = 1

        if format_type == "text":
            content = self.read_text_file(file_path)
        elif format_type == "markdown":
            content = self.read_markdown_file(file_path)
        elif format_type == "docx":
            content = self.read_docx_file(file_path)
        elif format_type == "pdf":
            content, total_pages = self.read_pdf_file(file_path)
        elif format_type == "code" or format_type == "json":
            # 代码文件使用普通文本读取，但添加语法高亮
            raw_content = self.read_text_file(file_path)
            # 添加代码高亮
            content = f"""
            <style>
                pre.code {{ background-color: #f6f8fa; padding: 16px; border-radius: 6px; overflow: auto; }}
                code {{ font-family: Consolas, Monaco, 'Andale Mono', monospace; }}
            </style>
            <pre class="code"><code>{raw_content}</code></pre>
            """

        # Update current file
        self.current_file = file_path
        self.current_content = content
        self.current_format = format_type

        # Add to history
        self.add_to_history(file_path)

        return {
            "success": True,
            "content": content,
            "format": format_type,
            "file_path": file_path,
            "file_name": os.path.basename(file_path),
            "total_pages": total_pages
        }

def import_datetime():
    """Import datetime module (to avoid circular imports)"""
    from datetime import datetime
    return datetime

def create_reader_ui() -> gr.Blocks:
    """
    Create reader UI

    Returns:
        Gradio Blocks interface
    """
    reader = AdvancedReader()

    with gr.Blocks(title="高级文档阅读器", theme=gr.themes.Soft()) as ui:
        # 创建状态框，用于显示日志
        log_output = gr.Textbox(label="日志", visible=False, lines=3)

        with gr.Row():
            gr.Markdown("# 高级文档阅读器")

        with gr.Row():
            gr.Markdown("专业的本地文档阅读器，支持TXT、Markdown和Word格式。")

        with gr.Row():
            with gr.Column(scale=1):
                file_input = gr.File(label="选择文件", file_types=[".txt", ".md", ".docx", ".doc"])

                with gr.Accordion("最近打开的文件", open=True):
                    history_list = gr.Dropdown(
                        choices=[],
                        label="历史记录",
                        interactive=True
                    )

                with gr.Accordion("阅读设置", open=True):
                    theme_selector = gr.Radio(
                        choices=["浅色", "深色", "护眼模式"],
                        label="主题",
                        value="浅色"
                    )
                    font_size = gr.Slider(
                        minimum=12,
                        maximum=24,
                        value=16,
                        step=1,
                        label="字体大小"
                    )

            with gr.Column(scale=3):
                file_info = gr.Markdown("## 请选择一个文件开始阅读")

                # For text and markdown
                content_display = gr.HTML(
                    label="文档内容",
                    visible=True
                )

        # Hidden state
        current_file_path = gr.State("")
        current_format = gr.State("")
        current_page = gr.State(1)
        total_pages = gr.State(1)

        # 创建PDF控件
        pdf_controls = gr.Row(visible=False)
        with pdf_controls:
            pdf_page_number = gr.Number(
                value=1,
                label="页码",
                minimum=1,
                step=1
            )
            pdf_prev_btn = gr.Button("上一页")
            pdf_next_btn = gr.Button("下一页")

        # Functions
        def update_content_display(theme, font_size):
            """Update content display based on theme and font size"""
            if not reader.current_content:
                return reader.current_content

            # Get base CSS
            if theme == "浅色":
                base_css = """
                body { background-color: #ffffff; color: #333333; }
                """
            elif theme == "深色":
                base_css = """
                body { background-color: #222222; color: #e0e0e0; }
                h1, h2, h3, h4, h5, h6 { color: #ffffff; }
                a { color: #4da6ff; }
                code { background-color: #333333; }
                pre { background-color: #333333; }
                blockquote { color: #a0a0a0; border-left-color: #444444; }
                table th, table td { border-color: #444444; }
                table tr { background-color: #222222; }
                table tr:nth-child(2n) { background-color: #333333; }
                """
            else:  # 护眼模式
                base_css = """
                body { background-color: #f5f5dc; color: #333333; }
                pre, code { background-color: #e8e8d0; }
                """

            # Add font size
            font_css = f"""
            body {{ font-size: {font_size}px; }}
            """

            # Combine CSS
            css = f"<style>{base_css}{font_css}</style>"

            # Add CSS to content
            if "<style>" in reader.current_content:
                # Replace existing style
                return reader.current_content.replace("<style>", css.replace("<style>", ""))
            else:
                # Add style
                return css + reader.current_content

        def open_file_from_upload(file):
            """Open file from upload"""
            if not file:
                return (
                    "## 请选择一个文件开始阅读",
                    gr.update(visible=True),
                    gr.update(visible=False),
                    "",
                    "",
                    1,
                    1
                )

            # Get file path
            file_path = file.name

            # Open file
            result = reader.open_file(file_path)

            if not result["success"]:
                return (
                    f"## 错误: {result['error']}",
                    gr.update(value=result["content"], visible=True),
                    gr.update(visible=False),
                    "",
                    "",
                    1,
                    1
                )

            # Update UI based on format
            if result["format"] == "PDF":
                return (
                    f"## {result['file_name']} (PDF, {result['total_pages']} 页)",
                    gr.update(value=result["content"], visible=True),
                    gr.update(visible=True),
                    result["file_path"],
                    result["format"],
                    1,
                    result["total_pages"]
                )
            else:
                return (
                    f"## {result['file_name']} ({result['format']})",
                    gr.update(value=result["content"], visible=True),
                    gr.update(visible=False),
                    result["file_path"],
                    result["format"],
                    1,
                    1
                )

        def open_file_from_path(file_path):
            """Open file from path"""
            if not file_path:
                return (
                    "## 请选择一个文件开始阅读",
                    gr.update(visible=True),
                    gr.update(visible=False),
                    "",
                    "",
                    1,
                    1
                )

            # Open file
            result = reader.open_file(file_path)

            if not result["success"]:
                return (
                    f"## 错误: {result['error']}",
                    gr.update(value=result["content"], visible=True),
                    gr.update(visible=False),
                    "",
                    "",
                    1,
                    1
                )

            # Update UI based on format
            if result["format"] == "PDF":
                return (
                    f"## {result['file_name']} (PDF, {result['total_pages']} 页)",
                    gr.update(value=result["content"], visible=True),
                    gr.update(visible=True),
                    result["file_path"],
                    result["format"],
                    1,
                    result["total_pages"]
                )
            else:
                return (
                    f"## {result['file_name']} ({result['format']})",
                    gr.update(value=result["content"], visible=True),
                    gr.update(visible=False),
                    result["file_path"],
                    result["format"],
                    1,
                    1
                )

        def get_history_list():
            """Get history list"""
            history = reader.get_history()
            return gr.update(choices=history, value=history[0] if history else None)

        def navigate_pdf(file_path, current_format, page_num, total_pages):
            """Navigate PDF"""
            if not file_path or current_format != "PDF":
                return gr.update(value="请先打开一个PDF文件")

            # Ensure page number is valid
            page_num = max(1, min(page_num, total_pages))

            try:
                # Open PDF
                doc = fitz.open(file_path)

                # Get page content
                text = doc[page_num - 1].get_text()

                # Create HTML
                html = f"""
                <div class="pdf-preview">
                    <h2>PDF Document - Page {page_num} of {total_pages}</h2>
                    <div class="pdf-content">
                        <pre>{text}</pre>
                    </div>
                </div>
                """

                return gr.update(value=html)
            except Exception as e:
                return gr.update(value=f"Error reading PDF page: {e}")

        def prev_page(current_page, total_pages):
            """Go to previous page"""
            new_page = max(1, current_page - 1)
            return new_page

        def next_page(current_page, total_pages):
            """Go to next page"""
            new_page = min(total_pages, current_page + 1)
            return new_page

        # Connect events
        # 添加日志显示函数
        def update_log(message):
            return gr.update(value=message, visible=True)

        # 定义一个包装函数，处理文件上传
        def file_upload_handler(file):
            if not file:
                return (
                    "## 请选择一个文件开始阅读",
                    gr.update(visible=True),
                    gr.update(visible=False),
                    "",
                    "",
                    1,
                    1,
                    gr.update(value="", visible=False)
                )

            # 打开文件
            result = open_file_from_upload(file)

            # 更新日志
            log_message = f"正在打开文件: {file.name}"

            return (
                result[0],  # file_info
                result[1],  # content_display
                result[2],  # pdf_controls
                result[3],  # current_file_path
                result[4],  # current_format
                result[5],  # current_page
                result[6],  # total_pages
                gr.update(value=log_message, visible=True)  # log_output
            )

        # 定义一个包装函数，处理历史记录选择
        def history_change_handler(path):
            if not path:
                return (
                    "## 请选择一个文件开始阅读",
                    gr.update(visible=True),
                    gr.update(visible=False),
                    "",
                    "",
                    1,
                    1,
                    gr.update(value="", visible=False)
                )

            # 打开文件
            result = open_file_from_path(path)

            # 更新日志
            log_message = f"正在打开文件: {path}"

            return (
                result[0],  # file_info
                result[1],  # content_display
                result[2],  # pdf_controls
                result[3],  # current_file_path
                result[4],  # current_format
                result[5],  # current_page
                result[6],  # total_pages
                gr.update(value=log_message, visible=True)  # log_output
            )

        # 定义一个包装函数，处理PDF导航
        def pdf_navigation_handler(path, fmt, page, total):
            # 导航到PDF页面
            content_result = navigate_pdf(path, fmt, page, total)

            # 更新日志
            log_message = f"正在浏览第{page}页"

            return (
                content_result,
                gr.update(value=log_message, visible=True)
            )

        file_input.upload(
            fn=file_upload_handler,
            inputs=[file_input],
            outputs=[
                file_info, content_display, pdf_controls, current_file_path,
                current_format, current_page, total_pages, log_output
            ]
        )

        history_list.change(
            fn=history_change_handler,
            inputs=[history_list],
            outputs=[
                file_info, content_display, pdf_controls, current_file_path,
                current_format, current_page, total_pages, log_output
            ]
        )

        theme_selector.change(
            fn=update_content_display,
            inputs=[theme_selector, font_size],
            outputs=[content_display]
        )

        font_size.change(
            fn=update_content_display,
            inputs=[theme_selector, font_size],
            outputs=[content_display]
        )

        # PDF导航功能
        pdf_page_number.change(
            fn=pdf_navigation_handler,
            inputs=[current_file_path, current_format, pdf_page_number, total_pages],
            outputs=[content_display, log_output]
        )

        pdf_prev_btn.click(
            fn=prev_page,
            inputs=[current_page, total_pages],
            outputs=[current_page]
        ).then(
            fn=pdf_navigation_handler,
            inputs=[current_file_path, current_format, current_page, total_pages],
            outputs=[content_display, log_output]
        )

        pdf_next_btn.click(
            fn=next_page,
            inputs=[current_page, total_pages],
            outputs=[current_page]
        ).then(
            fn=pdf_navigation_handler,
            inputs=[current_file_path, current_format, current_page, total_pages],
            outputs=[content_display, log_output]
        )

        # Initialize
        ui.load(
            fn=get_history_list,
            inputs=[],
            outputs=[history_list]
        )

        return ui

def browse_folder(folder_path: str) -> list[dict]:
    """浏览文件夹并返回文件列表

    Args:
        folder_path: 文件夹路径

    Returns:
        文件列表
    """
    files = []
    try:
        # 确保路径存在
        if not os.path.exists(folder_path):
            return []

        # 遍历文件夹
        for item in os.listdir(folder_path):
            item_path = os.path.join(folder_path, item)

            # 获取文件类型
            if os.path.isdir(item_path):
                item_type = "folder"
            else:
                # 检查文件扩展名
                ext = os.path.splitext(item)[1].lower()
                item_type = SUPPORTED_EXTENSIONS.get(ext, "unsupported")

            # 添加到文件列表
            files.append({
                "name": item,
                "path": item_path,
                "type": item_type,
                "size": os.path.getsize(item_path) if os.path.isfile(item_path) else 0,
                "modified": os.path.getmtime(item_path)
            })

        # 按类型和名称排序：文件夹在前，然后是文件
        files.sort(key=lambda x: (0 if x["type"] == "folder" else 1, x["name"]))

    except Exception as e:
        print(f"浏览文件夹时出错: {e}")

    return files

def create_folder_browser_ui():
    """创建文件夹浏览器UI"""
    reader = AdvancedReader()

    with gr.Blocks(title="高级阅读器 - 文件浏览器") as ui:
        gr.Markdown("# 高级阅读器")

        with gr.Row():
            with gr.Column(scale=1):
                # 添加文件夹路径输入
                folder_path = gr.Textbox(
                    label="文件夹路径",
                    placeholder="输入文件夹路径",
                    value=os.getcwd()
                )
                browse_btn = gr.Button("浏览")

                # 添加文件列表
                file_list = gr.Dataframe(
                    headers=["名称", "类型", "大小 (KB)"],
                    label="文件列表",
                    interactive=True,
                    row_count=10
                )

            with gr.Column(scale=2):
                file_info = gr.Markdown("## 请选择一个文件开始阅读")
                content_display = gr.HTML(
                    label="文档内容",
                    visible=True
                )

        # 隐藏状态
        current_file_path = gr.State("")

        # 日志输出
        log_output = gr.Markdown("", visible=False)

        # 函数定义
        def update_file_list(path):
            """更新文件列表"""
            try:
                # 检查路径是否存在
                if not os.path.exists(path):
                    return [], gr.update(value=f"错误: 路径不存在: {path}", visible=True)

                # 检查是否是文件夹
                if not os.path.isdir(path):
                    return [], gr.update(value=f"错误: 不是有效的文件夹: {path}", visible=True)

                # 获取文件列表
                files = browse_folder(path)

                # 格式化显示
                display_files = []
                for file in files:
                    size_kb = round(file["size"] / 1024, 2) if file["type"] != "folder" else "-"
                    display_files.append([file["name"], file["type"], size_kb])

                return display_files, gr.update(value=f"已加载文件夹: {path}", visible=True)
            except Exception as e:
                return [], gr.update(value=f"加载文件夹时出错: {e}", visible=True)

        def open_selected_file(selected_data, folder_path):
            """打开选中的文件"""
            if not selected_data or len(selected_data) == 0:
                return (
                    "## 请选择一个文件",
                    gr.update(value="", visible=True),
                    ""
                )

            # 获取选中的文件
            file_name = selected_data[0][0]  # 第一列是文件名
            file_type = selected_data[0][1]  # 第二列是文件类型

            # 如果是文件夹，则进入该文件夹
            if file_type == "folder":
                new_path = os.path.join(folder_path, file_name)
                files, log = update_file_list(new_path)
                return (
                    f"## 文件夹: {file_name}",
                    gr.update(value="", visible=True),
                    ""
                ), new_path, files, log

            # 如果是不支持的文件类型
            if file_type == "unsupported":
                return (
                    f"## 不支持的文件类型: {file_name}",
                    gr.update(value="不支持的文件类型", visible=True),
                    ""
                ), folder_path, [], gr.update(value="不支持的文件类型", visible=True)

            # 打开文件
            file_path = os.path.join(folder_path, file_name)
            result = reader.open_file(file_path)

            if not result["success"]:
                return (
                    f"## 错误: {result['error']}",
                    gr.update(value=result["content"], visible=True),
                    file_path
                ), folder_path, [], gr.update(value=f"打开文件时出错: {result['error']}", visible=True)

            return (
                f"## {result['file_name']} ({result['format']})",
                gr.update(value=result["content"], visible=True),
                file_path
            ), folder_path, [], gr.update(value=f"已打开文件: {file_path}", visible=True)

        # 绑定事件
        browse_btn.click(
            fn=update_file_list,
            inputs=[folder_path],
            outputs=[file_list, log_output]
        )

        # 封装打开文件的函数，分别返回各个组件的更新
        def open_file_wrapper(selected_data, folder_path):
            result = open_selected_file(selected_data, folder_path)
            # 分解返回值
            file_info_update = result[0][0]
            content_display_update = result[0][1]
            current_file_path_update = result[0][2]
            folder_path_update = result[1]
            file_list_update = result[2]
            log_output_update = result[3]

            return (
                file_info_update,
                content_display_update,
                current_file_path_update,
                folder_path_update,
                file_list_update,
                log_output_update
            )

        file_list.select(
            fn=open_file_wrapper,
            inputs=[file_list, folder_path],
            outputs=[
                file_info,
                content_display,
                current_file_path,
                folder_path,
                file_list,
                log_output
            ]
        )

        # 初始化加载当前目录
        ui.load(
            fn=update_file_list,
            inputs=[folder_path],
            outputs=[file_list, log_output]
        )

        return ui

def launch_advanced_reader():
    """Launch advanced reader"""
    try:
        # 创建文件浏览器UI
        ui = create_folder_browser_ui()

        # 尝试不同的端口，避免端口冲突
        try:
            # 首选端口
            ui.queue().launch(server_name="127.0.0.1", server_port=4556, share=False, prevent_thread_lock=True)
        except OSError:
            try:
                # 备选端口
                print("端口4556被占用，尝试端口4557...")
                ui.queue().launch(server_name="127.0.0.1", server_port=4557, share=False, prevent_thread_lock=True)
            except OSError:
                # 随机端口
                print("端口4557也被占用，使用随机端口...")
                ui.queue().launch(server_name="127.0.0.1", share=False, prevent_thread_lock=True)
    except Exception as e:
        print(f"启动高级阅读器时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    launch_advanced_reader()
