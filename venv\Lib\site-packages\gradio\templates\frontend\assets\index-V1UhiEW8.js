const __vite__fileDeps=["./Index-h9bXJ_mL.js","./Block-CMfAaXj9.js","./IconButtonWrapper.svelte_svelte_type_style_lang-CPevJZa9.js","./IconButtonWrapper-BULHeAAS.css","./MarkdownCode.svelte_svelte_type_style_lang-CFmoUF5p.js","./prism-python-DO0H4w6Q.js","./MarkdownCode-CMJLBV0e.css","./index-CWG4El0O.js","./StreamingBar.svelte_svelte_type_style_lang-CDNxkBIr.js","./StreamingBar-DOagx4HU.css","./IconButton-DbC-jsk_.js","./Clear-By3xiIwg.js","./Index-CyFoS1ud.js","./Index-CptIZeFZ.css","./Index-C5NYahSl.css","./Index-DkvwFonX.js","./BlockLabel-JbMBN4MZ.js","./Empty-BgOmVBFg.js","./FullscreenButton-B-slkwu9.js","./IconButtonWrapper-DrWC4NJv.js","./file-url-DoxvUUVV.js","./DownloadLink.svelte_svelte_type_style_lang-C_5UIfol.js","./DownloadLink-CqD3Uu0l.css","./Index-E3yBBMTH.css","./StaticAudio-BcP3Ojpm.js","./utils-BsGrhMNe.js","./ShareButton-BfWeO6Sd.js","./Community-Dw1micSV.js","./Download-DVtk-Jv3.js","./Music-CDm0RGMk.js","./AudioPlayer-D_1FuSMU.js","./Trim-JQYgj7Jd.js","./Play-B0Q0U1Qz.js","./Undo-DCjBnnSO.js","./hls-CnVhpNcu.js","./AudioPlayer-BAKhejK8.css","./DownloadLink-QIttOhoR.js","./Example-BQyGztrG.js","./Example-D7K5RtQ2.css","./index-S2_6NELW.js","./InteractiveAudio-DKSUMNnC.js","./Upload--7Xp8CSk.js","./Upload-A42O3qlm.css","./ModifyUpload-DAmzHHEC.js","./Edit-BpRIf5rU.js","./SelectSource-D4OOPOCG.js","./StreamingBar-BU9S4hA7.js","./InteractiveAudio-B76TQFG-.css","./UploadText-Dp_ycz35.js","./Index-B1HmtWjh.js","./Index-DE2G75tA.js","./___vite-browser-external_commonjs-proxy-D9TgwFVw.js","./__vite-browser-external-D7Ct-6yo.js","./Index-Dmcv9J9S.js","./Button-DBmw6N-D.js","./Image-CnqB5dbD.js","./Image-B8dFOee4.css","./Button-DTh9AgeE.css","./ImagePreview-DJhr8Mfv.css","./Index--dfOxlka.js","./Check-BiRlaMNo.js","./Copy-CxQ9EyK2.js","./File-BQ_9P3Ye.js","./MarkdownCode-Cd8Z5cIb.js","./index-CQUs-3jT.js","./index-CnqicUFC.js","./Trash-RbZEwH-j.js","./Index-Ru4sKdXi.css","./Example-CZ-iEz1g.js","./Index-CW_DIAqj.js","./Info-BDZmU2Ku.js","./Index-WIAKB-_s.css","./Example-DccrJI--.js","./Index-cWMuwh75.js","./BlockTitle-BijYJada.js","./Index-DMKGW8pW.css","./Example-Wp-_4AVX.js","./Example-oomIF0ca.css","./Index-Cvh-nP3Y.js","./Code-DGNrTu_I.js","./Index-DloLYeAi.css","./Example-BaLyJYAe.js","./Example-Bw8Q_3wB.css","./Index-Dg5M8VLh.js","./tinycolor-DhRrpXkc.js","./Index-DwWu86Nh.css","./index-CmzkM4vU.js","./Embed-DkWAkqUg.js","./Example-CqPGqNav.js","./Example-1kVNej19.css","./Index-CBvqT7Px.js","./dsv-DB8NKgIY.js","./Index-DR3SLfLc.js","./ImagePreview-YDrf4kgB.js","./utils-Gtzs_Zla.js","./ImageUploader-Dxn_iV-T.js","./DropdownArrow-DuA1Ifms.js","./Square-oAGqOwsh.js","./ImageUploader-C-a3Rtgj.css","./Example-CC8yxxGn.js","./Example-DikqVAPo.css","./Index-DWGlguU2.css","./Index-BPSRurdt.js","./Example-Cx2SdskM.js","./Example-ClKJOMGh.css","./Index-D3f6Hf9S.css","./Textbox-jWD3sCxr.css","./Index-CmYP8ZA9.js","./Example-BBLMS951.js","./Index-GDHg_u1o.css","./Index-CezLPlFV.js","./Index-tcNSQSor.css","./Example-BgQNfMWT.js","./Index-DRPpdVHV.js","./Dropdown-lPfdMq6j.js","./Dropdown-CWxB-qJp.css","./Example-DrmWnoSo.js","./Example-DpWs9cEC.css","./Index-BDMvYnhH.js","./FileUpload-C0dgSKNP.js","./FileUpload-CQVu-hjH.css","./Example-CIFMxn5c.js","./Example-DfhEULNF.css","./Index-C-3T0NT0.js","./Index-BKaa_GXG.css","./Index-DE1Sah7F.js","./Index-12OnbRhk.css","./Gallery-WYXHIDmJ.js","./Video-C-llMUaJ.js","./Video-DJw86Ppo.css","./Gallery-BqrYX9d2.css","./Index-CYXp1oKN.js","./Index-WEzAIkMk.js","./Index-Cgj6KPvj.css","./Index-XYFNClev.js","./color-DX9tUfLZ.js","./Index-Dwy3Ni24.css","./Index-BBxgX-PX.js","./Index-Csm0OGa9.css","./Example-C2a4WxRl.js","./Example-CSw4pLi5.css","./Example-BtWEWnXX.js","./Example-6rv12T44.css","./Index-BLqT3gRt.js","./Index-BNTz3k9B.css","./Index-CC80w9zK.js","./Index-DItMwpQq.css","./Index-DX8QzOB3.js","./LineChart-CKh1Fdep.js","./Index-D3BKJl5I.css","./Example-CxAuiy_R.js","./Index-VHcFswDo.js","./Index-7U9UAML0.css","./Example-uQ8MuYg6.js","./Index-whzS5J2I.js","./Index-Be3F7oKw.css","./Example-B1EET6YG.js","./Example-CCTTJ5R1.css","./Index-DxLSS6WM.js","./Send-DyoOovnk.js","./Video-fsmLZWjA.js","./Index-Clg86Lny.css","./Index-BZ6fZX36.js","./Index-WdTVQ0oj.css","./Example-CqL1e7EB.js","./Index-6qq3XBNc.js","./Index-Dclo02rM.css","./Example-C9__vDgN.js","./Index-BRvupx4g.js","./Index-D_75rweD.css","./Plot-mBuBMWSx.js","./Index-BS4qfRhS.js","./Example-BoMLuz1A.js","./Index-C2istuOy.js","./Index-Abr5JeXp.css","./Index-tcAhRwtE.js","./Index-CfowPFmo.css","./Index-B3a3KgKr.js","./Index-CNbhkFly.css","./Index-BlWK1-fD.js","./Index-B0hFno2n.css","./Example-BrizabXh.js","./Index-B_OkAlP9.js","./Index-SemGnOnE.css","./index-rDz047cy.js","./Toast-C4cLYcV8.js","./Index-C2AEGyOu.js","./Tabs-Cgg632kg.js","./Tabs-C0qLuAtA.css","./Index-Gmwqb-vD.css","./Index-Bs4_V4Rr.js","./Index-B6bwX9mR.js","./Textbox-BAD5XOM4.js","./Index-D-YxDJVI.js","./Index-DYDmCduo.css","./VideoPreview-Bmn4fgzX.js","./VideoPreview-wQufNXbv.css","./Example-97koPlcW.js","./Example-B5CSTz0f.css","./index-D_7Fqv5G.js","./index-CFBZQE_H.css","./Example-DxdiEFS_.js","./Index-BjBu9-9D.js","./Index-CgDrEMlk.css","./Index-vNlY8iMv.js","./Index-BJ_RfjVB.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import*as Je from"./svelte/svelte.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))r(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function o(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(n){if(n.ep)return;n.ep=!0;const i=o(n);fetch(n.href,i)}})();const Kd="modulepreload",Yd=function(e,t){return new URL(e,t).href},Tt={},p=function(t,o,r){let n=Promise.resolve();if(o&&o.length>0){const i=document.getElementsByTagName("link"),a=document.querySelector("meta[property=csp-nonce]"),s=a?.nonce||a?.getAttribute("nonce");n=Promise.all(o.map(u=>{if(u=Yd(u,r),u in Tt)return;Tt[u]=!0;const l=u.endsWith(".css"),c=l?'[rel="stylesheet"]':"";if(!!r)for(let m=i.length-1;m>=0;m--){const h=i[m];if(h.href===u&&(!l||h.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${c}`))return;const _=document.createElement("link");if(_.rel=l?"stylesheet":Kd,l||(_.as="script",_.crossOrigin=""),_.href=u,s&&_.setAttribute("nonce",s),document.head.appendChild(_),l)return new Promise((m,h)=>{_.addEventListener("load",m),_.addEventListener("error",()=>h(new Error(`Unable to preload CSS for ${u}`)))})}))}return n.then(()=>t()).catch(i=>{const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i})};var Ke=new Intl.Collator(0,{numeric:1}).compare;function ro(e,t,o){return e=e.split("."),t=t.split("."),Ke(e[0],t[0])||Ke(e[1],t[1])||(t[2]=t.slice(2).join("."),o=/[.-]/.test(e[2]=e.slice(2).join(".")),o==/[.-]/.test(t[2])?Ke(e[2],t[2]):o?-1:1)}const Qd="host",no="queue/data",e_="queue/join",At="upload",t_="login",o_="config",r_="info",n_="runtime",i_="sleeptime",a_="heartbeat",s_="component_server",l_="reset",c_="cancel",u_="https://gradio-space-api-fetcher-v2.hf.space/api",io="This application is currently busy. Please try again. ",de="Connection errored out. ",ie="Could not resolve app config. ",d_="Could not get space status. ",__="Could not get API info. ",ft="Space metadata could not be loaded. ",p_="Invalid URL. A full URL path is required.",h_="Not authorized to access this space. ",ao="Invalid credentials. Could not login. ",m_="Login credentials are required to access this space.",g_="File system access is only available in Node.js environments",so="Root URL not found in client config",f_="Error uploading file";function lo(e,t,o){return t.startsWith("http://")||t.startsWith("https://")?o?e:t:e+t}async function Pt(e,t,o){try{return(await(await fetch(`https://huggingface.co/api/spaces/${e}/jwt`,{headers:{Authorization:`Bearer ${t}`,...o?{Cookie:o}:{}}})).json()).token||!1}catch{return!1}}function b_(e){let t={};return e.forEach(({api_name:o,id:r})=>{o&&(t[o]=r)}),t}async function w_(e){const t=this.options.hf_token?{Authorization:`Bearer ${this.options.hf_token}`}:{};if(t["Content-Type"]="application/json",typeof window<"u"&&window.gradio_config&&location.origin!=="http://localhost:9876"&&!window.gradio_config.dev_mode){const o=window.gradio_config.root,r=window.gradio_config;let n=lo(e,r.root,!1);return r.root=n,{...r,path:o}}else if(e){const o=_o(e,o_),r=await this.fetch(o,{headers:t,credentials:"include"});if(r?.status===401&&!this.options.auth)throw new Error(m_);if(r?.status===401&&this.options.auth)throw new Error(ao);if(r?.status===200){let n=await r.json();return n.path=n.path??"",n.root=e,n.dependencies?.forEach((i,a)=>{i.id===void 0&&(i.id=a)}),n}else if(r?.status===401)throw new Error(h_);throw new Error(ie)}throw new Error(ie)}async function v_(){const{http_protocol:e,host:t}=await Me(this.app_reference,this.options.hf_token);try{if(this.options.auth){const o=await co(e,t,this.options.auth,this.fetch,this.options.hf_token);o&&this.set_cookies(o)}}catch(o){throw Error(o.message)}}async function co(e,t,o,r,n){const i=new FormData;i.append("username",o?.[0]),i.append("password",o?.[1]);let a={};n&&(a.Authorization=`Bearer ${n}`);const s=await r(`${e}//${t}/${t_}`,{headers:a,method:"POST",body:i,credentials:"include"});if(s.status===200)return s.headers.get("set-cookie");throw s.status===401?new Error(ao):new Error(ft)}function Ye(e){if(e.startsWith("http")){const{protocol:t,host:o,pathname:r}=new URL(e);return{ws_protocol:t==="https:"?"wss":"ws",http_protocol:t,host:o+(r!=="/"?r:"")}}else if(e.startsWith("file:"))return{ws_protocol:"ws",http_protocol:"http:",host:"lite.local"};return{ws_protocol:"wss",http_protocol:"https:",host:new URL(e).host}}const uo=e=>{let t=[];return e.split(/,(?=\s*[^\s=;]+=[^\s=;]+)/).forEach(r=>{const[n,i]=r.split(";")[0].split("=");n&&i&&t.push(`${n.trim()}=${i.trim()}`)}),t},bt=/^[a-zA-Z0-9_\-\.]+\/[a-zA-Z0-9_\-\.]+$/,y_=/.*hf\.space\/{0,1}.*$/;async function Me(e,t){const o={};t&&(o.Authorization=`Bearer ${t}`);const r=e.trim().replace(/\/$/,"");if(bt.test(r))try{const i=(await(await fetch(`https://huggingface.co/api/spaces/${r}/${Qd}`,{headers:o})).json()).host;return{space_id:e,...Ye(i)}}catch{throw new Error(ft)}if(y_.test(r)){const{ws_protocol:n,http_protocol:i,host:a}=Ye(r);return{space_id:a.split("/")[0].replace(".hf.space",""),ws_protocol:n,http_protocol:i,host:a}}return{space_id:!1,...Ye(r)}}const _o=(...e)=>{try{return e.reduce((t,o)=>(t=t.replace(/\/+$/,""),o=o.replace(/^\/+/,""),new URL(o,t+"/").toString()))}catch{throw new Error(p_)}};function k_(e,t,o){const r={named_endpoints:{},unnamed_endpoints:{}};return Object.keys(e).forEach(n=>{(n==="named_endpoints"||n==="unnamed_endpoints")&&(r[n]={},Object.entries(e[n]).forEach(([i,{parameters:a,returns:s}])=>{const u=t.dependencies.find(d=>d.api_name===i||d.api_name===i.replace("/",""))?.id||o[i.replace("/","")]||-1,l=u!==-1?t.dependencies.find(d=>d.id==u)?.types:{generator:!1,cancel:!1};if(u!==-1&&t.dependencies.find(d=>d.id==u)?.inputs?.length!==a.length){const d=t.dependencies.find(_=>_.id==u).inputs.map(_=>t.components.find(m=>m.id===_)?.type);try{d.forEach((_,m)=>{if(_==="state"){const h={component:"state",example:null,parameter_default:null,parameter_has_default:!0,parameter_name:null,hidden:!0};a.splice(m,0,h)}})}catch(_){console.error(_)}}const c=(d,_,m,h)=>({...d,description:E_(d?.type,m),type:x_(d?.type,_,m,h)||""});r[n][i]={parameters:a.map(d=>c(d,d?.component,d?.serializer,"parameter")),returns:s.map(d=>c(d,d?.component,d?.serializer,"return")),type:l}}))}),r}function x_(e,t,o,r){if(t==="Api")return e.type;switch(e?.type){case"string":return"string";case"boolean":return"boolean";case"number":return"number"}if(o==="JSONSerializable"||o==="StringSerializable")return"any";if(o==="ListStringSerializable")return"string[]";if(t==="Image")return r==="parameter"?"Blob | File | Buffer":"string";if(o==="FileSerializable")return e?.type==="array"?r==="parameter"?"(Blob | File | Buffer)[]":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}[]":r==="parameter"?"Blob | File | Buffer":"{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}";if(o==="GallerySerializable")return r==="parameter"?"[(Blob | File | Buffer), (string | null)][]":"[{ name: string; data: string; size?: number; is_file?: boolean; orig_name?: string}, (string | null))][]"}function E_(e,t){return t==="GallerySerializable"?"array of [file, label] tuples":t==="ListStringSerializable"?"array of strings":t==="FileSerializable"?"array of files or single file":e?.description}function Qe(e,t){switch(e.msg){case"send_data":return{type:"data"};case"send_hash":return{type:"hash"};case"queue_full":return{type:"update",status:{queue:!0,message:io,stage:"error",code:e.code,success:e.success}};case"heartbeat":return{type:"heartbeat"};case"unexpected_error":return{type:"unexpected_error",status:{queue:!0,message:e.message,stage:"error",success:!1}};case"estimation":return{type:"update",status:{queue:!0,stage:t||"pending",code:e.code,size:e.queue_size,position:e.rank,eta:e.rank_eta,success:e.success}};case"progress":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,progress_data:e.progress_data,success:e.success}};case"log":return{type:"log",data:e};case"process_generating":return{type:"generating",status:{queue:!0,message:e.success?null:e.output.error,stage:e.success?"generating":"error",code:e.code,progress_data:e.progress_data,eta:e.average_duration,changed_state_ids:e.success?e.output.changed_state_ids:void 0},data:e.success?e.output:null};case"process_streaming":return{type:"streaming",status:{queue:!0,message:e.output.error,stage:"streaming",time_limit:e.time_limit,code:e.code,progress_data:e.progress_data,eta:e.eta},data:e.output};case"process_completed":return"error"in e.output?{type:"update",status:{queue:!0,title:e.output.title,message:e.output.error,visible:e.output.visible,duration:e.output.duration,stage:"error",code:e.code,success:e.success}}:{type:"complete",status:{queue:!0,message:e.success?void 0:e.output.error,stage:e.success?"complete":"error",code:e.code,progress_data:e.progress_data,changed_state_ids:e.success?e.output.changed_state_ids:void 0},data:e.success?e.output:null};case"process_starts":return{type:"update",status:{queue:!0,stage:"pending",code:e.code,size:e.rank,position:0,success:e.success,eta:e.eta},original_msg:"process_starts"}}return{type:"none",status:{stage:"error",queue:!0}}}const S_=(e=[],t)=>{const o=t?t.parameters:[];if(Array.isArray(e))return e.length>o.length&&console.warn("Too many arguments provided for the endpoint."),e;const r=[],n=Object.keys(e);return o.forEach((i,a)=>{if(e.hasOwnProperty(i.parameter_name))r[a]=e[i.parameter_name];else if(i.parameter_has_default)r[a]=i.parameter_default;else throw new Error(`No value provided for required parameter: ${i.parameter_name}`)}),n.forEach(i=>{if(!o.some(a=>a.parameter_name===i))throw new Error(`Parameter \`${i}\` is not a valid keyword argument. Please refer to the API for usage.`)}),r.forEach((i,a)=>{if(i===void 0&&!o[a].parameter_has_default)throw new Error(`No value provided for required parameter: ${o[a].parameter_name}`)}),r};async function $_(){if(this.api_info)return this.api_info;const{hf_token:e}=this.options,{config:t}=this,o={"Content-Type":"application/json"};if(e&&(o.Authorization=`Bearer ${e}`),!!t)try{let r,n;if(typeof window<"u"&&window.gradio_api_info)n=window.gradio_api_info;else{if(ro(t?.version||"2.0.0","3.30")<0)r=await this.fetch(u_,{method:"POST",body:JSON.stringify({serialize:!1,config:JSON.stringify(t)}),headers:o,credentials:"include"});else{const i=_o(t.root,this.api_prefix,r_);r=await this.fetch(i,{headers:o,credentials:"include"})}if(!r.ok)throw new Error(de);n=await r.json()}return"api"in n&&(n=n.api),n.named_endpoints["/predict"]&&!n.unnamed_endpoints[0]&&(n.unnamed_endpoints[0]=n.named_endpoints["/predict"]),k_(n,t,this.api_map)}catch(r){""+r.message}}async function T_(e,t,o){const r={};this?.options?.hf_token&&(r.Authorization=`Bearer ${this.options.hf_token}`);const n=1e3,i=[];let a;for(let s=0;s<t.length;s+=n){const u=t.slice(s,s+n),l=new FormData;u.forEach(d=>{l.append("files",d)});try{const d=o?`${e}${this.api_prefix}/${At}?upload_id=${o}`:`${e}${this.api_prefix}/${At}`;a=await this.fetch(d,{method:"POST",body:l,headers:r,credentials:"include"})}catch(d){throw new Error(de+d.message)}if(!a.ok){const d=await a.text();return{error:`HTTP ${a.status}: ${d}`}}const c=await a.json();c&&i.push(...c)}return{files:i}}async function A_(e,t,o,r){let n=(Array.isArray(e)?e:[e]).map(a=>a.blob);const i=n.filter(a=>a.size>(r??1/0));if(i.length)throw new Error(`File size exceeds the maximum allowed size of ${r} bytes: ${i.map(a=>a.name).join(", ")}`);return await Promise.all(await this.upload_files(t,n,o).then(async a=>{if(a.error)throw new Error(a.error);return a.files?a.files.map((s,u)=>new Ve({...e[u],path:s,url:`${t}${this.api_prefix}/file=${s}`})):[]}))}async function wg(e,t){return e.map(o=>new Ve({path:o.name,orig_name:o.name,blob:o,size:o.size,mime_type:o.type,is_stream:t}))}class Ve{path;url;orig_name;size;blob;is_stream;mime_type;alt_text;b64;meta={_type:"gradio.FileData"};constructor({path:t,url:o,orig_name:r,size:n,blob:i,is_stream:a,mime_type:s,alt_text:u,b64:l}){this.path=t,this.url=o,this.orig_name=r,this.size=n,this.blob=o?void 0:i,this.is_stream=a,this.mime_type=s,this.alt_text=u,this.b64=l}}class P_{type;command;meta;fileData;constructor(t,o){this.type="command",this.command=t,this.meta=o}}typeof process<"u"&&process.versions&&process.versions.node;function Ot(e,t,o){for(;o.length>1;){const n=o.shift();if(typeof n=="string"||typeof n=="number")e=e[n];else throw new Error("Invalid key type")}const r=o.shift();if(typeof r=="string"||typeof r=="number")e[r]=t;else throw new Error("Invalid key type")}async function at(e,t=void 0,o=[],r=!1,n=void 0){if(Array.isArray(e)){let i=[];return await Promise.all(e.map(async(a,s)=>{let u=o.slice();u.push(String(s));const l=await at(e[s],r?n?.parameters[s]?.component||void 0:t,u,!1,n);i=i.concat(l)})),i}else{if(globalThis.Buffer&&e instanceof globalThis.Buffer||e instanceof Blob)return[{path:o,blob:new Blob([e]),type:t}];if(typeof e=="object"&&e!==null){let i=[];for(const a of Object.keys(e)){const s=[...o,a],u=e[a];i=i.concat(await at(u,void 0,s,!1,n))}return i}}return[]}function O_(e,t){let o=t?.dependencies?.find(r=>r.id==e)?.queue;return o!=null?!o:!t.enable_queue}function L_(e,t){return new Promise((o,r)=>{const n=new MessageChannel;n.port1.onmessage=({data:i})=>{n.port1.close(),o(i)},window.parent.postMessage(e,t,[n.port2])})}function Ee(e,t,o,r,n=!1){if(r==="input"&&!n)throw new Error("Invalid code path. Cannot skip state inputs for input.");if(r==="output"&&n)return e;let i=[],a=0;const s=r==="input"?t.inputs:t.outputs;for(let u=0;u<s.length;u++){const l=s[u];if(o.find(d=>d.id===l)?.type==="state"){if(n)if(e.length===s.length){const d=e[a];i.push(d),a++}else i.push(null);else{a++;continue}continue}else{const d=e[a];i.push(d),a++}}return i}async function I_(e,t,o){const r=this;await C_(r,t);const n=await at(t,void 0,[],!0,o);return(await Promise.all(n.map(async({path:a,blob:s,type:u})=>{if(!s)return{path:a,type:u};const l=await r.upload_files(e,[s]),c=l.files&&l.files[0];return{path:a,file_url:c,type:u,name:typeof File<"u"&&s instanceof File?s?.name:void 0}}))).forEach(({path:a,file_url:s,type:u,name:l})=>{if(u==="Gallery")Ot(t,s,a);else if(s){const c=new Ve({path:s,orig_name:l});Ot(t,c,a)}}),t}async function C_(e,t){if(!(e.config?.root||e.config?.root_url))throw new Error(so);await po(e,t)}async function po(e,t,o=[]){for(const r in t)t[r]instanceof P_?await D_(e,t,r):typeof t[r]=="object"&&t[r]!==null&&await po(e,t[r],[...o,r])}async function D_(e,t,o){let r=t[o];const n=e.config?.root||e.config?.root_url;if(!n)throw new Error(so);try{let i,a;if(typeof process<"u"&&process.versions&&process.versions.node){const c=await p(()=>import("./__vite-browser-external-D7Ct-6yo.js").then(_=>_._),[],import.meta.url);a=(await p(()=>import("./__vite-browser-external-D7Ct-6yo.js").then(_=>_._),[],import.meta.url)).resolve(process.cwd(),r.meta.path),i=await c.readFile(a)}else throw new Error(g_);const s=new Blob([i],{type:"application/octet-stream"}),u=await e.upload_files(n,[s]),l=u.files&&u.files[0];if(l){const c=new Ve({path:l,orig_name:r.meta.name||""});t[o]=c}}catch(i){console.error(f_,i)}}async function R_(e,t,o){const r={"Content-Type":"application/json"};this.options.hf_token&&(r.Authorization=`Bearer ${this.options.hf_token}`);try{var n=await this.fetch(e,{method:"POST",body:JSON.stringify(t),headers:{...r,...o},credentials:"include"})}catch{return[{error:de},500]}let i,a;try{i=await n.json(),a=n.status}catch(s){i={error:`Could not parse server response: ${s}`},a=500}return[i,a]}async function j_(e,t={}){let o=!1,r=!1;if(!this.config)throw new Error("Could not resolve app config");if(typeof e=="number")this.config.dependencies.find(n=>n.id==e);else{const n=e.replace(/^\//,"");this.config.dependencies.find(i=>i.id==this.api_map[n])}return new Promise(async(n,i)=>{const a=this.submit(e,t,null,null,!0);let s;for await(const u of a)u.type==="data"&&(r&&n(s),o=!0,s=u),u.type==="status"&&(u.stage==="error"&&i(u),u.stage==="complete"&&(r=!0,o&&n(s)))})}async function Te(e,t,o){let r=t==="subdomain"?`https://huggingface.co/api/spaces/by-subdomain/${e}`:`https://huggingface.co/api/spaces/${e}`,n,i;try{if(n=await fetch(r),i=n.status,i!==200)throw new Error;n=await n.json()}catch{o({status:"error",load_status:"error",message:d_,detail:"NOT_FOUND"});return}if(!n||i!==200)return;const{runtime:{stage:a},id:s}=n;switch(a){case"STOPPED":case"SLEEPING":o({status:"sleeping",load_status:"pending",message:"Space is asleep. Waking it up...",detail:a}),setTimeout(()=>{Te(e,t,o)},1e3);break;case"PAUSED":o({status:"paused",load_status:"error",message:"This space has been paused by the author. If you would like to try this demo, consider duplicating the space.",detail:a,discussions_enabled:await Lt(s)});break;case"RUNNING":case"RUNNING_BUILDING":o({status:"running",load_status:"complete",message:"Space is running.",detail:a});break;case"BUILDING":o({status:"building",load_status:"pending",message:"Space is building...",detail:a}),setTimeout(()=>{Te(e,t,o)},1e3);break;case"APP_STARTING":o({status:"starting",load_status:"pending",message:"Space is starting...",detail:a}),setTimeout(()=>{Te(e,t,o)},1e3);break;default:o({status:"space_error",load_status:"error",message:"This space is experiencing an issue.",detail:a,discussions_enabled:await Lt(s)});break}}const ho=async(e,t)=>{let o=0;const r=12,n=5e3;return new Promise(i=>{Te(e,bt.test(e)?"space_name":"subdomain",a=>{t(a),a.status==="running"||a.status==="error"||a.status==="paused"||a.status==="space_error"?i():(a.status==="sleeping"||a.status==="building")&&(o<r?(o++,setTimeout(()=>{ho(e,t).then(i)},n)):i())})})},N_=/^(?=[^]*\b[dD]iscussions{0,1}\b)(?=[^]*\b[dD]isabled\b)[^]*$/;async function Lt(e){try{const t=await fetch(`https://huggingface.co/api/spaces/${e}/discussions`,{method:"HEAD"}),o=t.headers.get("x-error-message");return!(!t.ok||o&&N_.test(o))}catch{return!1}}async function H_(e,t){const o={};t&&(o.Authorization=`Bearer ${t}`);try{const r=await fetch(`https://huggingface.co/api/spaces/${e}/${n_}`,{headers:o});if(r.status!==200)throw new Error("Space hardware could not be obtained.");const{hardware:n}=await r.json();return n.current}catch(r){throw new Error(r.message)}}async function B_(e,t,o){const r={};o&&(r.Authorization=`Bearer ${o}`);const n={seconds:t};try{const i=await fetch(`https://huggingface.co/api/spaces/${e}/${i_}`,{method:"POST",headers:{"Content-Type":"application/json",...r},body:JSON.stringify(n)});if(i.status!==200)throw new Error("Could not set sleep timeout on duplicated Space. Please visit *ADD HF LINK TO SETTINGS* to set a timeout manually to reduce billing charges.");return await i.json()}catch(i){throw new Error(i.message)}}const It=["cpu-basic","cpu-upgrade","cpu-xl","t4-small","t4-medium","a10g-small","a10g-large","a10g-largex2","a10g-largex4","a100-large","zero-a10g","h100","h100x8"];async function z_(e,t){const{hf_token:o,private:r,hardware:n,timeout:i,auth:a}=t;if(n&&!It.includes(n))throw new Error(`Invalid hardware type provided. Valid types are: ${It.map($=>`"${$}"`).join(",")}.`);const{http_protocol:s,host:u}=await Me(e,o);let l=null;if(a){const $=await co(s,u,a,fetch);$&&(l=uo($))}const c={Authorization:`Bearer ${o}`,"Content-Type":"application/json",...l?{Cookie:l.join("; ")}:{}},d=(await(await fetch("https://huggingface.co/api/whoami-v2",{headers:c})).json()).name,_=e.split("/")[1],m={repository:`${d}/${_}`};r&&(m.private=!0);let h;try{n||(h=await H_(e,o))}catch($){throw Error(ft+$.message)}const S=n||h||"cpu-basic";m.hardware=S;try{const $=await fetch(`https://huggingface.co/api/spaces/${e}/duplicate`,{method:"POST",headers:c,body:JSON.stringify(m)});if($.status===409)try{return await je.connect(`${d}/${_}`,t)}catch(D){throw console.error("Failed to connect Client instance:",D),D}else if($.status!==200)throw new Error($.statusText);const V=await $.json();return await B_(`${d}/${_}`,i||300,o),await je.connect(M_(V.url),t)}catch($){throw new Error($)}}function M_(e){const t=/https:\/\/huggingface.co\/spaces\/([^/]+\/[^/]+)/,o=e.match(t);if(o)return o[1]}class V_ extends TransformStream{#e="";constructor(t={allowCR:!1}){super({transform:(o,r)=>{for(o=this.#e+o;;){const n=o.indexOf(`
`),i=t.allowCR?o.indexOf("\r"):-1;if(i!==-1&&i!==o.length-1&&(n===-1||n-1>i)){r.enqueue(o.slice(0,i)),o=o.slice(i+1);continue}if(n===-1)break;const a=o[n-1]==="\r"?n-1:n;r.enqueue(o.slice(0,a)),o=o.slice(n+1)}this.#e=o},flush:o=>{if(this.#e==="")return;const r=t.allowCR&&this.#e.endsWith("\r")?this.#e.slice(0,-1):this.#e;o.enqueue(r)}})}}function U_(e){let t=new TextDecoderStream,o=new V_({allowCR:!0});return e.pipeThrough(t).pipeThrough(o)}function G_(e){let o=/[:]\s*/.exec(e),r=o&&o.index;if(r)return[e.substring(0,r),e.substring(r+o[0].length)]}function Ct(e,t,o){e.get(t)||e.set(t,o)}async function*F_(e,t){if(!e.body)return;let o=U_(e.body),r,n=o.getReader(),i;for(;;){if(t&&t.aborted)return n.cancel();if(r=await n.read(),r.done)return;if(!r.value){i&&(yield i),i=void 0;continue}let[a,s]=G_(r.value)||[];a&&(a==="data"?(i||={},i[a]=i[a]?i[a]+`
`+s:s):a==="event"?(i||={},i[a]=s):a==="id"?(i||={},i[a]=+s||s):a==="retry"&&(i||={},i[a]=+s||void 0))}}async function q_(e,t){let o=new Request(e,t);Ct(o.headers,"Accept","text/event-stream"),Ct(o.headers,"Content-Type","application/json");let r=await fetch(o);if(!r.ok)throw r;return F_(r,o.signal)}async function W_(){let{event_callbacks:e,unclosed_events:t,pending_stream_messages:o,stream_status:r,config:n,jwt:i}=this;const a=this;if(!n)throw new Error("Could not resolve app config");r.open=!0;let s=null,u=new URLSearchParams({session_hash:this.session_hash}).toString(),l=new URL(`${n.root}${this.api_prefix}/${no}?${u}`);if(i&&l.searchParams.set("__sign",i),s=this.stream(l),!s){console.warn("Cannot connect to SSE endpoint: "+l.toString());return}s.onmessage=async function(c){let d=JSON.parse(c.data);if(d.msg==="close_stream"){Re(r,a.abort_controller);return}const _=d.event_id;if(!_)await Promise.all(Object.keys(e).map(m=>e[m](d)));else if(e[_]&&n){d.msg==="process_completed"&&["sse","sse_v1","sse_v2","sse_v2.1","sse_v3"].includes(n.protocol)&&t.delete(_);let m=e[_];typeof window<"u"&&typeof document<"u"?setTimeout(m,0,d):m(d)}else o[_]||(o[_]=[]),o[_].push(d)},s.onerror=async function(){await Promise.all(Object.keys(e).map(c=>e[c]({msg:"unexpected_error",message:de})))}}function Re(e,t){e&&(e.open=!1,t?.abort())}function X_(e,t,o){!e[t]?(e[t]=[],o.data.forEach((n,i)=>{e[t][i]=n})):o.data.forEach((n,i)=>{let a=Z_(e[t][i],n);e[t][i]=a,o.data[i]=a})}function Z_(e,t){return t.forEach(([o,r,n])=>{e=J_(e,r,o,n)}),e}function J_(e,t,o,r){if(t.length===0){if(o==="replace")return r;if(o==="append")return e+r;throw new Error(`Unsupported action: ${o}`)}let n=e;for(let a=0;a<t.length-1;a++)n=n[t[a]];const i=t[t.length-1];switch(o){case"replace":n[i]=r;break;case"append":n[i]+=r;break;case"add":Array.isArray(n)?n.splice(Number(i),0,r):n[i]=r;break;case"delete":Array.isArray(n)?n.splice(Number(i),1):delete n[i];break;default:throw new Error(`Unknown action: ${o}`)}return e}function K_(e,t={}){const o={close:()=>{console.warn("Method not implemented.")},onerror:null,onmessage:null,onopen:null,readyState:0,url:e.toString(),withCredentials:!1,CONNECTING:0,OPEN:1,CLOSED:2,addEventListener:()=>{throw new Error("Method not implemented.")},dispatchEvent:()=>{throw new Error("Method not implemented.")},removeEventListener:()=>{throw new Error("Method not implemented.")}};return q_(e,t).then(async r=>{o.readyState=o.OPEN;try{for await(const n of r)o.onmessage&&o.onmessage(n);o.readyState=o.CLOSED}catch(n){o.onerror&&o.onerror(n),o.readyState=o.CLOSED}}).catch(r=>{console.error(r),o.onerror&&o.onerror(r),o.readyState=o.CLOSED}),o}function Y_(e,t={},o,r,n){try{let i=function(O){(n||Wd[O.type])&&l(O)},a=function(){for(We=!0;xe.length>0;)xe.shift()({value:void 0,done:!0})},s=function(O){We||(xe.length>0?xe.shift()(O):Xe.push(O))},u=function(O){s(Q_(O)),a()},l=function(O){s({value:O,done:!1})},c=function(){return Xe.length>0?Promise.resolve(Xe.shift()):We?Promise.resolve({value:void 0,done:!0}):new Promise(O=>xe.push(O))};const{hf_token:d}=this.options,{fetch:_,app_reference:m,config:h,session_hash:S,api_info:$,api_map:V,stream_status:D,pending_stream_messages:j,pending_diff_streams:z,event_callbacks:U,unclosed_events:C,post_data:B,options:G,api_prefix:W}=this,pe=this;if(!$)throw new Error("No API found");if(!h)throw new Error("Could not resolve app config");let{fn_index:b,endpoint_info:y,dependency:g}=ep($,e,V,h),P=S_(t,y),w,f,L=h.protocol??"ws",A="",N=()=>A;const v=typeof e=="number"?"/predict":e;let k,R=null,M=!1,qe={},ce=typeof window<"u"&&typeof document<"u"?new URLSearchParams(window.location.search).toString():"";const Wd=G?.events?.reduce((O,oe)=>(O[oe]=!0,O),{})||{};async function Xd(){const O={stage:"complete",queue:!1,time:new Date};M=O,i({...O,type:"status",endpoint:v,fn_index:b});let oe={},ue={};L==="ws"?(w&&w.readyState===0?w.addEventListener("open",()=>{w.close()}):w.close(),oe={fn_index:b,session_hash:S}):(Re(D,pe.abort_controller),a(),oe={event_id:R},ue={event_id:R,session_hash:S,fn_index:b});try{if(!h)throw new Error("Could not resolve app config");"event_id"in ue&&await _(`${h.root}${W}/${c_}`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(ue)}),await _(`${h.root}${W}/${l_}`,{headers:{"Content-Type":"application/json"},method:"POST",body:JSON.stringify(oe)})}catch{console.warn("The `/reset` endpoint could not be called. Subsequent endpoint results may be unreliable.")}}const Zd=async O=>{await this._resolve_hearbeat(O)};async function Et(O){if(!h)return;let oe=O.render_id;h.components=[...h.components.filter(X=>X.props.rendered_in!==oe),...O.components],h.dependencies=[...h.dependencies.filter(X=>X.rendered_in!==oe),...O.dependencies];const ue=h.components.some(X=>X.type==="state"),H=h.dependencies.some(X=>X.targets.some(Q=>Q[1]==="unload"));h.connect_heartbeat=ue||H,await Zd(h),i({type:"render",data:O,endpoint:v,fn_index:b})}this.handle_blob(h.root,P,y).then(async O=>{if(k={data:Ee(O,g,h.components,"input",!0)||[],event_data:o,fn_index:b,trigger_id:r},O_(b,h))i({type:"status",endpoint:v,stage:"pending",queue:!1,fn_index:b,time:new Date}),B(`${h.root}${W}/run${v.startsWith("/")?v:`/${v}`}${ce?"?"+ce:""}`,{...k,session_hash:S}).then(([H,X])=>{const Q=H.data;X==200?(i({type:"data",endpoint:v,fn_index:b,data:Ee(Q,g,h.components,"output",G.with_null_state),time:new Date,event_data:o,trigger_id:r}),H.render_config&&Et(H.render_config),i({type:"status",endpoint:v,fn_index:b,stage:"complete",eta:H.average_duration,queue:!1,time:new Date})):i({type:"status",stage:"error",endpoint:v,fn_index:b,message:H.error,queue:!1,time:new Date})}).catch(H=>{i({type:"status",stage:"error",message:H.message,endpoint:v,fn_index:b,queue:!1,time:new Date})});else if(L=="ws"){const{ws_protocol:H,host:X}=await Me(m,d);i({type:"status",stage:"pending",queue:!0,endpoint:v,fn_index:b,time:new Date});let Q=new URL(`${H}://${lo(X,h.path,!0)}/queue/join${ce?"?"+ce:""}`);this.jwt&&Q.searchParams.set("__sign",this.jwt),w=new WebSocket(Q),w.onclose=K=>{K.wasClean||i({type:"status",stage:"error",broken:!0,message:de,queue:!0,endpoint:v,fn_index:b,time:new Date})},w.onmessage=function(K){const ee=JSON.parse(K.data),{type:F,status:Z,data:J}=Qe(ee,qe[b]);if(F==="update"&&Z&&!M)i({type:"status",endpoint:v,fn_index:b,time:new Date,...Z}),Z.stage==="error"&&w.close();else if(F==="hash"){w.send(JSON.stringify({fn_index:b,session_hash:S}));return}else F==="data"?w.send(JSON.stringify({...k,session_hash:S})):F==="complete"?M=Z:F==="log"?i({type:"log",title:J.title,log:J.log,level:J.level,endpoint:v,duration:J.duration,visible:J.visible,fn_index:b}):F==="generating"&&i({type:"status",time:new Date,...Z,stage:Z?.stage,queue:!0,endpoint:v,fn_index:b});J&&(i({type:"data",time:new Date,data:Ee(J.data,g,h.components,"output",G.with_null_state),endpoint:v,fn_index:b,event_data:o,trigger_id:r}),M&&(i({type:"status",time:new Date,...M,stage:Z?.stage,queue:!0,endpoint:v,fn_index:b}),w.close()))},ro(h.version||"2.0.0","3.6")<0&&addEventListener("open",()=>w.send(JSON.stringify({hash:S})))}else if(L=="sse"){i({type:"status",stage:"pending",queue:!0,endpoint:v,fn_index:b,time:new Date});var ue=new URLSearchParams({fn_index:b.toString(),session_hash:S}).toString();let H=new URL(`${h.root}${W}/${no}?${ce?ce+"&":""}${ue}`);if(this.jwt&&H.searchParams.set("__sign",this.jwt),f=this.stream(H),!f)return Promise.reject(new Error("Cannot connect to SSE endpoint: "+H.toString()));f.onmessage=async function(X){const Q=JSON.parse(X.data),{type:K,status:ee,data:F}=Qe(Q,qe[b]);if(K==="update"&&ee&&!M)i({type:"status",endpoint:v,fn_index:b,time:new Date,...ee}),ee.stage==="error"&&(f?.close(),a());else if(K==="data"){let[Z,J]=await B(`${h.root}${W}/queue/data`,{...k,session_hash:S,event_id:R});J!==200&&(i({type:"status",stage:"error",message:de,queue:!0,endpoint:v,fn_index:b,time:new Date}),f?.close(),a())}else K==="complete"?M=ee:K==="log"?i({type:"log",title:F.title,log:F.log,level:F.level,endpoint:v,duration:F.duration,visible:F.visible,fn_index:b}):(K==="generating"||K==="streaming")&&i({type:"status",time:new Date,...ee,stage:ee?.stage,queue:!0,endpoint:v,fn_index:b});F&&(i({type:"data",time:new Date,data:Ee(F.data,g,h.components,"output",G.with_null_state),endpoint:v,fn_index:b,event_data:o,trigger_id:r}),M&&(i({type:"status",time:new Date,...M,stage:ee?.stage,queue:!0,endpoint:v,fn_index:b}),f?.close(),a()))}}else if(L=="sse_v1"||L=="sse_v2"||L=="sse_v2.1"||L=="sse_v3"){i({type:"status",stage:"pending",queue:!0,endpoint:v,fn_index:b,time:new Date});let H="";typeof window<"u"&&typeof document<"u"&&(H=window?.location?.hostname);const Q=H.includes(".dev.")?`https://moon-${H.split(".")[1]}.dev.spaces.huggingface.tech`:"https://huggingface.co";(typeof window<"u"&&typeof document<"u"&&window.parent!=window&&window.supports_zerogpu_headers?L_("zerogpu-headers",Q):Promise.resolve(null)).then(Z=>B(`${h.root}${W}/${e_}?${ce}`,{...k,session_hash:S},Z)).then(async([Z,J])=>{if(J===503)i({type:"status",stage:"error",message:io,queue:!0,endpoint:v,fn_index:b,time:new Date});else if(J!==200)i({type:"status",stage:"error",message:de,queue:!0,endpoint:v,fn_index:b,time:new Date});else{R=Z.event_id,A=R;let $t=async function(Ze){try{const{type:re,status:te,data:Y,original_msg:Jd}=Qe(Ze,qe[b]);if(re=="heartbeat")return;if(re==="update"&&te&&!M)i({type:"status",endpoint:v,fn_index:b,time:new Date,original_msg:Jd,...te});else if(re==="complete")M=te;else if(re=="unexpected_error")console.error("Unexpected error",te?.message),i({type:"status",stage:"error",message:te?.message||"An Unexpected Error Occurred!",queue:!0,endpoint:v,fn_index:b,time:new Date});else if(re==="log"){i({type:"log",title:Y.title,log:Y.log,level:Y.level,endpoint:v,duration:Y.duration,visible:Y.visible,fn_index:b});return}else(re==="generating"||re==="streaming")&&(i({type:"status",time:new Date,...te,stage:te?.stage,queue:!0,endpoint:v,fn_index:b}),Y&&g.connection!=="stream"&&["sse_v2","sse_v2.1","sse_v3"].includes(L)&&X_(z,R,Y));Y&&(i({type:"data",time:new Date,data:Ee(Y.data,g,h.components,"output",G.with_null_state),endpoint:v,fn_index:b}),Y.render_config&&await Et(Y.render_config),M&&(i({type:"status",time:new Date,...M,stage:te?.stage,queue:!0,endpoint:v,fn_index:b}),a())),(te?.stage==="complete"||te?.stage==="error")&&(U[R]&&delete U[R],R in z&&delete z[R])}catch(re){console.error("Unexpected client exception",re),i({type:"status",stage:"error",message:"An Unexpected Error Occurred!",queue:!0,endpoint:v,fn_index:b,time:new Date}),["sse_v2","sse_v2.1","sse_v3"].includes(L)&&(Re(D,pe.abort_controller),D.open=!1,a())}};R in j&&(j[R].forEach(Ze=>$t(Ze)),delete j[R]),U[R]=$t,C.add(R),D.open||await this.open_stream()}})}});let We=!1;const Xe=[],xe=[],St={[Symbol.asyncIterator]:()=>St,next:c,throw:async O=>(u(O),c()),return:async()=>(a(),c()),cancel:Xd,event_id:N};return St}catch(i){throw console.error("Submit function encountered an error:",i),i}}function Q_(e){return{then:(t,o)=>o(e)}}function ep(e,t,o,r){let n,i,a;if(typeof t=="number")n=t,i=e.unnamed_endpoints[n],a=r.dependencies.find(s=>s.id==t);else{const s=t.replace(/^\//,"");n=o[s],i=e.named_endpoints[t.trim()],a=r.dependencies.find(u=>u.id==o[s])}if(typeof n!="number")throw new Error("There is no endpoint matching that name of fn_index matching that number.");return{fn_index:n,endpoint_info:i,dependency:a}}class je{app_reference;options;config;api_prefix="";api_info;api_map={};session_hash=Math.random().toString(36).substring(2);jwt=!1;last_status={};cookies=null;stream_status={open:!1};closed=!1;pending_stream_messages={};pending_diff_streams={};event_callbacks={};unclosed_events=new Set;heartbeat_event=null;abort_controller=null;stream_instance=null;current_payload;ws_map={};get_url_config(t=null){if(!this.config)throw new Error(ie);t===null&&(t=window.location.href);const o=a=>a.replace(/^\/+|\/+$/g,"");let r=o(new URL(this.config.root).pathname),n=o(new URL(t).pathname),i;return n.startsWith(r)?i=o(n.substring(r.length)):i="",this.get_page_config(i)}get_page_config(t){if(!this.config)throw new Error(ie);let o=this.config;return t in o.page||(t=""),{...o,current_page:t,layout:o.page[t].layout,components:o.components.filter(r=>o.page[t].components.includes(r.id)),dependencies:this.config.dependencies.filter(r=>o.page[t].dependencies.includes(r.id))}}fetch(t,o){const r=new Headers(o?.headers||{});if(this&&this.cookies&&r.append("Cookie",this.cookies),this&&this.options.headers)for(const n in this.options.headers)r.append(n,this.options.headers[n]);return fetch(t,{...o,headers:r})}stream(t){const o=new Headers;if(this&&this.cookies&&o.append("Cookie",this.cookies),this&&this.options.headers)for(const r in this.options.headers)o.append(r,this.options.headers[r]);return this.abort_controller=new AbortController,this.stream_instance=K_(t.toString(),{credentials:"include",headers:o,signal:this.abort_controller.signal}),this.stream_instance}view_api;upload_files;upload;handle_blob;post_data;submit;predict;open_stream;resolve_config;resolve_cookies;constructor(t,o={events:["data"]}){this.app_reference=t,o.events||(o.events=["data"]),this.options=o,this.current_payload={},this.view_api=$_.bind(this),this.upload_files=T_.bind(this),this.handle_blob=I_.bind(this),this.post_data=R_.bind(this),this.submit=Y_.bind(this),this.predict=j_.bind(this),this.open_stream=W_.bind(this),this.resolve_config=w_.bind(this),this.resolve_cookies=v_.bind(this),this.upload=A_.bind(this),this.fetch=this.fetch.bind(this),this.handle_space_success=this.handle_space_success.bind(this),this.stream=this.stream.bind(this)}async init(){if((typeof window>"u"||!("WebSocket"in window))&&!global.WebSocket){const t=await p(()=>import("./browser-DuKHAX0p.js").then(o=>o.b),[],import.meta.url);global.WebSocket=t.WebSocket}this.options.auth&&await this.resolve_cookies(),await this._resolve_config().then(({config:t})=>this._resolve_hearbeat(t)),this.api_info=await this.view_api(),this.api_map=b_(this.config?.dependencies||[])}async _resolve_hearbeat(t){if(t&&(this.config=t,this.api_prefix=t.api_prefix||"",this.config&&this.config.connect_heartbeat&&this.config.space_id&&this.options.hf_token&&(this.jwt=await Pt(this.config.space_id,this.options.hf_token,this.cookies))),t.space_id&&this.options.hf_token&&(this.jwt=await Pt(t.space_id,this.options.hf_token)),this.config&&this.config.connect_heartbeat){const o=new URL(`${this.config.root}${this.api_prefix}/${a_}/${this.session_hash}`);this.jwt&&o.searchParams.set("__sign",this.jwt),this.heartbeat_event||(this.heartbeat_event=this.stream(o))}}static async connect(t,o={events:["data"]}){const r=new this(t,o);return await r.init(),r}close(){this.closed=!0,Re(this.stream_status,this.abort_controller)}set_current_payload(t){this.current_payload=t}static async duplicate(t,o={events:["data"]}){return z_(t,o)}async _resolve_config(){const{http_protocol:t,host:o,space_id:r}=await Me(this.app_reference,this.options.hf_token),{status_callback:n}=this.options;r&&n&&await ho(r,n);let i;try{if(i=await this.resolve_config(`${t}//${o}`),!i)throw new Error(ie);return this.config_success(i)}catch(a){if(r&&n)Te(r,bt.test(r)?"space_name":"subdomain",this.handle_space_success);else throw n&&n({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),Error(a)}}async config_success(t){if(this.config=t,this.api_prefix=t.api_prefix||"",typeof window<"u"&&typeof document<"u"&&window.location.protocol==="https:"&&(this.config.root=this.config.root.replace("http://","https://")),this.config.auth_required)return this.prepare_return_obj();try{this.api_info=await this.view_api()}catch(o){console.error(__+o.message)}return this.prepare_return_obj()}async handle_space_success(t){if(!this)throw new Error(ie);const{status_callback:o}=this.options;if(o&&o(t),t.status==="running")try{if(this.config=await this._resolve_config(),this.api_prefix=this?.config?.api_prefix||"",!this.config)throw new Error(ie);return await this.config_success(this.config)}catch(r){throw o&&o({status:"error",message:"Could not load this space.",load_status:"error",detail:"NOT_FOUND"}),r}}async component_server(t,o,r){if(!this.config)throw new Error(ie);const n={},{hf_token:i}=this.options,{session_hash:a}=this;i&&(n.Authorization=`Bearer ${this.options.hf_token}`);let s,u=this.config.components.find(c=>c.id===t);u?.props?.root_url?s=u.props.root_url:s=this.config.root;let l;if("binary"in r){l=new FormData;for(const c in r.data)c!=="binary"&&l.append(c,r.data[c]);l.set("component_id",t.toString()),l.set("fn_name",o),l.set("session_hash",a)}else l=JSON.stringify({data:r,component_id:t,fn_name:o,session_hash:a}),n["Content-Type"]="application/json";i&&(n.Authorization=`Bearer ${i}`);try{const c=await this.fetch(`${s}${this.api_prefix}/${s_}/`,{method:"POST",body:l,headers:n,credentials:"include"});if(!c.ok)throw new Error("Could not connect to component server: "+c.statusText);return await c.json()}catch(c){console.warn(c)}}set_cookies(t){this.cookies=uo(t).join("; ")}prepare_return_obj(){return{config:this.config,predict:this.predict,submit:this.submit,view_api:this.view_api,component_server:this.component_server}}async connect_ws(t){return new Promise((o,r)=>{let n;try{n=new WebSocket(t)}catch{this.ws_map[t]="failed";return}n.onopen=()=>{o()},n.onerror=i=>{console.error("WebSocket error:",i),this.close_ws(t),this.ws_map[t]="failed",o()},n.onclose=()=>{delete this.ws_map[t],this.ws_map[t]="failed"},n.onmessage=i=>{},this.ws_map[t]=n})}async send_ws_message(t,o){t in this.ws_map||await this.connect_ws(t);const r=this.ws_map[t];r instanceof WebSocket?r.send(JSON.stringify(o)):this.post_data(t,o)}async close_ws(t){if(t in this.ws_map){const o=this.ws_map[t];o instanceof WebSocket&&(o.close(),delete this.ws_map[t])}}}function ge(){}const vg=e=>e;function tp(e){return e()}function op(e){e.forEach(tp)}function rp(e){return typeof e=="function"}function np(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function mo(e,...t){if(e==null){for(const r of t)r(void 0);return ge}const o=e.subscribe(...t);return o.unsubscribe?()=>o.unsubscribe():o}function ip(e){let t;return mo(e,o=>t=o)(),t}function yg(e){const t=typeof e=="string"&&e.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[e,"px"]}const he=[];function ap(e,t){return{subscribe:se(e,t).subscribe}}function se(e,t=ge){let o;const r=new Set;function n(s){if(np(e,s)&&(e=s,o)){const u=!he.length;for(const l of r)l[1](),he.push(l,e);if(u){for(let l=0;l<he.length;l+=2)he[l][0](he[l+1]);he.length=0}}}function i(s){n(s(e))}function a(s,u=ge){const l=[s,u];return r.add(l),r.size===1&&(o=t(n,i)||ge),s(e),()=>{r.delete(l),r.size===0&&o&&(o(),o=null)}}return{set:n,update:i,subscribe:a}}function ye(e,t,o){const r=!Array.isArray(e),n=r?[e]:e;if(!n.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const i=t.length<2;return ap(o,(a,s)=>{let u=!1;const l=[];let c=0,d=ge;const _=()=>{if(c)return;d();const h=t(r?l[0]:l,a,s);i?a(h):d=rp(h)?h:ge},m=n.map((h,S)=>mo(h,$=>{l[S]=$,c&=~(1<<S),u&&_()},()=>{c|=1<<S}));return u=!0,_(),function(){op(m),d(),u=!1}})}var kg=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function sp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function xg(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var o=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};o.prototype=t.prototype}else o={};return Object.defineProperty(o,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(o,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}),o}var lp=function(t){return cp(t)&&!up(t)};function cp(e){return!!e&&typeof e=="object"}function up(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||pp(e)}var dp=typeof Symbol=="function"&&Symbol.for,_p=dp?Symbol.for("react.element"):60103;function pp(e){return e.$$typeof===_p}function hp(e){return Array.isArray(e)?[]:{}}function Ae(e,t){return t.clone!==!1&&t.isMergeableObject(e)?fe(hp(e),e,t):e}function mp(e,t,o){return e.concat(t).map(function(r){return Ae(r,o)})}function gp(e,t){if(!t.customMerge)return fe;var o=t.customMerge(e);return typeof o=="function"?o:fe}function fp(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[]}function Dt(e){return Object.keys(e).concat(fp(e))}function go(e,t){try{return t in e}catch{return!1}}function bp(e,t){return go(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))}function wp(e,t,o){var r={};return o.isMergeableObject(e)&&Dt(e).forEach(function(n){r[n]=Ae(e[n],o)}),Dt(t).forEach(function(n){bp(e,n)||(go(e,n)&&o.isMergeableObject(t[n])?r[n]=gp(n,o)(e[n],t[n],o):r[n]=Ae(t[n],o))}),r}function fe(e,t,o){o=o||{},o.arrayMerge=o.arrayMerge||mp,o.isMergeableObject=o.isMergeableObject||lp,o.cloneUnlessOtherwiseSpecified=Ae;var r=Array.isArray(t),n=Array.isArray(e),i=r===n;return i?r?o.arrayMerge(e,t,o):wp(e,t,o):Ae(t,o)}fe.all=function(t,o){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(r,n){return fe(r,n,o)},{})};var vp=fe,yp=vp;const kp=sp(yp);var st=function(e,t){return st=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,r){o.__proto__=r}||function(o,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(o[n]=r[n])},st(e,t)};function Ue(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");st(e,t);function o(){this.constructor=e}e.prototype=t===null?Object.create(t):(o.prototype=t.prototype,new o)}var T=function(){return T=Object.assign||function(t){for(var o,r=1,n=arguments.length;r<n;r++){o=arguments[r];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(t[i]=o[i])}return t},T.apply(this,arguments)};function xp(e,t){var o={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(o[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(o[r[n]]=e[r[n]]);return o}function et(e,t,o){if(o||arguments.length===2)for(var r=0,n=t.length,i;r<n;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var x;(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(x||(x={}));var I;(function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"})(I||(I={}));var be;(function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"})(be||(be={}));function Rt(e){return e.type===I.literal}function Ep(e){return e.type===I.argument}function fo(e){return e.type===I.number}function bo(e){return e.type===I.date}function wo(e){return e.type===I.time}function vo(e){return e.type===I.select}function yo(e){return e.type===I.plural}function Sp(e){return e.type===I.pound}function ko(e){return e.type===I.tag}function xo(e){return!!(e&&typeof e=="object"&&e.type===be.number)}function lt(e){return!!(e&&typeof e=="object"&&e.type===be.dateTime)}var Eo=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,$p=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function Tp(e){var t={};return e.replace($p,function(o){var r=o.length;switch(o[0]){case"G":t.era=r===4?"long":r===5?"narrow":"short";break;case"y":t.year=r===2?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][r-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][r-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=r===4?"long":r===5?"narrow":"short";break;case"e":if(r<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"c":if(r<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][r-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][r-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][r-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][r-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][r-1];break;case"s":t.second=["numeric","2-digit"][r-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=r<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),t}var Ap=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;function Pp(e){if(e.length===0)throw new Error("Number skeleton cannot be empty");for(var t=e.split(Ap).filter(function(_){return _.length>0}),o=[],r=0,n=t;r<n.length;r++){var i=n[r],a=i.split("/");if(a.length===0)throw new Error("Invalid number skeleton");for(var s=a[0],u=a.slice(1),l=0,c=u;l<c.length;l++){var d=c[l];if(d.length===0)throw new Error("Invalid number skeleton")}o.push({stem:s,options:u})}return o}function Op(e){return e.replace(/^(.*?)-/,"")}var jt=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,So=/^(@+)?(\+|#+)?[rs]?$/g,Lp=/(\*)(0+)|(#+)(0+)|(0+)/g,$o=/^(0+)$/;function Nt(e){var t={};return e[e.length-1]==="r"?t.roundingPriority="morePrecision":e[e.length-1]==="s"&&(t.roundingPriority="lessPrecision"),e.replace(So,function(o,r,n){return typeof n!="string"?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):n==="+"?t.minimumSignificantDigits=r.length:r[0]==="#"?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+(typeof n=="string"?n.length:0)),""}),t}function To(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function Ip(e){var t;if(e[0]==="E"&&e[1]==="E"?(t={notation:"engineering"},e=e.slice(2)):e[0]==="E"&&(t={notation:"scientific"},e=e.slice(1)),t){var o=e.slice(0,2);if(o==="+!"?(t.signDisplay="always",e=e.slice(2)):o==="+?"&&(t.signDisplay="exceptZero",e=e.slice(2)),!$o.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function Ht(e){var t={},o=To(e);return o||t}function Cp(e){for(var t={},o=0,r=e;o<r.length;o++){var n=r[o];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=Op(n.options[0]);continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=T(T(T({},t),{notation:"scientific"}),n.options.reduce(function(u,l){return T(T({},u),Ht(l))},{}));continue;case"engineering":t=T(T(T({},t),{notation:"engineering"}),n.options.reduce(function(u,l){return T(T({},u),Ht(l))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");n.options[0].replace(Lp,function(u,l,c,d,_,m){if(l)t.minimumIntegerDigits=c.length;else{if(d&&_)throw new Error("We currently do not support maximum integer digits");if(m)throw new Error("We currently do not support exact integer digits")}return""});continue}if($o.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(jt.test(n.stem)){if(n.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(jt,function(u,l,c,d,_,m){return c==="*"?t.minimumFractionDigits=l.length:d&&d[0]==="#"?t.maximumFractionDigits=d.length:_&&m?(t.minimumFractionDigits=_.length,t.maximumFractionDigits=_.length+m.length):(t.minimumFractionDigits=l.length,t.maximumFractionDigits=l.length),""});var i=n.options[0];i==="w"?t=T(T({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=T(T({},t),Nt(i)));continue}if(So.test(n.stem)){t=T(T({},t),Nt(n.stem));continue}var a=To(n.stem);a&&(t=T(T({},t),a));var s=Ip(n.stem);s&&(t=T(T({},t),s))}return t}var Ie={"001":["H","h"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["H","h","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["H","hB","h","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["H","h","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["H","h","hB","hb"],CU:["H","h","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["H","hB","h","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["H","h","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["H","h","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["H","h","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["H","h","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["H","hB","h","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["H","h","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["H","h","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["H","h","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"es-BO":["H","h","hB","hb"],"es-BR":["H","h","hB","hb"],"es-EC":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"es-PE":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]};function Dp(e,t){for(var o="",r=0;r<e.length;r++){var n=e.charAt(r);if(n==="j"){for(var i=0;r+1<e.length&&e.charAt(r+1)===n;)i++,r++;var a=1+(i&1),s=i<2?1:3+(i>>1),u="a",l=Rp(t);for((l=="H"||l=="k")&&(s=0);s-- >0;)o+=u;for(;a-- >0;)o=l+o}else n==="J"?o+="H":o+=n}return o}function Rp(e){var t=e.hourCycle;if(t===void 0&&e.hourCycles&&e.hourCycles.length&&(t=e.hourCycles[0]),t)switch(t){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw new Error("Invalid hourCycle")}var o=e.language,r;o!=="root"&&(r=e.maximize().region);var n=Ie[r||""]||Ie[o||""]||Ie["".concat(o,"-001")]||Ie["001"];return n[0]}var tt,jp=new RegExp("^".concat(Eo.source,"*")),Np=new RegExp("".concat(Eo.source,"*$"));function E(e,t){return{start:e,end:t}}var Hp=!!String.prototype.startsWith&&"_a".startsWith("a",1),Bp=!!String.fromCodePoint,zp=!!Object.fromEntries,Mp=!!String.prototype.codePointAt,Vp=!!String.prototype.trimStart,Up=!!String.prototype.trimEnd,Gp=!!Number.isSafeInteger,Fp=Gp?Number.isSafeInteger:function(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},ct=!0;try{var qp=Po("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");ct=((tt=qp.exec("a"))===null||tt===void 0?void 0:tt[0])==="a"}catch{ct=!1}var Bt=Hp?function(t,o,r){return t.startsWith(o,r)}:function(t,o,r){return t.slice(r,r+o.length)===o},ut=Bp?String.fromCodePoint:function(){for(var t=[],o=0;o<arguments.length;o++)t[o]=arguments[o];for(var r="",n=t.length,i=0,a;n>i;){if(a=t[i++],a>1114111)throw RangeError(a+" is not a valid code point");r+=a<65536?String.fromCharCode(a):String.fromCharCode(((a-=65536)>>10)+55296,a%1024+56320)}return r},zt=zp?Object.fromEntries:function(t){for(var o={},r=0,n=t;r<n.length;r++){var i=n[r],a=i[0],s=i[1];o[a]=s}return o},Ao=Mp?function(t,o){return t.codePointAt(o)}:function(t,o){var r=t.length;if(!(o<0||o>=r)){var n=t.charCodeAt(o),i;return n<55296||n>56319||o+1===r||(i=t.charCodeAt(o+1))<56320||i>57343?n:(n-55296<<10)+(i-56320)+65536}},Wp=Vp?function(t){return t.trimStart()}:function(t){return t.replace(jp,"")},Xp=Up?function(t){return t.trimEnd()}:function(t){return t.replace(Np,"")};function Po(e,t){return new RegExp(e,t)}var dt;if(ct){var Mt=Po("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");dt=function(t,o){var r;Mt.lastIndex=o;var n=Mt.exec(t);return(r=n[1])!==null&&r!==void 0?r:""}}else dt=function(t,o){for(var r=[];;){var n=Ao(t,o);if(n===void 0||Oo(n)||Yp(n))break;r.push(n),o+=n>=65536?2:1}return ut.apply(void 0,r)};var Zp=function(){function e(t,o){o===void 0&&(o={}),this.message=t,this.position={offset:0,line:1,column:1},this.ignoreTag=!!o.ignoreTag,this.locale=o.locale,this.requiresOtherClause=!!o.requiresOtherClause,this.shouldParseSkeletons=!!o.shouldParseSkeletons}return e.prototype.parse=function(){if(this.offset()!==0)throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(t,o,r){for(var n=[];!this.isEOF();){var i=this.char();if(i===123){var a=this.parseArgument(t,r);if(a.err)return a;n.push(a.val)}else{if(i===125&&t>0)break;if(i===35&&(o==="plural"||o==="selectordinal")){var s=this.clonePosition();this.bump(),n.push({type:I.pound,location:E(s,this.clonePosition())})}else if(i===60&&!this.ignoreTag&&this.peek()===47){if(r)break;return this.error(x.UNMATCHED_CLOSING_TAG,E(this.clonePosition(),this.clonePosition()))}else if(i===60&&!this.ignoreTag&&_t(this.peek()||0)){var a=this.parseTag(t,o);if(a.err)return a;n.push(a.val)}else{var a=this.parseLiteral(t,o);if(a.err)return a;n.push(a.val)}}}return{val:n,err:null}},e.prototype.parseTag=function(t,o){var r=this.clonePosition();this.bump();var n=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:I.literal,value:"<".concat(n,"/>"),location:E(r,this.clonePosition())},err:null};if(this.bumpIf(">")){var i=this.parseMessage(t+1,o,!0);if(i.err)return i;var a=i.val,s=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!_t(this.char()))return this.error(x.INVALID_TAG,E(s,this.clonePosition()));var u=this.clonePosition(),l=this.parseTagName();return n!==l?this.error(x.UNMATCHED_CLOSING_TAG,E(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:I.tag,value:n,children:a,location:E(r,this.clonePosition())},err:null}:this.error(x.INVALID_TAG,E(s,this.clonePosition())))}else return this.error(x.UNCLOSED_TAG,E(r,this.clonePosition()))}else return this.error(x.INVALID_TAG,E(r,this.clonePosition()))},e.prototype.parseTagName=function(){var t=this.offset();for(this.bump();!this.isEOF()&&Kp(this.char());)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(t,o){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(o);if(i){n+=i;continue}var a=this.tryParseUnquoted(t,o);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var u=E(r,this.clonePosition());return{val:{type:I.literal,value:n,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return!this.isEOF()&&this.char()===60&&(this.ignoreTag||!Jp(this.peek()||0))?(this.bump(),"<"):null},e.prototype.tryParseQuote=function(t){if(this.isEOF()||this.char()!==39)return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if(t==="plural"||t==="selectordinal")break;return null;default:return null}this.bump();var o=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(r===39)if(this.peek()===39)o.push(39),this.bump();else{this.bump();break}else o.push(r);this.bump()}return ut.apply(void 0,o)},e.prototype.tryParseUnquoted=function(t,o){if(this.isEOF())return null;var r=this.char();return r===60||r===123||r===35&&(o==="plural"||o==="selectordinal")||r===125&&t>0?null:(this.bump(),ut(r))},e.prototype.parseArgument=function(t,o){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(x.EXPECT_ARGUMENT_CLOSING_BRACE,E(r,this.clonePosition()));if(this.char()===125)return this.bump(),this.error(x.EMPTY_ARGUMENT,E(r,this.clonePosition()));var n=this.parseIdentifierIfPossible().value;if(!n)return this.error(x.MALFORMED_ARGUMENT,E(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(x.EXPECT_ARGUMENT_CLOSING_BRACE,E(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:I.argument,value:n,location:E(r,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(x.EXPECT_ARGUMENT_CLOSING_BRACE,E(r,this.clonePosition())):this.parseArgumentOptions(t,o,n,r);default:return this.error(x.MALFORMED_ARGUMENT,E(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var t=this.clonePosition(),o=this.offset(),r=dt(this.message,o),n=o+r.length;this.bumpTo(n);var i=this.clonePosition(),a=E(t,i);return{value:r,location:a}},e.prototype.parseArgumentOptions=function(t,o,r,n){var i,a=this.clonePosition(),s=this.parseIdentifierIfPossible().value,u=this.clonePosition();switch(s){case"":return this.error(x.EXPECT_ARGUMENT_TYPE,E(a,u));case"number":case"date":case"time":{this.bumpSpace();var l=null;if(this.bumpIf(",")){this.bumpSpace();var c=this.clonePosition(),d=this.parseSimpleArgStyleIfPossible();if(d.err)return d;var _=Xp(d.val);if(_.length===0)return this.error(x.EXPECT_ARGUMENT_STYLE,E(this.clonePosition(),this.clonePosition()));var m=E(c,this.clonePosition());l={style:_,styleLocation:m}}var h=this.tryParseArgumentClose(n);if(h.err)return h;var S=E(n,this.clonePosition());if(l&&Bt(l?.style,"::",0)){var $=Wp(l.style.slice(2));if(s==="number"){var d=this.parseNumberSkeletonFromString($,l.styleLocation);return d.err?d:{val:{type:I.number,value:r,location:S,style:d.val},err:null}}else{if($.length===0)return this.error(x.EXPECT_DATE_TIME_SKELETON,S);var V=$;this.locale&&(V=Dp($,this.locale));var _={type:be.dateTime,pattern:V,location:l.styleLocation,parsedOptions:this.shouldParseSkeletons?Tp(V):{}},D=s==="date"?I.date:I.time;return{val:{type:D,value:r,location:S,style:_},err:null}}}return{val:{type:s==="number"?I.number:s==="date"?I.date:I.time,value:r,location:S,style:(i=l?.style)!==null&&i!==void 0?i:null},err:null}}case"plural":case"selectordinal":case"select":{var j=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(x.EXPECT_SELECT_ARGUMENT_OPTIONS,E(j,T({},j)));this.bumpSpace();var z=this.parseIdentifierIfPossible(),U=0;if(s!=="select"&&z.value==="offset"){if(!this.bumpIf(":"))return this.error(x.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,E(this.clonePosition(),this.clonePosition()));this.bumpSpace();var d=this.tryParseDecimalInteger(x.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,x.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(d.err)return d;this.bumpSpace(),z=this.parseIdentifierIfPossible(),U=d.val}var C=this.tryParsePluralOrSelectOptions(t,s,o,z);if(C.err)return C;var h=this.tryParseArgumentClose(n);if(h.err)return h;var B=E(n,this.clonePosition());return s==="select"?{val:{type:I.select,value:r,options:zt(C.val),location:B},err:null}:{val:{type:I.plural,value:r,options:zt(C.val),offset:U,pluralType:s==="plural"?"cardinal":"ordinal",location:B},err:null}}default:return this.error(x.INVALID_ARGUMENT_TYPE,E(a,u))}},e.prototype.tryParseArgumentClose=function(t){return this.isEOF()||this.char()!==125?this.error(x.EXPECT_ARGUMENT_CLOSING_BRACE,E(t,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var t=0,o=this.clonePosition();!this.isEOF();){var r=this.char();switch(r){case 39:{this.bump();var n=this.clonePosition();if(!this.bumpUntil("'"))return this.error(x.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,E(n,this.clonePosition()));this.bump();break}case 123:{t+=1,this.bump();break}case 125:{if(t>0)t-=1;else return{val:this.message.slice(o.offset,this.offset()),err:null};break}default:this.bump();break}}return{val:this.message.slice(o.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(t,o){var r=[];try{r=Pp(t)}catch{return this.error(x.INVALID_NUMBER_SKELETON,o)}return{val:{type:be.number,tokens:r,location:o,parsedOptions:this.shouldParseSkeletons?Cp(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(t,o,r,n){for(var i,a=!1,s=[],u=new Set,l=n.value,c=n.location;;){if(l.length===0){var d=this.clonePosition();if(o!=="select"&&this.bumpIf("=")){var _=this.tryParseDecimalInteger(x.EXPECT_PLURAL_ARGUMENT_SELECTOR,x.INVALID_PLURAL_ARGUMENT_SELECTOR);if(_.err)return _;c=E(d,this.clonePosition()),l=this.message.slice(d.offset,this.offset())}else break}if(u.has(l))return this.error(o==="select"?x.DUPLICATE_SELECT_ARGUMENT_SELECTOR:x.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);l==="other"&&(a=!0),this.bumpSpace();var m=this.clonePosition();if(!this.bumpIf("{"))return this.error(o==="select"?x.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:x.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,E(this.clonePosition(),this.clonePosition()));var h=this.parseMessage(t+1,o,r);if(h.err)return h;var S=this.tryParseArgumentClose(m);if(S.err)return S;s.push([l,{value:h.val,location:E(m,this.clonePosition())}]),u.add(l),this.bumpSpace(),i=this.parseIdentifierIfPossible(),l=i.value,c=i.location}return s.length===0?this.error(o==="select"?x.EXPECT_SELECT_ARGUMENT_SELECTOR:x.EXPECT_PLURAL_ARGUMENT_SELECTOR,E(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(x.MISSING_OTHER_CLAUSE,E(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(t,o){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,a=0;!this.isEOF();){var s=this.char();if(s>=48&&s<=57)i=!0,a=a*10+(s-48),this.bump();else break}var u=E(n,this.clonePosition());return i?(a*=r,Fp(a)?{val:a,err:null}:this.error(o,u)):this.error(t,u)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var t=this.position.offset;if(t>=this.message.length)throw Error("out of bound");var o=Ao(this.message,t);if(o===void 0)throw Error("Offset ".concat(t," is at invalid UTF-16 code unit boundary"));return o},e.prototype.error=function(t,o){return{val:null,err:{kind:t,message:this.message,location:o}}},e.prototype.bump=function(){if(!this.isEOF()){var t=this.char();t===10?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=t<65536?1:2)}},e.prototype.bumpIf=function(t){if(Bt(this.message,t,this.offset())){for(var o=0;o<t.length;o++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(t){var o=this.offset(),r=this.message.indexOf(t,o);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(t){if(this.offset()>t)throw Error("targetOffset ".concat(t," must be greater than or equal to the current offset ").concat(this.offset()));for(t=Math.min(t,this.message.length);;){var o=this.offset();if(o===t)break;if(o>t)throw Error("targetOffset ".concat(t," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&Oo(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var t=this.char(),o=this.offset(),r=this.message.charCodeAt(o+(t>=65536?2:1));return r??null},e}();function _t(e){return e>=97&&e<=122||e>=65&&e<=90}function Jp(e){return _t(e)||e===47}function Kp(e){return e===45||e===46||e>=48&&e<=57||e===95||e>=97&&e<=122||e>=65&&e<=90||e==183||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039}function Oo(e){return e>=9&&e<=13||e===32||e===133||e>=8206&&e<=8207||e===8232||e===8233}function Yp(e){return e>=33&&e<=35||e===36||e>=37&&e<=39||e===40||e===41||e===42||e===43||e===44||e===45||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||e===91||e===92||e===93||e===94||e===96||e===123||e===124||e===125||e===126||e===161||e>=162&&e<=165||e===166||e===167||e===169||e===171||e===172||e===174||e===176||e===177||e===182||e===187||e===191||e===215||e===247||e>=8208&&e<=8213||e>=8214&&e<=8215||e===8216||e===8217||e===8218||e>=8219&&e<=8220||e===8221||e===8222||e===8223||e>=8224&&e<=8231||e>=8240&&e<=8248||e===8249||e===8250||e>=8251&&e<=8254||e>=8257&&e<=8259||e===8260||e===8261||e===8262||e>=8263&&e<=8273||e===8274||e===8275||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||e===8608||e>=8609&&e<=8610||e===8611||e>=8612&&e<=8613||e===8614||e>=8615&&e<=8621||e===8622||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||e===8658||e===8659||e===8660||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||e===8968||e===8969||e===8970||e===8971||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||e===9001||e===9002||e>=9003&&e<=9083||e===9084||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||e===9655||e>=9656&&e<=9664||e===9665||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||e===9839||e>=9840&&e<=10087||e===10088||e===10089||e===10090||e===10091||e===10092||e===10093||e===10094||e===10095||e===10096||e===10097||e===10098||e===10099||e===10100||e===10101||e>=10132&&e<=10175||e>=10176&&e<=10180||e===10181||e===10182||e>=10183&&e<=10213||e===10214||e===10215||e===10216||e===10217||e===10218||e===10219||e===10220||e===10221||e===10222||e===10223||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||e===10627||e===10628||e===10629||e===10630||e===10631||e===10632||e===10633||e===10634||e===10635||e===10636||e===10637||e===10638||e===10639||e===10640||e===10641||e===10642||e===10643||e===10644||e===10645||e===10646||e===10647||e===10648||e>=10649&&e<=10711||e===10712||e===10713||e===10714||e===10715||e>=10716&&e<=10747||e===10748||e===10749||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||e===11158||e>=11159&&e<=11263||e>=11776&&e<=11777||e===11778||e===11779||e===11780||e===11781||e>=11782&&e<=11784||e===11785||e===11786||e===11787||e===11788||e===11789||e>=11790&&e<=11798||e===11799||e>=11800&&e<=11801||e===11802||e===11803||e===11804||e===11805||e>=11806&&e<=11807||e===11808||e===11809||e===11810||e===11811||e===11812||e===11813||e===11814||e===11815||e===11816||e===11817||e>=11818&&e<=11822||e===11823||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||e===11840||e===11841||e===11842||e>=11843&&e<=11855||e>=11856&&e<=11857||e===11858||e>=11859&&e<=11903||e>=12289&&e<=12291||e===12296||e===12297||e===12298||e===12299||e===12300||e===12301||e===12302||e===12303||e===12304||e===12305||e>=12306&&e<=12307||e===12308||e===12309||e===12310||e===12311||e===12312||e===12313||e===12314||e===12315||e===12316||e===12317||e>=12318&&e<=12319||e===12320||e===12336||e===64830||e===64831||e>=65093&&e<=65094}function pt(e){e.forEach(function(t){if(delete t.location,vo(t)||yo(t))for(var o in t.options)delete t.options[o].location,pt(t.options[o].value);else fo(t)&&xo(t.style)||(bo(t)||wo(t))&&lt(t.style)?delete t.style.location:ko(t)&&pt(t.children)})}function Qp(e,t){t===void 0&&(t={}),t=T({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var o=new Zp(e,t).parse();if(o.err){var r=SyntaxError(x[o.err.kind]);throw r.location=o.err.location,r.originalMessage=o.err.message,r}return t?.captureLocation||pt(o.val),o.val}function ot(e,t){var o=t&&t.cache?t.cache:ih,r=t&&t.serializer?t.serializer:nh,n=t&&t.strategy?t.strategy:th;return n(e,{cache:o,serializer:r})}function eh(e){return e==null||typeof e=="number"||typeof e=="boolean"}function Lo(e,t,o,r){var n=eh(r)?r:o(r),i=t.get(n);return typeof i>"u"&&(i=e.call(this,r),t.set(n,i)),i}function Io(e,t,o){var r=Array.prototype.slice.call(arguments,3),n=o(r),i=t.get(n);return typeof i>"u"&&(i=e.apply(this,r),t.set(n,i)),i}function wt(e,t,o,r,n){return o.bind(t,e,r,n)}function th(e,t){var o=e.length===1?Lo:Io;return wt(e,this,o,t.cache.create(),t.serializer)}function oh(e,t){return wt(e,this,Io,t.cache.create(),t.serializer)}function rh(e,t){return wt(e,this,Lo,t.cache.create(),t.serializer)}var nh=function(){return JSON.stringify(arguments)};function vt(){this.cache=Object.create(null)}vt.prototype.get=function(e){return this.cache[e]};vt.prototype.set=function(e,t){this.cache[e]=t};var ih={create:function(){return new vt}},rt={variadic:oh,monadic:rh},we;(function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"})(we||(we={}));var Ge=function(e){Ue(t,e);function t(o,r,n){var i=e.call(this,o)||this;return i.code=r,i.originalMessage=n,i}return t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),Vt=function(e){Ue(t,e);function t(o,r,n,i){return e.call(this,'Invalid values for "'.concat(o,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),we.INVALID_VALUE,i)||this}return t}(Ge),ah=function(e){Ue(t,e);function t(o,r,n){return e.call(this,'Value for "'.concat(o,'" must be of type ').concat(r),we.INVALID_VALUE,n)||this}return t}(Ge),sh=function(e){Ue(t,e);function t(o,r){return e.call(this,'The intl string context variable "'.concat(o,'" was not provided to the string "').concat(r,'"'),we.MISSING_VALUE,r)||this}return t}(Ge),q;(function(e){e[e.literal=0]="literal",e[e.object=1]="object"})(q||(q={}));function lh(e){return e.length<2?e:e.reduce(function(t,o){var r=t[t.length-1];return!r||r.type!==q.literal||o.type!==q.literal?t.push(o):r.value+=o.value,t},[])}function ch(e){return typeof e=="function"}function Ce(e,t,o,r,n,i,a){if(e.length===1&&Rt(e[0]))return[{type:q.literal,value:e[0].value}];for(var s=[],u=0,l=e;u<l.length;u++){var c=l[u];if(Rt(c)){s.push({type:q.literal,value:c.value});continue}if(Sp(c)){typeof i=="number"&&s.push({type:q.literal,value:o.getNumberFormat(t).format(i)});continue}var d=c.value;if(!(n&&d in n))throw new sh(d,a);var _=n[d];if(Ep(c)){(!_||typeof _=="string"||typeof _=="number")&&(_=typeof _=="string"||typeof _=="number"?String(_):""),s.push({type:typeof _=="string"?q.literal:q.object,value:_});continue}if(bo(c)){var m=typeof c.style=="string"?r.date[c.style]:lt(c.style)?c.style.parsedOptions:void 0;s.push({type:q.literal,value:o.getDateTimeFormat(t,m).format(_)});continue}if(wo(c)){var m=typeof c.style=="string"?r.time[c.style]:lt(c.style)?c.style.parsedOptions:r.time.medium;s.push({type:q.literal,value:o.getDateTimeFormat(t,m).format(_)});continue}if(fo(c)){var m=typeof c.style=="string"?r.number[c.style]:xo(c.style)?c.style.parsedOptions:void 0;m&&m.scale&&(_=_*(m.scale||1)),s.push({type:q.literal,value:o.getNumberFormat(t,m).format(_)});continue}if(ko(c)){var h=c.children,S=c.value,$=n[S];if(!ch($))throw new ah(S,"function",a);var V=Ce(h,t,o,r,n,i),D=$(V.map(function(U){return U.value}));Array.isArray(D)||(D=[D]),s.push.apply(s,D.map(function(U){return{type:typeof U=="string"?q.literal:q.object,value:U}}))}if(vo(c)){var j=c.options[_]||c.options.other;if(!j)throw new Vt(c.value,_,Object.keys(c.options),a);s.push.apply(s,Ce(j.value,t,o,r,n));continue}if(yo(c)){var j=c.options["=".concat(_)];if(!j){if(!Intl.PluralRules)throw new Ge(`Intl.PluralRules is not available in this environment.
Try polyfilling it using "@formatjs/intl-pluralrules"
`,we.MISSING_INTL_API,a);var z=o.getPluralRules(t,{type:c.pluralType}).select(_-(c.offset||0));j=c.options[z]||c.options.other}if(!j)throw new Vt(c.value,_,Object.keys(c.options),a);s.push.apply(s,Ce(j.value,t,o,r,n,_-(c.offset||0)));continue}}return lh(s)}function uh(e,t){return t?T(T(T({},e||{}),t||{}),Object.keys(e).reduce(function(o,r){return o[r]=T(T({},e[r]),t[r]||{}),o},{})):e}function dh(e,t){return t?Object.keys(e).reduce(function(o,r){return o[r]=uh(e[r],t[r]),o},T({},e)):e}function nt(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,o){e[t]=o}}}}}function _h(e){return e===void 0&&(e={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:ot(function(){for(var t,o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];return new((t=Intl.NumberFormat).bind.apply(t,et([void 0],o,!1)))},{cache:nt(e.number),strategy:rt.variadic}),getDateTimeFormat:ot(function(){for(var t,o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];return new((t=Intl.DateTimeFormat).bind.apply(t,et([void 0],o,!1)))},{cache:nt(e.dateTime),strategy:rt.variadic}),getPluralRules:ot(function(){for(var t,o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];return new((t=Intl.PluralRules).bind.apply(t,et([void 0],o,!1)))},{cache:nt(e.pluralRules),strategy:rt.variadic})}}var Co=function(){function e(t,o,r,n){var i=this;if(o===void 0&&(o=e.defaultLocale),this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(u){var l=i.formatToParts(u);if(l.length===1)return l[0].value;var c=l.reduce(function(d,_){return!d.length||_.type!==q.literal||typeof d[d.length-1]!="string"?d.push(_.value):d[d.length-1]+=_.value,d},[]);return c.length<=1?c[0]||"":c},this.formatToParts=function(u){return Ce(i.ast,i.locales,i.formatters,i.formats,u,void 0,i.message)},this.resolvedOptions=function(){var u;return{locale:((u=i.resolvedLocale)===null||u===void 0?void 0:u.toString())||Intl.NumberFormat.supportedLocalesOf(i.locales)[0]}},this.getAst=function(){return i.ast},this.locales=o,this.resolvedLocale=e.resolveLocale(o),typeof t=="string"){if(this.message=t,!e.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var a=n||{};a.formatters;var s=xp(a,["formatters"]);this.ast=e.__parse(t,T(T({},s),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=dh(e.formats,r),this.formatters=n&&n.formatters||_h(this.formatterCache)}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(t){if(!(typeof Intl.Locale>"u")){var o=Intl.NumberFormat.supportedLocalesOf(t);return o.length>0?new Intl.Locale(o[0]):new Intl.Locale(typeof t=="string"?t:t[0])}},e.__parse=Qp,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();function ph(e,t){if(t==null)return;if(t in e)return e[t];const o=t.split(".");let r=e;for(let n=0;n<o.length;n++)if(typeof r=="object"){if(n>0){const i=o.slice(n,o.length).join(".");if(i in r){r=r[i];break}}r=r[o[n]]}else r=void 0;return r}const ae={},hh=(e,t,o)=>o&&(t in ae||(ae[t]={}),e in ae[t]||(ae[t][e]=o),o),Do=(e,t)=>{if(t==null)return;if(t in ae&&e in ae[t])return ae[t][e];const o=Le(t);for(let r=0;r<o.length;r++){const n=o[r],i=gh(n,e);if(i)return hh(e,t,i)}};let yt;const Oe=se({});function mh(e){return yt[e]||null}function Ro(e){return e in yt}function gh(e,t){if(!Ro(e))return null;const o=mh(e);return ph(o,t)}function fh(e){if(e==null)return;const t=Le(e);for(let o=0;o<t.length;o++){const r=t[o];if(Ro(r))return r}}function jo(e,...t){delete ae[e],Oe.update(o=>(o[e]=kp.all([o[e]||{},...t]),o))}ye([Oe],([e])=>Object.keys(e));Oe.subscribe(e=>yt=e);const De={};function bh(e,t){De[e].delete(t),De[e].size===0&&delete De[e]}function No(e){return De[e]}function wh(e){return Le(e).map(t=>{const o=No(t);return[t,o?[...o]:[]]}).filter(([,t])=>t.length>0)}function Ne(e){return e==null?!1:Le(e).some(t=>{var o;return(o=No(t))==null?void 0:o.size})}function vh(e,t){return Promise.all(t.map(r=>(bh(e,r),r().then(n=>n.default||n)))).then(r=>jo(e,...r))}const Se={};function Ho(e){if(!Ne(e))return e in Se?Se[e]:Promise.resolve();const t=wh(e);return Se[e]=Promise.all(t.map(([o,r])=>vh(o,r))).then(()=>{if(Ne(e))return Ho(e);delete Se[e]}),Se[e]}var Ut=Object.getOwnPropertySymbols,yh=Object.prototype.hasOwnProperty,kh=Object.prototype.propertyIsEnumerable,xh=(e,t)=>{var o={};for(var r in e)yh.call(e,r)&&t.indexOf(r)<0&&(o[r]=e[r]);if(e!=null&&Ut)for(var r of Ut(e))t.indexOf(r)<0&&kh.call(e,r)&&(o[r]=e[r]);return o};const Eh={number:{scientific:{notation:"scientific"},engineering:{notation:"engineering"},compactLong:{notation:"compact",compactDisplay:"long"},compactShort:{notation:"compact",compactDisplay:"short"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};function Sh({locale:e,id:t}){console.warn(`[svelte-i18n] The message "${t}" was not found in "${Le(e).join('", "')}".${Ne(le())?`

Note: there are at least one loader still registered to this locale that wasn't executed.`:""}`)}const $h={fallbackLocale:null,loadingDelay:200,formats:Eh,warnOnMissingMessages:!0,handleMissingMessage:void 0,ignoreTag:!0},$e=$h;function ve(){return $e}function Th(e){const t=e,{formats:o}=t,r=xh(t,["formats"]);let n=e.fallbackLocale;if(e.initialLocale)try{Co.resolveLocale(e.initialLocale)&&(n=e.initialLocale)}catch{console.warn(`[svelte-i18n] The initial locale "${e.initialLocale}" is not a valid locale.`)}return r.warnOnMissingMessages&&(delete r.warnOnMissingMessages,r.handleMissingMessage==null?r.handleMissingMessage=Sh:console.warn('[svelte-i18n] The "warnOnMissingMessages" option is deprecated. Please use the "handleMissingMessage" option instead.')),Object.assign($e,r,{initialLocale:n}),o&&("number"in o&&Object.assign($e.formats.number,o.number),"date"in o&&Object.assign($e.formats.date,o.date),"time"in o&&Object.assign($e.formats.time,o.time)),_e.set(n)}const it=se(!1);var Ah=Object.defineProperty,Ph=Object.defineProperties,Oh=Object.getOwnPropertyDescriptors,Gt=Object.getOwnPropertySymbols,Lh=Object.prototype.hasOwnProperty,Ih=Object.prototype.propertyIsEnumerable,Ft=(e,t,o)=>t in e?Ah(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,Ch=(e,t)=>{for(var o in t||(t={}))Lh.call(t,o)&&Ft(e,o,t[o]);if(Gt)for(var o of Gt(t))Ih.call(t,o)&&Ft(e,o,t[o]);return e},Dh=(e,t)=>Ph(e,Oh(t));let ht;const He=se(null);function qt(e){return e.split("-").map((t,o,r)=>r.slice(0,o+1).join("-")).reverse()}function Le(e,t=ve().fallbackLocale){const o=qt(e);return t?[...new Set([...o,...qt(t)])]:o}function le(){return ht??void 0}He.subscribe(e=>{ht=e??void 0,typeof window<"u"&&e!=null&&document.documentElement.setAttribute("lang",e)});const Rh=e=>{if(e&&fh(e)&&Ne(e)){const{loadingDelay:t}=ve();let o;return typeof window<"u"&&le()!=null&&t?o=window.setTimeout(()=>it.set(!0),t):it.set(!0),Ho(e).then(()=>{He.set(e)}).finally(()=>{clearTimeout(o),it.set(!1)})}return He.set(e)},_e=Dh(Ch({},He),{set:Rh}),jh=()=>typeof window>"u"?null:window.navigator.language||window.navigator.languages[0],Fe=e=>{const t=Object.create(null);return r=>{const n=JSON.stringify(r);return n in t?t[n]:t[n]=e(r)}};var Nh=Object.defineProperty,Be=Object.getOwnPropertySymbols,Bo=Object.prototype.hasOwnProperty,zo=Object.prototype.propertyIsEnumerable,Wt=(e,t,o)=>t in e?Nh(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,kt=(e,t)=>{for(var o in t||(t={}))Bo.call(t,o)&&Wt(e,o,t[o]);if(Be)for(var o of Be(t))zo.call(t,o)&&Wt(e,o,t[o]);return e},ke=(e,t)=>{var o={};for(var r in e)Bo.call(e,r)&&t.indexOf(r)<0&&(o[r]=e[r]);if(e!=null&&Be)for(var r of Be(e))t.indexOf(r)<0&&zo.call(e,r)&&(o[r]=e[r]);return o};const Pe=(e,t)=>{const{formats:o}=ve();if(e in o&&t in o[e])return o[e][t];throw new Error(`[svelte-i18n] Unknown "${t}" ${e} format.`)},Hh=Fe(e=>{var t=e,{locale:o,format:r}=t,n=ke(t,["locale","format"]);if(o==null)throw new Error('[svelte-i18n] A "locale" must be set to format numbers');return r&&(n=Pe("number",r)),new Intl.NumberFormat(o,n)}),Bh=Fe(e=>{var t=e,{locale:o,format:r}=t,n=ke(t,["locale","format"]);if(o==null)throw new Error('[svelte-i18n] A "locale" must be set to format dates');return r?n=Pe("date",r):Object.keys(n).length===0&&(n=Pe("date","short")),new Intl.DateTimeFormat(o,n)}),zh=Fe(e=>{var t=e,{locale:o,format:r}=t,n=ke(t,["locale","format"]);if(o==null)throw new Error('[svelte-i18n] A "locale" must be set to format time values');return r?n=Pe("time",r):Object.keys(n).length===0&&(n=Pe("time","short")),new Intl.DateTimeFormat(o,n)}),Mh=(e={})=>{var t=e,{locale:o=le()}=t,r=ke(t,["locale"]);return Hh(kt({locale:o},r))},Vh=(e={})=>{var t=e,{locale:o=le()}=t,r=ke(t,["locale"]);return Bh(kt({locale:o},r))},Uh=(e={})=>{var t=e,{locale:o=le()}=t,r=ke(t,["locale"]);return zh(kt({locale:o},r))},Gh=Fe((e,t=le())=>new Co(e,t,ve().formats,{ignoreTag:ve().ignoreTag})),Fh=(e,t={})=>{var o,r,n,i;let a=t;typeof e=="object"&&(a=e,e=a.id);const{values:s,locale:u=le(),default:l}=a;if(u==null)throw new Error("[svelte-i18n] Cannot format a message without first setting the initial locale.");let c=Do(e,u);if(!c)c=(i=(n=(r=(o=ve()).handleMissingMessage)==null?void 0:r.call(o,{locale:u,id:e,defaultValue:l}))!=null?n:l)!=null?i:e;else if(typeof c!="string")return console.warn(`[svelte-i18n] Message with id "${e}" must be of type "string", found: "${typeof c}". Gettin its value through the "$format" method is deprecated; use the "json" method instead.`),c;if(!s)return c;let d=c;try{d=Gh(c,u).format(s)}catch(_){_ instanceof Error&&console.warn(`[svelte-i18n] Message "${e}" has syntax error:`,_.message)}return d},qh=(e,t)=>Uh(t).format(e),Wh=(e,t)=>Vh(t).format(e),Xh=(e,t)=>Mh(t).format(e),Zh=(e,t=le())=>Do(e,t),Eg=ye([_e,Oe],()=>Fh);ye([_e],()=>qh);ye([_e],()=>Wh);ye([_e],()=>Xh);ye([_e,Oe],()=>Zh);let Mo=!1;typeof window<"u"&&"attachShadow"in Element.prototype&&"adoptedStyleSheets"in Document.prototype&&(Mo="adoptedStyleSheets"in document.createElement("div").attachShadow({mode:"open"}));function Xt(e,t){const o=new URL(import.meta.url).origin;var r=e;if(window.location.origin!==o&&(r=new URL(e,o).href),document.querySelector(`link[href='${r}']`))return Promise.resolve();const i=document.createElement("link");return i.rel="stylesheet",i.href=r,new Promise((a,s)=>{i.addEventListener("load",()=>a()),i.addEventListener("error",()=>{console.error(`Unable to preload CSS for ${r}`),a()}),t.appendChild(i)})}function Sg(e,t,o){if(!Mo)return e;o||(o=document.createElement("style")),o.remove();const r=new CSSStyleSheet;r.replaceSync(e);let n="";e=e.replace(/@import\s+url\((.*?)\);\s*/g,(u,l)=>(n+=`@import url(${l});
`,""));const i=r.cssRules;let a="",s=`.gradio-container.gradio-container-${t} .contain `;for(let u=0;u<i.length;u++){const l=i[u];let c=l.cssText.includes(".dark");if(l instanceof CSSStyleRule){const d=l.selectorText;if(d){const _=d.replace(".dark","").split(",").map(m=>`${c?".dark":""} ${s} ${m.trim()} `).join(",");a+=l.cssText,a+=l.cssText.replace(d,_)}}else if(l instanceof CSSMediaRule){let d=`@media ${l.media.mediaText} {`;for(let _=0;_<l.cssRules.length;_++){const m=l.cssRules[_];if(m instanceof CSSStyleRule){let h=m.cssText.includes(".dark ");const S=m.selectorText,$=S.replace(".dark","").split(",").map(V=>`${h?".dark":""} ${s} ${V.trim()} `).join(",");d+=m.cssText.replace(S,$)}}d+="}",a+=d}else if(l instanceof CSSKeyframesRule){a+=`@keyframes ${l.name} {`;for(let d=0;d<l.cssRules.length;d++){const _=l.cssRules[d];_ instanceof CSSKeyframeRule&&(a+=`${_.keyText} { ${_.style.cssText} }`)}a+="}"}else l instanceof CSSFontFaceRule&&(a+=`@font-face { ${l.style.cssText} }`)}return n+a}const Jh={accordion:{component:()=>p(()=>import("./Index-h9bXJ_mL.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14]),import.meta.url)},annotatedimage:{component:()=>p(()=>import("./Index-DkvwFonX.js"),__vite__mapDeps([15,1,2,3,4,5,6,16,17,18,10,19,7,8,9,11,20,21,22,23]),import.meta.url)},audio:{base:()=>p(()=>import("./StaticAudio-BcP3Ojpm.js"),__vite__mapDeps([24,2,3,25,4,5,6,16,10,17,26,27,28,29,19,30,31,32,33,20,21,22,34,35,36]),import.meta.url),example:()=>p(()=>import("./Example-BQyGztrG.js"),__vite__mapDeps([37,38]),import.meta.url),component:()=>p(()=>import("./index-S2_6NELW.js"),__vite__mapDeps([39,24,2,3,25,4,5,6,16,10,17,26,27,28,29,19,30,31,32,33,20,21,22,34,35,36,40,41,42,43,11,44,45,8,9,46,47,7,1,48,37,38]),import.meta.url)},box:{component:()=>p(()=>import("./Index-B1HmtWjh.js"),__vite__mapDeps([49,1,2,3,4,5,6]),import.meta.url)},browserstate:{component:()=>p(()=>import("./Index-DE2G75tA.js"),__vite__mapDeps([50,51,52]),import.meta.url)},button:{component:()=>p(()=>import("./Index-Dmcv9J9S.js"),__vite__mapDeps([53,54,55,20,21,22,56,2,3,4,5,6,57,58]),import.meta.url)},chatbot:{component:()=>p(()=>import("./Index--dfOxlka.js"),__vite__mapDeps([59,2,3,25,55,20,21,22,56,4,5,6,10,60,61,11,44,33,19,62,63,64,65,27,66,29,1,16,7,8,9,67,58]),import.meta.url)},checkbox:{example:()=>p(()=>import("./Example-CZ-iEz1g.js"),__vite__mapDeps([68,38]),import.meta.url),component:()=>p(()=>import("./Index-CW_DIAqj.js"),__vite__mapDeps([69,1,2,3,70,63,4,5,6,7,8,9,10,11,71]),import.meta.url)},checkboxgroup:{example:()=>p(()=>import("./Example-DccrJI--.js"),__vite__mapDeps([72,38]),import.meta.url),component:()=>p(()=>import("./Index-cWMuwh75.js"),__vite__mapDeps([73,1,2,3,74,70,63,4,5,6,7,8,9,10,11,75]),import.meta.url)},code:{example:()=>p(()=>import("./Example-Wp-_4AVX.js"),__vite__mapDeps([76,77]),import.meta.url),component:()=>p(()=>import("./Index-Cvh-nP3Y.js").then(e=>e.K),__vite__mapDeps([78,60,61,2,3,4,5,6,10,28,36,21,22,20,19,7,8,9,11,79,1,16,17,76,77,80]),import.meta.url)},colorpicker:{example:()=>p(()=>import("./Example-BaLyJYAe.js"),__vite__mapDeps([81,82]),import.meta.url),component:()=>p(()=>import("./Index-Dg5M8VLh.js"),__vite__mapDeps([83,84,2,3,74,70,63,4,5,6,1,7,8,9,10,11,81,82,85]),import.meta.url)},column:{component:()=>p(()=>import("./Index-CyFoS1ud.js"),__vite__mapDeps([12,7,8,9,2,3,4,5,6,10,11,13]),import.meta.url)},core:{component:()=>p(()=>import("./index-CmzkM4vU.js"),__vite__mapDeps([86,87]),import.meta.url)},dataframe:{example:()=>p(()=>import("./Example-CqPGqNav.js"),__vite__mapDeps([88,89]),import.meta.url),component:()=>p(()=>import("./Index-CBvqT7Px.js"),__vite__mapDeps([90,1,2,3,4,5,6,91,65,41,42,21,22,63,60,61,18,10,7,8,9,11,92,93,25,16,17,26,27,28,19,94,55,20,56,36,58,95,45,96,97,64,46,98,48,99,100,88,89,101]),import.meta.url)},dataset:{component:()=>p(()=>import("./Index-BPSRurdt.js"),__vite__mapDeps([102,1,2,3,4,5,6,8,9,103,104,105,106]),import.meta.url)},datetime:{example:()=>p(()=>import("./Example-BBLMS951.js"),[],import.meta.url),component:()=>p(()=>import("./Index-CmYP8ZA9.js"),__vite__mapDeps([107,1,2,3,74,70,63,4,5,6,108,109]),import.meta.url)},downloadbutton:{component:()=>p(()=>import("./Index-CezLPlFV.js"),__vite__mapDeps([110,54,55,20,21,22,56,2,3,4,5,6,57,58,111]),import.meta.url)},dropdown:{example:()=>p(()=>import("./Example-BgQNfMWT.js"),__vite__mapDeps([112,38]),import.meta.url),component:()=>p(()=>import("./Index-DRPpdVHV.js"),__vite__mapDeps([113,2,3,74,70,63,4,5,6,96,114,64,115,1,7,8,9,10,11,112,38]),import.meta.url)},file:{example:()=>p(()=>import("./Example-DrmWnoSo.js"),__vite__mapDeps([116,117]),import.meta.url),component:()=>p(()=>import("./Index-BDMvYnhH.js"),__vite__mapDeps([118,119,2,3,4,5,6,16,17,62,41,42,10,11,19,21,22,36,20,120,1,48,7,8,9,116,117]),import.meta.url)},fileexplorer:{example:()=>p(()=>import("./Example-CIFMxn5c.js"),__vite__mapDeps([121,122]),import.meta.url),component:()=>p(()=>import("./Index-C-3T0NT0.js"),__vite__mapDeps([123,2,3,62,1,4,5,6,16,7,8,9,10,11,124]),import.meta.url)},form:{component:()=>p(()=>import("./Index-DE1Sah7F.js"),__vite__mapDeps([125,126]),import.meta.url)},gallery:{base:()=>p(()=>import("./Gallery-WYXHIDmJ.js"),__vite__mapDeps([127,2,3,4,5,6,16,10,17,26,27,25,11,28,18,32,19,43,44,33,36,21,22,20,55,56,128,34,129,130,42,58]),import.meta.url),component:()=>p(()=>import("./Index-CYXp1oKN.js"),__vite__mapDeps([131,1,2,3,4,5,6,48,41,42,127,16,10,17,26,27,25,11,28,18,32,19,43,44,33,36,21,22,20,55,56,128,34,129,130,58,7,8,9,119,62,120,117]),import.meta.url)},group:{component:()=>p(()=>import("./Index-WEzAIkMk.js"),__vite__mapDeps([132,133]),import.meta.url)},highlightedtext:{component:()=>p(()=>import("./Index-XYFNClev.js"),__vite__mapDeps([134,135,2,3,1,4,5,6,16,17,7,8,9,10,11,136]),import.meta.url)},html:{base:()=>p(()=>import("./Index-BBxgX-PX.js"),__vite__mapDeps([137,7,8,9,2,3,4,5,6,10,11,79,1,16,25,138]),import.meta.url),example:()=>p(()=>import("./Example-C2a4WxRl.js"),__vite__mapDeps([139,140]),import.meta.url),component:()=>p(()=>import("./Index-BBxgX-PX.js"),__vite__mapDeps([137,7,8,9,2,3,4,5,6,10,11,79,1,16,25,138]),import.meta.url)},image:{base:()=>p(()=>import("./ImagePreview-YDrf4kgB.js"),__vite__mapDeps([93,2,3,25,4,5,6,16,10,17,26,27,28,18,19,94,55,20,21,22,56,36,58]),import.meta.url),example:()=>p(()=>import("./Example-CC8yxxGn.js"),__vite__mapDeps([99,55,20,21,22,56,100]),import.meta.url),component:()=>p(()=>import("./Index-DR3SLfLc.js"),__vite__mapDeps([92,93,2,3,25,4,5,6,16,10,17,26,27,28,18,19,94,55,20,21,22,56,36,58,95,11,45,41,42,96,97,8,9,64,46,98,1,48,7,99,100]),import.meta.url)},imageeditor:{example:()=>p(()=>import("./Example-BtWEWnXX.js"),__vite__mapDeps([141,2,3,4,5,6,55,20,21,22,56,95,16,10,11,18,45,41,42,19,94,96,97,8,9,64,46,98,142,58,100]),import.meta.url),component:()=>p(()=>import("./Index-BLqT3gRt.js"),__vite__mapDeps([143,93,2,3,25,4,5,6,16,10,17,26,27,28,18,19,94,55,20,21,22,56,36,58,95,11,45,41,42,96,97,8,9,64,46,98,1,7,51,52,60,66,33,84,144,100]),import.meta.url)},json:{component:()=>p(()=>import("./Index-CC80w9zK.js"),__vite__mapDeps([145,60,61,2,3,4,5,6,10,17,19,1,16,7,8,9,11,146]),import.meta.url)},label:{component:()=>p(()=>import("./Index-DX8QzOB3.js"),__vite__mapDeps([147,2,3,148,1,4,5,6,16,17,7,8,9,10,11,149]),import.meta.url)},markdown:{example:()=>p(()=>import("./Example-CxAuiy_R.js"),__vite__mapDeps([150,63,4,5,6,38]),import.meta.url),component:()=>p(()=>import("./Index-VHcFswDo.js"),__vite__mapDeps([151,2,3,25,60,61,63,4,5,6,10,19,7,8,9,11,1,150,38,152]),import.meta.url)},model3d:{example:()=>p(()=>import("./Example-uQ8MuYg6.js"),__vite__mapDeps([153,38]),import.meta.url),component:()=>p(()=>import("./Index-whzS5J2I.js"),__vite__mapDeps([154,2,3,4,5,6,16,10,28,62,33,19,41,42,43,11,44,36,21,22,20,1,17,48,7,8,9,153,38,155]),import.meta.url)},multimodaltextbox:{example:()=>p(()=>import("./Example-B1EET6YG.js"),__vite__mapDeps([156,55,20,21,22,56,2,3,4,5,6,128,34,129,157,58]),import.meta.url),component:()=>p(()=>import("./Index-DxLSS6WM.js"),__vite__mapDeps([158,2,3,74,70,63,4,5,6,11,62,45,41,42,29,159,97,160,21,22,55,20,56,40,43,10,28,44,33,19,36,16,8,9,46,30,25,31,32,17,34,35,47,1,7,156,128,129,157,58,161]),import.meta.url)},nativeplot:{example:()=>p(()=>import("./Example-Creifpe8.js"),[],import.meta.url),component:()=>p(()=>import("./Index-BZ6fZX36.js"),__vite__mapDeps([162,1,2,3,74,70,63,4,5,6,17,148,7,8,9,10,11,163]),import.meta.url)},number:{example:()=>p(()=>import("./Example-CqL1e7EB.js"),__vite__mapDeps([164,38]),import.meta.url),component:()=>p(()=>import("./Index-6qq3XBNc.js"),__vite__mapDeps([165,1,2,3,74,70,63,4,5,6,7,8,9,10,11,166]),import.meta.url)},paramviewer:{example:()=>p(()=>import("./Example-C9__vDgN.js"),__vite__mapDeps([167,38]),import.meta.url),component:()=>p(()=>import("./Index-BRvupx4g.js"),__vite__mapDeps([168,5,169]),import.meta.url)},plot:{base:()=>p(()=>import("./Plot-mBuBMWSx.js").then(e=>e.b),__vite__mapDeps([170,2,3,4,5,6,17]),import.meta.url),component:()=>p(()=>import("./Index-BS4qfRhS.js"),__vite__mapDeps([171,170,2,3,4,5,6,17,1,16,7,8,9,10,11]),import.meta.url)},radio:{example:()=>p(()=>import("./Example-BoMLuz1A.js"),__vite__mapDeps([172,38]),import.meta.url),component:()=>p(()=>import("./Index-C2istuOy.js"),__vite__mapDeps([173,1,2,3,74,70,63,4,5,6,7,8,9,10,11,172,38,174]),import.meta.url)},row:{component:()=>p(()=>import("./Index-tcAhRwtE.js"),__vite__mapDeps([175,7,8,9,2,3,4,5,6,10,11,176]),import.meta.url)},sidebar:{component:()=>p(()=>import("./Index-B3a3KgKr.js"),__vite__mapDeps([177,7,8,9,2,3,4,5,6,10,11,12,13,178]),import.meta.url)},sketchbox:{component:()=>p(()=>import("./Index-BlWK1-fD.js"),__vite__mapDeps([179,180]),import.meta.url)},slider:{example:()=>p(()=>import("./Example-BrizabXh.js"),__vite__mapDeps([181,38]),import.meta.url),component:()=>p(()=>import("./Index-B_OkAlP9.js"),__vite__mapDeps([182,1,2,3,74,70,63,4,5,6,7,8,9,10,11,183]),import.meta.url)},state:{component:()=>p(()=>import("./Index-uRgjJb4U.js"),[],import.meta.url)},statustracker:{component:()=>p(()=>import("./index-rDz047cy.js"),__vite__mapDeps([184,7,8,9,2,3,4,5,6,10,11,185,64,46]),import.meta.url)},tabitem:{component:()=>p(()=>import("./Index-C2AEGyOu.js"),__vite__mapDeps([186,187,188,12,7,8,9,2,3,4,5,6,10,11,13,189]),import.meta.url)},tabs:{component:()=>p(()=>import("./Index-Bs4_V4Rr.js"),__vite__mapDeps([190,187,188]),import.meta.url)},textbox:{example:()=>p(()=>import("./Example-Cx2SdskM.js"),__vite__mapDeps([103,104]),import.meta.url),component:()=>p(()=>import("./Index-B6bwX9mR.js"),__vite__mapDeps([191,192,2,3,74,70,63,4,5,6,60,61,159,97,64,106,1,7,8,9,10,11,103,104]),import.meta.url)},timer:{component:()=>p(()=>import("./Index-BMLc4VxK.js"),[],import.meta.url)},uploadbutton:{component:()=>p(()=>import("./Index-D-YxDJVI.js"),__vite__mapDeps([193,54,55,20,21,22,56,2,3,4,5,6,57,58,194]),import.meta.url)},video:{base:()=>p(()=>import("./VideoPreview-Bmn4fgzX.js").then(e=>e.a),__vite__mapDeps([195,2,3,4,5,6,16,10,17,26,27,25,28,160,19,36,21,22,20,31,32,33,128,34,129,43,11,44,196,42]),import.meta.url),example:()=>p(()=>import("./Example-97koPlcW.js"),__vite__mapDeps([197,128,20,21,22,34,129,198]),import.meta.url),component:()=>p(()=>import("./index-D_7Fqv5G.js"),__vite__mapDeps([199,41,42,2,3,4,5,6,16,160,45,21,22,55,20,56,95,10,11,18,19,94,96,97,8,9,64,46,98,128,34,129,195,17,26,27,25,28,36,31,32,33,43,44,196,197,198,1,48,7,200,58,100]),import.meta.url)}},ne={},xt=typeof window<"u";function Zt({api_url:e,name:t,id:o,variant:r}){const n=xt&&window.__GRADIO__CC__,i={...Jh,...n||{}};let a=o||t;if(ne[`${a}-${r}`])return{component:ne[`${a}-${r}`],name:t};try{if(!i?.[a]?.[r]&&!i?.[t]?.[r])throw new Error;return ne[`${a}-${r}`]=(i?.[a]?.[r]||i?.[t]?.[r])(),{name:t,component:ne[`${a}-${r}`]}}catch{if(!a)throw new Error(`Component not found: ${t}`);try{return ne[`${a}-${r}`]=Kh(e,a,r),{name:t,component:ne[`${a}-${r}`]}}catch(u){if(r==="example")return ne[`${a}-${r}`]=p(()=>import("./Example-DxdiEFS_.js"),__vite__mapDeps([201,38]),import.meta.url),{name:t,component:ne[`${a}-${r}`]};throw console.error(`failed to load: ${t}`),console.error(u),u}}}function Jt(e){return xt?new Promise((t,o)=>{const r=document.createElement("link");r.rel="stylesheet",r.href=e,document.head.appendChild(r),r.onload=()=>t(),r.onerror=()=>o()}):Promise.resolve()}function Kh(e,t,o){const r=xt?"client":"server";let n;return r==="server"?Promise.all([Jt(`${e}/custom_component/${t}/${o}/style.css`),p(()=>import("./Index-BjBu9-9D.js"),__vite__mapDeps([202,1,2,3,4,5,6,7,8,9,10,11,203]),import.meta.url)]).then(([i,a])=>a):(n=`${e}/custom_component/${t}/${r}/${o}/index.js`,Promise.all([Jt(`${e}/custom_component/${t}/${r}/${o}/style.css`),import(n)]).then(([i,a])=>a))}function Yh(){const e=se({}),t={},o={},r=new Map,n=new Map,i=new Map,a={};function s({fn_index:l,status:c,queue:d=!0,size:_,position:m=null,eta:h=null,message:S=null,progress:$,time_limit:V=null}){const D=o[l],j=t[l],z=a[l],U=D.map(C=>{let B;const G=r.get(C)||0;if(z==="pending"&&c!=="pending"){let W=G-1;r.set(C,W<0?0:W),B=W>0?"pending":c}else z==="pending"&&c==="pending"?B="pending":z!=="pending"&&c==="pending"?(B="pending",r.set(C,G+1)):B=c;return{id:C,queue_position:m,queue_size:_,eta:h,status:B,message:S,progress:$}});j.forEach(C=>{const B=n.get(C)||0;if(z==="pending"&&c!=="pending"){let G=B-1;n.set(C,G<0?0:G),i.set(C,c)}else z!=="pending"&&c==="pending"?(n.set(C,B+1),i.set(C,c)):i.delete(C)}),e.update(C=>(U.forEach(({id:B,queue_position:G,queue_size:W,eta:pe,status:b,message:y,progress:g})=>{C[B]={queue:d,queue_size:W,queue_position:G,eta:pe,message:y,progress:g,status:b,fn_index:l}}),C)),a[l]=c}function u(l,c,d){t[l]=c,o[l]=d}return{update:s,register:u,subscribe:e.subscribe,get_status_for_fn(l){return a[l]},get_inputs_to_update(){return i}}}let me=[];const Qh=typeof window<"u",em=Qh?requestAnimationFrame:async e=>await e();function $g(e){let t,o=se({}),r={},n,i,a,s,u=Yh();const l=se(e);let c=[],d,_={},m;function h(y){y.forEach(g=>{g.targets.forEach(P=>{const w=s[P[0]];w&&g.event_specific_args?.length>0&&g.event_specific_args?.forEach(f=>{w.props[f]=g[f]})})})}async function S({app:y,components:g,layout:P,dependencies:w,root:f,options:L}){U(),d=y,z(c),c=g,n=new Set,i=new Set,me=[],a=new Map,t=new Map,s={},m={id:P.id,type:"column",props:{interactive:!1,scale:L.fill_height?1:null},has_modes:!1,instance:null,component:null,component_class_id:"",key:null},g.push(m),w.forEach(A=>{u.register(A.id,A.inputs,A.show_progress_on||A.outputs),A.frontend_fn=Kt(A.js,!!A.backend_fn,A.inputs.length,A.outputs.length),Yt(A.targets,A.id,r),Qt(A,n,i)}),o.set(r),a=eo(g,f),s=g.reduce((A,N)=>(A[N.id]=N,A),{}),await V(P,f,c),l.set(m),h(w)}function $({render_id:y,components:g,layout:P,root:w,dependencies:f}){eo(g,w).forEach((k,R)=>{a.set(R,k)}),r={},f.forEach(k=>{u.register(k.id,k.inputs,k.outputs),k.frontend_fn=Kt(k.js,!!k.backend_fn,k.inputs.length,k.outputs.length),Yt(k.targets,k.id,r),Qt(k,n,i)}),o.set(r);let A=s[P.id],N=[];const v=k=>{N.push(k),k.children&&k.children.forEach(R=>{v(R)})};v(A),z(N),Object.entries(s).forEach(([k,R])=>{let M=Number(k);R.rendered_in===y&&(delete s[M],t.has(M)&&t.delete(M))}),g.forEach(k=>{s[k.id]=k,t.set(k.id,k)}),A.parent&&(A.parent.children[A.parent.children.indexOf(A)]=s[P.id]),V(P,w,c.concat(g),A.parent).then(()=>{l.set(m)}),h(f)}async function V(y,g,P,w){const f=s[y.id];if(f.component=(await a.get(f.component_class_id||f.type))?.default,f.parent=w,f.type==="dataset"&&(f.props.component_map=Vo(f.type,f.component_class_id,g,P,f.props.components).example_components),r[f.id]&&(f.props.attached_events=Object.keys(r[f.id])),f.props.interactive=rm(f.id,f.props.interactive,f.props.value,n,i),f.props.server=nm(f.id,f.props.server_fns,d),f.key!=null&&_[f.key]!==void 0&&(f.props.value=_[f.key]),t.set(f.id,f),y.children&&(f.children=await Promise.all(y.children.map(L=>V(L,g,P,f)))),f.type==="tabs"&&!f.props.initial_tabs){const A=(y.children?.map((N,v)=>{const k=s[N.id];return k.props.id??=N.id,{type:k.type,props:{...k.props,id:k.props.id,order:v}}})||[]).filter(N=>N.type==="tabitem");f.props.initial_tabs=A?.map(N=>({label:N.props.label,id:N.props.id,visible:N.props.visible,interactive:N.props.interactive,order:N.props.order}))}return f.type==="tabs"&&y.children?.forEach((L,A)=>{const N=s[L.id];N.props.order=A}),f}let D=!1,j=se(!1);function z(y){y.forEach(g=>{g.key!=null&&(_[g.key]=g.props.value)})}function U(){l.update(y=>{for(let g=0;g<me.length;g++)for(let P=0;P<me[g].length;P++){const w=me[g][P];if(!w)continue;const f=s[w.id];if(!f)continue;let L;w.value instanceof Map?L=new Map(w.value):w.value instanceof Set?L=new Set(w.value):Array.isArray(w.value)?L=[...w.value]:w.value==null?L=null:typeof w.value=="object"?L={...w.value}:L=w.value,f.props[w.prop]=L}return y}),me=[],D=!1,j.set(!1)}function C(y){y&&(me.push(y),D||(D=!0,j.set(!0),em(U)))}function B(y){let g=t.get(y);if(!g){const P=ip(l);g=G(P,y)}return g?g.instance?.get_value?g.instance.get_value():g.props.value:null}function G(y,g){if(y.id===g)return y;if(y.children)for(const P of y.children){const w=G(P,g);if(w)return w}}function W(y,g){const P=t.get(y);P&&P.instance?.modify_stream_state&&P.instance.modify_stream_state(g)}function pe(y){const g=t.get(y);return g?.instance?.get_stream_state?g.instance.get_stream_state():"not_set"}function b(y,g){const P=t.get(y);P?.instance?.set_time_limit&&P.instance.set_time_limit(g)}return{layout:l,targets:o,update_value:C,get_data:B,modify_stream:W,get_stream_state:pe,set_time_limit:b,loading_status:u,scheduled_updates:j,create_layout:S,rerender_layout:$}}const tm=Object.getPrototypeOf(async function(){}).constructor;function Kt(e,t,o,r){if(!e||e===!0)return null;const n=t?o===1:r===1;try{return new tm("__fn_args",`  let result = await (${e})(...__fn_args);
  if (typeof result === "undefined") return [];
  return (${n} && !Array.isArray(result)) ? [result] : result;`)}catch(i){return console.error("Could not parse custom js method."),console.error(i),null}}function Yt(e,t,o){return e.forEach(([r,n])=>{o[r]||(o[r]={}),o[r]?.[n]&&!o[r]?.[n].includes(t)?o[r][n].push(t):o[r][n]=[t]}),o}function Qt(e,t,o){return e.inputs.forEach(r=>t.add(r)),e.outputs.forEach(r=>o.add(r)),[t,o]}function om(e){return Array.isArray(e)&&e.length===0||e===""||e===0||!e}function rm(e,t,o,r,n){return t===!1?!1:t===!0?!0:!!(r.has(e)||!n.has(e)&&om(o))}function nm(e,t,o){return t?t.reduce((r,n)=>(r[n]=async(...i)=>(i.length===1&&(i=i[0]),await o.component_server(e,n,i)),r),{}):{}}function Vo(e,t,o,r,n){let i=new Map;e==="api"&&(e="state"),e==="dataset"&&n&&n.forEach(s=>{if(i.has(s))return;let u;const l=r.find(c=>c.type===s);l&&(u=Zt({api_url:o,name:s,id:l.component_class_id,variant:"example"}),i.set(s,u.component))});const a=Zt({api_url:o,name:e,id:t,variant:"component"});return{component:a.component,name:a.name,example_components:i.size>0?i:void 0}}function eo(e,t){let o=new Map;return e.forEach(r=>{const{component:n,example_components:i}=Vo(r.type,r.component_class_id,t,e);if(o.set(r.component_class_id||r.type,n),i)for(const[a,s]of i)o.set(a,s)}),o}const Uo="العربية",Go={annotated_image:"صورة مشروحة"},Fo={allow_recording_access:"يرجى السماح بالوصول إلى الميكروفون للتسجيل",audio:"الصوت",record_from_microphone:"تسجيل من الميكروفون",stop_recording:"إيقاف التسجيل",no_device_support:"لا يمكن الوصول إلى أجهزة الوسائط. تأكد من أنك تعمل على مصدر آمن (https) أو localhost (أو قمت بتمرير شهادة SSL صالحة إلى ssl_verify)، وأنك سمحت للمتصفح بالوصول إلى جهازك.",stop:"إيقاف",resume:"استئناف",record:"تسجيل",no_microphone:"لم يتم العثور على ميكروفون",pause:"إيقاف مؤقت",play:"تشغيل",waiting:"جاري الانتظار"},qo={connection_can_break:"على الهاتف المحمول، يمكن أن ينقطع الاتصال إذا تم تغيير التبويب أو دخل الجهاز في وضع السكون، مما يؤدي إلى فقدان موقعك في قائمة الانتظار.",long_requests_queue:"هناك قائمة انتظار طويلة من الطلبات المعلقة. قم بتكرار هذا الفضاء للتخطي.",lost_connection:"فقد الاتصال بسبب مغادرة الصفحة. إعادة الانضمام إلى قائمة الانتظار..."},Wo={checkbox:"خانة اختيار",checkbox_group:"مجموعة خانات الاختيار"},Xo={code:"الكود"},Zo={color_picker:"منتقي الألوان"},Jo={built_with:"بُني باستخدام",built_with_gradio:"تم الإنشاء بإستخدام Gradio",clear:"أمسح",download:"تحميل",edit:"تعديل",empty:"فارغ",error:"خطأ",hosted_on:"مستضاف على",loading:"جاري التحميل",logo:"شعار",or:"أو",remove:"إزالة",settings:"الإعدادات",share:"مشاركة",submit:"أرسل",undo:"تراجع",no_devices:"لم يتم العثور على أجهزة",language:"اللغة",display_theme:"مظهر العرض",pwa:"تطبيق ويب تقدمي"},Ko={incorrect_format:"تنسيق غير صحيح، يتم دعم ملفات CSV و TSV فقط",new_column:"إضافة عمود",new_row:"صف جديد",add_row_above:"إضافة صف فوق",add_row_below:"إضافة صف تحت",add_column_left:"إضافة عمود لليسار",add_column_right:"إضافة عمود لليمين"},Yo={dropdown:"قائمة منسدلة"},Qo={build_error:"هناك خطأ في البناء",config_error:"هناك خطأ في التكوين",contact_page_author:"يرجى الاتصال بمؤلف الصفحة لإعلامه.",no_app_file:"لا يوجد ملف تطبيق",runtime_error:"هناك خطأ في وقت التشغيل",space_not_working:'"المساحة لا تعمل لأن" {0}',space_paused:"المساحة متوقفة مؤقتًا",use_via_api:"استخدم عبر API"},er={uploading:"جاري الرفع..."},tr={highlighted_text:"نص مميز"},or={allow_webcam_access:"يرجى السماح بالوصول إلى كاميرا الويب للتسجيل.",brush_color:"لون الفرشاة",brush_radius:"حجم الفرشاة",image:"صورة",remove_image:"إزالة الصورة",select_brush_color:"اختر لون الفرشاة",start_drawing:"ابدأ الرسم",use_brush:"استخدم الفرشاة"},rr={label:"تسمية"},nr={enable_cookies:"إذا كنت تزور مساحة HuggingFace في وضع التصفح المتخفي، يجب عليك تمكين ملفات تعريف الارتباط من الجهات الخارجية.",incorrect_credentials:"بيانات الاعتماد غير صحيحة",username:"اسم المستخدم",password:"كلمة المرور",login:"تسجيل الدخول"},ir={number:"رقم"},ar={plot:"رسم بياني"},sr={radio:"زر راديو"},lr={slider:"شريط التمرير"},cr={click_to_upload:"انقر للتحميل",drop_audio:"أسقط الملف الصوتي هنا",drop_csv:"أسقط ملف البيانات هنا",drop_file:"أسقط الملف هنا",drop_image:"أسقط الصورة هنا",drop_video:"أسقط الفيديو هنا",drop_gallery:"أسقط الوسائط هنا",paste_clipboard:"لصق من الحافظة"},im={_name:Uo,"3D_model":{"3d_model":"نموذج ثلاثي الأبعاد"},annotated_image:Go,audio:Fo,blocks:qo,checkbox:Wo,code:Xo,color_picker:Zo,common:Jo,dataframe:Ko,dropdown:Yo,errors:Qo,file:er,highlighted_text:tr,image:or,label:rr,login:nr,number:ir,plot:ar,radio:sr,slider:lr,upload_text:cr},am=Object.freeze(Object.defineProperty({__proto__:null,_name:Uo,annotated_image:Go,audio:Fo,blocks:qo,checkbox:Wo,code:Xo,color_picker:Zo,common:Jo,dataframe:Ko,default:im,dropdown:Yo,errors:Qo,file:er,highlighted_text:tr,image:or,label:rr,login:nr,number:ir,plot:ar,radio:sr,slider:lr,upload_text:cr},Symbol.toStringTag,{value:"Module"})),ur="Català",dr={annotated_image:"Imatge Anotada"},_r={allow_recording_access:"Si us plau, permeteu l'accés al micròfon per gravar.",audio:"Àudio",record_from_microphone:"Gravar des del micròfon",stop_recording:"Aturar la gravació",no_device_support:"No s'ha pogut accedir als dispositius multimèdia. Comproveu que esteu executant en un origen segur (https) o localhost (o heu passat un certificat SSL vàlid a ssl_verify), i que heu permès l'accés del navegador al vostre dispositiu.",stop:"Aturar",resume:"Reprendre",record:"Gravar",no_microphone:"No s'ha trobat cap micròfon",pause:"Pausa",play:"Reproduir",waiting:"Esperant"},pr={connection_can_break:"En dispositius mòbils, la connexió es pot trencar si aquesta pestanya perd el focus o el dispositiu s'adorm, perdent la vostra posició a la cua.",long_requests_queue:"Hi ha una llarga cua de sol·licituds pendents. Dupliqueu aquest Space per saltar-la.",lost_connection:"S'ha perdut la connexió en deixar la pàgina. Tornant a la cua..."},hr={checkbox:"Casella de selecció",checkbox_group:"Grup de caselles"},mr={code:"Codi"},gr={color_picker:"Selector de color"},fr={built_with:"construït amb",built_with_gradio:"Construït amb gradio",clear:"Neteja",download:"Descarregar",edit:"Editar",empty:"Buit",error:"Error",hosted_on:"Allotjat a",loading:"S'està carregant",logo:"logotip",or:"o",remove:"Eliminar",settings:"Configuració",share:"Compartir",submit:"Envia",undo:"Desfer",no_devices:"No s'han trobat dispositius",language:"Idioma",display_theme:"Tema de visualització",pwa:"Aplicació web progressiva"},br={incorrect_format:"Format incorrecte, només s'admeten fitxers CSV i TSV",new_column:"Afegir columna",new_row:"Nova fila",add_row_above:"Afegir fila a dalt",add_row_below:"Afegir fila a baix",add_column_left:"Afegir columna a l'esquerra",add_column_right:"Afegir columna a la dreta"},wr={dropdown:"Desplegable"},vr={build_error:"hi ha un error de compilació",config_error:"hi ha un error de configuració",contact_page_author:"Si us plau, contacteu amb l'autor de la pàgina per informar-lo.",no_app_file:"no hi ha fitxer d'aplicació",runtime_error:"hi ha un error d'execució",space_not_working:`"L'Space no funciona perquè" {0}`,space_paused:"l'space està en pausa",use_via_api:"Utilitzar via API"},yr={uploading:"Pujant..."},kr={highlighted_text:"Text ressaltat"},xr={allow_webcam_access:"Si us plau, permeteu l'accés a la càmera web per gravar.",brush_color:"Color del pinzell",brush_radius:"Radi del pinzell",image:"Imatge",remove_image:"Eliminar imatge",select_brush_color:"Seleccionar color del pinzell",start_drawing:"Començar a dibuixar",use_brush:"Utilitzar pinzell"},Er={label:"Etiqueta"},Sr={enable_cookies:"Si esteu visitant un Space de HuggingFace en mode Incògnit, heu d'habilitar les cookies de tercers.",incorrect_credentials:"Credencials incorrectes",username:"nom d'usuari",password:"contrasenya",login:"Iniciar sessió"},$r={number:"Número"},Tr={plot:"Gràfic"},Ar={radio:"Ràdio"},Pr={slider:"Control lliscant"},Or={click_to_upload:"Feu clic per pujar",drop_audio:"Deixeu anar l'àudio aquí",drop_csv:"Deixeu anar el CSV aquí",drop_file:"Deixeu anar el fitxer aquí",drop_image:"Deixeu anar la imatge aquí",drop_video:"Deixeu anar el vídeo aquí",drop_gallery:"Deixeu anar els mitjans aquí",paste_clipboard:"Enganxar del porta-retalls"},sm={_name:ur,"3D_model":{"3d_model":"Model 3D"},annotated_image:dr,audio:_r,blocks:pr,checkbox:hr,code:mr,color_picker:gr,common:fr,dataframe:br,dropdown:wr,errors:vr,file:yr,highlighted_text:kr,image:xr,label:Er,login:Sr,number:$r,plot:Tr,radio:Ar,slider:Pr,upload_text:Or},lm=Object.freeze(Object.defineProperty({__proto__:null,_name:ur,annotated_image:dr,audio:_r,blocks:pr,checkbox:hr,code:mr,color_picker:gr,common:fr,dataframe:br,default:sm,dropdown:wr,errors:vr,file:yr,highlighted_text:kr,image:xr,label:Er,login:Sr,number:$r,plot:Tr,radio:Ar,slider:Pr,upload_text:Or},Symbol.toStringTag,{value:"Module"})),Lr="کوردی",Ir={annotated_image:"وێنەی نیشانە کراو"},Cr={allow_recording_access:"تکایە ڕێگە بدە بە بەکارهێنانی مایکرۆفۆنەکە بۆ تۆمارکردن.",audio:"دەنگ",record_from_microphone:"تۆمارکردن لە مایکەوە",stop_recording:"تۆمارکردن بوەستێنە",no_device_support:"ناتوانرێت دەستت بگات بە ئامێرەکانی میدیا. دڵنیابە لەوەی کە لەسەر سەرچاوەیەکی پارێزراو کاردەکەیت (https) یان localhost (یان بڕوانامەیەکی SSL دروستت داوە بە ssl_verify)، و ڕێگەت داوە بە وێبگەڕەکە دەستی بگات بە ئامێرەکەت.",stop:"وەستان",resume:"بەردەوامبوون",record:"تۆمارکردن",no_microphone:"هیچ مایکرۆفۆنێک نەدۆزرایەوە",pause:"وەستاندنی کاتی",play:"لێدان",waiting:"چاوەڕوانی"},Dr={connection_can_break:"لە مۆبایلدا، پەیوەندییەکە دەکرێت بپچڕێت ئەگەر ئەم تابە چالاک نەبێت یان ئامێرەکە بچێتە دۆخی پشوو، ئەمەش شوێنی خۆت لە ڕیزدا لەدەست دەدات.",long_requests_queue:"ڕیزێکی درێژی داواکاری هەیە. ئەم سپەیسە دووباد بکە بۆی چاوەڕوان نەبیت.",lost_connection:"پەیوەندی پچڕا بەهۆی جێهێشتنی پەیج. گەڕانەوە بۆ ڕیز..."},Rr={checkbox:"بۆکسی هەڵبژاردن",checkbox_group:"گروپی بۆکسی هەڵبژاردن"},jr={code:"کۆد"},Nr={color_picker:"هەڵبژاردنی ڕەنگ"},Hr={built_with:"دروستکراوە لەگەڵ...",built_with_gradio:"Gradio دروستکراوە بە",clear:"خاوێنکردنەوە",download:"دابەزاندن",edit:"بژارکردن",empty:"بەتاڵ",error:"هەڵە",hosted_on:"میوانداری کراوە لە",loading:"بارکردن",logo:"لۆگۆ",or:"یان",remove:"لابردن",settings:"ڕێکخستنەکان",share:"هاوبەشکردن",submit:"پێشکەشکردن",undo:"پووچکردنەوە",no_devices:"هیچ ئامێرێک نەدۆزرایەوە",language:"زمان",display_theme:"ڕووکاری نیشاندان",pwa:"بەرنامەی وێبی پێشکەوتوو"},Br={incorrect_format:"فۆرماتێکی هەڵە، تەنها فایلەکانی CSV و TSV پشتگیری دەکرێن",new_column:"زیادکردنی ستوون",new_row:"ڕیزی نوێ",add_row_above:"زیادکردنی ڕیز لە سەرەوە",add_row_below:"زیادکردنی ڕیز لە خوارەوە",add_column_left:"زیادکردنی ستوون لە چەپەوە",add_column_right:"زیادکردنی ستوون لە ڕاستەوە"},zr={dropdown:"لیستی داکەوتوو"},Mr={build_error:"هەڵەیەک هەیە لە دروستکردندا",config_error:"هەڵەیەک هەیە لە ڕێکخستندا",contact_page_author:"تکایە پەیوەندی بکە بە نووسەری لاپەڕەکە بۆ ئاگادارکردنەوە.",no_app_file:"فایلی بەرنامە نییە",runtime_error:"هەڵەیەک هەیە لە کاتی جێبەجێکردندا",space_not_working:'"سپەیسەکە کار ناکات چونکە" {0}',space_paused:"سپەیسەکە وەستێنراوە",use_via_api:"بەکارهێنان لە ڕێگەی API"},Vr={uploading:"بارکردن..."},Ur={highlighted_text:"دەقی دیاریکراو"},Gr={allow_webcam_access:"تکایە ڕێگە بدە بە بەکارهێنانی کامێرای وێب بۆ تۆمارکردن.",brush_color:"ڕەنگی فڵچە",brush_radius:"تیژڕەوی فڵچە",image:"وێنە",remove_image:"لابردنی وێنە",select_brush_color:"ڕەنگی فڵچە هەڵبژێرە",start_drawing:"دەست بکە بە وێنەکێشان",use_brush:"فڵچە بەکاربهێنە"},Fr={label:"لەیبڵ"},qr={enable_cookies:"ئەگەر تۆ سەردانی HuggingFace Space دەکەیت لە دۆخی نادیاردا، پێویستە کووکی لایەنی سێیەم چالاک بکەیت.",incorrect_credentials:"زانیاری چوونەژوورەوە هەڵەیە",username:"ناوی بەکارهێنەر",password:"وشەی نهێنی",login:"چوونە ژوورەوە"},Wr={number:"ژمارە"},Xr={plot:"هێڵکاری"},Zr={radio:"ڕادیۆ"},Jr={slider:"خلیسکە"},Kr={click_to_upload:"کلیک بکە بۆ بارکردن",drop_audio:"دەنگ لێرە دابنێ",drop_csv:"لێرەدا CSV دابنێ",drop_file:"فایل لێرە دابنێ",drop_image:"وێنە لێرەدا دابنێ",drop_video:"ڤیدیۆ لێرە دابنێ",drop_gallery:"میدیا لێرە دابنێ",paste_clipboard:"لکاندن لە کلیپبۆردەوە"},cm={_name:Lr,"3D_model":{"3d_model":"مۆدێلی سێ ڕەهەندی"},annotated_image:Ir,audio:Cr,blocks:Dr,checkbox:Rr,code:jr,color_picker:Nr,common:Hr,dataframe:Br,dropdown:zr,errors:Mr,file:Vr,highlighted_text:Ur,image:Gr,label:Fr,login:qr,number:Wr,plot:Xr,radio:Zr,slider:Jr,upload_text:Kr},um=Object.freeze(Object.defineProperty({__proto__:null,_name:Lr,annotated_image:Ir,audio:Cr,blocks:Dr,checkbox:Rr,code:jr,color_picker:Nr,common:Hr,dataframe:Br,default:cm,dropdown:zr,errors:Mr,file:Vr,highlighted_text:Ur,image:Gr,label:Fr,login:qr,number:Wr,plot:Xr,radio:Zr,slider:Jr,upload_text:Kr},Symbol.toStringTag,{value:"Module"})),Yr="Deutsch",Qr={annotated_image:"Annotiertes Bild"},en={allow_recording_access:"Bitte erlauben Sie den Zugriff auf das Mikrofon für die Aufnahme.",audio:"Audio",record_from_microphone:"Vom Mikrofon aufnehmen",stop_recording:"Aufnahme stoppen",no_device_support:"Auf Mediengeräte konnte nicht zugegriffen werden. Stellen Sie sicher, dass Sie sich auf einer sicheren Quelle (https) oder localhost befinden (oder ein gültiges SSL-Zertifikat an ssl_verify übergeben haben) und Sie dem Browser den Zugriff auf Ihr Gerät erlaubt haben.",stop:"Stopp",resume:"Fortsetzen",record:"Aufnehmen",no_microphone:"Kein Mikrofon gefunden",pause:"Pause",play:"Abspielen",waiting:"Warten"},tn={connection_can_break:"Auf Mobilgeräten kann die Verbindung unterbrochen werden, wenn dieser Tab den Fokus verliert oder das Gerät in den Ruhezustand geht, wodurch Ihre Position in der Warteschlange verloren geht.",long_requests_queue:"Es gibt eine lange Warteschlange ausstehender Anfragen. Duplizieren Sie diesen Space zum Überspringen.",lost_connection:"Verbindung durch Verlassen der Seite verloren. Kehre zur Warteschlange zurück..."},on={checkbox:"Kontrollkästchen",checkbox_group:"Kontrollkästchengruppe"},rn={code:"Code"},nn={color_picker:"Farbwähler"},an={built_with:"erstellt mit",built_with_gradio:"Mit Gradio erstellt",clear:"Löschen",download:"Herunterladen",edit:"Bearbeiten",empty:"Leer",error:"Fehler",hosted_on:"Gehostet auf",loading:"Laden",logo:"Logo",or:"oder",remove:"Entfernen",settings:"Einstellungen",share:"Teilen",submit:"Absenden",undo:"Rückgängig",no_devices:"Keine Geräte gefunden",language:"Sprache",display_theme:"Anzeigedesign",pwa:"Progressive Web App"},sn={incorrect_format:"Falsches Format, nur CSV- und TSV-Dateien werden unterstützt",new_column:"Spalte hinzufügen",new_row:"Neue Zeile",add_row_above:"Zeile oben hinzufügen",add_row_below:"Zeile unten hinzufügen",add_column_left:"Spalte links hinzufügen",add_column_right:"Spalte rechts hinzufügen"},ln={dropdown:"Dropdown"},cn={build_error:"Es gibt einen Build-Fehler",config_error:"Es gibt einen Konfigurationsfehler",contact_page_author:"Bitte kontaktieren Sie den Autor der Seite.",no_app_file:"Es gibt keine App-Datei",runtime_error:"Es gibt einen Laufzeitfehler",space_not_working:'"Space funktioniert nicht, weil" {0}',space_paused:"Der Space ist pausiert",use_via_api:"Über API verwenden"},un={uploading:"Hochladen..."},dn={highlighted_text:"Hervorgehobener Text"},_n={allow_webcam_access:"Bitte erlauben Sie den Zugriff auf die Webcam für die Aufnahme.",brush_color:"Pinselfarbe",brush_radius:"Pinselgröße",image:"Bild",remove_image:"Bild entfernen",select_brush_color:"Pinselfarbe auswählen",start_drawing:"Zeichnen beginnen",use_brush:"Pinsel verwenden"},pn={label:"Beschriftung"},hn={enable_cookies:"Wenn Sie einen HuggingFace Space im Inkognito-Modus besuchen, müssen Sie Cookies von Drittanbietern aktivieren.",incorrect_credentials:"Falsche Anmeldedaten",username:"Benutzername",password:"Passwort",login:"Anmelden"},mn={number:"Zahl"},gn={plot:"Diagramm"},fn={radio:"Radio"},bn={slider:"Schieberegler"},wn={click_to_upload:"Hochladen",drop_audio:"Audio hier ablegen",drop_csv:"CSV Datei hier ablegen",drop_file:"Datei hier ablegen",drop_image:"Bild hier ablegen",drop_video:"Video hier ablegen",drop_gallery:"Medien hier ablegen",paste_clipboard:"Aus Zwischenablage einfügen"},dm={_name:Yr,"3D_model":{"3d_model":"3D-Modell"},annotated_image:Qr,audio:en,blocks:tn,checkbox:on,code:rn,color_picker:nn,common:an,dataframe:sn,dropdown:ln,errors:cn,file:un,highlighted_text:dn,image:_n,label:pn,login:hn,number:mn,plot:gn,radio:fn,slider:bn,upload_text:wn},_m=Object.freeze(Object.defineProperty({__proto__:null,_name:Yr,annotated_image:Qr,audio:en,blocks:tn,checkbox:on,code:rn,color_picker:nn,common:an,dataframe:sn,default:dm,dropdown:ln,errors:cn,file:un,highlighted_text:dn,image:_n,label:pn,login:hn,number:mn,plot:gn,radio:fn,slider:bn,upload_text:wn},Symbol.toStringTag,{value:"Module"})),vn="English",yn={annotated_image:"Annotated Image"},kn={allow_recording_access:"Please allow access to the microphone for recording.",audio:"Audio",drop_to_upload:"Drop an audio file here to upload",record_from_microphone:"Record from microphone",stop_recording:"Stop recording",no_device_support:"Media devices could not be accessed. Check that you are running on a secure origin (https) or localhost (or you have passed a valid SSL certificate to ssl_verify), and you have allowed browser access to your device.",stop:"Stop",resume:"Resume",record:"Record",no_microphone:"No microphone found",pause:"Pause",play:"Play",waiting:"Waiting"},xn={connection_can_break:"On mobile, the connection can break if this tab is unfocused or the device sleeps, losing your position in queue.",long_requests_queue:"There is a long queue of requests pending. Duplicate this Space to skip.",lost_connection:"Lost connection due to leaving page. Rejoining queue...",waiting_for_inputs:"Waiting for file(s) to finish uploading, please retry."},En={checkbox:"Checkbox",checkbox_group:"Checkbox Group"},Sn={code:"Code"},$n={color_picker:"Color Picker"},Tn={built_with:"built with",built_with_gradio:"Built with Gradio",clear:"Clear",download:"Download",edit:"Edit",empty:"Empty",error:"Error",hosted_on:"Hosted on",loading:"Loading",logo:"logo",or:"or",remove:"Remove",settings:"Settings",share:"Share",submit:"Submit",undo:"Undo",no_devices:"No devices found",language:"Language",display_theme:"Display Theme",pwa:"Progressive Web App"},An={incorrect_format:"Incorrect format, only CSV and TSV files are supported",new_column:"Add column",new_row:"New row",add_row_above:"Add row above",add_row_below:"Add row below",delete_row:"Delete row",delete_column:"Delete column",add_column_left:"Add column to the left",add_column_right:"Add column to the right",sort_column:"Sort column",sort_ascending:"Sort ascending",sort_descending:"Sort descending",drop_to_upload:"Drop CSV or TSV files here to import data into dataframe",clear_sort:"Clear sort"},Pn={dropdown:"Dropdown"},On={build_error:"there is a build error",config_error:"there is a config error",contact_page_author:"Please contact the author of the page to let them know.",no_app_file:"there is no app file",runtime_error:"there is a runtime error",space_not_working:`"Space isn't working because" {0}`,space_paused:"the space is paused",use_via_api:"Use via API"},Ln={uploading:"Uploading..."},In={highlighted_text:"Highlighted Text"},Cn={allow_webcam_access:"Please allow access to the webcam for recording.",brush_color:"Brush color",brush_radius:"Brush radius",image:"Image",remove_image:"Remove Image",select_brush_color:"Select brush color",start_drawing:"Start drawing",use_brush:"Use brush",drop_to_upload:"Drop an image file here to upload"},Dn={label:"Label"},Rn={enable_cookies:"If you are visiting a HuggingFace Space in Incognito mode, you must enable third party cookies.",incorrect_credentials:"Incorrect Credentials",username:"username",password:"password",login:"Login"},jn={number:"Number"},Nn={plot:"Plot"},Hn={radio:"Radio"},Bn={slider:"Slider"},zn={click_to_upload:"Click to Upload",drop_audio:"Drop Audio Here",drop_csv:"Drop CSV Here",drop_file:"Drop File Here",drop_image:"Drop Image Here",drop_video:"Drop Video Here",drop_gallery:"Drop Media Here",paste_clipboard:"Paste from Clipboard"},Mn={drop_to_upload:"Drop a video file here to upload"},pm={_name:vn,"3D_model":{"3d_model":"3D Model",drop_to_upload:"Drop a 3D model (.obj, .glb, .stl, .gltf, .splat, or .ply) file here to upload"},annotated_image:yn,audio:kn,blocks:xn,checkbox:En,code:Sn,color_picker:$n,common:Tn,dataframe:An,dropdown:Pn,errors:On,file:Ln,highlighted_text:In,image:Cn,label:Dn,login:Rn,number:jn,plot:Nn,radio:Hn,slider:Bn,upload_text:zn,video:Mn},hm=Object.freeze(Object.defineProperty({__proto__:null,_name:vn,annotated_image:yn,audio:kn,blocks:xn,checkbox:En,code:Sn,color_picker:$n,common:Tn,dataframe:An,default:pm,dropdown:Pn,errors:On,file:Ln,highlighted_text:In,image:Cn,label:Dn,login:Rn,number:jn,plot:Nn,radio:Hn,slider:Bn,upload_text:zn,video:Mn},Symbol.toStringTag,{value:"Module"})),Vn="Español",Un={annotated_image:"Imagen Anotada"},Gn={allow_recording_access:"Por favor, permita el acceso al micrófono para grabar.",audio:"Audio",record_from_microphone:"Grabar desde el micrófono",stop_recording:"Detener grabación",no_device_support:"No se pudo acceder a los dispositivos multimedia. Compruebe que está ejecutando en un origen seguro (https) o localhost (o que ha pasado un certificado SSL válido a ssl_verify), y que ha permitido el acceso del navegador a su dispositivo.",stop:"Detener",resume:"Reanudar",record:"Grabar",no_microphone:"No se encontró micrófono",pause:"Pausar",play:"Reproducir",waiting:"Esperando"},Fn={connection_can_break:"En dispositivos móviles, la conexión puede interrumpirse si esta pestaña pierde el foco o el dispositivo entra en reposo, perdiendo su posición en la cola.",long_requests_queue:"Hay una larga cola de solicitudes pendientes. Duplique este Space para saltarse la cola.",lost_connection:"Se perdió la conexión al abandonar la página. Volviendo a la cola..."},qn={checkbox:"Casilla de verificación",checkbox_group:"Grupo de casillas"},Wn={code:"Código"},Xn={color_picker:"Selector de color"},Zn={built_with:"construido con",built_with_gradio:"Construido con Gradio",clear:"Limpiar",download:"Descargar",edit:"Editar",empty:"Vacío",error:"Error",hosted_on:"Alojado en",loading:"Cargando",logo:"logo",or:"o",remove:"Eliminar",settings:"Configuración",share:"Compartir",submit:"Enviar",undo:"Deshacer",no_devices:"No se encontraron dispositivos",language:"Idioma",display_theme:"Tema de visualización",pwa:"Aplicación web progresiva"},Jn={incorrect_format:"Formato incorrecto, solo se admiten archivos CSV y TSV",new_column:"Agregar columna",new_row:"Nueva fila",add_row_above:"Agregar fila arriba",add_row_below:"Agregar fila abajo",add_column_left:"Agregar columna a la izquierda",add_column_right:"Agregar columna a la derecha"},Kn={dropdown:"Menú desplegable"},Yn={build_error:"hay un error de compilación",config_error:"hay un error de configuración",contact_page_author:"Por favor, contacte al autor de la página para informarle.",no_app_file:"no hay archivo de aplicación",runtime_error:"hay un error de ejecución",space_not_working:'"El Space no funciona porque" {0}',space_paused:"el space está pausado",use_via_api:"Usar vía API"},Qn={uploading:"Subiendo..."},ei={highlighted_text:"Texto resaltado"},ti={allow_webcam_access:"Por favor, permita el acceso a la cámara web para grabar.",brush_color:"Color del pincel",brush_radius:"Radio del pincel",image:"Imagen",remove_image:"Eliminar imagen",select_brush_color:"Seleccionar color del pincel",start_drawing:"Empezar a dibujar",use_brush:"Usar pincel"},oi={label:"Etiqueta"},ri={enable_cookies:"Si está visitando un Space de HuggingFace en modo Incógnito, debe habilitar las cookies de terceros.",incorrect_credentials:"Credenciales incorrectas",username:"usuario",password:"contraseña",login:"Iniciar sesión"},ni={number:"Número"},ii={plot:"Gráfico"},ai={radio:"Radio"},si={slider:"Deslizador"},li={click_to_upload:"Haga clic para cargar",drop_audio:"Coloque el audio aquí",drop_csv:"Coloque el CSV aquí",drop_file:"Coloque el archivo aquí",drop_image:"Coloque la imagen aquí",drop_video:"Coloque el video aquí",drop_gallery:"Coloque las imágenes aquí",paste_clipboard:"Pegar desde el portapapeles"},mm={_name:Vn,"3D_model":{"3d_model":"Modelo 3D"},annotated_image:Un,audio:Gn,blocks:Fn,checkbox:qn,code:Wn,color_picker:Xn,common:Zn,dataframe:Jn,dropdown:Kn,errors:Yn,file:Qn,highlighted_text:ei,image:ti,label:oi,login:ri,number:ni,plot:ii,radio:ai,slider:si,upload_text:li},gm=Object.freeze(Object.defineProperty({__proto__:null,_name:Vn,annotated_image:Un,audio:Gn,blocks:Fn,checkbox:qn,code:Wn,color_picker:Xn,common:Zn,dataframe:Jn,default:mm,dropdown:Kn,errors:Yn,file:Qn,highlighted_text:ei,image:ti,label:oi,login:ri,number:ni,plot:ii,radio:ai,slider:si,upload_text:li},Symbol.toStringTag,{value:"Module"})),ci="Euskara",ui={annotated_image:"Irudi Erantsiak"},di={allow_recording_access:"Mesedez, baimendu mikrofonoaren sarbidea grabatzeko.",audio:"Audioa",record_from_microphone:"Mikrofonotik grabatu",stop_recording:"Grabaketa gelditu",no_device_support:"Ezin izan da multimedia gailuetara sartu. Ziurtatu jatorri seguru batean (https) edo localhost-en exekutatzen ari zarela (edo SSL ziurtagiri baliozkoa eman diozula ssl_verify-ri), eta nabigatzaileari zure gailura sarbidea eman diozula.",stop:"Gelditu",resume:"Jarraitu",record:"Grabatu",no_microphone:"Ez da mikrofonorik aurkitu",pause:"Pausatu",play:"Erreproduzitu",waiting:"Itxaroten"},_i={connection_can_break:"Mugikorretan, konexioa eten daiteke fitxa honek fokua galtzen badu edo gailua lokartu egiten bada, ilaran duzun posizioa galduz.",long_requests_queue:"Eskaera ilara luzea dago zain. Space hau bikoiztu saltatzeko.",lost_connection:"Konexioa galdu da orria uzteagatik. Ilarara itzultzen..."},pi={checkbox:"Kontrol-laukia",checkbox_group:"Kontrol-laukien taldea"},hi={code:"Kodea"},mi={color_picker:"Kolore hautatzailea"},gi={built_with:"honekin eraikia",built_with_gradio:"Gradio-rekin eraikia",clear:"Garbitu",download:"Deskargatu",edit:"Editatu",empty:"Hutsik",error:"Errorea",hosted_on:"Ostatatuta",loading:"Kargatzen",logo:"logoa",or:"edo",remove:"Kendu",settings:"Ezarpenak",share:"Partekatu",submit:"Bidali",undo:"Desegin",no_devices:"Ez da gailurik aurkitu",language:"Hizkuntza",display_theme:"Bistaratze gaia",pwa:"Web Aplikazio Aurreratua"},fi={incorrect_format:"Formatu okerra, CSV eta TSV fitxategiak soilik onartzen dira",new_column:"Zutabea gehitu",new_row:"Errenkada berria",add_row_above:"Errenkada gehitu gainean",add_row_below:"Errenkada gehitu azpian",add_column_left:"Zutabea gehitu ezkerrean",add_column_right:"Zutabea gehitu eskuinean"},bi={dropdown:"Goitibeherako menua"},wi={build_error:"eraikitze errore bat dago",config_error:"konfigurazio errore bat dago",contact_page_author:"Mesedez, jarri harremanetan orriaren egilearekin jakinarazteko.",no_app_file:"ez dago aplikazio fitxategirik",runtime_error:"exekuzio errore bat dago",space_not_working:'"Space-a ez dabil honako arrazoi honengatik:" {0}',space_paused:"space-a pausatuta dago",use_via_api:"API bidez erabili"},vi={uploading:"Igotzen..."},yi={highlighted_text:"Nabarmendutako testua"},ki={allow_webcam_access:"Mesedez, baimendu web-kameraren sarbidea grabatzeko.",brush_color:"Pintzelaren kolorea",brush_radius:"Pintzelaren tamaina",image:"Irudia",remove_image:"Irudia kendu",select_brush_color:"Aukeratu pintzelaren kolorea",start_drawing:"Hasi marrazten",use_brush:"Pintzela erabili"},xi={label:"Etiketa"},Ei={enable_cookies:"HuggingFace Space bat Inkognito moduan bisitatzen ari bazara, hirugarrenen cookieak gaitu behar dituzu.",incorrect_credentials:"Kredentzial okerrak",username:"erabiltzaile izena",password:"pasahitza",login:"Saioa hasi"},Si={number:"Zenbakia"},$i={plot:"Grafikoa"},Ti={radio:"Irratia"},Ai={slider:"Graduatzailea"},Pi={click_to_upload:"Klikatu igotzeko",drop_audio:"Utzi audioa hemen",drop_csv:"Utzi CSV-a hemen",drop_file:"Utzi fitxategia hemen",drop_image:"Utzi irudia hemen",drop_video:"Utzi bideoa hemen",drop_gallery:"Utzi multimedia hemen",paste_clipboard:"Itsatsi arbeleko edukia"},fm={_name:ci,"3D_model":{"3d_model":"3D Modeloa"},annotated_image:ui,audio:di,blocks:_i,checkbox:pi,code:hi,color_picker:mi,common:gi,dataframe:fi,dropdown:bi,errors:wi,file:vi,highlighted_text:yi,image:ki,label:xi,login:Ei,number:Si,plot:$i,radio:Ti,slider:Ai,upload_text:Pi},bm=Object.freeze(Object.defineProperty({__proto__:null,_name:ci,annotated_image:ui,audio:di,blocks:_i,checkbox:pi,code:hi,color_picker:mi,common:gi,dataframe:fi,default:fm,dropdown:bi,errors:wi,file:vi,highlighted_text:yi,image:ki,label:xi,login:Ei,number:Si,plot:$i,radio:Ti,slider:Ai,upload_text:Pi},Symbol.toStringTag,{value:"Module"})),Oi="فارسی",Li={annotated_image:"تصویر حاشیه‌نویسی شده"},Ii={allow_recording_access:"لطفاً برای ضبط صدا، دسترسی به میکروفون را مجاز کنید.",audio:"صدا",record_from_microphone:"ضبط از میکروفون",stop_recording:"توقف ضبط",no_device_support:"دسترسی به دستگاه‌های رسانه‌ای امکان‌پذیر نیست. اطمینان حاصل کنید که در یک منبع امن (https) یا localhost اجرا می‌شود (یا یک گواهی SSL معتبر به ssl_verify داده‌اید)، و به مرورگر اجازه دسترسی به دستگاه خود را داده‌اید.",stop:"توقف",resume:"ادامه",record:"ضبط",no_microphone:"میکروفونی یافت نشد",pause:"مکث",play:"پخش",waiting:"در حال انتظار"},Ci={connection_can_break:"در موبایل، اگر این تب از حالت فعال خارج شود یا دستگاه به خواب برود، اتصال می‌تواند قطع شود و موقعیت شما در صف از دست می‌رود.",long_requests_queue:"صف طولانی از درخواست‌های در انتظار وجود دارد. برای رد کردن صف، این Space را تکثیر کنید.",lost_connection:"اتصال به دلیل ترک صفحه قطع شد. در حال بازگشت به صف..."},Di={checkbox:"چک‌باکس",checkbox_group:"گروه چک‌باکس"},Ri={code:"کد"},ji={color_picker:"انتخابگر رنگ"},Ni={built_with:"ساخته شده با",built_with_gradio:"ساخته شده با Gradio",clear:"پاک کردن",download:"دانلود",edit:"ویرایش",empty:"خالی",error:"خطا",hosted_on:"میزبانی شده در",loading:"در حال بارگذاری",logo:"لوگو",or:"یا",remove:"حذف",settings:"تنظیمات",share:"اشتراک‌گذاری",submit:"ارسال",undo:"بازگشت",no_devices:"هیچ دستگاهی یافت نشد",language:"زبان",display_theme:"تم نمایش",pwa:"برنامه وب پیشرو"},Hi={incorrect_format:"فرمت نادرست، فقط فایل‌های CSV و TSV پشتیبانی می‌شوند",new_column:"افزودن ستون",new_row:"سطر جدید",add_row_above:"افزودن سطر در بالا",add_row_below:"افزودن سطر در پایین",add_column_left:"افزودن ستون در چپ",add_column_right:"افزودن ستون در راست"},Bi={dropdown:"منوی کشویی"},zi={build_error:"خطای ساخت وجود دارد",config_error:"خطای پیکربندی وجود دارد",contact_page_author:"لطفاً با نویسنده صفحه تماس بگیرید تا به او اطلاع دهید.",no_app_file:"فایل برنامه وجود ندارد",runtime_error:"خطای زمان اجرا وجود دارد",space_not_working:'"Space کار نمی‌کند زیرا" {0}',space_paused:"space متوقف شده است",use_via_api:"استفاده از طریق API"},Mi={uploading:"در حال آپلود..."},Vi={highlighted_text:"متن برجسته شده"},Ui={allow_webcam_access:"لطفاً برای ضبط، دسترسی به وب‌کم را مجاز کنید.",brush_color:"رنگ قلم",brush_radius:"اندازه قلم",image:"تصویر",remove_image:"حذف تصویر",select_brush_color:"انتخاب رنگ قلم",start_drawing:"شروع طراحی",use_brush:"استفاده از قلم"},Gi={label:"برچسب"},Fi={enable_cookies:"اگر در حالت ناشناس از HuggingFace Space بازدید می‌کنید، باید کوکی‌های شخص ثالث را فعال کنید.",incorrect_credentials:"اطلاعات ورود نادرست",username:"نام کاربری",password:"رمز عبور",login:"ورود"},qi={number:"عدد"},Wi={plot:"نمودار"},Xi={radio:"رادیو"},Zi={slider:"اسلایدر"},Ji={click_to_upload:"برای آپلود کلیک کنید",drop_audio:"فایل صوتی را اینجا رها کنید",drop_csv:"CSV را اینجا رها کنید",drop_file:"فایل را اینجا رها کنید",drop_image:"تصویر را اینجا رها کنید",drop_video:"ویدیو را اینجا رها کنید",drop_gallery:"رسانه را اینجا رها کنید",paste_clipboard:"چسباندن از کلیپ‌بورد"},wm={_name:Oi,"3D_model":{"3d_model":"مدل سه‌بعدی"},annotated_image:Li,audio:Ii,blocks:Ci,checkbox:Di,code:Ri,color_picker:ji,common:Ni,dataframe:Hi,dropdown:Bi,errors:zi,file:Mi,highlighted_text:Vi,image:Ui,label:Gi,login:Fi,number:qi,plot:Wi,radio:Xi,slider:Zi,upload_text:Ji},vm=Object.freeze(Object.defineProperty({__proto__:null,_name:Oi,annotated_image:Li,audio:Ii,blocks:Ci,checkbox:Di,code:Ri,color_picker:ji,common:Ni,dataframe:Hi,default:wm,dropdown:Bi,errors:zi,file:Mi,highlighted_text:Vi,image:Ui,label:Gi,login:Fi,number:qi,plot:Wi,radio:Xi,slider:Zi,upload_text:Ji},Symbol.toStringTag,{value:"Module"})),Ki="Suomi",Yi={annotated_image:"Merkitty kuva"},Qi={allow_recording_access:"Anna lupa mikrofonin käyttöön nauhoitusta varten.",audio:"Ääni",record_from_microphone:"Nauhoita mikrofonista",stop_recording:"Lopeta nauhoitus",no_device_support:"Medialaitteisiin ei saada yhteyttä. Varmista, että käytät suojattua lähdettä (https) tai localhostia (tai olet antanut kelvollisen SSL-varmenteen ssl_verify:lle), ja olet antanut selaimelle luvan käyttää laitettasi.",stop:"Pysäytä",resume:"Jatka",record:"Nauhoita",no_microphone:"Mikrofonia ei löydy",pause:"Tauko",play:"Toista",waiting:"Odotetaan"},ea={connection_can_break:"Mobiililaitteilla yhteys voi katketa, jos tämä välilehti menettää fokuksen tai laite menee lepotilaan, jolloin paikkasi jonossa menetetään.",long_requests_queue:"Jonossa on paljon odottavia pyyntöjä. Monista tämä Space ohittaaksesi jonon.",lost_connection:"Yhteys katkesi sivulta poistumisen vuoksi. Palataan jonoon..."},ta={checkbox:"Valintaruutu",checkbox_group:"Valintaruuturyhmä"},oa={code:"Koodi"},ra={color_picker:"Värivalitsin"},na={built_with:"tehty",built_with_gradio:"Tehty Gradiolla",clear:"Tyhjennä",download:"Lataa",edit:"Muokkaa",empty:"Tyhjä",error:"Virhe",hosted_on:"Isännöity",loading:"Ladataan",logo:"logo",or:"tai",remove:"Poista",settings:"Asetukset",share:"Jaa",submit:"Lähetä",undo:"Kumoa",no_devices:"Laitteita ei löytynyt",language:"Kieli",display_theme:"Näyttöteema",pwa:"Progressiivinen verkkosovellus"},ia={incorrect_format:"Väärä muoto, vain CSV- ja TSV-tiedostot ovat tuettuja",new_column:"Lisää sarake",new_row:"Uusi rivi",add_row_above:"Lisää rivi yläpuolelle",add_row_below:"Lisää rivi alapuolelle",add_column_left:"Lisää sarake vasemmalle",add_column_right:"Lisää sarake oikealle"},aa={dropdown:"Pudotusvalikko"},sa={build_error:"rakennusvirhe",config_error:"määritysvirhe",contact_page_author:"Ota yhteyttä sivun tekijään ilmoittaaksesi.",no_app_file:"sovellustiedostoa ei ole",runtime_error:"ajonaikainen virhe",space_not_working:'"Space ei toimi koska" {0}',space_paused:"space on keskeytetty",use_via_api:"Käytä API:n kautta"},la={uploading:"Ladataan..."},ca={highlighted_text:"Korostettu teksti"},ua={allow_webcam_access:"Anna lupa web-kameran käyttöön nauhoitusta varten.",brush_color:"Siveltimen väri",brush_radius:"Siveltimen koko",image:"Kuva",remove_image:"Poista kuva",select_brush_color:"Valitse siveltimen väri",start_drawing:"Aloita piirtäminen",use_brush:"Käytä sivellintä"},da={label:"Nimike"},_a={enable_cookies:"Jos vierailet HuggingFace Spacessa Incognito-tilassa, sinun täytyy sallia kolmannen osapuolen evästeet.",incorrect_credentials:"Virheelliset kirjautumistiedot",username:"käyttäjätunnus",password:"salasana",login:"Kirjaudu sisään"},pa={number:"Numero"},ha={plot:"Kaavio"},ma={radio:"Valintanappi"},ga={slider:"Liukusäädin"},fa={click_to_upload:"Napsauta ladataksesi",drop_audio:"Pudota äänitiedosto tähän",drop_csv:"Pudota CSV tähän",drop_file:"Pudota tiedosto tähän",drop_image:"Pudota kuva tähän",drop_video:"Pudota video tähän",drop_gallery:"Pudota media tähän",paste_clipboard:"Liitä leikepöydältä"},ym={_name:Ki,"3D_model":{"3d_model":"3D-malli"},annotated_image:Yi,audio:Qi,blocks:ea,checkbox:ta,code:oa,color_picker:ra,common:na,dataframe:ia,dropdown:aa,errors:sa,file:la,highlighted_text:ca,image:ua,label:da,login:_a,number:pa,plot:ha,radio:ma,slider:ga,upload_text:fa},km=Object.freeze(Object.defineProperty({__proto__:null,_name:Ki,annotated_image:Yi,audio:Qi,blocks:ea,checkbox:ta,code:oa,color_picker:ra,common:na,dataframe:ia,default:ym,dropdown:aa,errors:sa,file:la,highlighted_text:ca,image:ua,label:da,login:_a,number:pa,plot:ha,radio:ma,slider:ga,upload_text:fa},Symbol.toStringTag,{value:"Module"})),ba="Français",wa={annotated_image:"Image annotée"},va={allow_recording_access:"Veuillez autoriser l'accès au microphone pour l'enregistrement.",audio:"Audio",record_from_microphone:"Enregistrer depuis le microphone",stop_recording:"Arrêter l'enregistrement",no_device_support:"Impossible d'accéder aux périphériques multimédias. Assurez-vous que vous êtes sur une source sécurisée (https) ou localhost (ou avez passé un certificat SSL valide à ssl_verify), et que vous avez autorisé l'accès du navigateur à votre appareil.",stop:"Arrêter",resume:"Reprendre",record:"Enregistrer",no_microphone:"Aucun microphone trouvé",pause:"Pause",play:"Lecture",waiting:"En attente"},ya={connection_can_break:"Sur mobile, la connexion peut être interrompue si cet onglet perd le focus ou si l'appareil se met en veille, perdant votre position dans la file d'attente.",long_requests_queue:"Il y a une longue file d'attente de requêtes en attente. Dupliquez ce Space pour la contourner.",lost_connection:"Connexion perdue en quittant la page. Retour à la file d'attente..."},ka={checkbox:"Case à cocher",checkbox_group:"Groupe de cases à cocher"},xa={code:"Code"},Ea={color_picker:"Sélecteur de couleur"},Sa={built_with:"créé avec",built_with_gradio:"Créé avec Gradio",clear:"Effacer",download:"Télécharger",edit:"Modifier",empty:"Vide",error:"Erreur",hosted_on:"Hébergé sur",loading:"Chargement",logo:"logo",or:"ou",remove:"Supprimer",settings:"Paramètres",share:"Partager",submit:"Envoyer",undo:"Annuler",no_devices:"Aucun périphérique trouvé",language:"Langue",display_theme:"Thème d'affichage",pwa:"Application web progressive"},$a={incorrect_format:"Format incorrect, seuls les fichiers CSV et TSV sont pris en charge",new_column:"Ajouter une colonne",new_row:"Nouvelle ligne",add_row_above:"Ajouter une ligne au-dessus",add_row_below:"Ajouter une ligne en dessous",add_column_left:"Ajouter une colonne à gauche",add_column_right:"Ajouter une colonne à droite"},Ta={dropdown:"Menu déroulant"},Aa={build_error:"il y a une erreur de construction",config_error:"il y a une erreur de configuration",contact_page_author:"Veuillez contacter l'auteur de la page pour l'informer.",no_app_file:"il n'y a pas de fichier d'application",runtime_error:"il y a une erreur d'exécution",space_not_working:'"Le Space ne fonctionne pas car" {0}',space_paused:"le space est en pause",use_via_api:"Utiliser via l'API"},Pa={uploading:"Téléchargement..."},Oa={highlighted_text:"Texte surligné"},La={allow_webcam_access:"Veuillez autoriser l'accès à la webcam pour l'enregistrement.",brush_color:"Couleur du pinceau",brush_radius:"Taille du pinceau",image:"Image",remove_image:"Supprimer l'image",select_brush_color:"Sélectionner la couleur du pinceau",start_drawing:"Commencer à dessiner",use_brush:"Utiliser le pinceau"},Ia={label:"Étiquette"},Ca={enable_cookies:"Si vous visitez un Space HuggingFace en mode Incognito, vous devez activer les cookies tiers.",incorrect_credentials:"Identifiants incorrects",username:"nom d'utilisateur",password:"mot de passe",login:"Connexion"},Da={number:"Nombre"},Ra={plot:"Graphique"},ja={radio:"Bouton radio"},Na={slider:"Curseur"},Ha={click_to_upload:"Cliquez pour télécharger",drop_audio:"Déposez l'audio ici",drop_csv:"Déposez le CSV ici",drop_file:"Déposez le fichier ici",drop_image:"Déposez l'image ici",drop_video:"Déposez la vidéo ici",drop_gallery:"Déposez les médias ici",paste_clipboard:"Coller depuis le presse-papiers"},xm={_name:ba,"3D_model":{"3d_model":"Modèle 3D"},annotated_image:wa,audio:va,blocks:ya,checkbox:ka,code:xa,color_picker:Ea,common:Sa,dataframe:$a,dropdown:Ta,errors:Aa,file:Pa,highlighted_text:Oa,image:La,label:Ia,login:Ca,number:Da,plot:Ra,radio:ja,slider:Na,upload_text:Ha},Em=Object.freeze(Object.defineProperty({__proto__:null,_name:ba,annotated_image:wa,audio:va,blocks:ya,checkbox:ka,code:xa,color_picker:Ea,common:Sa,dataframe:$a,default:xm,dropdown:Ta,errors:Aa,file:Pa,highlighted_text:Oa,image:La,label:Ia,login:Ca,number:Da,plot:Ra,radio:ja,slider:Na,upload_text:Ha},Symbol.toStringTag,{value:"Module"})),Ba="עברית",za={annotated_image:"תמונה מוערת"},Ma={allow_recording_access:"אנא אפשר גישה למיקרופון להקלטה.",audio:"שמע",record_from_microphone:"הקלט ממיקרופון",stop_recording:"עצור הקלטה",no_device_support:"לא ניתן לגשת להתקני מדיה. וודא שאתה מריץ ממקור מאובטח (https) או localhost (או שהעברת אישור SSL תקף ל-ssl_verify), ושאישרת לדפדפן גישה למכשיר שלך.",stop:"עצור",resume:"המשך",record:"הקלט",no_microphone:"לא נמצא מיקרופון",pause:"השהה",play:"נגן",waiting:"ממתין"},Va={connection_can_break:"בנייד, החיבור עלול להתנתק אם כרטיסייה זו מאבדת מיקוד או המכשיר נכנס למצב שינה, מה שיגרום לאיבוד מקומך בתור.",long_requests_queue:"יש תור ארוך של בקשות ממתינות. שכפל Space זה כדי לדלג על התור.",lost_connection:"החיבור אבד בגלל עזיבת העמוד. חוזר לתור..."},Ua={checkbox:"תיבת סימון",checkbox_group:"קבוצת תיבות סימון"},Ga={code:"קוד"},Fa={color_picker:"בוחר צבעים"},qa={built_with:"נבנה עם",built_with_gradio:"נבנה עם Gradio",clear:"נקה",download:"הורד",edit:"ערוך",empty:"ריק",error:"שגיאה",hosted_on:"מאוחסן ב",loading:"טוען",logo:"לוגו",or:"או",remove:"הסר",settings:"הגדרות",share:"שתף",submit:"שלח",undo:"בטל",no_devices:"לא נמצאו התקנים",language:"שפה",display_theme:"ערכת נושא",pwa:"יישום ווב מתקדם"},Wa={incorrect_format:"פורמט שגוי, רק קבצי CSV ו-TSV נתמכים",new_column:"הוסף עמודה",new_row:"שורה חדשה",add_row_above:"הוסף שורה מעל",add_row_below:"הוסף שורה מתחת",add_column_left:"הוסף עמודה משמאל",add_column_right:"הוסף עמודה מימין"},Xa={dropdown:"תפריט נפתח"},Za={build_error:"יש שגיאת בנייה",config_error:"יש שגיאת תצורה",contact_page_author:"אנא צור קשר עם מחבר העמוד כדי ליידע אותו.",no_app_file:"אין קובץ יישום",runtime_error:"יש שגיאת זמן ריצה",space_not_working:'"ה-Space לא עובד כי" {0}',space_paused:"ה-space מושהה",use_via_api:"השתמש דרך API"},Ja={uploading:"מעלה..."},Ka={highlighted_text:"טקסט מודגש"},Ya={allow_webcam_access:"אנא אפשר גישה למצלמת האינטרנט להקלטה.",brush_color:"צבע מברשת",brush_radius:"גודל מברשת",image:"תמונה",remove_image:"הסר תמונה",select_brush_color:"בחר צבע מברשת",start_drawing:"התחל לצייר",use_brush:"השתמש במברשת"},Qa={label:"תווית"},es={enable_cookies:"אם אתה מבקר ב-Space של HuggingFace במצב גלישה בסתר, עליך לאפשר קובצי cookie של צד שלישי.",incorrect_credentials:"פרטי התחברות שגויים",username:"שם משתמש",password:"סיסמה",login:"התחבר"},ts={number:"מספר"},os={plot:"תרשים"},rs={radio:"כפתור רדיו"},ns={slider:"מחוון"},is={click_to_upload:"לחץ להעלאה",drop_audio:"שחרר קובץ שמע כאן",drop_csv:"שחרר CSV כאן",drop_file:"שחרר קובץ כאן",drop_image:"שחרר תמונה כאן",drop_video:"שחרר וידאו כאן",drop_gallery:"שחרר מדיה כאן",paste_clipboard:"הדבק מהלוח"},Sm={_name:Ba,"3D_model":{"3d_model":"מודל תלת מימד"},annotated_image:za,audio:Ma,blocks:Va,checkbox:Ua,code:Ga,color_picker:Fa,common:qa,dataframe:Wa,dropdown:Xa,errors:Za,file:Ja,highlighted_text:Ka,image:Ya,label:Qa,login:es,number:ts,plot:os,radio:rs,slider:ns,upload_text:is},$m=Object.freeze(Object.defineProperty({__proto__:null,_name:Ba,annotated_image:za,audio:Ma,blocks:Va,checkbox:Ua,code:Ga,color_picker:Fa,common:qa,dataframe:Wa,default:Sm,dropdown:Xa,errors:Za,file:Ja,highlighted_text:Ka,image:Ya,label:Qa,login:es,number:ts,plot:os,radio:rs,slider:ns,upload_text:is},Symbol.toStringTag,{value:"Module"})),as="हिंदी",ss={annotated_image:"एनोटेट की गई छवि"},ls={allow_recording_access:"कृपया रिकॉर्डिंग के लिए माइक्रोफ़ोन एक्सेस की अनुमति दें।",audio:"ऑडियो",record_from_microphone:"माइक्रोफ़ोन से रिकॉर्ड करें",stop_recording:"रिकॉर्डिंग रोकें",no_device_support:"मीडिया डिवाइस तक पहुंच नहीं हो सकी। सुनिश्चित करें कि आप एक सुरक्षित स्रोत (https) या localhost पर चल रहे हैं (या ssl_verify को एक वैध SSL प्रमाणपत्र दिया है), और आपने अपने ब्राउज़र को अपने डिवाइस तक पहुंच की अनुमति दी है।",stop:"रोकें",resume:"जारी रखें",record:"रिकॉर्ड",no_microphone:"कोई माइक्रोफ़ोन नहीं मिला",pause:"रोकें",play:"चलाएं",waiting:"प्रतीक्षा कर रहा है"},cs={connection_can_break:"मोबाइल पर, यदि यह टैब फोकस खो देता है या डिवाइस स्लीप मोड में चला जाता है, तो कनेक्शन टूट सकता है, जिससे कतार में आपका स्थान खो जाएगा।",long_requests_queue:"लंबित अनुरोधों की एक लंबी कतार है। इसे छोड़ने के लिए इस Space को डुप्लिकेट करें।",lost_connection:"पेज छोड़ने के कारण कनेक्शन टूट गया। कतार में वापस जा रहा है..."},us={checkbox:"चेकबॉक्स",checkbox_group:"चेकबॉक्स समूह"},ds={code:"कोड"},_s={color_picker:"रंग चयनकर्ता"},ps={built_with:"के साथ बनाया गया",built_with_gradio:"Gradio के साथ बनाया गया",clear:"साफ़ करें",download:"डाउनलोड",edit:"संपादित करें",empty:"खाली",error:"त्रुटि",hosted_on:"पर होस्ट किया गया",loading:"लोड हो रहा है",logo:"लोगो",or:"या",remove:"हटाएं",settings:"सेटिंग्स",share:"साझा करें",submit:"सबमिट करें",undo:"पूर्ववत करें",no_devices:"कोई डिवाइस नहीं मिला",language:"भाषा",display_theme:"प्रदर्शन थीम",pwa:"प्रोग्रेसिव वेब ऐप"},hs={incorrect_format:"गलत प्रारूप, केवल CSV और TSV फ़ाइलें समर्थित हैं",new_column:"कॉलम जोड़ें",new_row:"नई पंक्ति",add_row_above:"ऊपर पंक्ति जोड़ें",add_row_below:"नीचे पंक्ति जोड़ें",add_column_left:"बाएं कॉलम जोड़ें",add_column_right:"दाएं कॉलम जोड़ें"},ms={dropdown:"ड्रॉपडाउन"},gs={build_error:"एक बिल्ड त्रुटि है",config_error:"एक कॉन्फ़िगरेशन त्रुटि है",contact_page_author:"कृपया पेज के लेखक को सूचित करने के लिए संपर्क करें।",no_app_file:"कोई ऐप फ़ाइल नहीं है",runtime_error:"एक रनटाइम त्रुटि है",space_not_working:'"Space काम नहीं कर रहा है क्योंकि" {0}',space_paused:"space रोका गया है",use_via_api:"API के माध्यम से उपयोग करें"},fs={uploading:"अपलोड हो रहा है..."},bs={highlighted_text:"हाइलाइट किया गया टेक्स्ट"},ws={allow_webcam_access:"कृपया रिकॉर्डिंग के लिए वेबकैम एक्सेस की अनुमति दें।",brush_color:"ब्रश का रंग",brush_radius:"ब्रश का आकार",image:"छवि",remove_image:"छवि हटाएं",select_brush_color:"ब्रश का रंग चुनें",start_drawing:"चित्रकारी शुरू करें",use_brush:"ब्रश का उपयोग करें"},vs={label:"लेबल"},ys={enable_cookies:"यदि आप इनकॉग्निटो मोड में HuggingFace Space का दौरा कर रहे हैं, तो आपको थर्ड-पार्टी कुकीज़ को सक्षम करना होगा।",incorrect_credentials:"गलत क्रेडेंशियल्स",username:"उपयोगकर्ता नाम",password:"पासवर्ड",login:"लॉग इन करें"},ks={number:"संख्या"},xs={plot:"प्लॉट"},Es={radio:"रेडियो"},Ss={slider:"स्लाइडर"},$s={click_to_upload:"अपलोड करने के लिए क्लिक करें",drop_audio:"ऑडियो यहाँ छोड़ें",drop_csv:"CSV यहाँ छोड़ें",drop_file:"फ़ाइल यहाँ छोड़ें",drop_image:"छवि यहाँ छोड़ें",drop_video:"वीडियो यहाँ छोड़ें",drop_gallery:"मीडिया यहाँ छोड़ें",paste_clipboard:"क्लिपबोर्ड से पेस्ट करें"},Tm={_name:as,"3D_model":{"3d_model":"3D मॉडल"},annotated_image:ss,audio:ls,blocks:cs,checkbox:us,code:ds,color_picker:_s,common:ps,dataframe:hs,dropdown:ms,errors:gs,file:fs,highlighted_text:bs,image:ws,label:vs,login:ys,number:ks,plot:xs,radio:Es,slider:Ss,upload_text:$s},Am=Object.freeze(Object.defineProperty({__proto__:null,_name:as,annotated_image:ss,audio:ls,blocks:cs,checkbox:us,code:ds,color_picker:_s,common:ps,dataframe:hs,default:Tm,dropdown:ms,errors:gs,file:fs,highlighted_text:bs,image:ws,label:vs,login:ys,number:ks,plot:xs,radio:Es,slider:Ss,upload_text:$s},Symbol.toStringTag,{value:"Module"})),Ts="日本語",As={annotated_image:"注釈付き画像"},Ps={allow_recording_access:"録音のためにマイクへのアクセスを許可してください。",audio:"音声",record_from_microphone:"マイクから録音",stop_recording:"録音を停止",no_device_support:"メディアデバイスにアクセスできません。安全なソース（https）またはlocalhostで実行していること（またはssl_verifyに有効なSSL証明書を渡していること）、ブラウザにデバイスへのアクセスを許可していることを確認してください。",stop:"停止",resume:"再開",record:"録音",no_microphone:"マイクが見つかりません",pause:"一時停止",play:"再生",waiting:"待機中"},Os={connection_can_break:"モバイルでは、このタブがフォーカスを失うかデバイスがスリープモードになると接続が切断され、キュー内の位置が失われる可能性があります。",long_requests_queue:"保留中のリクエストの長いキューがあります。このSpaceを複製してスキップしてください。",lost_connection:"ページを離れたため接続が切断されました。キューに戻ります..."},Ls={checkbox:"チェックボックス",checkbox_group:"チェックボックスグループ"},Is={code:"コード"},Cs={color_picker:"カラーピッカー"},Ds={built_with:"で作成",built_with_gradio:"Gradioで作成",clear:"クリア",download:"ダウンロード",edit:"編集",empty:"空",error:"エラー",hosted_on:"でホスト",loading:"読み込み中",logo:"ロゴ",or:"または",remove:"削除",settings:"設定",share:"共有",submit:"送信",undo:"元に戻す",no_devices:"デバイスが見つかりません",language:"言語",display_theme:"表示テーマ",pwa:"プログレッシブウェブアプリ"},Rs={incorrect_format:"不正なフォーマット、CSVとTSVファイルのみサポートされています",new_column:"列を追加",new_row:"新しい行",add_row_above:"上に行を追加",add_row_below:"下に行を追加",add_column_left:"左に列を追加",add_column_right:"右に列を追加"},js={dropdown:"ドロップダウン"},Ns={build_error:"ビルドエラーがあります",config_error:"設定エラーがあります",contact_page_author:"ページの作者に連絡して知らせてください。",no_app_file:"アプリファイルがありません",runtime_error:"ランタイムエラーがあります",space_not_working:'"Spaceが動作していません。理由：" {0}',space_paused:"spaceが一時停止されています",use_via_api:"APIを介して使用"},Hs={uploading:"アップロード中..."},Bs={highlighted_text:"ハイライトされたテキスト"},zs={allow_webcam_access:"録画のためにウェブカメラへのアクセスを許可してください。",brush_color:"ブラシの色",brush_radius:"ブラシのサイズ",image:"画像",remove_image:"画像を削除",select_brush_color:"ブラシの色を選択",start_drawing:"描画を開始",use_brush:"ブラシを使用"},Ms={label:"ラベル"},Vs={enable_cookies:"シークレットモードでHuggingFace Spaceを訪問している場合、サードパーティのCookieを有効にする必要があります。",incorrect_credentials:"認証情報が正しくありません",username:"ユーザー名",password:"パスワード",login:"ログイン"},Us={number:"数値"},Gs={plot:"プロット"},Fs={radio:"ラジオボタン"},qs={slider:"スライダー"},Ws={click_to_upload:"クリックしてアップロード",drop_audio:"音声をここにドロップ",drop_csv:"CSVをここにドロップ",drop_file:"ファイルをここにドロップ",drop_image:"画像をここにドロップ",drop_video:"動画をここにドロップ",drop_gallery:"メディアをここにドロップ",paste_clipboard:"クリップボードから貼り付け"},Pm={_name:Ts,"3D_model":{"3d_model":"3Dモデル"},annotated_image:As,audio:Ps,blocks:Os,checkbox:Ls,code:Is,color_picker:Cs,common:Ds,dataframe:Rs,dropdown:js,errors:Ns,file:Hs,highlighted_text:Bs,image:zs,label:Ms,login:Vs,number:Us,plot:Gs,radio:Fs,slider:qs,upload_text:Ws},Om=Object.freeze(Object.defineProperty({__proto__:null,_name:Ts,annotated_image:As,audio:Ps,blocks:Os,checkbox:Ls,code:Is,color_picker:Cs,common:Ds,dataframe:Rs,default:Pm,dropdown:js,errors:Ns,file:Hs,highlighted_text:Bs,image:zs,label:Ms,login:Vs,number:Us,plot:Gs,radio:Fs,slider:qs,upload_text:Ws},Symbol.toStringTag,{value:"Module"})),Xs="한국어",Zs={annotated_image:"주석이 달린 이미지"},Js={allow_recording_access:"녹음을 위해 마이크 접근을 허용해 주세요.",audio:"오디오",record_from_microphone:"마이크로 녹음",stop_recording:"녹음 중지",no_device_support:"미디어 장치에 접근할 수 없습니다. 보안 소스(https) 또는 localhost에서 실행 중인지(또는 ssl_verify에 유효한 SSL 인증서를 전달했는지), 브라우저에 장치 접근 권한을 부여했는지 확인하세요.",stop:"중지",resume:"재개",record:"녹음",no_microphone:"마이크를 찾을 수 없습니다",pause:"일시정지",play:"재생",waiting:"대기 중"},Ks={connection_can_break:"모바일에서는 이 탭이 포커스를 잃거나 기기가 절전 모드로 전환되면 연결이 끊어져 대기열의 위치를 잃을 수 있습니다.",long_requests_queue:"대기 중인 요청의 긴 대기열이 있습니다. 이 Space를 복제하여 건너뛰세요.",lost_connection:"페이지를 떠나 연결이 끊어졌습니다. 대기열로 돌아가는 중..."},Ys={checkbox:"체크박스",checkbox_group:"체크박스 그룹"},Qs={code:"코드"},el={color_picker:"색상 선택기"},tl={built_with:"로 제작됨",built_with_gradio:"Gradio로 제작됨",clear:"지우기",download:"다운로드",edit:"편집",empty:"비어 있음",error:"오류",hosted_on:"에서 호스팅됨",loading:"로딩 중",logo:"로고",or:"또는",remove:"제거",settings:"설정",share:"공유",submit:"제출",undo:"실행 취소",no_devices:"장치를 찾을 수 없습니다",language:"언어",display_theme:"디스플레이 테마",pwa:"프로그레시브 웹 앱"},ol={incorrect_format:"잘못된 형식입니다. CSV 및 TSV 파일만 지원됩니다",new_column:"열 추가",new_row:"새 행",add_row_above:"위에 행 추가",add_row_below:"아래에 행 추가",add_column_left:"왼쪽에 열 추가",add_column_right:"오른쪽에 열 추가"},rl={dropdown:"드롭다운"},nl={build_error:"빌드 오류가 있습니다",config_error:"구성 오류가 있습니다",contact_page_author:"페이지 작성자에게 연락하여 알려주세요.",no_app_file:"앱 파일이 없습니다",runtime_error:"런타임 오류가 있습니다",space_not_working:'"Space가 작동하지 않는 이유:" {0}',space_paused:"space가 일시 중지되었습니다",use_via_api:"API를 통해 사용"},il={uploading:"업로드 중..."},al={highlighted_text:"강조 표시된 텍스트"},sl={allow_webcam_access:"녹화를 위해 웹캠 접근을 허용해 주세요.",brush_color:"브러시 색상",brush_radius:"브러시 크기",image:"이미지",remove_image:"이미지 제거",select_brush_color:"브러시 색상 선택",start_drawing:"그리기 시작",use_brush:"브러시 사용"},ll={label:"레이블"},cl={enable_cookies:"시크릿 모드에서 HuggingFace Space를 방문하는 경우 타사 쿠키를 활성화해야 합니다.",incorrect_credentials:"잘못된 자격 증명",username:"사용자 이름",password:"비밀번호",login:"로그인"},ul={number:"숫자"},dl={plot:"플롯"},_l={radio:"라디오"},pl={slider:"슬라이더"},hl={click_to_upload:"클릭하여 업로드",drop_audio:"오디오를 여기에 드롭",drop_csv:"CSV를 여기에 드롭",drop_file:"파일을 여기에 드롭",drop_image:"이미지를 여기에 드롭",drop_video:"비디오를 여기에 드롭",drop_gallery:"미디어를 여기에 드롭",paste_clipboard:"클립보드에서 붙여넣기"},Lm={_name:Xs,"3D_model":{"3d_model":"3D 모델"},annotated_image:Zs,audio:Js,blocks:Ks,checkbox:Ys,code:Qs,color_picker:el,common:tl,dataframe:ol,dropdown:rl,errors:nl,file:il,highlighted_text:al,image:sl,label:ll,login:cl,number:ul,plot:dl,radio:_l,slider:pl,upload_text:hl},Im=Object.freeze(Object.defineProperty({__proto__:null,_name:Xs,annotated_image:Zs,audio:Js,blocks:Ks,checkbox:Ys,code:Qs,color_picker:el,common:tl,dataframe:ol,default:Lm,dropdown:rl,errors:nl,file:il,highlighted_text:al,image:sl,label:ll,login:cl,number:ul,plot:dl,radio:_l,slider:pl,upload_text:hl},Symbol.toStringTag,{value:"Module"})),ml="Lietuvių",gl={built_with_gradio:"sukurta su gradio",clear:"Trinti",or:"arba",submit:"Pateikti",settings:"Nustatymai"},fl={click_to_upload:"Spustelėkite norėdami įkelti",drop_audio:"Įkelkite garso įrašą čia",drop_csv:"Įkelkite CSV čia",drop_file:"Įkelkite bylą čia",drop_image:"Įkelkite paveikslėlį čia",drop_video:"Įkelkite vaizdo įrašą čia"},Cm={_name:ml,common:gl,upload_text:fl},Dm=Object.freeze(Object.defineProperty({__proto__:null,_name:ml,common:gl,default:Cm,upload_text:fl},Symbol.toStringTag,{value:"Module"})),bl="Norsk bokmål",wl={annotated_image:"Annotert bilde"},vl={allow_recording_access:"Vennligst tillat mikrofontilgang for opptak.",audio:"Lyd",record_from_microphone:"Ta opp fra mikrofon",stop_recording:"Stopp opptak",no_device_support:"Kan ikke få tilgang til medieenheter. Sørg for at du kjører på en sikker kilde (https) eller localhost (eller har gitt et gyldig SSL-sertifikat til ssl_verify), og at du har gitt nettleseren tilgang til enheten din.",stop:"Stopp",resume:"Fortsett",record:"Ta opp",no_microphone:"Ingen mikrofon funnet",pause:"Pause",play:"Spill av",waiting:"Venter"},yl={connection_can_break:"På mobil kan tilkoblingen brytes hvis denne fanen mister fokus eller enheten går i dvale, og du mister plassen din i køen.",long_requests_queue:"Det er en lang kø med ventende forespørsler. Dupliser denne Space for å hoppe over køen.",lost_connection:"Mistet tilkobling på grunn av at siden ble forlatt. Går tilbake til køen..."},kl={checkbox:"Avkrysningsboks",checkbox_group:"Avkrysningsboksgruppe"},xl={code:"Kode"},El={color_picker:"Fargevelger"},Sl={built_with:"bygget med",built_with_gradio:"Bygget med Gradio",clear:"Tøm",download:"Last ned",edit:"Rediger",empty:"Tom",error:"Feil",hosted_on:"hostet på",loading:"Laster",logo:"logo",or:"eller",remove:"Fjern",settings:"Innstillinger",share:"Del",submit:"Send",undo:"Angre",no_devices:"Ingen enheter funnet",language:"Språk",display_theme:"Visningstema",pwa:"Progressiv webapplikasjon"},$l={incorrect_format:"Feil format, kun CSV- og TSV-filer støttes",new_column:"Legg til kolonne",new_row:"Ny rad",add_row_above:"Legg til rad over",add_row_below:"Legg til rad under",add_column_left:"Legg til kolonne til venstre",add_column_right:"Legg til kolonne til høyre"},Tl={dropdown:"Nedtrekksmeny"},Al={build_error:"det er en byggefeil",config_error:"det er en konfigurasjonsfeil",contact_page_author:"Vennligst kontakt sidens forfatter for å informere dem.",no_app_file:"det er ingen app-fil",runtime_error:"det er en kjøretidsfeil",space_not_working:'"Space fungerer ikke fordi" {0}',space_paused:"space er pauset",use_via_api:"Bruk via API"},Pl={uploading:"Laster opp..."},Ol={highlighted_text:"Uthevet tekst"},Ll={allow_webcam_access:"Vennligst tillat webkameratilgang for opptak.",brush_color:"Penselfarge",brush_radius:"Penselstørrelse",image:"Bilde",remove_image:"Fjern bilde",select_brush_color:"Velg penselfarge",start_drawing:"Start tegning",use_brush:"Bruk pensel"},Il={label:"Etikett"},Cl={enable_cookies:"Hvis du besøker en HuggingFace Space i inkognitomodus, må du aktivere tredjeparts informasjonskapsler.",incorrect_credentials:"Feil påloggingsinformasjon",username:"brukernavn",password:"passord",login:"Logg inn"},Dl={number:"Tall"},Rl={plot:"Plott"},jl={radio:"Radio"},Nl={slider:"Glidebryter"},Hl={click_to_upload:"Klikk for å laste opp",drop_audio:"Slipp lyd her",drop_csv:"Slipp CSV her",drop_file:"Slipp fil her",drop_image:"Slipp bilde her",drop_video:"Slipp video her",drop_gallery:"Slipp media her",paste_clipboard:"Lim inn fra utklippstavle"},Rm={_name:bl,"3D_model":{"3d_model":"3D-modell"},annotated_image:wl,audio:vl,blocks:yl,checkbox:kl,code:xl,color_picker:El,common:Sl,dataframe:$l,dropdown:Tl,errors:Al,file:Pl,highlighted_text:Ol,image:Ll,label:Il,login:Cl,number:Dl,plot:Rl,radio:jl,slider:Nl,upload_text:Hl},jm=Object.freeze(Object.defineProperty({__proto__:null,_name:bl,annotated_image:wl,audio:vl,blocks:yl,checkbox:kl,code:xl,color_picker:El,common:Sl,dataframe:$l,default:Rm,dropdown:Tl,errors:Al,file:Pl,highlighted_text:Ol,image:Ll,label:Il,login:Cl,number:Dl,plot:Rl,radio:jl,slider:Nl,upload_text:Hl},Symbol.toStringTag,{value:"Module"})),Bl="Nederlands",zl={built_with_gradio:"gemaakt met gradio",clear:"Wis",or:"of",submit:"Zend in",settings:"Instellingen"},Ml={click_to_upload:"Klik om the Uploaden",drop_audio:"Sleep een Geluidsbestand hier",drop_csv:"Sleep een CSV hier",drop_file:"Sleep een Document hier",drop_image:"Sleep een Afbeelding hier",drop_video:"Sleep een Video hier"},Nm={_name:Bl,common:zl,upload_text:Ml},Hm=Object.freeze(Object.defineProperty({__proto__:null,_name:Bl,common:zl,default:Nm,upload_text:Ml},Symbol.toStringTag,{value:"Module"})),Vl="Polski",Ul={annotated_image:"Obraz z adnotacjami"},Gl={allow_recording_access:"Proszę zezwolić na dostęp do mikrofonu w celu nagrywania.",audio:"Audio",record_from_microphone:"Nagraj z mikrofonu",stop_recording:"Zatrzymaj nagrywanie",no_device_support:"Nie można uzyskać dostępu do urządzeń multimedialnych. Upewnij się, że działasz na bezpiecznym źródle (https) lub localhost (lub przekazałeś prawidłowy certyfikat SSL do ssl_verify), i że zezwoliłeś przeglądarce na dostęp do twojego urządzenia.",stop:"Stop",resume:"Wznów",record:"Nagrywaj",no_microphone:"Nie znaleziono mikrofonu",pause:"Pauza",play:"Odtwórz",waiting:"Oczekiwanie"},Fl={connection_can_break:"Na urządzeniu mobilnym połączenie może zostać przerwane, jeśli ta karta straci fokus lub urządzenie przejdzie w tryb uśpienia, tracąc twoje miejsce w kolejce.",long_requests_queue:"Istnieje długa kolejka oczekujących żądań. Zduplikuj ten Space, aby ją pominąć.",lost_connection:"Utracono połączenie z powodu opuszczenia strony. Powrót do kolejki..."},ql={checkbox:"Pole wyboru",checkbox_group:"Grupa pól wyboru"},Wl={code:"Kod"},Xl={color_picker:"Wybór koloru"},Zl={built_with:"zbudowane z",built_with_gradio:"Zbudowane z Gradio",clear:"Wyczyść",download:"Pobierz",edit:"Edytuj",empty:"Pusty",error:"Błąd",hosted_on:"hostowane na",loading:"Ładowanie",logo:"logo",or:"lub",remove:"Usuń",settings:"Ustawienia",share:"Udostępnij",submit:"Wyślij",undo:"Cofnij",no_devices:"Nie znaleziono urządzeń",language:"Język",display_theme:"Motyw wyświetlania",pwa:"Progresywna aplikacja webowa"},Jl={incorrect_format:"Nieprawidłowy format, obsługiwane są tylko pliki CSV i TSV",new_column:"Dodaj kolumnę",new_row:"Nowy wiersz",add_row_above:"Dodaj wiersz powyżej",add_row_below:"Dodaj wiersz poniżej",add_column_left:"Dodaj kolumnę po lewej",add_column_right:"Dodaj kolumnę po prawej"},Kl={dropdown:"Lista rozwijana"},Yl={build_error:"wystąpił błąd kompilacji",config_error:"wystąpił błąd konfiguracji",contact_page_author:"Proszę skontaktować się z autorem strony, aby go poinformować.",no_app_file:"brak pliku aplikacji",runtime_error:"wystąpił błąd wykonania",space_not_working:'"Space nie działa, ponieważ" {0}',space_paused:"space jest wstrzymany",use_via_api:"Użyj przez API"},Ql={uploading:"Przesyłanie..."},ec={highlighted_text:"Wyróżniony tekst"},tc={allow_webcam_access:"Proszę zezwolić na dostęp do kamery internetowej w celu nagrywania.",brush_color:"Kolor pędzla",brush_radius:"Rozmiar pędzla",image:"Obraz",remove_image:"Usuń obraz",select_brush_color:"Wybierz kolor pędzla",start_drawing:"Rozpocznij rysowanie",use_brush:"Użyj pędzla"},oc={label:"Etykieta"},rc={enable_cookies:"Jeśli odwiedzasz HuggingFace Space w trybie incognito, musisz włączyć pliki cookie stron trzecich.",incorrect_credentials:"Nieprawidłowe dane logowania",username:"nazwa użytkownika",password:"hasło",login:"Zaloguj się"},nc={number:"Liczba"},ic={plot:"Wykres"},ac={radio:"Przycisk opcji"},sc={slider:"Suwak"},lc={click_to_upload:"Kliknij, aby przesłać",drop_audio:"Upuść audio tutaj",drop_csv:"Upuść CSV tutaj",drop_file:"Upuść plik tutaj",drop_image:"Upuść obraz tutaj",drop_video:"Upuść wideo tutaj",drop_gallery:"Upuść media tutaj",paste_clipboard:"Wklej ze schowka"},Bm={_name:Vl,"3D_model":{"3d_model":"Model 3D"},annotated_image:Ul,audio:Gl,blocks:Fl,checkbox:ql,code:Wl,color_picker:Xl,common:Zl,dataframe:Jl,dropdown:Kl,errors:Yl,file:Ql,highlighted_text:ec,image:tc,label:oc,login:rc,number:nc,plot:ic,radio:ac,slider:sc,upload_text:lc},zm=Object.freeze(Object.defineProperty({__proto__:null,_name:Vl,annotated_image:Ul,audio:Gl,blocks:Fl,checkbox:ql,code:Wl,color_picker:Xl,common:Zl,dataframe:Jl,default:Bm,dropdown:Kl,errors:Yl,file:Ql,highlighted_text:ec,image:tc,label:oc,login:rc,number:nc,plot:ic,radio:ac,slider:sc,upload_text:lc},Symbol.toStringTag,{value:"Module"})),cc="Português do Brasil",uc={built_with_gradio:"Construído com gradio",clear:"Limpar",error:"Erro",flag:"Marcar",loading:"Carregando",or:"ou",submit:"Enviar"},dc={click_to_upload:"Clique para o Upload",drop_audio:"Solte o Áudio Aqui",drop_csv:"Solte o CSV Aqui",drop_file:"Solte o Arquivo Aqui",drop_image:"Solte a Imagem Aqui",drop_video:"Solte o Vídeo Aqui"},Mm={_name:cc,common:uc,upload_text:dc},Vm=Object.freeze(Object.defineProperty({__proto__:null,_name:cc,common:uc,default:Mm,upload_text:dc},Symbol.toStringTag,{value:"Module"})),_c="Português",pc={annotated_image:"Imagem anotada"},hc={allow_recording_access:"Por favor, permita o acesso ao microfone para gravação.",audio:"Áudio",record_from_microphone:"Gravar do microfone",stop_recording:"Parar gravação",no_device_support:"Não é possível acessar dispositivos de mídia. Certifique-se de que você está executando em uma fonte segura (https) ou localhost (ou passou um certificado SSL válido para ssl_verify), e que você permitiu o acesso do navegador ao seu dispositivo.",stop:"Parar",resume:"Retomar",record:"Gravar",no_microphone:"Nenhum microfone encontrado",pause:"Pausar",play:"Reproduzir",waiting:"Aguardando"},mc={connection_can_break:"No celular, a conexão pode ser interrompida se esta aba perder o foco ou o dispositivo entrar em modo de suspensão, perdendo sua posição na fila.",long_requests_queue:"H�� uma longa fila de solicitações pendentes. Duplique este Space para pular a fila.",lost_connection:"Conexão perdida devido à saída da página. Retornando à fila..."},gc={checkbox:"Caixa de seleção",checkbox_group:"Grupo de caixas de seleção"},fc={code:"Código"},bc={color_picker:"Seletor de cor"},wc={built_with:"construído com",built_with_gradio:"Construído com Gradio",clear:"Limpar",download:"Baixar",edit:"Editar",empty:"Vazio",error:"Erro",hosted_on:"hospedado em",loading:"Carregando",logo:"logo",or:"ou",remove:"Remover",settings:"Configurações",share:"Compartilhar",submit:"Enviar",undo:"Desfazer",no_devices:"Nenhum dispositivo encontrado",language:"Idioma",display_theme:"Tema de exibição",pwa:"Aplicativo web progressivo"},vc={incorrect_format:"Formato incorreto, apenas arquivos CSV e TSV são suportados",new_column:"Adicionar coluna",new_row:"Nova linha",add_row_above:"Adicionar linha acima",add_row_below:"Adicionar linha abaixo",add_column_left:"Adicionar coluna à esquerda",add_column_right:"Adicionar coluna à direita"},yc={dropdown:"Lista suspensa"},kc={build_error:"há um erro de compilação",config_error:"há um erro de configuração",contact_page_author:"Por favor, entre em contato com o autor da página para informá-lo.",no_app_file:"não há arquivo de aplicativo",runtime_error:"há um erro de execução",space_not_working:'"O Space não está funcionando porque" {0}',space_paused:"o space está pausado",use_via_api:"Use via API"},xc={uploading:"Enviando..."},Ec={highlighted_text:"Texto destacado"},Sc={allow_webcam_access:"Por favor, permita o acesso à webcam para gravação.",brush_color:"Cor do pincel",brush_radius:"Tamanho do pincel",image:"Imagem",remove_image:"Remover imagem",select_brush_color:"Selecionar cor do pincel",start_drawing:"Começar a desenhar",use_brush:"Usar pincel"},$c={label:"Rótulo"},Tc={enable_cookies:"Se você estiver visitando um Space HuggingFace no modo anônimo, você precisa habilitar cookies de terceiros.",incorrect_credentials:"Credenciais incorretas",username:"nome de usuário",password:"senha",login:"Entrar"},Ac={number:"Número"},Pc={plot:"Gráfico"},Oc={radio:"Botão de opção"},Lc={slider:"Controle deslizante"},Ic={click_to_upload:"Clique para enviar",drop_audio:"Solte o áudio aqui",drop_csv:"Solte o CSV aqui",drop_file:"Solte o arquivo aqui",drop_image:"Solte a imagem aqui",drop_video:"Solte o vídeo aqui",drop_gallery:"Solte a mídia aqui",paste_clipboard:"Colar da área de transferência"},Um={_name:_c,"3D_model":{"3d_model":"Modelo 3D"},annotated_image:pc,audio:hc,blocks:mc,checkbox:gc,code:fc,color_picker:bc,common:wc,dataframe:vc,dropdown:yc,errors:kc,file:xc,highlighted_text:Ec,image:Sc,label:$c,login:Tc,number:Ac,plot:Pc,radio:Oc,slider:Lc,upload_text:Ic},Gm=Object.freeze(Object.defineProperty({__proto__:null,_name:_c,annotated_image:pc,audio:hc,blocks:mc,checkbox:gc,code:fc,color_picker:bc,common:wc,dataframe:vc,default:Um,dropdown:yc,errors:kc,file:xc,highlighted_text:Ec,image:Sc,label:$c,login:Tc,number:Ac,plot:Pc,radio:Oc,slider:Lc,upload_text:Ic},Symbol.toStringTag,{value:"Module"})),Cc="Română",Dc={annotated_image:"Imagine adnotată"},Rc={allow_recording_access:"Vă rugăm să permiteți accesul la microfon pentru înregistrare.",audio:"Audio",record_from_microphone:"Înregistrare de la microfon",stop_recording:"Oprire înregistrare",no_device_support:"Nu se poate accesa dispozitivele media. Asigurați-vă că rulați pe o sursă sigură (https) sau localhost (sau ați transmis un certificat SSL valid la ssl_verify), și că ați permis browserului accesul la dispozitivul dvs.",stop:"Stop",resume:"Reluare",record:"Înregistrare",no_microphone:"Nu s-a găsit niciun microfon",pause:"Pauză",play:"Redare",waiting:"Așteptare"},jc={connection_can_break:"Pe mobil, conexiunea poate fi întreruptă dacă această filă își pierde focusul sau dispozitivul intră în modul de repaus, pierzându-vă poziția în coadă.",long_requests_queue:"Există o coadă lungă de cereri în așteptare. Duplicați acest Space pentru a o evita.",lost_connection:"Conexiune pierdută din cauza părăsirii paginii. Revenire la coadă..."},Nc={checkbox:"Casetă de bifat",checkbox_group:"Grup de casete de bifat"},Hc={code:"Cod"},Bc={color_picker:"Selector de culoare"},zc={built_with:"construit cu",built_with_gradio:"Construit cu Gradio",clear:"Șterge",download:"Descarcă",edit:"Editează",empty:"Gol",error:"Eroare",hosted_on:"găzduit pe",loading:"Se încarcă",logo:"logo",or:"sau",remove:"Elimină",settings:"Setări",share:"Distribuie",submit:"Trimite",undo:"Anulează",no_devices:"Nu s-au găsit dispozitive",language:"Limbă",display_theme:"Temă de afișare",pwa:"Aplicație web progresivă"},Mc={incorrect_format:"Format incorect, sunt acceptate doar fișiere CSV și TSV",new_column:"Adaugă coloană",new_row:"Rând nou",add_row_above:"Adaugă rând deasupra",add_row_below:"Adaugă rând dedesubt",add_column_left:"Adaugă coloană la stânga",add_column_right:"Adaugă coloană la dreapta"},Vc={dropdown:"Listă derulantă"},Uc={build_error:"există o eroare de compilare",config_error:"există o eroare de configurare",contact_page_author:"Vă rugăm să contactați autorul paginii pentru a-l informa.",no_app_file:"nu există fișier de aplicație",runtime_error:"există o eroare de execuție",space_not_working:'"Space-ul nu funcționează deoarece" {0}',space_paused:"space-ul este în pauză",use_via_api:"Utilizați prin API"},Gc={uploading:"Se încarcă..."},Fc={highlighted_text:"Text evidențiat"},qc={allow_webcam_access:"Vă rugăm să permiteți accesul la camera web pentru înregistrare.",brush_color:"Culoare pensulă",brush_radius:"Dimensiune pensulă",image:"Imagine",remove_image:"Elimină imaginea",select_brush_color:"Selectează culoarea pensulei",start_drawing:"Începe să desenezi",use_brush:"Folosește pensula"},Wc={label:"Etichetă"},Xc={enable_cookies:"Dacă vizitați un Space HuggingFace în modul incognito, trebuie să activați cookie-urile terțe.",incorrect_credentials:"Credențiale incorecte",username:"nume de utilizator",password:"parolă",login:"Autentificare"},Zc={number:"Număr"},Jc={plot:"Grafic"},Kc={radio:"Buton radio"},Yc={slider:"Glisor"},Qc={click_to_upload:"Click pentru încărcare",drop_audio:"Plasează audio aici",drop_csv:"Plasează CSV aici",drop_file:"Plasează fișierul aici",drop_image:"Plasează imaginea aici",drop_video:"Plasează video aici",drop_gallery:"Plasează media aici",paste_clipboard:"Lipește din clipboard"},Fm={_name:Cc,"3D_model":{"3d_model":"Model 3D"},annotated_image:Dc,audio:Rc,blocks:jc,checkbox:Nc,code:Hc,color_picker:Bc,common:zc,dataframe:Mc,dropdown:Vc,errors:Uc,file:Gc,highlighted_text:Fc,image:qc,label:Wc,login:Xc,number:Zc,plot:Jc,radio:Kc,slider:Yc,upload_text:Qc},qm=Object.freeze(Object.defineProperty({__proto__:null,_name:Cc,annotated_image:Dc,audio:Rc,blocks:jc,checkbox:Nc,code:Hc,color_picker:Bc,common:zc,dataframe:Mc,default:Fm,dropdown:Vc,errors:Uc,file:Gc,highlighted_text:Fc,image:qc,label:Wc,login:Xc,number:Zc,plot:Jc,radio:Kc,slider:Yc,upload_text:Qc},Symbol.toStringTag,{value:"Module"})),eu="Русский",tu={annotated_image:"Изображение с аннотациями"},ou={allow_recording_access:"Пожалуйста, разрешите доступ к микрофону для записи.",audio:"Аудио",record_from_microphone:"Запись с микрофона",stop_recording:"Остановить запись",no_device_support:"Невозможно получить доступ к медиа-устройствам. Убедитесь, что вы работаете с безопасного источника (https) или localhost (или передали действительный SSL-сертификат в ssl_verify), и что вы разрешили браузеру доступ к вашему устройству.",stop:"Стоп",resume:"Продолжить",record:"Запись",no_microphone:"Микрофон не найден",pause:"Пауза",play:"Воспроизвести",waiting:"Ожидание"},ru={connection_can_break:"На мобильном устройстве соединение может прерваться, если эта вкладка потеряет фокус или устройство перейдет в спящий режим, что приведет к потере вашего места в очереди.",long_requests_queue:"Существует длинная очередь ожидающих запросов. Создайте дубликат этого Space, чтобы пропустить очередь.",lost_connection:"Соединение потеряно из-за ухода со страницы. Возвращение в очередь..."},nu={checkbox:"Флажок",checkbox_group:"Группа флажков"},iu={code:"Код"},au={color_picker:"Выбор цвета"},su={built_with:"создано с помощью",built_with_gradio:"Создано с помощью Gradio",clear:"Очистить",download:"Скачать",edit:"Редактировать",empty:"Пусто",error:"Ошибка",hosted_on:"размещено на",loading:"Загрузка",logo:"логотип",or:"или",remove:"Удалить",settings:"Настройки",share:"Поделиться",submit:"Отправить",undo:"Отменить",no_devices:"Устройства не найдены",language:"Язык",display_theme:"Тема оформления",pwa:"Прогрессивное веб-приложение"},lu={incorrect_format:"Неверный формат, поддерживаются только файлы CSV и TSV",new_column:"Добавить столбец",new_row:"Новая строка",add_row_above:"Добавить строку выше",add_row_below:"Добавить строку ниже",add_column_left:"Добавить столбец слева",add_column_right:"Добавить столбец справа"},cu={dropdown:"Выпадающий список"},uu={build_error:"произошла ошибка сборки",config_error:"произошла ошибка конфигурации",contact_page_author:"Пожалуйста, свяжитесь с автором страницы, чтобы сообщить об этом.",no_app_file:"отсутствует файл приложения",runtime_error:"произошла ошибка выполнения",space_not_working:'"Space не работает, потому что" {0}',space_paused:"space приостановлен",use_via_api:"Использовать через API"},du={uploading:"Загрузка..."},_u={highlighted_text:"Выделенный текст"},pu={allow_webcam_access:"Пожалуйста, разрешите доступ к веб-камере для записи.",brush_color:"Цвет кисти",brush_radius:"Размер кисти",image:"Изображение",remove_image:"Удалить изображение",select_brush_color:"Выбрать цвет кисти",start_drawing:"Начать рисование",use_brush:"Использовать кисть"},hu={label:"Метка"},mu={enable_cookies:"Если вы посещаете Space HuggingFace в режиме инкогнито, вам необходимо включить сторонние куки.",incorrect_credentials:"Неверные учетные данные",username:"имя пользователя",password:"пароль",login:"Войти"},gu={number:"Число"},fu={plot:"График"},bu={radio:"Переключатель"},wu={slider:"Ползунок"},vu={click_to_upload:"Нажмите для загрузки",drop_audio:"Перетащите аудио сюда",drop_csv:"Перетащите CSV сюда",drop_file:"Перетащите файл сюда",drop_image:"Перетащите изображение сюда",drop_video:"Перетащите видео сюда",drop_gallery:"Перетащите медиа сюда",paste_clipboard:"Вставить из буфера обмена"},Wm={_name:eu,"3D_model":{"3d_model":"3D модель"},annotated_image:tu,audio:ou,blocks:ru,checkbox:nu,code:iu,color_picker:au,common:su,dataframe:lu,dropdown:cu,errors:uu,file:du,highlighted_text:_u,image:pu,label:hu,login:mu,number:gu,plot:fu,radio:bu,slider:wu,upload_text:vu},Xm=Object.freeze(Object.defineProperty({__proto__:null,_name:eu,annotated_image:tu,audio:ou,blocks:ru,checkbox:nu,code:iu,color_picker:au,common:su,dataframe:lu,default:Wm,dropdown:cu,errors:uu,file:du,highlighted_text:_u,image:pu,label:hu,login:mu,number:gu,plot:fu,radio:bu,slider:wu,upload_text:vu},Symbol.toStringTag,{value:"Module"})),yu="Svenska",ku={annotated_image:"Annoterad bild"},xu={allow_recording_access:"Tillåt mikrofonåtkomst för inspelning.",audio:"Ljud",record_from_microphone:"Spela in från mikrofon",stop_recording:"Stoppa inspelning",no_device_support:"Kan inte få åtkomst till medieenheter. Se till att du använder en säker källa (https) eller localhost (eller har gett ett giltigt SSL-certifikat till ssl_verify) och att du har gett webbläsaren åtkomst till din enhet.",stop:"Stopp",resume:"Fortsätt",record:"Spela in",no_microphone:"Ingen mikrofon hittades",pause:"Paus",play:"Spela upp",waiting:"Väntar"},Eu={connection_can_break:"På mobila enheter kan anslutningen brytas om fliken lämnas eller om enheten går i viloläge, och du förlorar din plats i kön.",long_requests_queue:"Det är en lång kö med väntande förfrågningar. Duplicera detta Space för att hoppa över kön.",lost_connection:"Anslutningen bröts eftersom sidan lämnades. Återgår till kön..."},Su={checkbox:"Kryssruta",checkbox_group:"Kryssrutgrupp"},$u={code:"Kod"},Tu={color_picker:"Färgväljare"},Au={built_with:"byggt med",built_with_gradio:"Byggt med Gradio",clear:"Rensa",download:"Ladda ner",edit:"Redigera",empty:"Tom",error:"Fel",hosted_on:"värd på",loading:"Laddar",logo:"Logotyp",or:"eller",remove:"Ta bort",settings:"Inställningar",share:"Dela",submit:"Skicka",undo:"Ångra",no_devices:"Inga enheter hittades",language:"Språk",display_theme:"Visningstema",pwa:"Progressiv webbapplikation"},Pu={incorrect_format:"Fel format, endast CSV- och TSV-filer stöds",new_column:"Lägg till kolumn",new_row:"Ny rad",add_row_above:"Lägg till rad ovanför",add_row_below:"Lägg till rad nedanför",add_column_left:"Lägg till kolumn till vänster",add_column_right:"Lägg till kolumn till höger"},Ou={dropdown:"Rullgardinsmeny"},Lu={build_error:"det finns ett byggfel",config_error:"det finns ett konfigurationsfel",contact_page_author:"Kontakta sidans författare för att informera dem.",no_app_file:"det finns ingen app-fil",runtime_error:"det finns ett körtidsfel",space_not_working:'"Space fungerar inte eftersom" {0}',space_paused:"space är pausat",use_via_api:"Använd via API"},Iu={uploading:"Laddar upp..."},Cu={highlighted_text:"Markerad text"},Du={allow_webcam_access:"Tillåt webbkameråtkomst för inspelning.",brush_color:"Penselfärg",brush_radius:"Penselstorlek",image:"Bild",remove_image:"Ta bort bild",select_brush_color:"Välj penselfärg",start_drawing:"Börja rita",use_brush:"Använd pensel"},Ru={label:"Etikett"},ju={enable_cookies:"Om du besöker ett HuggingFace Space i inkognitoläge måste du aktivera tredjepartscookies.",incorrect_credentials:"Felaktiga inloggningsuppgifter",username:"användarnamn",password:"lösenord",login:"Logga in"},Nu={number:"Nummer"},Hu={plot:"Diagram"},Bu={radio:"Radio"},zu={slider:"Skjutreglage"},Mu={click_to_upload:"Klicka för att ladda upp",drop_audio:"Släpp ljud här",drop_csv:"Släpp CSV här",drop_file:"Släpp fil här",drop_image:"Släpp bild här",drop_video:"Släpp video här",drop_gallery:"Släpp media här",paste_clipboard:"Klistra in från urklipp"},Zm={_name:yu,"3D_model":{"3d_model":"3D-modell"},annotated_image:ku,audio:xu,blocks:Eu,checkbox:Su,code:$u,color_picker:Tu,common:Au,dataframe:Pu,dropdown:Ou,errors:Lu,file:Iu,highlighted_text:Cu,image:Du,label:Ru,login:ju,number:Nu,plot:Hu,radio:Bu,slider:zu,upload_text:Mu},Jm=Object.freeze(Object.defineProperty({__proto__:null,_name:yu,annotated_image:ku,audio:xu,blocks:Eu,checkbox:Su,code:$u,color_picker:Tu,common:Au,dataframe:Pu,default:Zm,dropdown:Ou,errors:Lu,file:Iu,highlighted_text:Cu,image:Du,label:Ru,login:ju,number:Nu,plot:Hu,radio:Bu,slider:zu,upload_text:Mu},Symbol.toStringTag,{value:"Module"})),Vu="தமிழ்",Uu={built_with_gradio:"கிரேடியோ வுடன் உருவாக்கப்பட்டது",clear:"அழிக்கவும்",or:"அல்லது",submit:"சமர்ப்பிக்கவும்"},Gu={click_to_upload:"பதிவேற்ற அழுத்தவும்",drop_audio:"ஆடியோவை பதிவேற்றவும்",drop_csv:"csv ஐ பதிவேற்றவும்",drop_file:"கோப்பை பதிவேற்றவும்",drop_image:"படத்தை பதிவேற்றவும்",drop_video:"காணொளியை பதிவேற்றவும்"},Km={_name:Vu,common:Uu,upload_text:Gu},Ym=Object.freeze(Object.defineProperty({__proto__:null,_name:Vu,common:Uu,default:Km,upload_text:Gu},Symbol.toStringTag,{value:"Module"})),Fu="ภาษาไทย",qu={annotated_image:"รูปภาพที่มีคำอธิบาย"},Wu={allow_recording_access:"กรุณาอนุญาตการเข้าถึงไมโครโฟนเพื่อบันทึกเสียง",audio:"เสียง",drop_to_upload:"ลากและวางไฟล์เสียงที่นี่เพื่ออัปโหลด",record_from_microphone:"บันทึกจากไมโครโฟน",stop_recording:"หยุดบันทึก",no_device_support:"ไม่สามารถเข้าถึงอุปกรณ์สื่อได้ โปรดตรวจสอบว่าคุณใช้งานบนโดเมนที่ปลอดภัย (https) หรือ localhost (หรือคุณได้กำหนดค่า SSL ที่ถูกต้อง) และอนุญาตให้เบราว์เซอร์เข้าถึงอุปกรณ์ของคุณ",stop:"หยุด",resume:"ทำงานต่อ",record:"บันทึก",no_microphone:"ไม่พบไมโครโฟน",pause:"หยุดชั่วคราว",play:"เล่น",waiting:"กำลังรอ"},Xu={connection_can_break:"บนมือถือ การเชื่อมต่ออาจหลุดหากแท็บนี้ไม่ได้อยู่ในโฟกัสหรืออุปกรณ์เข้าสู่โหมดพัก ส่งผลให้เสียตำแหน่งในคิว",long_requests_queue:"มีคิวคำขอรออยู่เป็นจำนวนมาก คัดลอก Space นี้เพื่อข้ามคิว",lost_connection:"การเชื่อมต่อขาดหายเนื่องจากออกจากหน้า กำลังเข้าคิวใหม่...",waiting_for_inputs:"กำลังรอไฟล์อัปโหลดเสร็จ กรุณาลองใหม่"},Zu={checkbox:"กล่องเลือก",checkbox_group:"กลุ่มกล่องเลือก"},Ju={code:"โค้ด"},Ku={color_picker:"ตัวเลือกสี"},Yu={built_with:"สร้างด้วย",built_with_gradio:"สร้างด้วย Gradio",clear:"ล้าง",download:"ดาวน์โหลด",edit:"แก้ไข",empty:"ว่างเปล่า",error:"ข้อผิดพลาด",hosted_on:"โฮสต์บน",loading:"กำลังโหลด",logo:"โลโก้",or:"หรือ",remove:"ลบ",settings:"การตั้งค่า",share:"แชร์",submit:"ส่ง",undo:"เลิกทำ",no_devices:"ไม่พบอุปกรณ์",language:"ภาษา",display_theme:"ธีมการแสดงผล",pwa:"โปรเกรสซีฟเว็บแอพ"},Qu={incorrect_format:"รูปแบบไม่ถูกต้อง รองรับเฉพาะไฟล์ CSV และ TSV",new_column:"เพิ่มคอลัมน์",new_row:"เพิ่มแถวใหม่",add_row_above:"เพิ่มแถวด้านบน",add_row_below:"เพิ่มแถวด้านล่าง",delete_row:"ลบแถว",delete_column:"ลบคอลัมน์",add_column_left:"เพิ่มคอลัมน์ทางซ้าย",add_column_right:"เพิ่มคอลัมน์ทางขวา",sort_column:"เรียงลำดับคอลัมน์",sort_ascending:"เรียงจากน้อยไปมาก",sort_descending:"เรียงจากมากไปน้อย",drop_to_upload:"ลากและวางไฟล์ CSV หรือ TSV ที่นี่เพื่อนำเข้าข้อมูล"},ed={dropdown:"เมนูดรอปดาวน์"},td={build_error:"เกิดข้อผิดพลาดในการสร้าง",config_error:"เกิดข้อผิดพลาดในการกำหนดค่า",contact_page_author:"โปรดติดต่อผู้ดูแลเพจเพื่อแจ้งให้ทราบ",no_app_file:"ไม่พบไฟล์แอป",runtime_error:"เกิดข้อผิดพลาดขณะทำงาน",space_not_working:'"Space ใช้งานไม่ได้เนื่องจาก" {0}',space_paused:"Space ถูกหยุดชั่วคราว",use_via_api:"ใช้งานผ่าน API"},od={uploading:"กำลังอัปโหลด..."},rd={highlighted_text:"ข้อความที่ถูกเน้น"},nd={allow_webcam_access:"กรุณาอนุญาตการเข้าถึงเว็บแคมเพื่อบันทึกวิดีโอ",brush_color:"สีแปรง",brush_radius:"ขนาดแปรง",image:"รูปภาพ",remove_image:"ลบรูปภาพ",select_brush_color:"เลือกสีแปรง",start_drawing:"เริ่มวาด",use_brush:"ใช้แปรง",drop_to_upload:"ลากและวางไฟล์รูปภาพที่นี่เพื่ออัปโหลด"},id={label:"ป้ายกำกับ"},ad={enable_cookies:"หากคุณเข้าใช้งาน HuggingFace Space ในโหมดไม่ระบุตัวตน คุณต้องเปิดใช้งานคุกกี้ของบุคคลที่สาม",incorrect_credentials:"ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง",username:"ชื่อผู้ใช้",password:"รหัสผ่าน",login:"เข้าสู่ระบบ"},sd={number:"ตัวเลข"},ld={plot:"กราฟ"},cd={radio:"ปุ่มตัวเลือก"},ud={slider:"แถบเลื่อน"},dd={click_to_upload:"คลิกเพื่ออัปโหลด",drop_audio:"ลากและวางไฟล์เสียงที่นี่",drop_csv:"ลากและวางไฟล์ CSV ที่นี่",drop_file:"ลากและวางไฟล์ที่นี่",drop_image:"ลากและวางไฟล์รูปภาพที่นี่",drop_video:"ลากและวางไฟล์วิดีโอที่นี่",drop_gallery:"ลากและวางไฟล์สื่อที่นี่",paste_clipboard:"วางจากคลิปบอร์ด"},_d={drop_to_upload:"ลากและวางไฟล์วิดีโอที่นี่เพื่ออัปโหลด"},Qm={_name:Fu,"3D_model":{"3d_model":"โมเดล 3 มิติ",drop_to_upload:"ลากและวางไฟล์โมเดล 3 มิติ (.obj, .glb, .stl, .gltf, .splat, หรือ .ply) ที่นี่เพื่ออัปโหลด"},annotated_image:qu,audio:Wu,blocks:Xu,checkbox:Zu,code:Ju,color_picker:Ku,common:Yu,dataframe:Qu,dropdown:ed,errors:td,file:od,highlighted_text:rd,image:nd,label:id,login:ad,number:sd,plot:ld,radio:cd,slider:ud,upload_text:dd,video:_d},eg=Object.freeze(Object.defineProperty({__proto__:null,_name:Fu,annotated_image:qu,audio:Wu,blocks:Xu,checkbox:Zu,code:Ju,color_picker:Ku,common:Yu,dataframe:Qu,default:Qm,dropdown:ed,errors:td,file:od,highlighted_text:rd,image:nd,label:id,login:ad,number:sd,plot:ld,radio:cd,slider:ud,upload_text:dd,video:_d},Symbol.toStringTag,{value:"Module"})),pd="Türkçe",hd={built_with_gradio:"Gradio ile oluşturulmuştur",clear:"Temizle",or:"veya",submit:"Yükle"},md={click_to_upload:"Yüklemek için Tıkla",drop_audio:"Kaydı Buraya Sürükle",drop_csv:"CSV'yi Buraya Sürükle",drop_file:"Dosyayı Buraya Sürükle",drop_image:"Resmi Buraya Sürükle",drop_video:"Videoyu Buraya Sürükle"},tg={_name:pd,common:hd,upload_text:md},og=Object.freeze(Object.defineProperty({__proto__:null,_name:pd,common:hd,default:tg,upload_text:md},Symbol.toStringTag,{value:"Module"})),gd="Українська",fd={built_with_gradio:"Зроблено на основі gradio",clear:"Очистити",or:"або",submit:"Надіслати",settings:"Налаштування"},bd={click_to_upload:"Натисніть щоб завантажити",drop_audio:"Перетягніть аудіо сюди",drop_csv:"Перетягніть CSV-файл сюди",drop_file:"Перетягніть файл сюди",drop_image:"Перетягніть зображення сюди",drop_video:"Перетягніть відео сюди"},rg={_name:gd,common:fd,upload_text:bd},ng=Object.freeze(Object.defineProperty({__proto__:null,_name:gd,common:fd,default:rg,upload_text:bd},Symbol.toStringTag,{value:"Module"})),wd="اردو",vd={built_with_gradio:"کے ساتھ بنایا گیا Gradio",clear:"ہٹا دیں",or:"یا",submit:"جمع کریں",settings:"ترتیبات"},yd={click_to_upload:"اپ لوڈ کے لیے کلک کریں",drop_audio:"یہاں آڈیو ڈراپ کریں",drop_csv:"یہاں فائل ڈراپ کریں",drop_file:"یہاں فائل ڈراپ کریں",drop_image:"یہاں تصویر ڈراپ کریں",drop_video:"یہاں ویڈیو ڈراپ کریں"},ig={_name:wd,common:vd,upload_text:yd},ag=Object.freeze(Object.defineProperty({__proto__:null,_name:wd,common:vd,default:ig,upload_text:yd},Symbol.toStringTag,{value:"Module"})),kd={built_with_gradio:"gradio bilan qilingan",clear:"Tozalash",submit:"Yubor"},xd={click_to_upload:"Yuklash uchun Bosing",drop_audio:"Audioni Shu Yerga Tashlang",drop_csv:"CSVni Shu Yerga Tashlang",drop_file:"Faylni Shu Yerga Tashlang",drop_image:"Rasmni Shu Yerga Tashlang",drop_video:"Videoni Shu Yerga Tashlang"},sg={common:kd,upload_text:xd},lg=Object.freeze(Object.defineProperty({__proto__:null,common:kd,default:sg,upload_text:xd},Symbol.toStringTag,{value:"Module"})),Ed={annotated_image:"标注图像"},Sd={allow_recording_access:"请允许访问麦克风以进行录音。",audio:"音频",record_from_microphone:"从麦克风录制",stop_recording:"停止录制",no_device_support:"无法访问媒体设备。请检查您是否在安全来源（https）或本地主机上运行（或者您已经通过 ssl_verify 传递了有效的 SSL 证书），并且您已经允许浏览器访问您的设备。",stop:"停止",resume:"继续",record:"录制",no_microphone:"找不到麦克风",pause:"暂停",play:"播放",waiting:"等待"},$d={connection_can_break:"在移动设备上，如果此标签页失去焦点或设备休眠，连接可能会中断，导致您在队列中失去位置。",long_requests_queue:"有一个长时间的待处理请求队列。复制此空间以跳过。",lost_connection:"由于离开页面，连接已丢失。重新加入队列..."},Td={checkbox:"复选框",checkbox_group:"复选框组"},Ad={code:"代码"},Pd={color_picker:"颜色选择器"},Od={built_with:"构建于",built_with_gradio:"使用 Gradio 构建",clear:"清除",download:"下载",edit:"编辑",empty:"空",error:"错误",hosted_on:"托管在",loading:"加载中",logo:"标志",or:"或",remove:"移除",share:"分享",submit:"提交",undo:"撤销"},Ld={incorrect_format:"格式不正确，仅支持 CSV 和 TSV 文件",new_column:"新列",new_row:"新行"},Id={dropdown:"下拉菜单"},Cd={build_error:"存在构建错误",config_error:"存在配置错误",contact_page_author:"请联系页面的作者并告知他们。",no_app_file:"不存在应用文件",runtime_error:"存在运行时错误",space_not_working:'"空间无法工作，原因：" {0}',space_paused:"空间已暂停",use_via_api:"通过 API 使用"},Dd={uploading:"正在上传..."},Rd={highlighted_text:"高亮文本"},jd={allow_webcam_access:"请允许访问网络摄像头以进行录制。",brush_color:"画笔颜色",brush_radius:"画笔半径",image:"图像",remove_image:"移除图像",select_brush_color:"选择画笔颜色",start_drawing:"开始绘画",use_brush:"使用画笔"},Nd={label:"标签"},Hd={enable_cookies:"如果您正在使用隐身模式访问 HuggingFace 空间，您必须启用第三方 cookie。",incorrect_credentials:"凭据不正确",login:"登录"},Bd={number:"数字"},zd={plot:"图表"},Md={radio:"单选框"},Vd={slider:"滑块"},Ud={click_to_upload:"点击上传",drop_audio:"将音频拖放到此处",drop_csv:"将 CSV 文件拖放到此处",drop_file:"将文件拖放到此处",drop_image:"将图像拖放到此处",drop_video:"将视频拖放到此处"},cg={"3D_model":{"3d_model":"3D模型"},annotated_image:Ed,audio:Sd,blocks:$d,checkbox:Td,code:Ad,color_picker:Pd,common:Od,dataframe:Ld,dropdown:Id,errors:Cd,file:Dd,highlighted_text:Rd,image:jd,label:Nd,login:Hd,number:Bd,plot:zd,radio:Md,slider:Vd,upload_text:Ud},ug=Object.freeze(Object.defineProperty({__proto__:null,annotated_image:Ed,audio:Sd,blocks:$d,checkbox:Td,code:Ad,color_picker:Pd,common:Od,dataframe:Ld,default:cg,dropdown:Id,errors:Cd,file:Dd,highlighted_text:Rd,image:jd,label:Nd,login:Hd,number:Bd,plot:zd,radio:Md,slider:Vd,upload_text:Ud},Symbol.toStringTag,{value:"Module"})),Gd={built_with_gradio:"使用Gradio構建",clear:"清除",or:"或",submit:"提交"},Fd={click_to_upload:"點擊上傳",drop_audio:"拖放音訊至此處",drop_csv:"拖放CSV至此處",drop_file:"拖放檔案至此處",drop_image:"拖放圖片至此處",drop_video:"拖放影片至此處"},dg={common:Gd,upload_text:Fd},_g=Object.freeze(Object.defineProperty({__proto__:null,common:Gd,default:dg,upload_text:Fd},Symbol.toStringTag,{value:"Module"})),to=Object.assign({"./lang/ar.json":am,"./lang/ca.json":lm,"./lang/ckb.json":um,"./lang/de.json":_m,"./lang/en.json":hm,"./lang/es.json":gm,"./lang/eu.json":bm,"./lang/fa.json":vm,"./lang/fi.json":km,"./lang/fr.json":Em,"./lang/he.json":$m,"./lang/hi.json":Am,"./lang/ja.json":Om,"./lang/ko.json":Im,"./lang/lt.json":Dm,"./lang/nb.json":jm,"./lang/nl.json":Hm,"./lang/pl.json":zm,"./lang/pt-BR.json":Vm,"./lang/pt.json":Gm,"./lang/ro.json":qm,"./lang/ru.json":Xm,"./lang/sv.json":Jm,"./lang/ta.json":Ym,"./lang/th.json":eg,"./lang/tr.json":og,"./lang/uk.json":ng,"./lang/ur.json":ag,"./lang/uz.json":lg,"./lang/zh-CN.json":ug,"./lang/zh-TW.json":_g});function pg(){let e={};for(const t in to){const o=t.split("/").pop().split(".").shift();e[o]=to[t].default}return e}const ze=pg(),hg=Object.keys(ze),Tg=Object.entries(ze).map(([e,t])=>[t._name||e,e]);for(const e in ze)jo(e,ze[e]);let oo=!1;async function Ag(){if(oo)return;const e=jh(),t=e&&hg.includes(e)?e:"en";await Th({fallbackLocale:"en",initialLocale:t}),oo=!0}function Pg(e){_e.set(e)}const mg="./assets/index-Bq9js3go.css";let mt;mt=[];let gt,qd,gg=new Promise(e=>{qd=e});async function fg(){gt=(await p(()=>import("./Index-vNlY8iMv.js"),__vite__mapDeps([204,87,7,8,9,2,3,4,5,6,10,11,21,22,205]),import.meta.url)).default,qd()}function bg(){const e={SvelteComponent:Je.SvelteComponent};for(const o in Je)o!=="SvelteComponent"&&(o==="SvelteComponentDev"?e[o]=e.SvelteComponent:e[o]=Je[o]);window.__gradio__svelte__internal=e;class t extends HTMLElement{control_page_title;initial_height;is_embed;container;info;autoscroll;eager;theme_mode;host;space;src;app;loading;updating;constructor(){super(),this.host=this.getAttribute("host"),this.space=this.getAttribute("space"),this.src=this.getAttribute("src"),this.control_page_title=this.getAttribute("control_page_title"),this.initial_height=this.getAttribute("initial_height")??"300px",this.is_embed=this.getAttribute("embed")??"true",this.container=this.getAttribute("container")??"true",this.info=this.getAttribute("info")??!0,this.autoscroll=this.getAttribute("autoscroll"),this.eager=this.getAttribute("eager"),this.theme_mode=this.getAttribute("theme_mode"),this.updating=!1,this.loading=!1}async connectedCallback(){await fg(),this.loading=!0,this.app&&this.app.$destroy(),typeof mt!="string"&&mt.forEach(i=>Xt(i,document.head)),await Xt(mg,document.head);const r=new CustomEvent("domchange",{bubbles:!0,cancelable:!1,composed:!0});new MutationObserver(i=>{this.dispatchEvent(r)}).observe(this,{childList:!0}),this.app=new gt({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"5-21-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:je,app_mode:window.__gradio_mode__==="app"}}),this.updating&&this.setAttribute(this.updating.name,this.updating.value),this.loading=!1}static get observedAttributes(){return["src","space","host"]}async attributeChangedCallback(r,n,i){if(await gg,(r==="host"||r==="space"||r==="src")&&i!==n){if(this.updating={name:r,value:i},this.loading)return;this.app&&this.app.$destroy(),this.space=null,this.host=null,this.src=null,r==="host"?this.host=i:r==="space"?this.space=i:r==="src"&&(this.src=i),this.app=new gt({target:this,props:{space:this.space?this.space.trim():this.space,src:this.src?this.src.trim():this.src,info:this.info!=="false",container:this.container!=="false",is_embed:this.is_embed!=="false",initial_height:this.initial_height,eager:this.eager==="true",version:"5-21-0",theme_mode:this.theme_mode,autoscroll:this.autoscroll==="true",control_page_title:this.control_page_title==="true",Client:je,app_mode:window.__gradio_mode__==="app"}}),this.updating=!1}}}customElements.get("gradio-app")||customElements.define("gradio-app",t)}bg();export{Eg as $,tm as A,p as _,pg as a,Pg as b,$g as c,xg as d,yg as e,kg as f,sp as g,wg as h,vg as i,rp as j,ip as k,Tg as l,Xt as m,ge as n,_e as o,Sg as p,Zt as q,Ag as s,se as w};
//# sourceMappingURL=index-V1UhiEW8.js.map
