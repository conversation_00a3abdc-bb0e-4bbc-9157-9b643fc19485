"""
智能体协调器模块
用于协调多个智能体的工作，确保它们能够有效协作完成小说创作任务
特别优化了对4K上下文模型的支持
"""

import os
import json
import time
from typing import Dict, List, Any, Optional, Tuple, Callable
import traceback

# 导入智能体相关模块
try:
    from src.core.agent import Agent, AGENT_REGISTRY
    from src.agents.writing_agents import (
        WritingAgent, PolishingAgent, ExpansionAgent,
        ReviewAgent, OptimizationAgent, IllustrationAgent,
        register_writing_agents
    )
    from src.agents.chapter_transition import ChapterTransition
    from src.agents.chapter_generator import ChapterGenerator
    from src.utils.logger import (
        logger, process_logger,
        log_process_start, log_process_end, log_process_step, log_error,
        log_model_usage, log_chapter_generation, setup_chapter_logger
    )
    from src.core.model_config import AI_MODEL_CONFIG, MCP_CONFIG
    from src.utils.mcp_client import get_mcp_client, call_llm, call_agent, generate_illustration as mcp_generate_illustration
except ImportError:
    # 如果导入失败，创建简单的替代品
    print("警告: 无法导入智能体模块，使用简单替代")

    class Agent:
        def __init__(self, name, model=None):
            self.name = name
            self.model = model

        def call_llm(self, prompt):
            return f"LLM调用示例响应: {prompt[:50]}..."

    # 创建简单的智能体注册表
    class AgentRegistry:
        def __init__(self):
            self.agents = {}

        def register(self, agent):
            self.agents[agent.name] = agent
            print(f"步骤: 模型配置 - {agent.name} 使用模型: {agent.model}")
            return agent

        def get_agent(self, name):
            return self.agents.get(name)

    AGENT_REGISTRY = AgentRegistry()
    WritingAgent = PolishingAgent = ExpansionAgent = ReviewAgent = OptimizationAgent = IllustrationAgent = Agent
    # 创建简单的章节过渡和生成器类
    class ChapterTransition:
        def __init__(self, agent=None):
            self.agent = agent

        def generate_transition(self, previous_chapter_ending="", next_chapter_outline="", chapter_num=1, chapter_title=""):
            return f"第{chapter_num}章的过渡内容..."

    class ChapterGenerator:
        def __init__(self, llm_caller=None):
            self.llm_caller = llm_caller

        def generate_chapter_content(self, context):
            return f"第{context.get('chapter_num', 1)}章的内容..."

    def register_writing_agents():
        print("注册智能体（模拟）")
        # 注册所有智能体
        AGENT_REGISTRY.register(Agent("WritingAgent", "glm-4-flash"))
        AGENT_REGISTRY.register(Agent("PolishingAgent", "glm-4-flash"))
        AGENT_REGISTRY.register(Agent("ExpansionAgent", "internlm/internlm2_5-7b-chat"))
        AGENT_REGISTRY.register(Agent("ReviewAgent", "glm-4-flash"))
        AGENT_REGISTRY.register(Agent("OptimizationAgent", "internlm/internlm2_5-7b-chat"))
        AGENT_REGISTRY.register(Agent("IllustrationAgent", "cogview-3-flash"))

    def log_process_start(msg):
        print(f"开始: {msg}")
        return time.time()

    def log_process_end(msg, start_time):
        print(f"结束: {msg}, 耗时: {time.time() - start_time:.2f}秒")

    def log_process_step(step, details):
        print(f"步骤: {step} - {details}")

    def log_error(msg, e=None):
        print(f"错误: {msg}" + (f" - {e}" if e else ""))

    def log_model_usage(model_name, tokens_in, tokens_out, duration_ms, success=True):
        print(f"模型使用: {model_name}, 输入tokens: {tokens_in}, 输出tokens: {tokens_out}, 耗时: {duration_ms}ms, 状态: {'SUCCESS' if success else 'FAILED'}")

    def log_chapter_generation(chapter_num, chapter_title, tokens, duration_ms, success=True):
        print(f"章节生成: 第{chapter_num}章 {chapter_title}, tokens: {tokens}, 耗时: {duration_ms}ms, 状态: {'SUCCESS' if success else 'FAILED'}")

    def setup_chapter_logger(novel_title, chapter_num):
        print(f"设置章节日志记录器: {novel_title} 第{chapter_num}章")
        return type('', (), {'info': print, 'error': print, 'warning': print, 'debug': print})()

    logger = process_logger = type('', (), {'info': print, 'error': print})()

    AI_MODEL_CONFIG = {
        "agent_models": {
            "WritingAgent": "glm-4-flash",
            "PolishingAgent": "glm-4-flash",
            "ExpansionAgent": "internlm/internlm2_5-7b-chat",
            "ReviewAgent": "glm-4-flash",
            "OptimizationAgent": "internlm/internlm2_5-7b-chat",
            "IllustrationAgent": "cogview-3-flash"
        }
    }

class AgentOrchestrator:
    """智能体协调器，用于协调多个智能体的工作"""

    def __init__(self):
        """初始化智能体协调器"""
        self.agents = {}
        self.chapter_transition = None
        self.chapter_generator = None
        self.initialized = False
        self.mcp_client = None

        # 创建日志目录
        os.makedirs("logs/mcp", exist_ok=True)

    def initialize(self):
        """初始化所有智能体"""
        if self.initialized:
            return

        start_time = log_process_start("初始化智能体协调器")

        # 初始化MCP客户端
        try:
            from src.utils.mcp_client import get_mcp_client
            self.mcp_client = get_mcp_client()
            log_process_step("初始化MCP客户端", "成功")
        except ImportError as e:
            print(f"错误: 初始化MCP客户端失败 - {e}")
            self.mcp_client = None

        # 注册所有写作智能体
        register_writing_agents()
        log_process_step("智能体初始化", "已注册所有写作智能体")

        # 获取所有已注册的智能体
        self.agents = {
            "writing": AGENT_REGISTRY.get_agent("WritingAgent"),
            "polishing": AGENT_REGISTRY.get_agent("PolishingAgent"),
            "expansion": AGENT_REGISTRY.get_agent("ExpansionAgent"),
            "review": AGENT_REGISTRY.get_agent("ReviewAgent"),
            "optimization": AGENT_REGISTRY.get_agent("OptimizationAgent"),
            "illustration": AGENT_REGISTRY.get_agent("IllustrationAgent")
        }

        # 检查是否所有智能体都已成功注册
        missing_agents = [name for name, agent in self.agents.items() if agent is None]
        if missing_agents:
            print(f"错误: 部分智能体注册失败: {', '.join(missing_agents)}")
            # 尝试重新注册缺失的智能体
            for name in missing_agents:
                agent_class = globals().get(f"{name.capitalize()}Agent")
                if agent_class:
                    self.agents[name] = AGENT_REGISTRY.register(agent_class(f"{name.capitalize()}Agent"))
                    log_process_step("智能体恢复", f"已重新注册 {name} 智能体")

        # 初始化章节过渡生成器
        self.chapter_transition = ChapterTransition(self.agents["writing"])
        log_process_step("智能体初始化", "已初始化章节过渡生成器")

        # 初始化章节生成器
        self.chapter_generator = ChapterGenerator(
            llm_caller=self.agents["writing"].call_llm if self.agents["writing"] else None
        )
        log_process_step("智能体初始化", "已初始化章节生成器")

        # 更新智能体使用的模型
        self._update_agent_models()

        self.initialized = True
        log_process_end("初始化智能体协调器", start_time)

    def _update_agent_models(self):
        """更新智能体使用的模型"""
        agent_models = AI_MODEL_CONFIG.get("agent_models", {})

        # 更新每个智能体的模型
        for name, agent in self.agents.items():
            if agent is not None:
                agent_type = f"{name.capitalize()}Agent"
                if agent_type in agent_models:
                    agent.model = agent_models[agent_type]
                    log_process_step("模型配置", f"{agent_type} 使用模型: {agent.model}")

    def generate_chapter(self,
                        chapter_num: int,
                        novel_info: Dict[str, str],
                        chapter_outline: str,
                        prev_chapter_content: Optional[str] = None,
                        next_chapter_outline: Optional[str] = None,
                        progress_callback: Optional[Callable[[str], None]] = None) -> str:
        """
        生成一个完整的小说章节，包括内容生成、润色、扩展和优化

        参数:
            chapter_num: 章节号
            novel_info: 小说信息，包含标题、类型、风格、场景和角色
            chapter_outline: 章节大纲
            prev_chapter_content: 前一章内容，用于生成过渡段
            next_chapter_outline: 下一章大纲，用于设置悬念和过渡
            progress_callback: 进度回调函数

        返回:
            生成的章节内容
        """
        # 确保智能体已初始化
        if not self.initialized:
            self.initialize()

        # 开始计时
        start_time = log_process_start(f"生成第{chapter_num}章")

        # 进度回调
        def update_progress(message):
            if progress_callback:
                progress_callback(message)
            log_process_step(f"第{chapter_num}章进度", message)

        update_progress(f"开始生成第{chapter_num}章...")

        try:
            # 1. 生成章节过渡段（如果有前一章内容）
            transition = ""
            if prev_chapter_content and chapter_num > 1 and self.chapter_transition:
                update_progress("生成章节过渡段...")
                transition_start = log_process_start(f"生成第{chapter_num}章过渡段")

                try:
                    # 提取前一章的最后一段
                    prev_paragraphs = prev_chapter_content.split('\n\n')
                    last_paragraph = prev_paragraphs[-2] if len(prev_paragraphs) > 2 else prev_paragraphs[-1]

                    # 生成过渡段
                    transition = self.chapter_transition.generate_transition(
                        previous_chapter_ending=last_paragraph,
                        next_chapter_outline=chapter_outline,
                        chapter_num=chapter_num,
                        chapter_title=f"第{chapter_num}章"
                    )

                    log_process_end(f"生成第{chapter_num}章过渡段", transition_start)
                    update_progress("章节过渡段生成完成")
                except Exception as e:
                    log_error(f"生成第{chapter_num}章过渡段失败", e)
                    update_progress(f"生成过渡段失败: {e}")

            # 2. 生成初始章节内容
            update_progress("生成初始章节内容...")
            content_start = log_process_start(f"生成第{chapter_num}章初始内容")

            # 准备上下文
            context = {
                "title": novel_info.get("title", ""),
                "genre": novel_info.get("genre", ""),
                "style": novel_info.get("style", ""),
                "scene": novel_info.get("scene", ""),
                "characters": novel_info.get("characters", ""),
                "chapter_outline": chapter_outline,
                "previous_chapter_content": prev_chapter_content,
                "transition": transition
            }

            # 使用MCP协议生成内容
            if self.mcp_client:
                # 使用MCP协议调用写作智能体
                update_progress("使用MCP协议生成内容...")

                # 获取写作模型
                writing_model = AI_MODEL_CONFIG.get("agent_models", {}).get("WritingAgent", "glm-4-flash")

                # 构建提示词
                prompt = f"""
你是一位专业的小说写作家，擅长创作高质量的中文小说。请为以下小说创作第{chapter_num}章的内容：

标题：{novel_info.get('title', '')}
类型：{novel_info.get('genre', '')}
风格：{novel_info.get('style', '')}
场景：{novel_info.get('scene', '')}
角色：{novel_info.get('characters', '')}

章节大纲：
{chapter_outline}

{'前一章结尾：' + prev_chapter_content[-500:] if prev_chapter_content else ''}

{'过渡段：' + transition if transition else ''}

要求：
1. 创作一个完整的章节，字数至少5000字
2. 包含生动的场景描写、自然的对话和丰富的情感表达
3. 确保章节开头与前一章结尾无缝衔接
4. 章节结尾设置悬念引导读者继续阅读
5. 内容要符合小说的风格和基调
6. 包含丰富的感官描写，增强沉浸感

请直接给出章节内容，不需要额外的解释。
"""

                # 使用MCP协议调用模型
                from src.utils.mcp_client import call_llm
                initial_content = call_llm(writing_model, prompt)

            # 如果没有MCP客户端，使用原有方法
            elif self.chapter_generator:
                initial_content = self.chapter_generator.generate_chapter_content(context)
            else:
                # 备用方法：使用写作智能体直接生成
                writing_agent = self.agents.get("writing")
                if writing_agent:
                    prompt = f"""
请为以下小说创作第{chapter_num}章的内容：

标题：{novel_info.get('title', '')}
类型：{novel_info.get('genre', '')}
风格：{novel_info.get('style', '')}
场景：{novel_info.get('scene', '')}
角色：{novel_info.get('characters', '')}

章节大纲：
{chapter_outline}

{'前一章结尾：' + prev_chapter_content[-500:] if prev_chapter_content else ''}

{'过渡段：' + transition if transition else ''}

请创作一个完整的章节，字数至少5000字，包含生动的场景描写、自然的对话和丰富的情感表达。
确保章节开头与前一章结尾无缝衔接，章节结尾设置悬念引导读者继续阅读。
请直接给出章节内容，不需要额外的解释。
"""
                    initial_content = writing_agent.call_llm(prompt)
                else:
                    raise ValueError("无法找到写作智能体，无法生成章节内容")

            log_process_end(f"生成第{chapter_num}章初始内容", content_start)
            update_progress("初始章节内容生成完成")

            # 3. 润色章节内容
            update_progress("润色章节内容...")
            polish_start = log_process_start(f"润色第{chapter_num}章内容")

            polishing_agent = self.agents.get("polishing")
            if polishing_agent:
                polish_prompt = f"""
你是一位专业的中文小说编辑，请对以下小说章节进行润色和完善：

标题：{novel_info.get('title', '')}
类型：{novel_info.get('genre', '')}
风格：{novel_info.get('style', '')}

原始内容：
{initial_content}

润色要求：
1. 提升语言的流畅性和文学性，修正不自然的表达
2. 确保情节的连贯性和合理性
3. 保持角色形象和性格的一致性
4. 增强场景描写的细节和氛围感
5. 优化对话的自然度和个性化
6. 确保章节结尾有足够的悬念
7. 避免出现任何提示词痕迹

请直接给出润色后的完整内容，不需要解释你做了哪些修改。
"""
                polished_content = polishing_agent.call_llm(polish_prompt)
            else:
                polished_content = initial_content
                update_progress("跳过润色步骤（未找到润色智能体）")

            log_process_end(f"润色第{chapter_num}章内容", polish_start)
            update_progress("章节润色完成")

            # 4. 扩展章节内容（确保达到字数要求）
            update_progress("扩展章节内容...")
            expansion_start = log_process_start(f"扩展第{chapter_num}章内容")

            expansion_agent = self.agents.get("expansion")
            # 如果内容已经超过3000字，直接跳过扩写步骤
            if len(polished_content) >= 3000:
                expanded_content = polished_content
                update_progress(f"跳过扩展步骤（内容已超过3000字，当前{len(polished_content)}字）")
            # 如果内容不足3000字且有扩写智能体
            elif expansion_agent and len(polished_content) < 3000:
                expansion_prompt = f"""
你是一位专业的中文小说扩写专家。请对以下小说章节进行合理扩展：

原文：
{polished_content}

扩展要求：
1. 保持原有情节不变，只进行内容丰富和细节扩展
2. 重点扩展场景描写、人物对话和心理活动
3. 增加感官描写（视觉、听觉、嗅觉等）增强沉浸感
4. 适当增加环境氛围的描写，使场景更加立体
5. 扩展后的总字数控制在8000字左右
6. 保持写作风格的一致性

请直接给出扩展后的完整内容，不需要解释你的扩展思路。
"""
                expanded_content = expansion_agent.call_llm(expansion_prompt)
            else:
                expanded_content = polished_content
                if not expansion_agent:
                    update_progress("跳过扩展步骤（未找到扩展智能体）")
                else:
                    update_progress("跳过扩展步骤（内容已达到字数要求）")

            log_process_end(f"扩展第{chapter_num}章内容", expansion_start)
            update_progress("章节扩展完成")

            # 5. 优化章节内容
            update_progress("优化章节内容...")
            optimization_start = log_process_start(f"优化第{chapter_num}章内容")

            optimization_agent = self.agents.get("optimization")
            if optimization_agent:
                optimization_prompt = f"""
你是一位专业的中文小说优化专家。请对以下小说章节进行全面优化：

原文：
{expanded_content}

优化要求：
1. 确保情节的连贯性和合理性，修正任何逻辑漏洞
2. 优化人物对话，使其更加自然、生动，符合角色性格
3. 增强场景描写的细节和氛围感，提升沉浸感
4. 优化章节结尾，确保有足够的悬念引导读者继续阅读
5. 提升语言的文学性和表现力，避免平淡或重复的表达
6. 确保没有任何提示词痕迹或AI自我指代
7. 如果有下一章的信息，确保本章结尾能够自然过渡到下一章

请直接给出优化后的完整内容，不需要解释你的优化思路。
"""
                optimized_content = optimization_agent.call_llm(optimization_prompt)
            else:
                optimized_content = expanded_content
                update_progress("跳过优化步骤（未找到优化智能体）")

            log_process_end(f"优化第{chapter_num}章内容", optimization_start)
            update_progress("章节优化完成")

            # 6. 审核章节内容
            update_progress("审核章节内容...")
            review_start = log_process_start(f"审核第{chapter_num}章内容")

            review_agent = self.agents.get("review")
            if review_agent:
                review_prompt = f"""
你是一位专业的中文小说审核编辑。请对以下小说章节进行全面审核：

章节内容：
{optimized_content}

审核要点：
1. 情节连贯性：检查情节是否连贯，是否有逻辑漏洞或矛盾
2. 角色一致性：检查角色的言行是否符合其设定和性格
3. 语言质量：检查语言是否流畅、生动，是否有不自然的表达
4. 结构平衡：检查章节结构是否合理，是否有明显的节奏问题
5. 悬念设置：检查章节结尾是否有足够的悬念引导读者继续阅读
6. AI痕迹：检查是否有明显的AI生成痕迹或提示词残留

请提供一份简洁的审核报告，指出章节的优点和需要改进的地方。
"""
                review_result = review_agent.call_llm(review_prompt)
                update_progress(f"审核结果: {review_result[:200]}...")

                # 如果审核发现问题，进行修复
                if "需要改进" in review_result or "问题" in review_result:
                    update_progress("根据审核结果修复章节内容...")
                    fix_prompt = f"""
你是一位专业的中文小说编辑。请根据以下审核报告修复小说章节：

章节内容：
{optimized_content}

审核报告：
{review_result}

请直接给出修复后的完整章节内容，不需要解释你做了哪些修改。
"""
                    final_content = review_agent.call_llm(fix_prompt)
                else:
                    final_content = optimized_content
            else:
                final_content = optimized_content
                update_progress("跳过审核步骤（未找到审核智能体）")

            log_process_end(f"审核第{chapter_num}章内容", review_start)
            update_progress("章节审核完成")

            # 7. 确保章节标题正确
            chapter_title = f"第{chapter_num}章"
            title_match = None

            # 尝试从大纲中提取标题
            import re
            patterns = [
                r'1\. \*\*章节标题\*\*：(.+)',
                r'####\s*\*\*第\d+章[\uff1a:](.+?)\*\*',
                r'第\d+章[\uff1a:]\s*(.+?)\n',
                r'第\d+章[\uff1a:]\s*(.+)'
            ]

            for pattern in patterns:
                title_match = re.search(pattern, chapter_outline)
                if title_match:
                    break

            if title_match:
                chapter_title = f"第{chapter_num}章：{title_match.group(1).strip()}"

            # 确保章节内容以正确的标题开始
            if not final_content.startswith(chapter_title):
                final_content = f"{chapter_title}\n\n{final_content}"

            log_process_end(f"生成第{chapter_num}章", start_time)
            update_progress(f"第{chapter_num}章生成完成，共{len(final_content)}字")

            return final_content

        except Exception as e:
            print(f"错误: 生成第{chapter_num}章失败 - {e}")
            log_process_end(f"生成第{chapter_num}章", start_time)
            update_progress(f"生成章节失败: {e}")
            return f"第{chapter_num}章\n\n生成章节时出错: {e}\n\n{traceback.format_exc()}"

    def generate_illustration(self,
                             title: str,
                             scene_description: str,
                             style: str = "写实风格",
                             output_path: Optional[str] = None) -> Tuple[bool, str]:
        """
        生成章节插图

        参数:
            title: 小说标题
            scene_description: 场景描述
            style: 插图风格
            output_path: 输出路径

        返回:
            (成功标志, 结果信息)
        """
        # 确保智能体已初始化
        if not self.initialized:
            self.initialize()

        # 开始计时
        start_time = log_process_start(f"生成插图: {title}")

        try:
            # 获取插图智能体
            illustration_agent = self.agents.get("illustration")
            if not illustration_agent:
                log_error("未找到插图智能体")
                log_process_end(f"生成插图: {title}", start_time)
                return False, "未找到插图智能体"

            # 使用MCP协议生成插图
            if self.mcp_client:
                # 使用MCP协议生成插图
                log_process_step("插图生成", "使用MCP协议生成插图")

                # 使用MCP协议生成插图
                from src.utils.mcp_client import generate_illustration as mcp_generate_illustration
                success, result = mcp_generate_illustration(
                    title=title,
                    scene_description=scene_description,
                    style=style,
                    output_path=output_path
                )

            else:
                # 生成插图提示词
                prompt = f"""
你是一位专业的插画艺术指导。请为以下小说场景创作一个详细的插图提示词：

小说标题：{title}
场景描述：{scene_description}
风格：{style}

请创作一个详细的插图提示词，描述这个场景中最具视觉冲击力和故事代表性的瞬间。提示词应包含：
1. 场景的主要元素和构图
2. 光线、色彩和氛围
3. 环境细节和背景元素
4. 画面风格和艺术效果

提示词长度控制在100-150字，必须是中文，不要使用英文。请直接给出提示词，不需要额外的解释。
"""
                # 生成插图提示词
                illustration_prompt = illustration_agent.call_llm(prompt)
                log_process_step("插图生成", f"生成插图提示词: {illustration_prompt[:100]}...")

                # 调用插图生成函数
                from src.illustration import generate_novel_illustration
                success, result = generate_novel_illustration(
                    title=title,
                    scene=illustration_prompt,
                    style=style,
                    output_path=output_path
                )

            log_process_end(f"生成插图: {title}", start_time)
            return success, result

        except Exception as e:
            print(f"错误: 生成插图失败 - {e}")
            log_process_end(f"生成插图: {title}", start_time)
            return False, f"生成插图时出错: {e}"

# 创建全局智能体协调器实例
orchestrator = AgentOrchestrator()

def get_orchestrator() -> AgentOrchestrator:
    """获取智能体协调器实例"""
    return orchestrator
