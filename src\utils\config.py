"""
配置模块
用于加载和管理配置
"""
Configuration file for the multi-agent novel creation system.
Contains model configurations and MCP protocol settings.
"""

# OpenAI Model Configuration
OPENAI_CONFIG = {
    # Model definitions with their capabilities and parameters
    "models": {
        "gpt-4": {
            "description": "Most powerful model for complex reasoning and creative tasks",
            "max_tokens": 8192,
            "temperature": 0.7,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "timeout": 120,  # seconds
            "retry_count": 3,
        },
        "gpt-4-turbo": {
            "description": "Faster version of GPT-4 with similar capabilities",
            "max_tokens": 4096,
            "temperature": 0.7,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "timeout": 60,  # seconds
            "retry_count": 3,
        },
        "gpt-3.5-turbo": {
            "description": "Fast and cost-effective model for standard tasks",
            "max_tokens": 4096,
            "temperature": 0.8,
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "timeout": 30,  # seconds
            "retry_count": 3,
        },
        "claude-3-opus": {
            "description": "Anthropic's most capable model for complex reasoning",
            "max_tokens": 8192,
            "temperature": 0.7,
            "top_p": 1.0,
            "timeout": 120,  # seconds
            "retry_count": 3,
        },
        "claude-3-sonnet": {
            "description": "Balanced model for creative writing tasks",
            "max_tokens": 4096,
            "temperature": 0.8,
            "top_p": 1.0,
            "timeout": 60,  # seconds
            "retry_count": 3,
        }
    },

    # Agent-specific model assignments
    "agent_models": {
        "CreativeDirector": "gpt-4",
        "PlotArchitect": "gpt-4",
        "CharacterDesigner": "claude-3-sonnet",
        "ChapterGenerator": "claude-3-opus",
        "EditorAgent": "gpt-4",
        "PublisherAgent": "gpt-3.5-turbo",
        "QualityMonitor": "gpt-4",
        "DashboardAgent": "gpt-3.5-turbo"
    }
}

# MCP Protocol Configuration
MCP_CONFIG = {
    "enabled": True,
    "timeout": 30,  # seconds
    "retry_count": 3,
    "retry_delay": 2,  # seconds
    "log_requests": True,
    "log_responses": True,
    "log_directory": "logs/mcp",

    # Blender-specific MCP settings
    "blender": {
        "enabled": True,
        "host": "localhost",
        "port": 5000,
        "api_version": "v1",
        "timeout": 60,  # seconds
        "max_retries": 3
    }
}

# System Configuration
SYSTEM_CONFIG = {
    "output_directory": "output",
    "log_directory": "logs",
    "temp_directory": "temp",
    "max_concurrent_tasks": 10,
    "batch_size": 5,  # Number of chapters to process in a batch
    "quality_threshold": 0.75,  # Minimum quality score (0-1)
    "debug_mode": True,

    # Performance settings
    "use_gpu": True,
    "distributed": False,  # Enable for multi-node setup
    "worker_count": 4,  # Number of worker processes
}

# Default prompt templates
PROMPT_TEMPLATES = {
    "creative_director": """
    You are a creative director tasked with creating a novel outline.
    Theme: {theme}
    Style: {style}

    Create a comprehensive outline including:
    1. Main theme and subthemes
    2. Genre and stylistic elements
    3. Number of chapters (between 10-30)
    4. Key plot points and events
    5. Setting description

    Respond in JSON format.
    """,

    "plot_architect": """
    You are a plot architect expanding a novel outline into a detailed plot structure.

    Outline: {outline}

    Create a detailed plot structure including:
    1. Chapter-by-chapter breakdown
    2. Main story arcs
    3. Subplots and their integration
    4. Key scenes and turning points
    5. Pacing recommendations

    Respond in JSON format.
    """,

    "character_designer": """
    You are a character designer creating characters for a novel.

    Theme: {theme}
    Genre: {genre}

    Create detailed character profiles including:
    1. Protagonists (1-3)
    2. Antagonists (1-2)
    3. Supporting characters (3-5)

    For each character include:
    - Name and basic demographics
    - Physical description
    - Personality traits and quirks
    - Background and history
    - Goals, motivations, and conflicts
    - Character arc
    - Relationships with other characters

    Respond in JSON format.
    """,

    "chapter_generator": """
    You are writing Chapter {chapter_number}: {chapter_title} for a novel.

    Genre: {genre}
    Style: {style}

    Chapter events: {events}
    Characters involved: {characters}
    Setting: {setting}

    Write an engaging, well-paced chapter that advances the plot and develops the characters.
    Focus on showing rather than telling, with vivid descriptions and meaningful dialogue.

    Write approximately 2000-3000 words.
    """,

    "editor": """
    You are an editor reviewing a chapter of a novel.

    Chapter: {chapter_text}

    Edit this chapter for:
    1. Grammar and spelling
    2. Style consistency
    3. Pacing and flow
    4. Character consistency
    5. Plot coherence
    6. Dialogue authenticity

    Provide the improved version of the chapter.
    """
}