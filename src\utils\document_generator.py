"""
Document Generator Module - Handles the generation of Word and Markdown documents
"""

import os
import re
from typing import Dict, List, Any, Optional
from docx import Document
from docx.shared import Inches

class Chapter:
    """Class representing a novel chapter"""
    
    def __init__(self, number: int, title: str):
        self.number = number
        self.title = title
        self.content = ""
        self.illustrations = []
        self.status = "pending"  # pending, in_progress, completed, error
        self.metadata = {}
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            "number": self.number,
            "title": self.title,
            "content": self.content,
            "illustrations": self.illustrations[:] if self.illustrations else [],
            "status": self.status,
            "metadata": dict(self.metadata)
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Chapter':
        """Create from dictionary"""
        chapter = cls(
            number=data.get("number", 0),
            title=data.get("title", "未命名章节")
        )
        chapter.content = data.get("content", "")
        chapter.illustrations = data.get("illustrations", [])[:]
        chapter.status = data.get("status", "pending")
        chapter.metadata = dict(data.get("metadata", {}))
        return chapter

class DocumentGenerator:
    """
    Handles the generation of Word and Markdown documents for novels
    """
    
    def __init__(self, output_dir: str, title: str):
        """
        Initialize the document generator
        
        Args:
            output_dir: Directory to save output files
            title: Novel title
        """
        self.output_dir = output_dir
        self.title = title
        os.makedirs(output_dir, exist_ok=True)
        
    def generate_word_document(self, chapters: List[Chapter], include_illustrations: bool = True) -> str:
        """
        Generate a Word document from chapters
        
        Args:
            chapters: List of Chapter objects
            include_illustrations: Whether to include illustrations
            
        Returns:
            Path to the generated document
        """
        print("\n=== Generating Word document ===")
        
        doc_path = os.path.join(self.output_dir, f"{self.title}.docx")
        
        try:
            # Check if document already exists
            if os.path.exists(doc_path):
                # Try to load existing document
                try:
                    doc = Document(doc_path)
                    print(f"Loaded existing document: {doc_path}")
                    
                    # Count existing chapters
                    existing_chapters = 0
                    for para in doc.paragraphs:
                        if para.style.name.startswith('Heading 1') and '第' in para.text and '章' in para.text:
                            existing_chapters += 1
                    
                    print(f"Document contains {existing_chapters} existing chapters")
                except Exception as e:
                    print(f"Error loading existing document: {e}")
                    doc = Document()
                    doc.add_heading(self.title, 0)
                    existing_chapters = 0
            else:
                # Create new document
                doc = Document()
                doc.add_heading(self.title, 0)
                existing_chapters = 0
                
            # Add new chapters
            chapters_to_add = [ch for ch in chapters if ch.number > existing_chapters]
            
            if not chapters_to_add:
                print("No new chapters to add")
                return doc_path
                
            print(f"Adding {len(chapters_to_add)} new chapters")
            
            # Add each chapter
            for chapter in chapters_to_add:
                try:
                    # Add chapter title
                    doc.add_heading(f"第{chapter.number}章 {chapter.title}", 1)
                    
                    # Add chapter content
                    if chapter.content:
                        # Process paragraphs
                        paragraphs = chapter.content.split("\n\n")
                        for para in paragraphs:
                            if para.strip():
                                doc.add_paragraph(para.strip())
                    else:
                        doc.add_paragraph("本章节内容生成失败或尚未完成。")
                    
                    # Add illustrations at the end of the chapter
                    if include_illustrations and chapter.illustrations:
                        # Add a separator
                        doc.add_paragraph("")
                        
                        # Add illustration heading
                        doc.add_paragraph(f"第{chapter.number}章插图", style="Heading 3")
                        
                        # Add each illustration
                        for img_path in chapter.illustrations:
                            if img_path and os.path.exists(img_path):
                                try:
                                    # Add the image
                                    doc.add_picture(img_path, width=Inches(6))
                                    
                                    # Add caption
                                    doc.add_paragraph(f"图：第{chapter.number}章场景插图", style="Caption")
                                except Exception as img_error:
                                    print(f"Error adding illustration: {img_error}")
                    
                    # Add page break
                    doc.add_page_break()
                except Exception as chapter_error:
                    print(f"Error processing Chapter {chapter.number}: {chapter_error}")
                    continue
            
            # Save document
            doc.save(doc_path)
            print(f"Word document saved to: {doc_path}")
            
            return doc_path
        except Exception as e:
            print(f"Error generating Word document: {e}")
            return ""
    
    def generate_markdown_document(self, chapters: List[Chapter], include_illustrations: bool = True) -> str:
        """
        Generate a Markdown document from chapters
        
        Args:
            chapters: List of Chapter objects
            include_illustrations: Whether to include illustrations
            
        Returns:
            Path to the generated document
        """
        md_path = os.path.join(self.output_dir, f"{self.title}.md")
        
        try:
            # Start with title
            md_content = f"# {self.title}\n\n"
            
            # Add each chapter
            for chapter in chapters:
                # Chapter title
                md_content += f"## 第{chapter.number}章 {chapter.title}\n\n"
                
                # Chapter content
                if chapter.content:
                    paragraphs = chapter.content.split("\n\n")
                    for para in paragraphs:
                        if para.strip():
                            md_content += f"{para.strip()}\n\n"
                else:
                    md_content += "本章节内容生成失败或尚未完成。\n\n"
                
                # Add illustrations
                if include_illustrations and chapter.illustrations:
                    for i, img_path in enumerate(chapter.illustrations):
                        if img_path and os.path.exists(img_path):
                            # Create relative path
                            rel_path = os.path.relpath(img_path, self.output_dir)
                            md_content += f"![第{chapter.number}章插图{i+1}]({rel_path})\n\n"
            
            # Write to file
            with open(md_path, "w", encoding="utf-8") as f:
                f.write(md_content)
                
            print(f"Markdown document saved to: {md_path}")
            return md_path
        except Exception as e:
            print(f"Error generating Markdown document: {e}")
            return ""
