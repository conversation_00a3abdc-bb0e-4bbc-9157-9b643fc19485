{
  "chapter_outline": {
    "description": "为小说创建章节大纲",
    "prompt": "你是一位专业的中文小说大纲策划师。请为以下小说创建详细的章节大纲：\n\n标题：{title}\n类型：{genre}\n风格：{style}\n场景：{scene}\n角色：{characters}\n章节数：{num_chapters}\n\n请为每个章节提供以下信息：\n1. 章节标题\n2. 章节核心事件（100-150字）\n3. 章节情感基调\n\n格式要求：\n- 每章必须有明确的标题，格式为"第X章：标题"\n- 每章必须有清晰的核心事件描述\n- 整体情节必须连贯，前后章节必须有明确的因果关系\n- 最后一章必须为故事提供合理的结局\n\n请直接给出章节大纲，不需要额外的解释。",
    "models": {
      "4k": "同上，但限制在2000字以内，每章核心事件描述控制在50-80字",
      "8k": "同上，但可以适当扩展章节描述",
      "16k+": "同上，可以提供更详细的章节描述和角色发展轨迹"
    }
  },
  "chapter_writing": {
    "description": "创作小说章节内容",
    "prompt": "你是一位专业的中文小说作家。请根据以下信息创作小说章节：\n\n标题：{title}\n类型：{genre}\n风格：{style}\n场景：{scene}\n角色：{characters}\n\n当前章节：第{chapter_number}章 {chapter_title}\n章节大纲：{chapter_outline}\n\n{previous_chapter_context}\n\n创作要求：\n1. 严格按照章节大纲创作，不要偏离核心情节\n2. 章节长度控制在3000-4000字\n3. 包含生动的场景描写、自然的对话和适当的心理活动\n4. 符合{style}的写作风格和{genre}的类型特点\n5. 章节结尾必须留有悬念，引导读者继续阅读\n6. 避免出现任何提示词痕迹或AI自我指代\n7. 确保与前文的完美衔接，保持时间、地点、人物状态的连续性\n\n请直接给出章节内容，不需要额外的解释或说明。",
    "models": {
      "4k": "同上，但章节长度控制在2000-2500字，减少场景描写的细节",
      "8k": "同上，章节长度控制在3000-4000字",
      "16k+": "同上，章节长度可以扩展到4000-6000字，增加更丰富的场景和心理描写"
    }
  },
  "chapter_polishing": {
    "description": "润色小说章节内容",
    "prompt": "你是一位专业的中文小说编辑。请对以下小说章节进行润色和完善：\n\n标题：{title}\n类型：{genre}\n风格：{style}\n\n原始内容：\n{content}\n\n润色要求：\n1. 提升语言的流畅性和文学性，修正不自然的表达\n2. 确保情节的连贯性和合理性\n3. 保持角色形象和性格的一致性\n4. 增强场景描写的细节和氛围感\n5. 优化对话的自然度和个性化\n6. 确保章节结尾有足够的悬念\n7. 避免出现任何提示词痕迹\n\n请直接给出润色后的完整内容，不需要解释你做了哪些修改。",
    "models": {
      "4k": "同上，但专注于最基本的语言流畅性和情节连贯性修正",
      "8k": "同上",
      "16k+": "同上，可以进行更全面的润色和完善"
    }
  },
  "chapter_expansion": {
    "description": "扩展小说章节内容",
    "prompt": "你是一位专业的中文小说扩写专家。请对以下小说章节进行合理扩展：\n\n原文：\n{content}\n\n扩展要求：\n1. 保持原有情节不变，只进行内容丰富和细节扩展\n2. 重点扩展场景描写、人物对话和心理活动\n3. 增加感官描写（视觉、听觉、嗅觉等）增强沉浸感\n4. 适当增加环境氛围的描写，使场景更加立体\n5. 扩展后的总字数控制在{target_length}字左右\n6. 保持写作风格的一致性\n\n请直接给出扩展后的完整内容，不需要解释你的扩展思路。",
    "models": {
      "4k": "同上，但目标字数控制在2500字左右，只进行最必要的扩展",
      "8k": "同上，目标字数控制在4000字左右",
      "16k+": "同上，目标字数可以达到6000字左右"
    }
  },
  "chapter_optimization": {
    "description": "优化小说章节内容",
    "prompt": "你是一位专业的中文小说优化专家。请对以下小说章节进行全面优化：\n\n原文：\n{content}\n\n优化要求：\n1. 确保情节的连贯性和合理性，修正任何逻辑漏洞\n2. 优化人物对话，使其更加自然、生动，符合角色性格\n3. 增强场景描写的细节和氛围感，提升沉浸感\n4. 优化章节结尾，确保有足够的悬念引导读者继续阅读\n5. 提升语言的文学性和表现力，避免平淡或重复的表达\n6. 确保没有任何提示词痕迹或AI自我指代\n\n请直接给出优化后的完整内容，不需要解释你的优化思路。",
    "models": {
      "4k": "同上，但专注于最基本的情节连贯性和结尾悬念优化",
      "8k": "同上",
      "16k+": "同上，可以进行更全面的优化"
    }
  },
  "illustration_generation": {
    "description": "生成章节插图提示词",
    "prompt": "你是一位专业的插画艺术指导。请为以下小说章节场景创作一个详细的插图提示词：\n\n章节标题：{chapter_title}\n场景描述：{scene_description}\n风格：{style}\n\n请创作一个详细的插图提示词，描述这个场景中最具视觉冲击力和故事代表性的瞬间。提示词应包含：\n1. 场景的主要元素和构图\n2. 光线、色彩和氛围\n3. 如有人物，描述其外貌、表情和动作\n4. 环境细节和背景元素\n5. 画面风格和艺术效果\n\n提示词长度控制在100-150字，必须是中文，不要使用英文。请直接给出提示词，不需要额外的解释。",
    "models": {
      "4k": "同上，但提示词长度控制在80-100字",
      "8k": "同上",
      "16k+": "同上，可以提供更详细的视觉描述"
    }
  },
  "chapter_transition": {
    "description": "创建章节之间的过渡",
    "prompt": "你是一位专业的中文小说作家，擅长创作连贯流畅的章节过渡。请确保新章节与前一章节的完美衔接：\n\n前一章节结尾：\n{previous_chapter_ending}\n\n新章节信息：\n- 章节编号：第{chapter_number}章\n- 章节标题：{chapter_title}\n- 章节大纲：{chapter_outline}\n\n衔接要求：\n1. 新章节的开头必须与前一章节的结尾完美衔接，保持时间、地点、人物和情节的连续性\n2. 不得出现任何时间跳跃或场景突变，除非在前一章结尾已有明确暗示\n3. 人物的心理状态、对话和行动必须与前一章结尾保持一致\n4. 如果前一章以悬念或未完成的动作结束，新章节必须直接承接并发展该情节\n5. 保持写作风格、语气和节奏的一致性\n\n请创作新章节的开头段落（约300-500字），确保与前一章的完美衔接。",
    "models": {
      "4k": "同上，但开头段落控制在200-300字",
      "8k": "同上",
      "16k+": "同上，可以创作更详细的过渡段落"
    }
  },
  "chapter_review": {
    "description": "审核小说章节内容",
    "prompt": "你是一位专业的中文小说审核编辑。请对以下小说章节进行全面审核：\n\n章节内容：\n{content}\n\n审核要点：\n1. 情节连贯性：检查情节是否连贯，是否有逻辑漏洞或矛盾\n2. 角色一致性：检查角色的言行是否符合其设定和性格\n3. 语言质量：检查语言是否流畅、生动，是否有不自然的表达\n4. 结构平衡：检查章节结构是否合理，是否有明显的节奏问题\n5. 悬念设置：检查章节结尾是否有足够的悬念引导读者继续阅读\n6. AI痕迹：检查是否有明显的AI生成痕迹或提示词残留\n\n请提供一份简洁的审核报告，指出章节的优点和需要改进的地方。",
    "models": {
      "4k": "同上，但专注于最基本的情节连贯性和AI痕迹检查",
      "8k": "同上",
      "16k+": "同上，可以提供更详细的审核报告"
    }
  }
}
