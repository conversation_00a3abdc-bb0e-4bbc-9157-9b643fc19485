{"version": 3, "mappings": ";krBAAA,SAASA,GAAEA,EAAEC,EAAEC,EAAEC,EAAE,CAAC,OAAO,IAAID,IAAIA,EAAE,UAAW,SAASE,EAAEC,EAAE,CAAC,SAASC,EAAEN,EAAE,CAAC,GAAG,CAACO,EAAEJ,EAAE,KAAKH,CAAC,CAAC,CAAC,OAAOA,EAAE,CAACK,EAAEL,CAAC,CAAC,CAAC,CAAC,SAASQ,EAAER,EAAE,CAAC,GAAG,CAACO,EAAEJ,EAAE,MAAMH,CAAC,CAAC,CAAC,OAAOA,EAAE,CAACK,EAAEL,CAAC,CAAC,CAAC,CAAC,SAASO,EAAEP,EAAE,CAAC,IAAIC,EAAED,EAAE,KAAKI,EAAEJ,EAAE,KAAK,GAAGC,EAAED,EAAE,MAAMC,aAAaC,EAAED,EAAE,IAAIC,EAAG,SAASF,EAAE,CAACA,EAAEC,CAAC,CAAC,CAAC,GAAI,KAAKK,EAAEE,CAAC,CAAC,CAACD,GAAGJ,EAAEA,EAAE,MAAMH,EAAK,EAAE,GAAG,KAAM,EAAC,CAAG,EAAqD,MAAMC,EAAC,CAAC,aAAa,CAAC,KAAK,UAAU,GAAG,KAAK,GAAG,KAAK,iBAAiB,KAAK,GAAG,KAAK,mBAAmB,CAAC,iBAAiB,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,KAAK,UAAU,CAAC,EAAE,IAAI,CAAC,EAAiB,GAAE,KAAK,CAAC,MAAMC,EAAE,IAAI,CAAC,KAAK,oBAAoB,EAAEA,CAAC,EAAE,KAAK,oBAAoB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,iBAAiB,EAAEA,CAAC,EAAEA,CAAC,CAAC,MAAM,IAAI,KAAK,oBAAoB,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,EAAE,CAAC,IAAI,GAAU,EAAE,KAAK,UAAU,CAAC,KAA1B,MAAuC,IAAT,QAAY,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,KAAK,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,UAAU,CAAE,EAAC,KAAK,KAAK,EAAE,CAAC,KAAK,UAAU,CAAC,GAAG,KAAK,UAAU,CAAC,EAAE,QAASF,GAAGA,EAAE,GAAG,CAAC,CAAG,EAAC,CAAC,MAAME,WAAUD,EAAC,CAAC,YAAY,EAAE,CAAC,QAAQ,KAAK,cAAc,CAAE,EAAC,KAAK,QAAQ,CAAC,CAAC,QAAQ,CAAE,MAAK,EAAE,CAAC,KAAK,WAAW,EAAE,KAAK,OAAQ,EAAC,SAAS,CAAC,KAAK,KAAK,SAAS,EAAE,KAAK,cAAc,QAAS,GAAG,EAAC,CAAI,EAAC,CAAC,MAAME,GAAE,CAAC,aAAa,YAAY,aAAa,YAAY,WAAW,EAAE,MAAMC,WAAUF,EAAC,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAE,EAAC,CAAC,EAAE,CAAC,oBAA2B,EAAE,EAAE,sBAAZ,MAA0C,IAAT,OAAW,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,OAAO,KAAK,KAAK,cAAc,IAAI,CAAC,OAAO,OAAO,EAAE,CAAC,OAAO,IAAIE,GAAE,GAAG,CAAE,EAAC,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,IAAI,aAAa,EAAE,EAAE,wBAAwB,CAAC,EAAED,EAAE,EAAE,eAAgB,EAAC,EAAE,QAAQA,CAAC,EAAE,MAAMC,EAAED,EAAE,kBAAkBE,EAAE,IAAI,aAAaD,CAAC,EAAEE,EAAEF,EAAE,EAAE,WAAW,IAAII,EAAE,MAAMD,EAAE,IAAI,CAACJ,EAAE,uBAAuBE,CAAC,EAAE,KAAK,aAAa,KAAK,WAAW,QAAQ,YAAY,EAAE,KAAK,WAAW,QAAQ,SAAS,GAAG,KAAK,WAAW,KAAK,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAGE,EAAE,sBAAsBD,CAAC,CAAC,EAAE,OAAOA,IAAI,IAAI,CAAC,qBAAqBC,CAAC,EAAW,GAAE,WAAY,EAAU,GAAE,OAAO,CAAC,CAAC,SAASP,EAAE,CAAC,OAAOD,GAAE,KAAK,OAAO,OAAQ,WAAW,CAAC,IAAIA,EAAE,GAAG,CAACA,EAAE,MAAM,UAAU,aAAa,aAAa,CAAC,MAAM,CAAiBC,GAAE,UAAW,CAAC,SAASA,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAOD,EAAE,CAAC,MAAM,IAAI,MAAM,mCAAmCA,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,gBAAgBA,CAAC,EAAE,OAAO,KAAK,cAAc,KAAK,KAAK,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,OAAOA,EAAEA,CAAC,EAAG,CAAC,SAAS,CAAC,KAAK,SAAS,KAAK,OAAO,UAAS,EAAG,QAAS,GAAG,EAAE,KAAM,GAAG,KAAK,OAAO,KAAK,KAAK,cAAc,KAAK,CAAC,eAAeC,EAAE,CAAC,OAAOD,GAAE,KAAK,OAAO,OAAQ,WAAW,CAAC,MAAMA,EAAE,KAAK,SAAS,MAAM,KAAK,SAASC,CAAC,GAAG,EAAE,KAAK,eAAe,IAAI,cAAcD,EAAE,CAAC,SAAS,KAAK,QAAQ,UAAUG,GAAE,KAAMH,GAAG,cAAc,gBAAgBA,CAAC,GAAI,mBAAmB,KAAK,QAAQ,kBAAkB,CAAC,EAAE,KAAK,cAAc,EAAE,KAAK,cAAe,EAAC,MAAMI,EAAE,CAAE,EAAC,EAAE,gBAAgBJ,GAAG,CAACA,EAAE,KAAK,KAAK,GAAGI,EAAE,KAAKJ,EAAE,IAAI,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,IAAIA,EAAE,MAAMC,EAAE,IAAI,KAAKG,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,KAAK,KAAK,aAAaH,CAAC,EAAO,KAAK,QAAQ,sBAAlB,MAAgDD,EAAE,KAAK,cAAf,MAAqCA,IAAT,QAAYA,EAAE,KAAK,IAAI,gBAAgBC,CAAC,CAAC,EAAE,EAAE,EAAE,MAAK,EAAG,KAAK,KAAK,cAAc,CAAC,CAAG,EAAC,aAAa,CAAC,IAAI,EAAE,QAA6B,EAAE,KAAK,iBAAf,MAAwC,IAAT,OAAW,OAAO,EAAE,SAAlE,WAAwE,CAAC,UAAU,CAAC,IAAI,EAAE,QAA0B,EAAE,KAAK,iBAAf,MAAwC,IAAT,OAAW,OAAO,EAAE,SAA/D,QAAqE,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,YAAa,KAAW,EAAE,KAAK,iBAAf,MAAwC,IAAT,QAAY,EAAE,KAAI,EAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,YAAW,KAAa,EAAE,KAAK,iBAAf,MAAwC,IAAT,QAAY,EAAE,MAAO,EAAC,KAAK,KAAK,cAAc,EAAE,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,SAAQ,KAAa,EAAE,KAAK,iBAAf,MAAwC,IAAT,QAAY,EAAE,SAAS,KAAK,KAAK,eAAe,EAAE,CAAC,OAAO,0BAA0B,CAAC,OAAOD,GAAE,KAAK,OAAO,OAAQ,WAAW,CAAC,OAAO,UAAU,aAAa,iBAAkB,EAAC,KAAM,GAAG,EAAE,OAAQA,GAAkBA,EAAE,OAAjB,aAAyB,EAAG,EAAC,SAAS,CAAC,MAAM,QAAS,EAAC,KAAK,cAAe,EAAC,KAAK,SAAS,CAAC,uQCGroH,sHAwC9BS,EAAU,yBAAf,OAAIP,GAAA,iKAACO,EAAU,sBAAf,OAAIP,GAAA,6HAAJ,sDAFgBQ,EAAAD,KAAK,qBAAqB,8EAA5CE,GAAsDC,EAAAC,EAAAC,CAAA,kBAApCC,EAAA,GAAAL,OAAAD,KAAK,qBAAqB,OAAAO,GAAAf,EAAAS,CAAA,wCAGPA,EAAAD,KAAU,MAAK,yCAApCI,EAAA,QAAAI,EAAAR,KAAU,iCAAzBE,GAA4DC,EAAAC,EAAAC,CAAA,kBAAxBC,EAAA,GAAAL,OAAAD,KAAU,MAAK,KAAAO,GAAAf,EAAAS,CAAA,EAApCK,EAAA,GAAAE,OAAAR,KAAU,wGAJtBA,EAAU,GAAC,SAAW,EAACS,wJAFlBT,EAAU,GAAC,SAAW,UAHjCE,GAYQC,EAAAO,EAAAL,CAAA,oGATGL,EAAU,GAAC,SAAW,uEAjCrB,SAAAW,CAAA,EAAAC,EACA,YAAAC,EAAA,IAAAD,QAELE,EAAWC,2HAIjB,OAAc,OAAW,QAEnB,IAAAC,EAAA,GACJC,GAAa,2BAA2B,KACtCC,GAAA,KACAL,EAAaK,CAAA,EACbA,EAAQ,QAASC,GAAA,CACZA,EAAO,UACVH,EAAY,KAAKG,CAAM,QAGzBN,EAAaG,CAAA,GAGP,OAAAI,EAAA,OACJA,aAAe,cAAgBA,EAAI,MAAQ,mBAC9CN,EAAS,QAASH,EAAK,8BAA8B,GAEhDS,soBCoFkCpB,EAAW,mEAAnDE,GAA0DC,EAAAkB,EAAAhB,CAAA,2BAAlBL,EAAW,8CA1CTsB,EAAAtB,KAAK,cAAc,WAazDuB,EAAAvB,KAAK,YAAY,aAcjBwB,EAAAxB,KAAK,YAAY,iBAYsByB,EAAAzB,KAAK,cAAc,mCAE1D,IAAA0B,EAAA1B,OAAWA,EAAuB,IAAAS,GAAAT,CAAA,gaApClB2B,EAAAC,EAAA,QAAAC,EAAA,gBAAA7B,EAAO,YAAQ,EAAK,qBAAuB,IAAE,kSAVpEE,GAmDKC,EAAA2B,EAAAzB,CAAA,EAlDJ0B,EAgDKD,EAAAE,CAAA,EA/CJD,EAIAC,EAAAC,CAAA,yBAEAF,EAWAC,EAAAJ,CAAA,yBAEAG,EAYAC,EAAAE,CAAA,yBAEAH,EAKAC,EAAAG,CAAA,+BACAJ,EAIAC,EAAAI,CAAA,+LAxC2C,CAAAC,GAAA/B,EAAA,IAAAgB,OAAAtB,KAAK,cAAc,OAAAO,GAAA+B,EAAAhB,CAAA,GAazD,CAAAe,GAAA/B,EAAA,IAAAiB,OAAAvB,KAAK,YAAY,OAAAO,GAAAgC,EAAAhB,CAAA,GARD,CAAAc,GAAA/B,EAAA,GAAAuB,OAAA,gBAAA7B,EAAO,YAAa,uBAAuB,IAAE,qCAsB7D,CAAAqC,GAAA/B,EAAA,IAAAkB,OAAAxB,KAAK,YAAY,OAAAO,GAAAiC,EAAAhB,CAAA,GAYsB,CAAAa,GAAA/B,EAAA,IAAAmB,OAAAzB,KAAK,cAAc,OAAAO,GAAAkC,EAAAhB,CAAA,EAE1DzB,OAAWA,EAAuB,iYA3G7B,WAAA0C,CAAA,EAAA9B,EACA,MAAAD,CAAA,EAAAC,GACA,UAAA+B,EAAY,IAAA/B,EAEnBC,EAAA,GACA+B,EACAC,EACAC,EACAC,EACAC,EACAC,EAAoB,GAEb,aAAAC,CAAA,EAAAtC,EACA,yBAAAuC,CAAA,EAAAvC,GACA,OAAAwC,EAAS,IAAAxC,4CAkDPgC,EAAYS,kBAEP,MAAAC,EAAA,IAAAZ,EAAO,2DAIZK,EAAUM,+BAGhBX,EAAO,aACVA,EAAO,gBAAe,EACtBA,EAAO,cAAa,GAGrBA,EAAO,cAAa,6CAKVM,EAAgBK,gCAItBX,EAAO,aACVA,EAAO,gBAAe,EACtBA,EAAO,cAAa,GAGrBA,EAAO,cAAa,6CAMVG,EAAWQ,kBAEN,MAAAE,EAAA,IAAAb,EAAO,2DAGZI,EAAYO,kBAEP,MAAAG,EAAA,IAAAd,EAAO,iUAzFtBA,EAAO,GAAG,oBACZA,EAAO,WAEPe,EAAA,EAAAb,EAAa,MAAM,QAAU,OAAAA,CAAA,EAC7Ba,EAAA,EAAAV,EAAW,MAAM,QAAU,OAAAA,CAAA,EAC3BU,EAAA,EAAAZ,EAAY,MAAM,QAAU,QAAAA,CAAA,kBAG1BH,EAAO,GAAG,kBACRA,EAAO,aACVA,EAAO,kBACPA,EAAO,iBAERA,EAAO,UAEPe,EAAA,EAAAb,EAAa,MAAM,QAAU,OAAAA,CAAA,EAC7Ba,EAAA,EAAAV,EAAW,MAAM,QAAU,OAAAA,CAAA,EAC3BU,EAAA,EAAAZ,EAAY,MAAM,QAAU,OAAAA,CAAA,EAC5BY,EAAA,EAAAb,EAAa,SAAW,GAAAA,CAAA,kBAGtBF,EAAO,GAAG,oBACZe,EAAA,EAAAZ,EAAY,MAAM,QAAU,OAAAA,CAAA,EAC5BY,EAAA,EAAAX,EAAa,MAAM,QAAU,QAAAA,CAAA,EAC7BW,EAAA,EAAAV,EAAW,MAAM,QAAU,OAAAA,CAAA,EAC3BU,EAAA,GAAAT,EAAiB,MAAM,QAAU,OAAAA,CAAA,kBAG/BN,EAAO,GAAG,qBACZe,EAAA,EAAAZ,EAAY,MAAM,QAAU,QAAAA,CAAA,EAC5BY,EAAA,EAAAX,EAAa,MAAM,QAAU,OAAAA,CAAA,EAC7BW,EAAA,EAAAb,EAAa,MAAM,QAAU,OAAAA,CAAA,EAC7Ba,EAAA,EAAAV,EAAW,MAAM,QAAU,OAAAA,CAAA,EAC3BU,EAAA,GAAAT,EAAiB,MAAM,QAAU,OAAAA,CAAA,sBAG3BL,GAAc,CAAAM,GACpBP,EAAO,sBACPO,EAAoB,MAEpBP,EAAO,qBACPO,EAAoB,4kCC9DG,6DAEc,iEAgN9BS,EAAA1D,EAAS,aAAUA,MAAe,GAAC2D,GAAA3D,CAAA,yBAGnCA,EAAM,IAAA4D,yMANb1D,GAYKC,EAAA2B,EAAAzB,CAAA,EAXJ0B,EAAiDD,EAAAT,CAAA,kBACjDU,EASKD,EAAAE,CAAA,4CARChC,EAAS,aAAUA,MAAe,0LACTC,EAAA4D,GAAY7D,EAAY,4FAArDE,GAA6DC,EAAAkB,EAAAhB,CAAA,iBAAhCC,EAAA,WAAAL,OAAA4D,GAAY7D,EAAY,UAAAO,GAAAf,EAAAS,CAAA,kIAKrDC,GAAyDC,EAAAkB,EAAAhB,CAAA,kEAFjCJ,EAAA4D,GAAY7D,EAAO,uFAA3CE,GAAmDC,EAAAkB,EAAAhB,CAAA,iBAA3BC,EAAA,UAAAL,OAAA4D,GAAY7D,EAAO,UAAAO,GAAAf,EAAAS,CAAA,gHAcpB,wBAAAD,KAAiB,wBAC7B,YAAA6D,GAAY7D,EAAO,8OADPM,EAAA,OAAAwD,EAAA,wBAAA9D,KAAiB,yBAC7BM,EAAA,WAAAwD,EAAA,YAAAD,GAAY7D,EAAO,sQAOrBA,EAAkB,2EAKhB,8FANEA,EAAiB,yBAAjBA,EAAiB,qRACrBA,EAAkB,4NADdA,EAAiB,uPA7B5B0D,GAAA1D,EAAU,KAAAA,EAAkB,MAAAA,KAAiB,yBAAuB+D,GAAA/D,CAAA,EAgBrEgE,EAAAhE,QAAwBA,EAAa,KAAAiE,GAAAjE,CAAA,EAWrCkE,EAAAlE,MAAqBA,EAAa,KAAAS,GAAAT,CAAA,qRAnCxCE,GAoDKC,EAAAgE,EAAA9D,CAAA,EAnDJ0B,EAICoC,EAAAnC,CAAA,kBACDD,EAAsEoC,EAAArC,CAAA,2FAEhE9B,EAAU,KAAAA,EAAkB,MAAAA,KAAiB,+EAgB9CA,QAAwBA,EAAa,4GAWrCA,MAAqBA,EAAa,wPA7N5B,SAAAoE,CAAA,EAAAxD,EACA,MAAAD,CAAA,EAAAC,EACA,eAAAyD,CAAA,EAAAzD,EAIA,mBAAA0D,CAAA,EAAA1D,EACA,kBAAA2D,EAAA,CACV,wBAAyB,KAAA3D,EAEf,oBAAA4D,CAAA,EAAA5D,GACA,SAAA6D,EAAW,IAAA7D,GACX,UAAA+B,EAAY,IAAA/B,EAEnB8D,EACAC,EACAC,EAAU,GAEVC,EACAC,EAEApC,EACAqC,EAA+B,KAG/BC,EACAC,EACAC,EACAC,EAAU,EACVC,EACAhC,EAAS,GAETiC,EAAe,EAEb,MAAAC,EAAA,KACL,cAAcF,CAAQ,EACtBA,EAAW,iBACV3B,EAAA,GAAA0B,KAAA,GACE,MAGErE,EAAWC,KAWR,SAAAwE,GAAA,IACRD,SACAlC,EAAS,IACTtC,EAAS,iBAAiB,EACtByD,EAAiB,6BAChBiB,EAAiBV,EACjBU,IAAgBA,EAAe,MAAM,QAAU,yBAItCC,GAAoBC,EAAA,MAClCP,EAAU,QACV/B,EAAS,IACT,cAAcgC,CAAQ,MAEf,MAAAO,EAAA,MAAqBD,EAAK,cAI1BE,EAAqB,MAHP,kBACnB,WAAYtB,EAAkB,aAEI,gBAAgBqB,CAAY,EAE3DC,GACG,MAAAC,GAAcD,CAAY,EAAE,KAAY,MAAAE,GAAA,OACvCzB,EAAe,CAAAyB,CAAK,EAAG,QAAQ,QAC/BzB,EAAe,CAAAyB,CAAK,EAAG,gBAAgB,GAEvC,OAAAvG,EAAA,CACR,QAAQ,MAAMA,CAAC,GAkCX,MAAAwG,EAAA,KACDjB,GAAArB,EAAA,GAAqBqB,EAAoB,UAAY,GAAAA,CAAA,EACrDJ,IAAA,QAA2BA,EAAY,UACtCI,IACLJ,EAAcsB,GAAW,QACrB,GAAA1B,EACH,UAAW,GACX,UAAWQ,QAGZpC,EAASgC,EAAY,eAAezD,GAAa,WACjDyB,GAAQ,GAAG,aAAc+C,EAAmB,EAC5C/C,GAAQ,GAAG,eAAgB6C,CAAqB,EAChD7C,GAAQ,GAAG,oBACV5B,EAAS,iBAAiB,EAC1B,cAAcsE,CAAQ,IAGvB1C,GAAQ,GAAG,aAAegD,GAAA,MACzBX,EAAgB,IAAI,gBAAgBW,CAAI,SAElCO,EAAanB,EACbnC,GAAYkC,EAEdoB,IAAYA,EAAW,MAAM,QAAU,QACvCtD,IAAaoC,IAChBpC,GAAU,UAAY,GACtBuD,SAKGA,EAAA,SACDvD,EAAYkC,GACXE,GAAkBpC,IACvBc,EAAA,EAAAkB,EAAoBqB,GAAW,QAC9B,UAAWrD,EACX,IAAKoC,EACF,GAAAT,MAIC6B,GAAA,MACLC,EACAC,IAAA,KAEAjC,EAAO,QACD,MAAAkC,GAAc3B,EAAkB,iBAClC2B,IAAA,MACGT,GAAcS,GAAaF,EAAOC,CAAG,EAAE,KACrC,MAAAE,GAAA,OACAlC,EAAe,CAAAkC,CAAY,EAAG,QAAQ,QACtClC,EAAe,CAAAkC,CAAY,EAAG,gBAAgB,EACpD5B,EAAkB,UAClBuB,MAGHpF,EAAS,MAAM,GAGhB0F,GAAA,KACCT,IAEA,OAAO,iBAAiB,UAAYxG,GAAA,CAC/BA,EAAE,MAAQ,aACbkH,GAAW9B,EAAmB,EAAG,EACvBpF,EAAE,MAAQ,aACpBkH,GAAW9B,KAAuB,gDASzBG,EAAmBzB,sDAGfwB,EAAkBxB,sDAIf2B,EAAO3B,4DAQL4B,EAAW5B,0DAmBfsB,EAAiB+B,0bA5I/BhE,GAAQ,GAAG,qBACb4C,yBAGEX,GAAmB,GAAG,SAAWgC,GAAA,MACnCzB,EAAiByB,CAAA,EACjB1B,GAAgBxB,EAAA,EAAAwB,EAAY,YAAcpB,GAAY8C,CAAQ,EAAA1B,CAAA,uBAG5DN,GAAmB,GACrB,aACCiC,GACA5B,OAAYA,EAAQ,YAAcnB,GAAY+C,CAAW,EAAA5B,CAAA,qBAGxDL,GAAmB,GAAG,aACxB7D,EAAS,OAAO,OAChB8D,EAAU,wBAGRD,GAAmB,GAAG,YACxB7D,EAAS,MAAM,OACf8D,EAAU,wBAGRD,GAAmB,GAAG,cACxB7D,EAAS,MAAM,OACf8D,EAAU,s4CC1Ha,+FA+CP5E,EAAS,GAAG,QAAU,MAAM,UAF5CE,GAGCC,EAAA0G,EAAAxG,CAAA,uCADeL,EAAS,GAAG,QAAU,MAAM,wDAyCzC8G,EAAA9G,KAAK,cAAc,wLAVrBE,GAWQC,EAAA4G,EAAA1G,CAAA,EAJP0B,EAEMgF,EAAAC,CAAA,wDACL1G,EAAA,IAAAwG,OAAA9G,KAAK,cAAc,OAAAO,GAAA0G,EAAAH,CAAA,iEAbnBA,EAAA9G,KAAK,eAAe,0LATtBE,GAUQC,EAAA4G,EAAA1G,CAAA,EAJP0B,EAEKgF,EAAAF,CAAA,2EACJ,CAAAxE,GAAA/B,EAAA,KAAAwG,OAAA9G,KAAK,eAAe,OAAAO,GAAA0G,EAAAH,CAAA,iIAZpB9G,EAAgB,GAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,qLAVpDA,EAAgB,GAAG,qBAAuB,aAAa,6BAD/DE,GAYQC,EAAA4G,EAAA1G,CAAA,EALP0B,EAGMgF,EAAAG,CAAA,sEACLlH,EAAgB,GAAGA,KAAK,aAAa,EAAIA,KAAK,YAAY,QAAAO,GAAAgC,EAAAhB,CAAA,iBAVpDvB,EAAgB,GAAG,qBAAuB,aAAa,8GAT5D0D,EAAA1D,KAAiB,yBAAuB+D,GAAA/D,CAAA,0CAOvC,OAAAA,OAAcA,EAAO,KAchBA,MAAaA,EAAO,wUAtBhCE,GAmDKC,EAAA2B,EAAAzB,CAAA,wBA5CJ0B,EA2CKD,EAAAE,CAAA,oDAjDAhC,KAAiB,4bApCX,UAAA2C,EAAY,IAAA/B,GACZ,iBAAAuG,EAAmB,IAAAvG,EACnB,MAAAwG,CAAA,EAAAxG,EACA,QAAA8B,CAAA,EAAA9B,EACA,MAAAD,CAAA,EAAAC,EACA,mBAAA0D,CAAA,EAAA1D,EACA,kBAAA2D,EAAA,CACV,wBAAyB,KAAA3D,GAEf,QAAAyG,EAAU,IAAAzG,EAEjB8D,EACA4C,EAEAxC,EAEAjE,EAAA,GAEJ2F,GAAA,KACCT,MAGK,MAAAA,EAAA,KACDrB,IAAA,QAA2BA,EAAY,UACtCI,IACLJ,EAAcsB,GAAW,QACrB,GAAA1B,EACH,OAAQ,IACR,UAAWQ,QAGZwC,EAAiB5C,EAAY,eAAezD,GAAa,uDAO7C6D,EAAmBzB,wBAS5BiE,GAAgB,QAAO,EACvBF,YAaAA,YAYAE,GAAgB,SAAQ,EACxB5E,8pDClFK,WAAA6E,GAAA,sBAAAxG,UAA8C,sGA4S3Cf,EAAoB,GAAGA,KAAM,IAAM,sBAFnCA,EAAK,yjBAELA,EAAoB,GAAGA,KAAM,IAAM,kkBAjDzC,OAAAA,OAAkB,aAAY,EA2BzBA,OAAkB,SAAQ,kpBAYtB,WAAAA,MAAK,sBAAsB,sNAR9BA,EAAW,0NAQRM,EAAA,UAAAkH,EAAA,WAAAxH,MAAK,sBAAsB,qTAtCTA,EAAK,iDAC/BA,EAAS,slDAQH,QAAAA,QAAiB,+MAAjBM,EAAA,aAAAmH,EAAA,QAAAzH,QAAiB,sLAjBxB0H,GACC,MAAA1H,EAAkB,eAAYA,OAAU,WACxCA,EAAK,IAAIA,EAAI,IAAC,aAAa,sFAI7BA,EAAK,KAAK,MAAQA,EAAS,uFAwEyBA,EAAK,4OA1ElCA,EAAU,kDAAvCE,GA2EKC,EAAA0G,EAAAxG,CAAA,+GA9EGC,EAAA,QAAAqH,EAAA,MAAA3H,EAAkB,eAAYA,OAAU,0BACxCA,EAAK,IAAIA,EAAI,IAAC,aAAa,2VAENA,EAAU,yPA7KhC4H,GAAmB,qDA5Dd,MAAAlB,EAAyB,MAAA9F,EACzB,OAAAiH,CAAA,EAAAjH,EACA,MAAAkH,CAAA,EAAAlH,EACA,MAAAmH,CAAA,EAAAnH,GACA,WAAAoH,EAAa,IAAApH,GACb,qBAAAqH,EAAuB,IAAArH,EACvB,SAAAsH,EAAA,CAIoB,aAAc,QAAQ,GAAAtH,GAC1C,QAAAuH,EAAU,IAAAvH,GACV,UAAAwH,EAAY,IAAAxH,EACZ,MAAAD,CAAA,EAAAC,EACA,mBAAA0D,CAAA,EAAA1D,EACA,sBAAAyH,EAAA,IAAAzH,EACA,kBAAA2D,EAAA,IAAA3D,EACA,UAAA0H,CAAA,EAAA1H,EACA,eAAA2H,CAAA,EAAA3H,EACA,oBAAA4D,EAAA,WACA,SAAAC,EAAW,IAAA7D,GACX,cAAA4H,EAA+B,MAAA5H,EAC/B,QAAA6H,CAAA,EAAA7H,EACA,gBAAA8H,CAAA,EAAA9H,EACA,cAAA+H,CAAA,EAAA/H,GACA,UAAAgI,EAAY,IAAAhI,GACZ,UAAA+B,EAAY,IAAA/B,GACZ,WAAAiI,GAAa,IAAAjI,EAEpBkI,EAA4B,KAC5BC,EAA8C,eAErCC,GACZC,GAAA,CAEIA,IAAU,eACbH,EAAa,WACbC,EAAe,WACLE,IAAU,eACpBF,EAAe,gBAEfA,EAAe,SAIJG,EAAkB7H,GAAA,CAC1BsB,GAAAc,EAAA,GAAWqF,EAAazH,CAAA,GAOzB,IAAA8H,EACA/E,EAAO,GACPgF,EACAC,EAAA,GACAC,GAAuC,GACvCC,GAAS,GAGTC,GAAA,GACAC,EAKK,SAAAC,GAAA,CACRD,EAAA,eACQ,sBAA2B,4DAC3B,sBAAuC,2CAI1C,OAAoB,OAAW,KACnBrB,GACjBsB,UAGK5I,EAAWC,KAkBXsD,EAAA,MACLsF,EACAC,IAAA,KAEIC,GAAkB,SAAKF,EAAO,WAAW,EACvC,MAAAG,GAAA,MAAYC,GAAe,CAAAF,EAAW,EAAGD,IAAU,QAAQ,MACjElD,GACQ,MAAA+B,EAAOqB,GAAKhC,EAAM,OAAWU,SAA0B,IAAI,OACjE,SAEA,CAAC,GACH1H,EAAS8I,EAAOlD,CAAK,GAGtBa,GAAA,KACKa,GAAae,GAAYA,EAAS,QAAU,YAC/CA,EAAS,SAII,eAAAa,IAAA,CACV,IAAAC,MAGHA,EAAe,gBAAU,aAAa,cAAe,MAAO,IACpD,OAAA7I,EAAA,KACH,UAAU,cACdN,EAAS,QAASH,EAAK,yBAAyB,aAG7CS,aAAe,cAAgBA,EAAI,MAAQ,mBAC9CN,EAAS,QAASH,EAAK,8BAA8B,UAGhD,MAAAS,KAEH6I,GAAU,KAEV,IAAA7B,EAAA,CACM,qBAAA8B,EAAe,SAAAC,EAAA,GAAc,QAAAC,EAC/B,iBAAQ,IAAIX,CAAe,QAC5BU,GAAe,MAAAC,GAAA,QACrBjB,EAAee,MAAcD,GAAU,SAAU,eACjDd,EAAS,iBAAiB,gBAAiBkB,EAAY,OAEvD5G,EAAA,GAAA0F,EAAA,IAAe,cAAcc,CAAM,GACnCd,EAAS,iBAAiB,gBAAkBS,GAAA,CAC3CJ,GAAa,KAAKI,EAAM,IAAI,IAG9BT,EAAS,iBAAiB,qBACzBxG,EAAY,IAEN,MAAA0B,EAAcmF,GAAc,QAAQ,EACpC,MAAAnF,EAAcmF,GAAc,gBAAgB,EAClDA,GAAA,KAEDD,GAAS,mBAGKc,GAAaT,EAAA,CACvB,IAAAU,EAAA,MAAeV,EAAM,KAAK,cAC1BW,GAAA,IAAc,WAAWD,CAAM,EAK/B,GAJClB,IACJ3F,EAAA,GAAA2F,EAAA,IAAa,WAAWkB,EAAO,MAAM,EAAG1C,EAAgB,IACxD2C,GAAc,eAAWD,EAAO,MAAM1C,EAAgB,IAEnDO,EACHkB,EAAe,KAAKkB,EAAO,OAEvB,IAAAC,GAAA,CAAapB,CAAM,EAAE,OAAOC,EAAA,CAAiBkB,EAAO,GACnD,IAAA5H,GAAaoG,IAAiB,iBACnC1E,EAAcmG,GAAW,QAAQ,EACjC/G,EAAA,GAAA4F,EAAA,KAaa,eAAA3G,IAAA,KACdC,EAAY,IACZ7B,EAAS,iBAAiB,EACrByI,IAAc,MAAAS,UACnBZ,EAAS,QACLhB,GAAae,EAAS,OAAS,aAClCA,EAAS,MAAMR,EAAe,GAAI,EAI3B,SAAA8B,IAAA,CACR3J,EAAS,SAAU,IAAI,EACvBA,EAAS,OAAO,OAChBsD,EAAO,QACPsC,EAAQ,eAGAgE,GAAc,QAAAC,GAAA,KACtBjE,EAAQiE,CAAA,EACR7J,EAAS,SAAU6J,CAAM,EACzB7J,EAAS,SAAU6J,CAAM,EAGX,eAAAvD,IAAA,KACdzE,EAAY,IAERyF,IACHtH,EAAS,cAAc,EACvBA,EAAS,gBAAgB,EACzBqI,EAAS,OAELhB,QACHmB,GAAuC,IAExCjF,EAAcmF,GAAc,gBAAgB,EAC5C1I,EAAS,OAAO,OAChBsD,EAAO,uLAmDO,MAAAwG,GAAA,SAAAD,CAAM,IAAO7J,EAAS,QAAS6J,CAAM,EAcnCE,GAAA,IAAApH,EAAA,GAAAW,EAAO,MAAM,osCA3O5BtD,EAAS,OAAQwH,CAAQ,qBA6HrBgB,IAAwCnB,IAAY,UAC1DmB,GAAuC,IACnCF,GAAUC,GAAA,KACTmB,EAA2B,CAAApB,CAAM,EAAE,OAAOC,CAAc,EAC5D5F,EAAA,GAAA4F,EAAA,IACAhF,EAAcmG,EAAW,QAAQ,oCA4CnC,CAAQ7H,GAAawG,GAAU/B,uCACxBzE,GAAawG,GAAUzG", "names": ["e", "t", "i", "s", "r", "n", "o", "d", "a", "ctx", "t_value", "insert", "target", "option", "anchor", "dirty", "set_data", "option_value_value", "create_if_block", "select", "i18n", "$$props", "micDevices", "dispatch", "createEventDispatcher", "tempDevices", "RecordPlugin", "devices", "device", "err", "time", "t0_value", "t2_value", "t4_value", "t7_value", "if_block", "attr", "button1", "button1_class_value", "div1", "append", "div0", "button0", "button2", "button3", "button4", "current", "t0", "t2", "t4", "t7", "record", "recording", "recordButton", "pauseButton", "resumeButton", "stopButton", "stopButtonPaused", "recording_ongoing", "record_time", "show_recording_waveform", "timing", "$$value", "click_handler", "click_handler_3", "click_handler_4", "$$invalidate", "if_block0", "create_if_block_4", "create_if_block_3", "format_time", "waveformrecordcontrols_changes", "create_if_block_2", "if_block1", "create_if_block_1", "if_block2", "div2", "mode", "dispatch_blob", "waveform_settings", "waveform_options", "handle_reset_value", "editable", "micWaveform", "recordingWaveform", "playing", "recordingContainer", "microphoneContainer", "recordedAudio", "timeRef", "durationRef", "audio_duration", "seconds", "interval", "trimDuration", "start_interval", "record_start_callback", "waveformCanvas", "record_end_callback", "blob", "array_buffer", "audio_buffer", "process_audio", "audio", "create_mic_waveform", "WaveSurfer", "microphone", "create_recording_waveform", "handle_trim_audio", "start", "end", "decodedData", "trimmedAudio", "onMount", "skip_audio", "value", "duration", "currentTime", "div", "t1_value", "button", "span1", "t1", "span2", "paused_recording", "stop", "waiting", "waveformRecord", "onDestroy", "upload_1_changes", "streamaudio_changes", "Music", "blocklabel_changes", "NUM_HEADER_BYTES", "label", "root", "loop", "show_label", "show_download_button", "sources", "pending", "streaming", "trim_region_settings", "dragging", "active_source", "max_file_size", "upload", "stream_handler", "stream_every", "uploading", "class_name", "time_limit", "stream_state", "modify_stream", "state", "set_time_limit", "recorder", "header", "pending_stream", "submit_pending_stream_on_pending_end", "inited", "audio_chunks", "module_promises", "get_modules", "blobs", "event", "_audio_blob", "val", "prepare_files", "prepare_audio", "stream", "MediaRecorder", "register", "connect", "handle_chunk", "buffer", "payload", "blobParts", "clear", "handle_load", "detail", "error_handler", "edit_handler_1"], "ignoreList": [0], "sources": ["../../../../node_modules/.pnpm/wavesurfer.js@7.4.2/node_modules/wavesurfer.js/dist/plugins/record.js", "../../../../js/audio/shared/DeviceSelect.svelte", "../../../../js/audio/shared/WaveformRecordControls.svelte", "../../../../js/audio/recorder/AudioRecorder.svelte", "../../../../js/audio/streaming/StreamAudio.svelte", "../../../../js/audio/interactive/InteractiveAudio.svelte"], "sourcesContent": ["function e(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{d(s.next(e))}catch(e){n(e)}}function a(e){try{d(s.throw(e))}catch(e){n(e)}}function d(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}d((s=s.apply(e,t||[])).next())}))}\"function\"==typeof SuppressedError&&SuppressedError;class t{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),null==i?void 0:i.once){const i=()=>{this.removeEventListener(e,i),this.removeEventListener(e,t)};return this.addEventListener(e,i),i}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;null===(i=this.listeners[e])||void 0===i||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach((e=>e(...t)))}}class i extends t{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((e=>e()))}}const s=[\"audio/webm\",\"audio/wav\",\"audio/mpeg\",\"audio/mp4\",\"audio/mp3\"];class r extends i{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:null!==(t=e.audioBitsPerSecond)&&void 0!==t?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new r(e||{})}renderMicStream(e){const t=new AudioContext,i=t.createMediaStreamSource(e),s=t.createAnalyser();i.connect(s);const r=s.frequencyBinCount,n=new Float32Array(r),o=r/t.sampleRate;let a;const d=()=>{s.getFloatTimeDomainData(n),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load(\"\",[n],o)),a=requestAnimationFrame(d)};return d(),()=>{cancelAnimationFrame(a),null==i||i.disconnect(),null==t||t.close()}}startMic(t){return e(this,void 0,void 0,(function*(){let e;try{e=yield navigator.mediaDevices.getUserMedia({audio:!(null==t?void 0:t.deviceId)||{deviceId:t.deviceId}})}catch(e){throw new Error(\"Error accessing the microphone: \"+e.message)}const i=this.renderMicStream(e);return this.subscriptions.push(this.once(\"destroy\",i)),this.stream=e,e}))}stopMic(){this.stream&&(this.stream.getTracks().forEach((e=>e.stop())),this.stream=null,this.mediaRecorder=null)}startRecording(t){return e(this,void 0,void 0,(function*(){const e=this.stream||(yield this.startMic(t)),i=this.mediaRecorder||new MediaRecorder(e,{mimeType:this.options.mimeType||s.find((e=>MediaRecorder.isTypeSupported(e))),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=i,this.stopRecording();const r=[];i.ondataavailable=e=>{e.data.size>0&&r.push(e.data)},i.onstop=()=>{var e;const t=new Blob(r,{type:i.mimeType});this.emit(\"record-end\",t),!1!==this.options.renderRecordedAudio&&(null===(e=this.wavesurfer)||void 0===e||e.load(URL.createObjectURL(t)))},i.start(),this.emit(\"record-start\")}))}isRecording(){var e;return\"recording\"===(null===(e=this.mediaRecorder)||void 0===e?void 0:e.state)}isPaused(){var e;return\"paused\"===(null===(e=this.mediaRecorder)||void 0===e?void 0:e.state)}stopRecording(){var e;this.isRecording()&&(null===(e=this.mediaRecorder)||void 0===e||e.stop())}pauseRecording(){var e;this.isRecording()&&(null===(e=this.mediaRecorder)||void 0===e||e.pause(),this.emit(\"record-pause\"))}resumeRecording(){var e;this.isPaused()&&(null===(e=this.mediaRecorder)||void 0===e||e.resume(),this.emit(\"record-resume\"))}static getAvailableAudioDevices(){return e(this,void 0,void 0,(function*(){return navigator.mediaDevices.enumerateDevices().then((e=>e.filter((e=>\"audioinput\"===e.kind))))}))}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}export{r as default};\n", "<script lang=\"ts\">\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\n\texport let i18n: I18nFormatter;\n\texport let micDevices: MediaDeviceInfo[] = [];\n\n\tconst dispatch = createEventDispatcher<{\n\t\terror: string;\n\t}>();\n\n\t$: if (typeof window !== \"undefined\") {\n\t\ttry {\n\t\t\tlet tempDevices: MediaDeviceInfo[] = [];\n\t\t\tRecordPlugin.getAvailableAudioDevices().then(\n\t\t\t\t(devices: MediaDeviceInfo[]) => {\n\t\t\t\t\tmicDevices = devices;\n\t\t\t\t\tdevices.forEach((device) => {\n\t\t\t\t\t\tif (device.deviceId) {\n\t\t\t\t\t\t\ttempDevices.push(device);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tmicDevices = tempDevices;\n\t\t\t\t}\n\t\t\t);\n\t\t} catch (err) {\n\t\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\t\tdispatch(\"error\", i18n(\"audio.allow_recording_access\"));\n\t\t\t}\n\t\t\tthrow err;\n\t\t}\n\t}\n</script>\n\n<select\n\tclass=\"mic-select\"\n\taria-label=\"Select input device\"\n\tdisabled={micDevices.length === 0}\n>\n\t{#if micDevices.length === 0}\n\t\t<option value=\"\">{i18n(\"audio.no_microphone\")}</option>\n\t{:else}\n\t\t{#each micDevices as micDevice}\n\t\t\t<option value={micDevice.deviceId}>{micDevice.label}</option>\n\t\t{/each}\n\t{/if}\n</select>\n\n<style>\n\t.mic-select {\n\t\theight: var(--size-8);\n\t\tbackground: var(--block-background-fill);\n\t\tpadding: 0px var(--spacing-xxl);\n\t\tborder-radius: var(--button-large-radius);\n\t\tfont-size: var(--text-md);\n\t\tborder: 1px solid var(--block-border-color);\n\t\tgap: var(--size-1);\n\t}\n\n\tselect {\n\t\ttext-overflow: ellipsis;\n\t\tmax-width: var(--size-40);\n\t}\n\n\t@media (max-width: 375px) {\n\t\tselect {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Pause } from \"@gradio/icons\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport DeviceSelect from \"./DeviceSelect.svelte\";\n\n\texport let record: RecordPlugin;\n\texport let i18n: I18nFormatter;\n\texport let recording = false;\n\n\tlet micDevices: MediaDeviceInfo[] = [];\n\tlet recordButton: HTMLButtonElement;\n\tlet pauseButton: HTMLButtonElement;\n\tlet resumeButton: HTMLButtonElement;\n\tlet stopButton: HTMLButtonElement;\n\tlet stopButtonPaused: HTMLButtonElement;\n\tlet recording_ongoing = false;\n\n\texport let record_time: string;\n\texport let show_recording_waveform: boolean | undefined;\n\texport let timing = false;\n\n\t$: record.on(\"record-start\", () => {\n\t\trecord.startMic();\n\n\t\trecordButton.style.display = \"none\";\n\t\tstopButton.style.display = \"flex\";\n\t\tpauseButton.style.display = \"block\";\n\t});\n\n\t$: record.on(\"record-end\", () => {\n\t\tif (record.isPaused()) {\n\t\t\trecord.resumeRecording();\n\t\t\trecord.stopRecording();\n\t\t}\n\t\trecord.stopMic();\n\n\t\trecordButton.style.display = \"flex\";\n\t\tstopButton.style.display = \"none\";\n\t\tpauseButton.style.display = \"none\";\n\t\trecordButton.disabled = false;\n\t});\n\n\t$: record.on(\"record-pause\", () => {\n\t\tpauseButton.style.display = \"none\";\n\t\tresumeButton.style.display = \"block\";\n\t\tstopButton.style.display = \"none\";\n\t\tstopButtonPaused.style.display = \"flex\";\n\t});\n\n\t$: record.on(\"record-resume\", () => {\n\t\tpauseButton.style.display = \"block\";\n\t\tresumeButton.style.display = \"none\";\n\t\trecordButton.style.display = \"none\";\n\t\tstopButton.style.display = \"flex\";\n\t\tstopButtonPaused.style.display = \"none\";\n\t});\n\n\t$: if (recording && !recording_ongoing) {\n\t\trecord.startRecording();\n\t\trecording_ongoing = true;\n\t} else {\n\t\trecord.stopRecording();\n\t\trecording_ongoing = false;\n\t}\n</script>\n\n<div class=\"controls\">\n\t<div class=\"wrapper\">\n\t\t<button\n\t\t\tbind:this={recordButton}\n\t\t\tclass=\"record record-button\"\n\t\t\ton:click={() => record.startRecording()}>{i18n(\"audio.record\")}</button\n\t\t>\n\n\t\t<button\n\t\t\tbind:this={stopButton}\n\t\t\tclass=\"stop-button {record.isPaused() ? 'stop-button-paused' : ''}\"\n\t\t\ton:click={() => {\n\t\t\t\tif (record.isPaused()) {\n\t\t\t\t\trecord.resumeRecording();\n\t\t\t\t\trecord.stopRecording();\n\t\t\t\t}\n\n\t\t\t\trecord.stopRecording();\n\t\t\t}}>{i18n(\"audio.stop\")}</button\n\t\t>\n\n\t\t<button\n\t\t\tbind:this={stopButtonPaused}\n\t\t\tid=\"stop-paused\"\n\t\t\tclass=\"stop-button-paused\"\n\t\t\ton:click={() => {\n\t\t\t\tif (record.isPaused()) {\n\t\t\t\t\trecord.resumeRecording();\n\t\t\t\t\trecord.stopRecording();\n\t\t\t\t}\n\n\t\t\t\trecord.stopRecording();\n\t\t\t}}>{i18n(\"audio.stop\")}</button\n\t\t>\n\n\t\t<button\n\t\t\taria-label=\"pause\"\n\t\t\tbind:this={pauseButton}\n\t\t\tclass=\"pause-button\"\n\t\t\ton:click={() => record.pauseRecording()}><Pause /></button\n\t\t>\n\t\t<button\n\t\t\tbind:this={resumeButton}\n\t\t\tclass=\"resume-button\"\n\t\t\ton:click={() => record.resumeRecording()}>{i18n(\"audio.resume\")}</button\n\t\t>\n\t\t{#if timing && !show_recording_waveform}\n\t\t\t<time class=\"duration-button duration\">{record_time}</time>\n\t\t{/if}\n\t</div>\n\t<DeviceSelect bind:micDevices {i18n} />\n</div>\n\n<style>\n\t.controls {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.record {\n\t\tmargin-right: var(--spacing-md);\n\t}\n\n\t.stop-button-paused {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--button-large-radius);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--block-border-color);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.stop-button-paused::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\t.stop-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t\tanimation: scaling 1800ms infinite;\n\t}\n\n\t.stop-button {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--button-large-radius);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--primary-600);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.record-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\n\t.record-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-24);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--button-large-radius);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--block-border-color);\n\t}\n\n\t.duration-button {\n\t\tborder-radius: var(--button-large-radius);\n\t}\n\n\t.stop-button:disabled {\n\t\tcursor: not-allowed;\n\t}\n\n\t.record-button:disabled {\n\t\tcursor: not-allowed;\n\t\topacity: 0.5;\n\t}\n\n\t@keyframes scaling {\n\t\t0% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t\t50% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1.2;\n\t\t}\n\t\t100% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t}\n\n\t.pause-button {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tborder: 1px solid var(--block-border-color);\n\t\tborder-radius: var(--button-large-radius);\n\t\tpadding: var(--spacing-md);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.resume-button {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tborder: 1px solid var(--block-border-color);\n\t\tborder-radius: var(--button-large-radius);\n\t\tpadding: var(--spacing-xl);\n\t\tline-height: 1px;\n\t\tfont-size: var(--text-md);\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t.duration {\n\t\tdisplay: flex;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tborder: 1px solid var(--block-border-color);\n\t\tpadding: var(--spacing-md);\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin: var(--size-1) var(--size-1) 0 0;\n\t}\n\n\t:global(::part(region)) {\n\t\tborder-radius: var(--radius-md);\n\t\theight: 98% !important;\n\t\tborder: 1px solid var(--trim-region-color);\n\t\tbackground-color: unset;\n\t\tborder-width: 1px 3px;\n\t}\n\n\t:global(::part(region))::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: var(--trim-region-color);\n\t\topacity: 0.2;\n\t\tborder-radius: var(--radius-md);\n\t}\n\n\t:global(::part(region-handle)) {\n\t\twidth: 5px !important;\n\t\tborder: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport { skip_audio, process_audio } from \"../shared/utils\";\n\timport WSRecord from \"wavesurfer.js/dist/plugins/record.js\";\n\timport WaveformControls from \"../shared/WaveformControls.svelte\";\n\timport WaveformRecordControls from \"../shared/WaveformRecordControls.svelte\";\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\timport { format_time } from \"@gradio/utils\";\n\n\texport let mode: string;\n\texport let i18n: I18nFormatter;\n\texport let dispatch_blob: (\n\t\tblobs: Uint8Array[] | Blob[],\n\t\tevent: \"stream\" | \"change\" | \"stop_recording\"\n\t) => Promise<void> | undefined;\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions = {\n\t\tshow_recording_waveform: true\n\t};\n\texport let handle_reset_value: () => void;\n\texport let editable = true;\n\texport let recording = false;\n\n\tlet micWaveform: WaveSurfer;\n\tlet recordingWaveform: WaveSurfer;\n\tlet playing = false;\n\n\tlet recordingContainer: HTMLDivElement;\n\tlet microphoneContainer: HTMLDivElement;\n\n\tlet record: WSRecord;\n\tlet recordedAudio: string | null = null;\n\n\t// timestamps\n\tlet timeRef: HTMLTimeElement;\n\tlet durationRef: HTMLTimeElement;\n\tlet audio_duration: number;\n\tlet seconds = 0;\n\tlet interval: NodeJS.Timeout;\n\tlet timing = false;\n\t// trimming\n\tlet trimDuration = 0;\n\n\tconst start_interval = (): void => {\n\t\tclearInterval(interval);\n\t\tinterval = setInterval(() => {\n\t\t\tseconds++;\n\t\t}, 1000);\n\t};\n\n\tconst dispatch = createEventDispatcher<{\n\t\tstart_recording: undefined;\n\t\tpause_recording: undefined;\n\t\tstop_recording: undefined;\n\t\tstop: undefined;\n\t\tplay: undefined;\n\t\tpause: undefined;\n\t\tend: undefined;\n\t\tedit: undefined;\n\t}>();\n\n\tfunction record_start_callback(): void {\n\t\tstart_interval();\n\t\ttiming = true;\n\t\tdispatch(\"start_recording\");\n\t\tif (waveform_options.show_recording_waveform) {\n\t\t\tlet waveformCanvas = microphoneContainer;\n\t\t\tif (waveformCanvas) waveformCanvas.style.display = \"block\";\n\t\t}\n\t}\n\n\tasync function record_end_callback(blob: Blob): Promise<void> {\n\t\tseconds = 0;\n\t\ttiming = false;\n\t\tclearInterval(interval);\n\t\ttry {\n\t\t\tconst array_buffer = await blob.arrayBuffer();\n\t\t\tconst context = new AudioContext({\n\t\t\t\tsampleRate: waveform_settings.sampleRate\n\t\t\t});\n\t\t\tconst audio_buffer = await context.decodeAudioData(array_buffer);\n\n\t\t\tif (audio_buffer)\n\t\t\t\tawait process_audio(audio_buffer).then(async (audio: Uint8Array) => {\n\t\t\t\t\tawait dispatch_blob([audio], \"change\");\n\t\t\t\t\tawait dispatch_blob([audio], \"stop_recording\");\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\tconsole.error(e);\n\t\t}\n\t}\n\n\t$: record?.on(\"record-resume\", () => {\n\t\tstart_interval();\n\t});\n\n\t$: recordingWaveform?.on(\"decode\", (duration: any) => {\n\t\taudio_duration = duration;\n\t\tdurationRef && (durationRef.textContent = format_time(duration));\n\t});\n\n\t$: recordingWaveform?.on(\n\t\t\"timeupdate\",\n\t\t(currentTime: any) =>\n\t\t\ttimeRef && (timeRef.textContent = format_time(currentTime))\n\t);\n\n\t$: recordingWaveform?.on(\"pause\", () => {\n\t\tdispatch(\"pause\");\n\t\tplaying = false;\n\t});\n\n\t$: recordingWaveform?.on(\"play\", () => {\n\t\tdispatch(\"play\");\n\t\tplaying = true;\n\t});\n\n\t$: recordingWaveform?.on(\"finish\", () => {\n\t\tdispatch(\"stop\");\n\t\tplaying = false;\n\t});\n\n\tconst create_mic_waveform = (): void => {\n\t\tif (microphoneContainer) microphoneContainer.innerHTML = \"\";\n\t\tif (micWaveform !== undefined) micWaveform.destroy();\n\t\tif (!microphoneContainer) return;\n\t\tmicWaveform = WaveSurfer.create({\n\t\t\t...waveform_settings,\n\t\t\tnormalize: false,\n\t\t\tcontainer: microphoneContainer\n\t\t});\n\n\t\trecord = micWaveform.registerPlugin(RecordPlugin.create());\n\t\trecord?.on(\"record-end\", record_end_callback);\n\t\trecord?.on(\"record-start\", record_start_callback);\n\t\trecord?.on(\"record-pause\", () => {\n\t\t\tdispatch(\"pause_recording\");\n\t\t\tclearInterval(interval);\n\t\t});\n\n\t\trecord?.on(\"record-end\", (blob) => {\n\t\t\trecordedAudio = URL.createObjectURL(blob);\n\n\t\t\tconst microphone = microphoneContainer;\n\t\t\tconst recording = recordingContainer;\n\n\t\t\tif (microphone) microphone.style.display = \"none\";\n\t\t\tif (recording && recordedAudio) {\n\t\t\t\trecording.innerHTML = \"\";\n\t\t\t\tcreate_recording_waveform();\n\t\t\t}\n\t\t});\n\t};\n\n\tconst create_recording_waveform = (): void => {\n\t\tlet recording = recordingContainer;\n\t\tif (!recordedAudio || !recording) return;\n\t\trecordingWaveform = WaveSurfer.create({\n\t\t\tcontainer: recording,\n\t\t\turl: recordedAudio,\n\t\t\t...waveform_settings\n\t\t});\n\t};\n\n\tconst handle_trim_audio = async (\n\t\tstart: number,\n\t\tend: number\n\t): Promise<void> => {\n\t\tmode = \"edit\";\n\t\tconst decodedData = recordingWaveform.getDecodedData();\n\t\tif (decodedData)\n\t\t\tawait process_audio(decodedData, start, end).then(\n\t\t\t\tasync (trimmedAudio: Uint8Array) => {\n\t\t\t\t\tawait dispatch_blob([trimmedAudio], \"change\");\n\t\t\t\t\tawait dispatch_blob([trimmedAudio], \"stop_recording\");\n\t\t\t\t\trecordingWaveform.destroy();\n\t\t\t\t\tcreate_recording_waveform();\n\t\t\t\t}\n\t\t\t);\n\t\tdispatch(\"edit\");\n\t};\n\n\tonMount(() => {\n\t\tcreate_mic_waveform();\n\n\t\twindow.addEventListener(\"keydown\", (e) => {\n\t\t\tif (e.key === \"ArrowRight\") {\n\t\t\t\tskip_audio(recordingWaveform, 0.1);\n\t\t\t} else if (e.key === \"ArrowLeft\") {\n\t\t\t\tskip_audio(recordingWaveform, -0.1);\n\t\t\t}\n\t\t});\n\t});\n</script>\n\n<div class=\"component-wrapper\">\n\t<div\n\t\tclass=\"microphone\"\n\t\tbind:this={microphoneContainer}\n\t\tdata-testid=\"microphone-waveform\"\n\t/>\n\t<div bind:this={recordingContainer} data-testid=\"recording-waveform\" />\n\n\t{#if (timing || recordedAudio) && waveform_options.show_recording_waveform}\n\t\t<div class=\"timestamps\">\n\t\t\t<time bind:this={timeRef} class=\"time\">0:00</time>\n\t\t\t<div>\n\t\t\t\t{#if mode === \"edit\" && trimDuration > 0}\n\t\t\t\t\t<time class=\"trim-duration\">{format_time(trimDuration)}</time>\n\t\t\t\t{/if}\n\t\t\t\t{#if timing}\n\t\t\t\t\t<time class=\"duration\">{format_time(seconds)}</time>\n\t\t\t\t{:else}\n\t\t\t\t\t<time bind:this={durationRef} class=\"duration\">0:00</time>\n\t\t\t\t{/if}\n\t\t\t</div>\n\t\t</div>\n\t{/if}\n\n\t{#if microphoneContainer && !recordedAudio}\n\t\t<WaveformRecordControls\n\t\t\tbind:record\n\t\t\t{i18n}\n\t\t\t{timing}\n\t\t\t{recording}\n\t\t\tshow_recording_waveform={waveform_options.show_recording_waveform}\n\t\t\trecord_time={format_time(seconds)}\n\t\t/>\n\t{/if}\n\n\t{#if recordingWaveform && recordedAudio}\n\t\t<WaveformControls\n\t\t\tbind:waveform={recordingWaveform}\n\t\t\tcontainer={recordingContainer}\n\t\t\t{playing}\n\t\t\t{audio_duration}\n\t\t\t{i18n}\n\t\t\t{editable}\n\t\t\tinteractive={true}\n\t\t\t{handle_trim_audio}\n\t\t\tbind:trimDuration\n\t\t\tbind:mode\n\t\t\tshow_redo\n\t\t\t{handle_reset_value}\n\t\t\t{waveform_options}\n\t\t/>\n\t{/if}\n</div>\n\n<style>\n\t.microphone {\n\t\twidth: 100%;\n\t\tdisplay: none;\n\t}\n\n\t.component-wrapper {\n\t\tpadding: var(--size-3);\n\t\twidth: 100%;\n\t}\n\n\t.timestamps {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\twidth: 100%;\n\t\tpadding: var(--size-1) 0;\n\t\tmargin: var(--spacing-md) 0;\n\t}\n\n\t.time {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t.duration {\n\t\tcolor: var(--neutral-400);\n\t}\n\n\t.trim-duration {\n\t\tcolor: var(--color-accent);\n\t\tmargin-right: var(--spacing-sm);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onMount } from \"svelte\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\timport { Spinner } from \"@gradio/icons\";\n\timport WaveSurfer from \"wavesurfer.js\";\n\timport RecordPlugin from \"wavesurfer.js/dist/plugins/record.js\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\timport DeviceSelect from \"../shared/DeviceSelect.svelte\";\n\n\texport let recording = false;\n\texport let paused_recording = false;\n\texport let stop: () => void;\n\texport let record: () => void;\n\texport let i18n: I18nFormatter;\n\texport let waveform_settings: Record<string, any>;\n\texport let waveform_options: WaveformOptions = {\n\t\tshow_recording_waveform: true\n\t};\n\texport let waiting = false;\n\n\tlet micWaveform: WaveSurfer;\n\tlet waveformRecord: RecordPlugin;\n\n\tlet microphoneContainer: HTMLDivElement;\n\n\tlet micDevices: MediaDeviceInfo[] = [];\n\n\tonMount(() => {\n\t\tcreate_mic_waveform();\n\t});\n\n\tconst create_mic_waveform = (): void => {\n\t\tif (micWaveform !== undefined) micWaveform.destroy();\n\t\tif (!microphoneContainer) return;\n\t\tmicWaveform = WaveSurfer.create({\n\t\t\t...waveform_settings,\n\t\t\theight: 100,\n\t\t\tcontainer: microphoneContainer\n\t\t});\n\n\t\twaveformRecord = micWaveform.registerPlugin(RecordPlugin.create());\n\t};\n</script>\n\n<div class=\"mic-wrap\">\n\t{#if waveform_options.show_recording_waveform}\n\t\t<div\n\t\t\tbind:this={microphoneContainer}\n\t\t\tstyle:display={recording ? \"block\" : \"none\"}\n\t\t/>\n\t{/if}\n\t<div class=\"controls\">\n\t\t{#if recording && !waiting}\n\t\t\t<button\n\t\t\t\tclass={paused_recording ? \"stop-button-paused\" : \"stop-button\"}\n\t\t\t\ton:click={() => {\n\t\t\t\t\twaveformRecord?.stopMic();\n\t\t\t\t\tstop();\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<span class=\"record-icon\">\n\t\t\t\t\t<span class=\"pinger\" />\n\t\t\t\t\t<span class=\"dot\" />\n\t\t\t\t</span>\n\t\t\t\t{paused_recording ? i18n(\"audio.pause\") : i18n(\"audio.stop\")}\n\t\t\t</button>\n\t\t{:else if recording && waiting}\n\t\t\t<button\n\t\t\t\tclass=\"spinner-button\"\n\t\t\t\ton:click={() => {\n\t\t\t\t\tstop();\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<div class=\"icon\">\n\t\t\t\t\t<Spinner />\n\t\t\t\t</div>\n\t\t\t\t{i18n(\"audio.waiting\")}\n\t\t\t</button>\n\t\t{:else}\n\t\t\t<button\n\t\t\t\tclass=\"record-button\"\n\t\t\t\ton:click={() => {\n\t\t\t\t\twaveformRecord?.startMic();\n\t\t\t\t\trecord();\n\t\t\t\t}}\n\t\t\t>\n\t\t\t\t<span class=\"record-icon\">\n\t\t\t\t\t<span class=\"dot\" />\n\t\t\t\t</span>\n\t\t\t\t{i18n(\"audio.record\")}\n\t\t\t</button>\n\t\t{/if}\n\n\t\t<DeviceSelect bind:micDevices {i18n} />\n\t</div>\n</div>\n\n<style>\n\t.controls {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.mic-wrap {\n\t\tdisplay: block;\n\t\talign-items: center;\n\t\tmargin: var(--spacing-xl);\n\t}\n\n\t.icon {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tfill: var(--primary-600);\n\t\tstroke: var(--primary-600);\n\t}\n\n\t.stop-button-paused {\n\t\tdisplay: none;\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--button-large-radius);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--block-border-color);\n\t\tmargin-right: 5px;\n\t}\n\n\t.stop-button-paused::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\n\t.stop-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t\tanimation: scaling 1800ms infinite;\n\t}\n\n\t.stop-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-20);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--button-large-radius);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--primary-600);\n\t\tmargin-right: 5px;\n\t\tdisplay: flex;\n\t}\n\n\t.spinner-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-24);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--radius-3xl);\n\t\talign-items: center;\n\t\tborder: 1px solid var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t\tdisplay: flex;\n\t\tjustify-content: space-evenly;\n\t}\n\n\t.record-button::before {\n\t\tcontent: \"\";\n\t\theight: var(--size-4);\n\t\twidth: var(--size-4);\n\t\tborder-radius: var(--radius-full);\n\t\tbackground: var(--primary-600);\n\t\tmargin: 0 var(--spacing-xl);\n\t}\n\n\t.record-button {\n\t\theight: var(--size-8);\n\t\twidth: var(--size-24);\n\t\tbackground-color: var(--block-background-fill);\n\t\tborder-radius: var(--button-large-radius);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tborder: 1px solid var(--block-border-color);\n\t}\n\n\t@keyframes scaling {\n\t\t0% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t\t50% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1.2;\n\t\t}\n\t\t100% {\n\t\t\tbackground-color: var(--primary-600);\n\t\t\tscale: 1;\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onD<PERSON><PERSON>, createEventDispatcher, tick } from \"svelte\";\n\timport { Upload, ModifyUpload } from \"@gradio/upload\";\n\timport { prepare_files, type FileData, type Client } from \"@gradio/client\";\n\timport { BlockLabel } from \"@gradio/atoms\";\n\timport { Music } from \"@gradio/icons\";\n\timport { StreamingBar } from \"@gradio/statustracker\";\n\timport AudioPlayer from \"../player/AudioPlayer.svelte\";\n\n\timport type { IBlobEvent, IMediaRecorder } from \"extendable-media-recorder\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport AudioRecorder from \"../recorder/AudioRecorder.svelte\";\n\timport StreamAudio from \"../streaming/StreamAudio.svelte\";\n\timport { SelectSource } from \"@gradio/atoms\";\n\timport type { WaveformOptions } from \"../shared/types\";\n\n\texport let value: null | FileData = null;\n\texport let label: string;\n\texport let root: string;\n\texport let loop: boolean;\n\texport let show_label = true;\n\texport let show_download_button = false;\n\texport let sources:\n\t\t| [\"microphone\"]\n\t\t| [\"upload\"]\n\t\t| [\"microphone\", \"upload\"]\n\t\t| [\"upload\", \"microphone\"] = [\"microphone\", \"upload\"];\n\texport let pending = false;\n\texport let streaming = false;\n\texport let i18n: I18nFormatter;\n\texport let waveform_settings: Record<string, any>;\n\texport let trim_region_settings = {};\n\texport let waveform_options: WaveformOptions = {};\n\texport let dragging: boolean;\n\texport let active_source: \"microphone\" | \"upload\";\n\texport let handle_reset_value: () => void = () => {};\n\texport let editable = true;\n\texport let max_file_size: number | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let stream_handler: Client[\"stream\"];\n\texport let stream_every: number;\n\texport let uploading = false;\n\texport let recording = false;\n\texport let class_name = \"\";\n\n\tlet time_limit: number | null = null;\n\tlet stream_state: \"open\" | \"waiting\" | \"closed\" = \"closed\";\n\n\texport const modify_stream: (state: \"open\" | \"closed\" | \"waiting\") => void = (\n\t\tstate: \"open\" | \"closed\" | \"waiting\"\n\t) => {\n\t\tif (state === \"closed\") {\n\t\t\ttime_limit = null;\n\t\t\tstream_state = \"closed\";\n\t\t} else if (state === \"waiting\") {\n\t\t\tstream_state = \"waiting\";\n\t\t} else {\n\t\t\tstream_state = \"open\";\n\t\t}\n\t};\n\n\texport const set_time_limit = (time: number): void => {\n\t\tif (recording) time_limit = time;\n\t};\n\n\t$: dispatch(\"drag\", dragging);\n\n\t// TODO: make use of this\n\t// export let type: \"normal\" | \"numpy\" = \"normal\";\n\tlet recorder: IMediaRecorder;\n\tlet mode = \"\";\n\tlet header: Uint8Array | undefined = undefined;\n\tlet pending_stream: Uint8Array[] = [];\n\tlet submit_pending_stream_on_pending_end = false;\n\tlet inited = false;\n\n\tconst NUM_HEADER_BYTES = 44;\n\tlet audio_chunks: Blob[] = [];\n\tlet module_promises: [\n\t\tPromise<typeof import(\"extendable-media-recorder\")>,\n\t\tPromise<typeof import(\"extendable-media-recorder-wav-encoder\")>\n\t];\n\n\tfunction get_modules(): void {\n\t\tmodule_promises = [\n\t\t\timport(\"extendable-media-recorder\"),\n\t\t\timport(\"extendable-media-recorder-wav-encoder\")\n\t\t];\n\t}\n\n\tconst is_browser = typeof window !== \"undefined\";\n\tif (is_browser && streaming) {\n\t\tget_modules();\n\t}\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: FileData | null;\n\t\tstream: FileData;\n\t\tedit: never;\n\t\tplay: never;\n\t\tpause: never;\n\t\tstop: never;\n\t\tend: never;\n\t\tdrag: boolean;\n\t\terror: string;\n\t\tupload: FileData;\n\t\tclear: undefined;\n\t\tstart_recording: undefined;\n\t\tpause_recording: undefined;\n\t\tstop_recording: undefined;\n\t\tclose_stream: undefined;\n\t}>();\n\n\tconst dispatch_blob = async (\n\t\tblobs: Uint8Array[] | Blob[],\n\t\tevent: \"stream\" | \"change\" | \"stop_recording\"\n\t): Promise<void> => {\n\t\tlet _audio_blob = new File(blobs, \"audio.wav\");\n\t\tconst val = await prepare_files([_audio_blob], event === \"stream\");\n\t\tvalue = (\n\t\t\t(await upload(val, root, undefined, max_file_size || undefined))?.filter(\n\t\t\t\tBoolean\n\t\t\t) as FileData[]\n\t\t)[0];\n\t\tdispatch(event, value);\n\t};\n\n\tonDestroy(() => {\n\t\tif (streaming && recorder && recorder.state !== \"inactive\") {\n\t\t\trecorder.stop();\n\t\t}\n\t});\n\n\tasync function prepare_audio(): Promise<void> {\n\t\tlet stream: MediaStream | null;\n\n\t\ttry {\n\t\t\tstream = await navigator.mediaDevices.getUserMedia({ audio: true });\n\t\t} catch (err) {\n\t\t\tif (!navigator.mediaDevices) {\n\t\t\t\tdispatch(\"error\", i18n(\"audio.no_device_support\"));\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (err instanceof DOMException && err.name == \"NotAllowedError\") {\n\t\t\t\tdispatch(\"error\", i18n(\"audio.allow_recording_access\"));\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthrow err;\n\t\t}\n\t\tif (stream == null) return;\n\n\t\tif (streaming) {\n\t\t\tconst [{ MediaRecorder, register }, { connect }] =\n\t\t\t\tawait Promise.all(module_promises);\n\t\t\tawait register(await connect());\n\t\t\trecorder = new MediaRecorder(stream, { mimeType: \"audio/wav\" });\n\t\t\trecorder.addEventListener(\"dataavailable\", handle_chunk);\n\t\t} else {\n\t\t\trecorder = new MediaRecorder(stream);\n\t\t\trecorder.addEventListener(\"dataavailable\", (event) => {\n\t\t\t\taudio_chunks.push(event.data);\n\t\t\t});\n\t\t}\n\t\trecorder.addEventListener(\"stop\", async () => {\n\t\t\trecording = false;\n\t\t\t// recorder.stop();\n\t\t\tawait dispatch_blob(audio_chunks, \"change\");\n\t\t\tawait dispatch_blob(audio_chunks, \"stop_recording\");\n\t\t\taudio_chunks = [];\n\t\t});\n\t\tinited = true;\n\t}\n\n\tasync function handle_chunk(event: IBlobEvent): Promise<void> {\n\t\tlet buffer = await event.data.arrayBuffer();\n\t\tlet payload = new Uint8Array(buffer);\n\t\tif (!header) {\n\t\t\theader = new Uint8Array(buffer.slice(0, NUM_HEADER_BYTES));\n\t\t\tpayload = new Uint8Array(buffer.slice(NUM_HEADER_BYTES));\n\t\t}\n\t\tif (pending) {\n\t\t\tpending_stream.push(payload);\n\t\t} else {\n\t\t\tlet blobParts = [header].concat(pending_stream, [payload]);\n\t\t\tif (!recording || stream_state === \"waiting\") return;\n\t\t\tdispatch_blob(blobParts, \"stream\");\n\t\t\tpending_stream = [];\n\t\t}\n\t}\n\n\t$: if (submit_pending_stream_on_pending_end && pending === false) {\n\t\tsubmit_pending_stream_on_pending_end = false;\n\t\tif (header && pending_stream) {\n\t\t\tlet blobParts: Uint8Array[] = [header].concat(pending_stream);\n\t\t\tpending_stream = [];\n\t\t\tdispatch_blob(blobParts, \"stream\");\n\t\t}\n\t}\n\n\tasync function record(): Promise<void> {\n\t\trecording = true;\n\t\tdispatch(\"start_recording\");\n\t\tif (!inited) await prepare_audio();\n\t\theader = undefined;\n\t\tif (streaming && recorder.state != \"recording\") {\n\t\t\trecorder.start(stream_every * 1000);\n\t\t}\n\t}\n\n\tfunction clear(): void {\n\t\tdispatch(\"change\", null);\n\t\tdispatch(\"clear\");\n\t\tmode = \"\";\n\t\tvalue = null;\n\t}\n\n\tfunction handle_load({ detail }: { detail: FileData }): void {\n\t\tvalue = detail;\n\t\tdispatch(\"change\", detail);\n\t\tdispatch(\"upload\", detail);\n\t}\n\n\tasync function stop(): Promise<void> {\n\t\trecording = false;\n\n\t\tif (streaming) {\n\t\t\tdispatch(\"close_stream\");\n\t\t\tdispatch(\"stop_recording\");\n\t\t\trecorder.stop();\n\n\t\t\tif (pending) {\n\t\t\t\tsubmit_pending_stream_on_pending_end = true;\n\t\t\t}\n\t\t\tdispatch_blob(audio_chunks, \"stop_recording\");\n\t\t\tdispatch(\"clear\");\n\t\t\tmode = \"\";\n\t\t}\n\t}\n\n\t$: if (!recording && recorder) stop();\n\t$: if (recording && recorder) record();\n</script>\n\n<BlockLabel\n\t{show_label}\n\tIcon={Music}\n\tfloat={active_source === \"upload\" && value === null}\n\tlabel={label || i18n(\"audio.audio\")}\n/>\n<div class=\"audio-container {class_name}\">\n\t<StreamingBar {time_limit} />\n\t{#if value === null || streaming}\n\t\t{#if active_source === \"microphone\"}\n\t\t\t<ModifyUpload {i18n} on:clear={clear} />\n\t\t\t{#if streaming}\n\t\t\t\t<StreamAudio\n\t\t\t\t\t{record}\n\t\t\t\t\t{recording}\n\t\t\t\t\t{stop}\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{waveform_settings}\n\t\t\t\t\t{waveform_options}\n\t\t\t\t\twaiting={stream_state === \"waiting\"}\n\t\t\t\t/>\n\t\t\t{:else}\n\t\t\t\t<AudioRecorder\n\t\t\t\t\tbind:mode\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{editable}\n\t\t\t\t\t{recording}\n\t\t\t\t\t{dispatch_blob}\n\t\t\t\t\t{waveform_settings}\n\t\t\t\t\t{waveform_options}\n\t\t\t\t\t{handle_reset_value}\n\t\t\t\t\ton:start_recording\n\t\t\t\t\ton:pause_recording\n\t\t\t\t\ton:stop_recording\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t{:else if active_source === \"upload\"}\n\t\t\t<!-- explicitly listed out audio mimetypes due to iOS bug not recognizing audio/* -->\n\t\t\t<Upload\n\t\t\t\tfiletype=\"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*\"\n\t\t\t\ton:load={handle_load}\n\t\t\t\tbind:dragging\n\t\t\t\tbind:uploading\n\t\t\t\ton:error={({ detail }) => dispatch(\"error\", detail)}\n\t\t\t\t{root}\n\t\t\t\t{max_file_size}\n\t\t\t\t{upload}\n\t\t\t\t{stream_handler}\n\t\t\t\taria_label={i18n(\"audio.drop_to_upload\")}\n\t\t\t>\n\t\t\t\t<slot />\n\t\t\t</Upload>\n\t\t{/if}\n\t{:else}\n\t\t<ModifyUpload\n\t\t\t{i18n}\n\t\t\ton:clear={clear}\n\t\t\ton:edit={() => (mode = \"edit\")}\n\t\t\tdownload={show_download_button ? value.url : null}\n\t\t/>\n\n\t\t<AudioPlayer\n\t\t\tbind:mode\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{i18n}\n\t\t\t{dispatch_blob}\n\t\t\t{waveform_settings}\n\t\t\t{waveform_options}\n\t\t\t{trim_region_settings}\n\t\t\t{handle_reset_value}\n\t\t\t{editable}\n\t\t\t{loop}\n\t\t\tinteractive\n\t\t\ton:stop\n\t\t\ton:play\n\t\t\ton:pause\n\t\t\ton:edit\n\t\t/>\n\t{/if}\n\t<SelectSource {sources} bind:active_source handle_clear={clear} />\n</div>\n\n<style>\n\t.audio-container {\n\t\theight: calc(var(--size-full) - var(--size-6));\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t}\n\n\t.audio-container.compact-audio {\n\t\tmargin-top: calc(var(--size-8) * -1);\n\t\theight: auto;\n\t\tpadding: 0px;\n\t\tgap: var(--size-2);\n\t\tmin-height: var(--size-5);\n\t}\n\n\t.compact-audio :global(.audio-player) {\n\t\tpadding: 0px;\n\t}\n\n\t.compact-audio :global(.controls) {\n\t\tgap: 0px;\n\t\tpadding: 0px;\n\t}\n\n\t.compact-audio :global(.waveform-container) {\n\t\theight: var(--size-12) !important;\n\t}\n\n\t.compact-audio :global(.player-container) {\n\t\tmin-height: unset;\n\t\theight: auto;\n\t}\n</style>\n"], "file": "assets/InteractiveAudio-DKSUMNnC.js"}