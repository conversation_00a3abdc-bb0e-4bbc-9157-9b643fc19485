{".class": "MypyFile", "_fullname": "src.agents.writing_agent", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AGENT_REGISTRY": {".class": "SymbolTableNode", "cross_ref": "src.core.agent.AGENT_REGISTRY", "kind": "Gdef"}, "AI_MODEL_CONFIG": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.AI_MODEL_CONFIG", "kind": "Gdef"}, "Agent": {".class": "SymbolTableNode", "cross_ref": "src.core.agent.Agent", "kind": "Gdef"}, "AgentRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.agents.writing_agent.AgentRegistry", "name": "AgentRegistry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.AgentRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.agents.writing_agent", "mro": ["src.agents.writing_agent.AgentRegistry", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.AgentRegistry.__init__", "name": "__init__", "type": null}}, "agents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.writing_agent.AgentRegistry.agents", "name": "agents", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.AgentRegistry.get_agent", "name": "get_agent", "type": null}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "agent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.AgentRegistry.register", "name": "register", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.agents.writing_agent.AgentRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.agents.writing_agent.AgentRegistry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DEFAULT_MODEL_NAMES": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.DEFAULT_MODEL_NAMES", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PromptManager": {".class": "SymbolTableNode", "cross_ref": "src.core.prompt_manager.PromptManager", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WritingAgent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["src.core.agent.Agent"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.agents.writing_agent.WritingAgent", "name": "WritingAgent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.agents.writing_agent", "mro": ["src.agents.writing_agent.WritingAgent", "src.core.agent.Agent", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "model"], "arg_types": ["src.agents.writing_agent.WritingAgent", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WritingAgent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_character_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent._extract_character_status", "name": "_extract_character_status", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["src.agents.writing_agent.WritingAgent", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_character_status of WritingAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_key_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent._extract_key_events", "name": "_extract_key_events", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["src.agents.writing_agent.WritingAgent", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_key_events of WritingAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_location_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent._extract_location_info", "name": "_extract_location_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["src.agents.writing_agent.WritingAgent", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_location_info of WritingAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_time_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent._extract_time_info", "name": "_extract_time_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["src.agents.writing_agent.WritingAgent", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_time_info of WritingAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_previous_chapter_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "chapter_context", "prev_chapter_num"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent._load_previous_chapter_content", "name": "_load_previous_chapter_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "chapter_context", "prev_chapter_num"], "arg_types": ["src.agents.writing_agent.WritingAgent", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_previous_chapter_content of WritingAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_llm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prompt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent.call_llm", "name": "call_llm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prompt"], "arg_types": ["src.agents.writing_agent.WritingAgent", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_llm of WritingAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_outline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "novel_title", "novel_genre", "novel_style", "novel_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent.generate_outline", "name": "generate_outline", "type": null}}, "get_previous_chapter_ending": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chapter_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent.get_previous_chapter_ending", "name": "get_previous_chapter_ending", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chapter_context"], "arg_types": ["src.agents.writing_agent.WritingAgent", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_previous_chapter_ending of WritingAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent.handle_message", "name": "handle_message", "type": null}}, "prompt_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.writing_agent.WritingAgent.prompt_manager", "name": "prompt_manager", "type": "src.core.prompt_manager.PromptManager"}}, "write_chapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chapter_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent.write_chapter", "name": "write_chapter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chapter_context"], "arg_types": ["src.agents.writing_agent.WritingAgent", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_chapter of WritingAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "title", "genre", "style", "scene", "characters", "previous_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.writing_agent.WritingAgent.write_content", "name": "write_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "title", "genre", "style", "scene", "characters", "previous_content"], "arg_types": ["src.agents.writing_agent.WritingAgent", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_content of WritingAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.agents.writing_agent.WritingAgent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.agents.writing_agent.WritingAgent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agent.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agent.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agent.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agent.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agent.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.writing_agent.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "prompt_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.agents.writing_agent.prompt_manager", "name": "prompt_manager", "type": "src.core.prompt_manager.PromptManager"}}}, "path": "E:\\storymc\\src\\agents\\writing_agent.py"}