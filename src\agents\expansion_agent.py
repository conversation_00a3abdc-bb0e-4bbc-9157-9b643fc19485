"""
扩展智能体模块
负责扩展和丰富小说内容
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, cast

try:
    from src.core.agent import Agent, AGENT_REGISTRY
except ImportError:
    # 如果导入失败，创建简单的Agent类
    class Agent:
        def __init__(self, name, model=None):
            self.name = name
            self.model = model

    # 创建简单的AGENT_REGISTRY类
    class AgentRegistry:
        def __init__(self):
            self.agents = {}

        def register(self, agent):
            self.agents[agent.name] = agent
            return agent

        def get_agent(self, name):
            return self.agents.get(name)

    AGENT_REGISTRY = AgentRegistry()

try:
    from src.core.prompt_manager import PromptManager
except ImportError:
    # 如果导入失败，创建简单的PromptManager类
    class PromptManager:
        def get_prompt(self, prompt_name, **kwargs):
            return f"提示词 {prompt_name} 示例内容"

try:
    from src.core.model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES
except ImportError:
    # 如果导入失败，创建简单的配置
    AI_MODEL_CONFIG = {
        "agent_models": {},
        "default_model": "glm-4-flash"
    }
    DEFAULT_MODEL_NAMES = {"glm": "glm-4-flash"}

# 初始化提示词管理器
prompt_manager = PromptManager()

class ExpansionAgent(Agent):
    """
    扩写智能体，负责扩展和丰富小说内容
    """

    def __init__(self, name: str, model: Optional[str] = None):
        """初始化扩写智能体"""
        # 确保AI_MODEL_CONFIG是字典类型
        agent_models = cast(Dict[str, Any], AI_MODEL_CONFIG.get("agent_models", {}))
        default_model = cast(str, AI_MODEL_CONFIG.get("default_model", ""))

        # 获取模型名称
        model = model or agent_models.get("ExpansionAgent", default_model)
        super().__init__(name, model)
        self.prompt_manager = prompt_manager

    def handle_message(self, msg):
        """处理接收到的消息"""
        if msg['type'] == 'content':
            print(f"扩写智能体接收到内容...")
            expanded_content = self.expand_content(msg['content'])

            # 发送结果给下一个智能体（如果指定了）
            if 'next_agent' in msg:
                self.send_message(
                    msg['next_agent'],
                    {
                        'type': 'content',
                        'content': expanded_content,
                        'metadata': msg.get('metadata', {})
                    }
                )

            return expanded_content

    def expand_content(self, original_text: str) -> str:
        """
        扩展小说内容，使用更智能的方式进行有针对性的扩展，并去除重复内容

        参数:
            original_text: 原始文本

        返回:
            扩展后的内容
        """
        print("正在分析内容并进行有针对性扩展...")

        # 检查原始内容长度
        original_length = len(original_text)
        print(f"原始内容长度: {original_length} 字符")

        # 检查内容是否包含错误信息或提示词
        if original_length < 100 or "错误" in original_text[:100] or "抱歉" in original_text[:100]:
            print("检测到内容可能是提示词或内容过短，将生成真正的小说内容")
            # 使用直接提示生成内容
            direct_prompt = f"""
你是一位专业的中文小说作家，请创作一个完整的小说章节。

原始内容（可能包含错误或提示词）：
{original_text[:200]}...

请创作一个完整的小说章节，要求：
1. 内容丰富，情节连贯，场景描写生动
2. 包含适量的对话和心理描写
3. 字数在5000-8000字之间
4. 不要包含任何提示词或AI自我指代
5. 直接给出章节内容，不需要额外的解释

请直接给出章节内容，不需要额外的解释。
"""
            expanded_content = self.call_llm(direct_prompt)
            print(f"生成完整的小说章节内容，长度: {len(expanded_content)} 字符")

            # 如果生成的内容仍然太短，尝试再次扩充
            if len(expanded_content) < 3000:
                print(f"警告: 生成的内容长度仅为 {len(expanded_content)} 字符，将尝试扩充内容")
                expansion_prompt = f"""
你是一位专业的中文小说扩写专家。请对以下小说章节进行合理扩展：

原文：
{expanded_content}

扩写要求：
1. 场景细节：增加场景的细节描写，包括环境、气氛、光线、声音等元素，使场景更加立体。
2. 情感表达：丰富情感层次，通过细腻的描写展现角色的情感变化和内心冲突。
3. 背景补充：适当补充故事背景和世界观设定，增强故事的深度和可信度。
4. 字数要求：扩展后的内容应达到8000字以上。
5. 避免重复和出现提示词内容，整体保持一致。

请直接给出扩写后的内容，不需要解释扩写思路。
"""
                expanded_content = self.call_llm(expansion_prompt)
                print(f"内容再次扩充，当前长度: {len(expanded_content)} 字符")

            return self._remove_duplicates(expanded_content)

        # 如果原文字数已经超过3000字，直接跳过扩写步骤
        if original_length >= 3000:
            print(f"原文字数已超过3000字（{original_length}字），跳过扩写步骤")
            return original_text

        # 目标长度设置为3000字以上，不设置上限，避免过度重复
        target_length = max(3000, int(original_length * 1.5))  # 至少扩展到3000字，不设置上限
        print(f"目标长度设置为 {target_length} 字符，当前长度 {original_length} 字符")

        # 如果内容已经足够长，只进行质量提升而不大幅增加字数
        if original_length >= 8000:
            print(f"内容长度已达到{original_length}字，进行质量提升而非大幅扩展")
            quality_prompt = f"""
你是一位专业的小说编辑，请对以下小说章节进行质量提升，不需要大幅增加字数：

{original_text}

请重点提升以下方面：
1. 使场景描写更加生动和有气氛
2. 增强人物对话的真实感和个性化
3. 优化情节过渡和节奏
4. 增强关键情节的戏剧性和引人入胜的程度
5. 使用更丰富的修辞手法和表现力更强的语言
6. 检测并删除任何重复的段落或内容

请直接给出提升后的完整内容，不需要额外的解释。
"""
            enhanced_content = self.call_llm(quality_prompt)
            print(f"内容质量提升完成，当前字数: {len(enhanced_content)} 字")
            return self._remove_duplicates(enhanced_content)

        # 对于需要扩展的内容
        print(f"内容需要扩展，目标长度: {target_length} 字")

        # 构建扩写提示词
        expansion_prompt = f"""
你是一位专业的中文小说扩写专家，擅长对小说内容进行合理的扩展和丰富。请对以下小说内容进行扩写：

原文：
{original_text}

扩写要求：
1. 场景细节：增加场景的细节描写，包括环境、气氛、光线、声音等元素，使场景更加立体。
2. 角色刻画：深化角色形象，增加角色的外貌、动作、表情、心理活动等描写，使角色更加丰满。
3. 情感表达：丰富情感层次，通过细腻的描写展现角色的情感变化和内心冲突。
4. 对话扩展：适当扩展对话内容，通过对话展现角色性格和推动情节发展。
5. 背景补充：适当补充故事背景和世界观设定，增强故事的深度和可信度。
6. 情节丰富：在不改变主要情节走向的前提下，增加合理的情节支线或细节。
7. 字数要求：扩写后的内容应达到{target_length}字以上，但质量比字数更重要。
8. 避免重复：不要为了凑字数而生成重复内容，确保每个段落都有独特的信息。
9. 避免提示词：不要在内容中出现提示词痕迹，整体风格保持一致。

请直接给出扩写后的内容，不需要解释扩写思路。扩写应当自然融入原文，保持风格一致，避免生硬的拼接感。
"""

        # 调用LLM扩展内容
        expanded_content = self.call_llm(expansion_prompt)

        # 检查并删除重复段落
        expanded_content = self._remove_duplicates(expanded_content)

        # 如果扩展后仍不足目标长度，再次尝试扩展
        if len(expanded_content) < target_length * 0.8:  # 如果不足目标的80%
            print(f"扩展后内容仍不足目标长度，再次扩展...")
            detail_prompt = f"""
你是一位专业的中文小说创作者，请对以下内容进行详细扩展，使其更加生动、丰富：

{expanded_content}

请重点扩展以下方面：
1. 增加更多的环境描写和细节
2. 丰富人物的心理活动和对话
3. 增加情节的铺垫和过渡
4. 使用更多的修辞手法和生动的语言

请确保扩展后的内容达到{target_length}字以上，当前内容约{len(expanded_content)}字。注意质量比字数更重要，避免生成重复内容。
请直接给出扩展后的完整内容，不需要额外的解释或说明。
"""
            expanded_content = self.call_llm(detail_prompt)

        print(f"内容扩展完成，从 {original_length} 扩展到 {len(expanded_content)} 字符")
        return self._remove_duplicates(expanded_content)

    def _remove_duplicates(self, text: str) -> str:
        """
        增强版的重复内容检测和删除函数，更严格的相似度检测

        参数:
            text: 原始文本

        返回:
            去除重复后的文本
        """
        # 按段落分割文本
        paragraphs = text.split('\n\n')

        # 如果段落数量太少，不处理
        if len(paragraphs) < 5:
            return text

        # 创建一个列表存储不重复的段落
        unique_paragraphs = []
        # 存储段落的指纹(简化内容)用于相似度检测
        paragraph_fingerprints = []
        # 存储已见过的段落内容（用于完全相同的检测）
        seen_paragraphs = set()

        # 统计重复段落
        duplicate_count = 0
        similar_count = 0

        for para in paragraphs:
            # 忽略空段落
            if not para.strip():
                unique_paragraphs.append(para)
                continue

            # 如果段落太短，直接保留
            if len(para) < 50:
                unique_paragraphs.append(para)
                continue

            # 检查是否与已有段落完全相同
            if para.strip() in seen_paragraphs:
                duplicate_count += 1
                continue

            # 创建段落的指纹 - 提取关键词和句子结构
            # 简化版：取段落中的关键词组合
            words = para.strip().split()
            if len(words) > 10:
                # 取段落中的部分关键词作为指纹
                fingerprint = ' '.join(sorted([w for w in words if len(w) > 1])[:20])
            else:
                fingerprint = para.strip()

            # 检查是否与已有段落相似
            is_similar = False
            for i, existing_fp in enumerate(paragraph_fingerprints):
                # 简单的相似度检测
                similarity = self._similarity(fingerprint, existing_fp)

                # 降低相似度阈值，更严格地检测相似内容
                similarity_threshold = 0.6  # 降低阈值，更容易检测到相似内容

                if similarity > similarity_threshold:
                    # 如果相似度超过阈值，进一步检查段落内容
                    # 获取原段落内容进行比较
                    existing_para = unique_paragraphs[i]

                    # 计算段落长度的比例，如果长度相差太大，可能不是重复内容
                    len_ratio = min(len(para), len(existing_para)) / max(len(para), len(existing_para))

                    # 如果长度比例超过0.7（长度相近）且相似度超过阈值
                    if len_ratio > 0.7 and similarity > similarity_threshold:
                        is_similar = True
                        similar_count += 1
                        break

            if not is_similar:
                # 添加到不重复段落列表
                unique_paragraphs.append(para)
                paragraph_fingerprints.append(fingerprint)
                seen_paragraphs.add(para.strip())

        # 如果检测到重复段落，打印日志
        if duplicate_count > 0 or similar_count > 0:
            print(f"检测到并删除了 {duplicate_count} 个完全重复的段落和 {similar_count} 个高度相似的段落")

        # 重新组合文本
        return '\n\n'.join(unique_paragraphs)

    def _similarity(self, str1: str, str2: str) -> float:
        """
        计算两个字符串的相似度

        参数:
            str1: 第一个字符串
            str2: 第二个字符串

        返回:
            相似度，范围0-1
        """
        # 简单的Jaccard相似度
        set1 = set(str1.split())
        set2 = set(str2.split())

        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0
