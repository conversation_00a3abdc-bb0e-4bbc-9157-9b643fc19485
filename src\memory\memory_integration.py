"""
Memory Integration Module

This module integrates the memory management system with the agent orchestration system.
It provides functions to initialize and use the memory system during novel generation.
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
import traceback

# 尝试导入嵌入管理器
try:
    from src.memory.embedding_manager import (
        get_embedding_manager, get_embedding,
        search_documents, rerank_documents
    )
    EMBEDDING_ENABLED = True
    print("Embedding manager enabled")
except ImportError as e:
    print(f"Warning: Embedding manager not found: {e}")
    EMBEDDING_ENABLED = False

try:
    # Import memory management components
    from src.memory.memory_manager import MemoryManager, SlidingWindowCache, EntityStore, PlotSummary
except ImportError:
    print("Warning: Memory management components not found, using simplified versions")

    # Create simplified versions if imports fail
    class SlidingWindowCache:
        def __init__(self, max_size=10):
            self.segments = []

        def add(self, segment):
            self.segments.append(segment)

        def get_recent(self, num_segments=None):
            return "\n\n".join(self.segments[-num_segments:] if num_segments else self.segments)

        def clear(self):
            self.segments.clear()

    class EntityStore:
        def __init__(self):
            self.entities = {}
            self.relationships = {}
            self.clues = {}

        def add_entity(self, name, attributes):
            self.entities[name] = attributes

        def get_active_clues(self):
            return []

        def save(self, filepath):
            pass

        def load(self, filepath):
            return False

    class PlotSummary:
        def __init__(self):
            self.chapter_summaries = {}
            self.global_summary = ""

        def add_chapter_summary(self, chapter_num, summary):
            self.chapter_summaries[chapter_num] = summary

        def save(self, filepath):
            pass

        def load(self, filepath):
            return False

    class MemoryManager:
        def __init__(self, novel_dir):
            self.novel_dir = novel_dir
            self.short_term = SlidingWindowCache()
            self.medium_term = EntityStore()
            self.long_term = PlotSummary()

        def add_segment(self, segment):
            self.short_term.add(segment)

        def get_context_for_generation(self, llm_caller=None):
            return {
                "recent_content": self.short_term.get_recent(),
                "global_summary": self.long_term.global_summary,
                "active_clues": self.medium_term.get_active_clues()
            }

        def extract_entities_from_text(self, text, llm_caller=None):
            pass

        def summarize_chapter(self, chapter_num, chapter_content, llm_caller=None):
            self.long_term.add_chapter_summary(chapter_num, chapter_content[:500])

        def save_memory(self):
            pass

# Global memory manager instance
_memory_manager = None

def get_memory_manager(novel_dir: str) -> MemoryManager:
    """
    Get or create a memory manager for the given novel directory

    Args:
        novel_dir: Novel directory

    Returns:
        Memory manager instance
    """
    global _memory_manager

    if _memory_manager is None or _memory_manager.novel_dir != novel_dir:
        _memory_manager = MemoryManager(novel_dir)
        print(f"Created new memory manager for {novel_dir}")

    return _memory_manager

def enhance_prompt_with_memory(prompt: str, novel_dir: str, llm_caller: Callable = None) -> str:
    """
    Enhance a prompt with memory context

    Args:
        prompt: Original prompt
        novel_dir: Novel directory
        llm_caller: Function to call LLM

    Returns:
        Enhanced prompt
    """
    memory_manager = get_memory_manager(novel_dir)
    context = memory_manager.get_context_for_generation(llm_caller)

    # Add global summary if available
    if context["global_summary"]:
        prompt += f"\n\n故事总结（请确保与此保持一致）：\n{context['global_summary']}\n"

    # 如果启用了嵌入功能，使用语义搜索找到相关内容
    if EMBEDDING_ENABLED and prompt:
        # 使用提示词作为查询，搜索相关记忆
        relevant_memories = search_memory(prompt, novel_dir)

        # 取前3个最相关的记忆
        if relevant_memories:
            top_memories = relevant_memories[:3]
            memories_text = "\n\n".join([memory["document"] for memory in top_memories])
            prompt += f"\n\n相关内容（请确保与此保持一致）：\n{memories_text}\n"
    # 否则使用传统方式
    elif context["recent_content"]:
        prompt += f"\n\n最近内容（请确保连贯性）：\n{context['recent_content']}\n"

    # Add active clues if available
    if context["active_clues"]:
        clues_text = "\n".join([f"- {clue['description']}" for clue in context["active_clues"]])
        prompt += f"\n\n活跃线索（请在本章中使用或发展这些线索）：\n{clues_text}\n"

    return prompt

def update_memory_from_chapter(chapter_content: str, chapter_num: int, novel_dir: str, llm_caller: Callable = None) -> None:
    """
    Update memory with chapter content

    Args:
        chapter_content: Chapter content
        chapter_num: Chapter number
        novel_dir: Novel directory
        llm_caller: Function to call LLM
    """
    memory_manager = get_memory_manager(novel_dir)

    # Extract entities
    memory_manager.extract_entities_from_text(chapter_content, llm_caller)

    # Summarize chapter
    memory_manager.summarize_chapter(chapter_num, chapter_content, llm_caller)

    # Save memory
    memory_manager.save_memory()

    print(f"Updated memory for chapter {chapter_num}")

def create_entity_relationship_graph(novel_dir: str) -> Dict[str, Any]:
    """
    Create entity relationship graph

    Args:
        novel_dir: Novel directory

    Returns:
        Graph data
    """
    memory_manager = get_memory_manager(novel_dir)
    return memory_manager.medium_term.get_entity_graph() if hasattr(memory_manager.medium_term, 'get_entity_graph') else {}

def check_consistency(chapter_content: str, novel_dir: str, llm_caller: Callable = None) -> Tuple[bool, str]:
    """
    Check consistency of chapter content with memory

    Args:
        chapter_content: Chapter content
        novel_dir: Novel directory
        llm_caller: Function to call LLM

    Returns:
        (is_consistent, consistency_report)
    """
    memory_manager = get_memory_manager(novel_dir)
    context = memory_manager.get_context_for_generation(llm_caller)

    # If no LLM caller or no context, assume consistent
    if not llm_caller or not context["global_summary"]:
        return True, "No consistency check performed"

    # Create consistency check prompt
    prompt = f"""
You are a consistency checker for a novel. Please analyze the following chapter content and determine if it is consistent with the story summary and recent content.

Story summary:
{context["global_summary"]}

Recent content:
{context["recent_content"]}

Chapter content to check:
{chapter_content[:2000]}...

Please check for the following types of inconsistencies:
1. Character inconsistencies (names, personalities, relationships)
2. Plot inconsistencies (contradictions with previous events)
3. Setting inconsistencies (locations, time periods)
4. Logical inconsistencies (impossible events given the story's rules)

Format your response as JSON:
{{
  "is_consistent": true/false,
  "issues": [
    {{
      "type": "character/plot/setting/logical",
      "description": "Description of the inconsistency",
      "severity": "high/medium/low"
    }}
  ],
  "summary": "Brief summary of consistency analysis"
}}
"""

    try:
        response = llm_caller(prompt)

        # Try to parse JSON response
        try:
            # 处理响应可能已经是Python对象的情况
            if isinstance(response, dict):
                data = response
            else:
                data = json.loads(response)

            is_consistent = data.get("is_consistent", True)
            summary = data.get("summary", "No summary provided")

            # Format issues
            issues = data.get("issues", [])
            issues_text = "\n".join([f"- {issue['type'].upper()}: {issue['description']} (Severity: {issue['severity']})" for issue in issues])

            report = f"{summary}\n\n{issues_text if issues else 'No issues found.'}"
            return is_consistent, report
        except (json.JSONDecodeError, TypeError) as e:
            # 如果JSON解析失败，记录错误并假设一致
            print(f"Error in memory consistency check: {e}")
            # 保存原始响应以便调试
            debug_dir = os.path.join(novel_dir, "memory", "debug")
            os.makedirs(debug_dir, exist_ok=True)
            debug_file = os.path.join(debug_dir, "consistency_response_error.txt")
            with open(debug_file, "w", encoding="utf-8") as f:
                f.write(str(response))
            # If JSON parsing fails, assume consistent but return the raw response
            return True, f"Consistency check response (not in JSON format):\n{str(response)[:200]}..."
    except Exception as e:
        return True, f"Error in consistency check: {e}"

def check_clue_usage(novel_dir: str) -> List[str]:
    """
    Check for unused clues

    Args:
        novel_dir: Novel directory

    Returns:
        List of unused clue descriptions
    """
    memory_manager = get_memory_manager(novel_dir)
    return memory_manager.check_clue_usage() if hasattr(memory_manager, 'check_clue_usage') else []

def add_segment_to_memory(segment: str, novel_dir: str) -> None:
    """
    Add a segment to short-term memory

    Args:
        segment: Text segment
        novel_dir: Novel directory
    """
    memory_manager = get_memory_manager(novel_dir)
    memory_manager.add_segment(segment)
    print(f"Added segment to memory (length: {len(segment)})")

def clear_short_term_memory(novel_dir: str) -> None:
    """
    Clear short-term memory

    Args:
        novel_dir: Novel directory
    """
    memory_manager = get_memory_manager(novel_dir)
    memory_manager.short_term.clear()
    print("Cleared short-term memory")

def search_memory(query: str, novel_dir: str, llm_caller: Callable = None) -> List[Dict[str, Any]]:
    """
    Search memory using embeddings

    Args:
        query: Search query
        novel_dir: Novel directory
        llm_caller: Function to call LLM

    Returns:
        List of relevant memory segments with scores
    """
    # 如果嵌入功能未启用，使用简单的搜索
    if not EMBEDDING_ENABLED:
        memory_manager = get_memory_manager(novel_dir)
        segments = memory_manager.short_term.segments

        # 简单的关键词匹配
        results = []
        for segment in segments:
            if query.lower() in segment.lower():
                results.append({"document": segment, "score": 1.0})

        return results

    # 使用嵌入功能进行搜索
    memory_manager = get_memory_manager(novel_dir)
    segments = memory_manager.short_term.segments

    if not segments:
        return []

    # 使用嵌入搜索
    return search_documents(query, segments, novel_dir)
