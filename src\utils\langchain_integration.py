"""
LangChain集成模块
用于集成LangChain框架，实现高级生成功能
"""

import os
import re
import json
import time
import logging
from typing import Dict, Any, Optional, List, Union, Tuple

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/langchain.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("langchain_integration")

# 创建日志目录
os.makedirs("logs", exist_ok=True)

class LangChainIntegration:
    """LangChain集成类，提供高级生成功能"""

    def __init__(self):
        """初始化LangChain集成"""
        self.session_id = f"novel_session_{int(time.time())}"
        logger.info(f"初始化LangChain集成，会话ID: {self.session_id}")

        # 尝试导入LangChain
        self.langchain_available = False

        # 尝试导入不同版本的LangChain
        try:
            # 尝试导入新版LangChain
            import langchain
            import langchain_core
            import langchain_community

            # 导入核心组件
            from langchain_community.llms import OpenAI
            from langchain_community.chat_models import ChatOpenAI
            from langchain_core.messages import HumanMessage, SystemMessage
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.memory import ConversationBufferWindowMemory, ConversationSummaryMemory

            self.langchain_available = True
            logger.info("成功导入新版LangChain组件")
        except ImportError as e:
            logger.warning(f"无法导入新版LangChain组件: {e}")

            # 尝试导入旧版LangChain
            try:
                # 导入核心组件
                from langchain.llms import OpenAI
                from langchain.chat_models import ChatOpenAI
                from langchain.schema import HumanMessage, SystemMessage
                from langchain.prompts import ChatPromptTemplate
                from langchain.memory import ConversationBufferWindowMemory, ConversationSummaryMemory

                # 导入向量存储
                from langchain.vectorstores import Chroma
                from langchain.embeddings import OpenAIEmbeddings

                # 导入工具和智能体
                from langchain.agents import Tool, AgentExecutor, create_react_agent

                self.langchain_available = True
                logger.info("成功导入旧版LangChain组件")
            except ImportError as e:
                logger.warning(f"无法导入旧版LangChain组件: {e}")
                self.langchain_available = False

    def call_llm(self,
                model: str,
                prompt: str,
                system_message: Optional[str] = None,
                temperature: float = 0.7,
                max_tokens: int = 4096) -> str:
        """
        调用语言模型

        参数:
            model: 模型名称
            prompt: 提示词
            system_message: 系统消息
            temperature: 温度参数
            max_tokens: 最大生成token数

        返回:
            模型生成的内容
        """
        logger.info(f"调用LLM模型 {model}，提示词: {prompt[:50]}...")

        if not self.langchain_available:
            logger.warning("LangChain不可用，使用模拟响应")
            # 分析提示词，提取章节信息
            import re
            chapter_num = 1
            chapter_match = re.search(r'第(\d+)章', prompt)
            if chapter_match:
                try:
                    chapter_num = int(chapter_match.group(1))
                except ValueError:
                    pass

            title_match = re.search(r'标题[：:](.*?)\n', prompt)
            title = "" if not title_match else title_match.group(1).strip()

            # 当LangChain不可用时，返回错误信息
            logger.error("需要安装LangChain及其依赖包才能生成高质量内容")
            return f"错误: 无法生成内容。请安装LangChain及其依赖包，如numexpr等。可使用命令: pip install langchain numexpr"

        try:
            # 导入必要的组件
            try:
                from langchain.llms import OpenAI
                from langchain.chat_models import ChatOpenAI
                from langchain.schema import HumanMessage, SystemMessage

                # 构建消息
                messages = []
                if system_message:
                    messages.append(SystemMessage(content=system_message))
                messages.append(HumanMessage(content=prompt))

                # 根据模型类型选择不同的调用方式
                if "chat" in model.lower() or "gpt" in model.lower() or "glm" in model.lower():
                    chat_model = ChatOpenAI(model_name=model, temperature=temperature)
                    response = chat_model.invoke(messages)
                    return response.content
                else:
                    llm = OpenAI(model_name=model, temperature=temperature)
                    response = llm.invoke(prompt)
                    return response
            except ImportError as e:
                logger.warning(f"LangChain组件导入失败: {e}")
                # 分析提示词，提取章节信息
                import re
                chapter_num = 1
                chapter_match = re.search(r'第(\d+)章', prompt)
                if chapter_match:
                    try:
                        chapter_num = int(chapter_match.group(1))
                    except ValueError:
                        pass

                title_match = re.search(r'标题[：:](.*?)\n', prompt)
                title = "" if not title_match else title_match.group(1).strip()

                # 当LangChain不可用时，返回错误信息
                logger.error("需要安装LangChain及其依赖包才能生成高质量内容")
                return f"错误: 无法生成内容。请安装LangChain及其依赖包，如numexpr等。可使用命令: pip install langchain numexpr"
        except Exception as e:
            logger.error(f"调用LangChain LLM失败: {e}")
            # 分析提示词，提取章节信息
            import re
            chapter_num = 1
            chapter_match = re.search(r'第(\d+)章', prompt)
            if chapter_match:
                try:
                    chapter_num = int(chapter_match.group(1))
                except ValueError:
                    pass

            title_match = re.search(r'标题[：:](.*?)\n', prompt)
            title = "" if not title_match else title_match.group(1).strip()

            # 当LangChain不可用时，返回错误信息
            logger.error("需要安装LangChain及其依赖包才能生成高质量内容")
            return f"错误: 无法生成内容。请安装LangChain及其依赖包，如numexpr等。可使用命令: pip install langchain numexpr"

    def generate_chapter(self,
                        chapter_num: int,
                        title: str,
                        outline: str,
                        previous_chapters: Optional[List[str]] = None) -> str:
        """
        生成小说章节

        参数:
            chapter_num: 章节号
            title: 小说标题
            outline: 章节大纲
            previous_chapters: 之前的章节内容

        返回:
            生成的章节内容
        """
        logger.info(f"生成第{chapter_num}章: {title}")

        if not self.langchain_available:
            logger.warning("LangChain不可用，无法生成内容")
            return f"错误: 无法生成内容。请安装LangChain及其依赖包，如numexpr等。可使用命令: pip install langchain numexpr"

        try:
            # 导入必要的组件
            from langchain.prompts import ChatPromptTemplate
            from langchain.chat_models import ChatOpenAI
            from langchain.memory import ConversationBufferWindowMemory

            # 创建记忆管理
            memory = ConversationBufferWindowMemory(k=3)

            # 如果有之前的章节，添加到记忆中
            if previous_chapters:
                for i, chapter in enumerate(previous_chapters[-3:]):
                    memory.save_context(
                        {"input": f"第{chapter_num-len(previous_chapters)+i}章大纲"},
                        {"output": chapter[:500] + "..."}  # 只保存前500个字符
                    )

            # 创建提示模板
            template = """
            你是一位专业的小说写作家，擅长创作高质量的中文小说。

            请为以下小说创作第{chapter_num}章的内容：

            标题：{title}

            章节大纲：{outline}

            要求：
            1. 内容要符合章节大纲
            2. 情节要连贯，人物形象要一致
            3. 语言要生动，描写要细腻
            4. 章节长度在3000-5000字之间

            历史内容：
            {history}

            请直接开始创作，不需要添加额外的说明。
            """

            prompt = ChatPromptTemplate.from_template(template)

            # 创建聊天模型
            chat_model = ChatOpenAI(temperature=0.7)

            # 生成章节内容
            response = prompt.format_prompt(
                chapter_num=chapter_num,
                title=title,
                outline=outline,
                history=memory.buffer
            )

            result = chat_model.invoke(response.to_messages())
            return result.content
        except Exception as e:
            logger.error(f"生成章节失败: {e}")
            return f"错误: 生成章节失败: {e}"

    def generate_outline(self, title: str, theme: str, num_chapters: int = 10) -> List[str]:
        """
        生成小说大纲

        参数:
            title: 小说标题
            theme: 小说主题
            num_chapters: 章节数量

        返回:
            章节大纲列表
        """
        logger.info(f"生成小说大纲: {title}, {num_chapters}章")

        if not self.langchain_available:
            logger.warning("LangChain不可用，无法生成内容")
            return [f"错误: 无法生成内容。请安装LangChain及其依赖包，如numexpr等。可使用命令: pip install langchain numexpr"]

        try:
            # 导入必要的组件
            from langchain.prompts import ChatPromptTemplate
            from langchain.chat_models import ChatOpenAI

            # 创建提示模板
            template = """
            你是一位专业的小说策划师，擅长创作引人入胜的小说大纲。

            请为以下小说创作一个{num_chapters}章的详细大纲：

            标题：{title}
            主题：{theme}

            要求：
            1. 每章大纲要包含该章的主要情节和关键事件
            2. 整体情节要有起承转合，符合叙事规律
            3. 人物形象要丰满，情节要合理
            4. 要有伏笔和悬念，增加小说的吸引力

            请按照以下格式输出：
            第1章：[章节标题] - [章节大纲]
            第2章：[章节标题] - [章节大纲]
            ...

            请直接开始创作，不需要添加额外的说明。
            """

            prompt = ChatPromptTemplate.from_template(template)

            # 创建聊天模型
            chat_model = ChatOpenAI(temperature=0.7)

            # 生成大纲
            response = prompt.format_prompt(
                title=title,
                theme=theme,
                num_chapters=num_chapters
            )

            result = chat_model.invoke(response.to_messages())

            # 解析结果
            outlines = []
            for line in result.content.split("\n"):
                if line.strip() and "章：" in line:
                    outlines.append(line.strip())

            return outlines
        except Exception as e:
            logger.error(f"生成大纲失败: {e}")
            return [f"错误: 生成大纲失败: {e}"]

    def check_consistency(self,
                         chapter_content: str,
                         character_info: Dict[str, Any],
                         previous_events: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
        """
        检查章节内容的一致性

        参数:
            chapter_content: 章节内容
            character_info: 角色信息
            previous_events: 之前的事件

        返回:
            (一致性检查结果, 问题列表)
        """
        logger.info("检查章节一致性")

        if not self.langchain_available:
            logger.warning("LangChain不可用，无法进行一致性检查")
            return False, ["错误: 无法进行一致性检查。请安装LangChain及其依赖包，如numexpr等。可使用命令: pip install langchain numexpr"]

        try:
            # 导入必要的组件
            from langchain.prompts import ChatPromptTemplate
            from langchain.chat_models import ChatOpenAI

            # 创建提示模板
            template = """
            你是一位专业的小说编辑，负责检查小说内容的一致性。

            请检查以下章节内容是否与已知的角色信息和事件保持一致：

            章节内容：
            {chapter_content}

            角色信息：
            {character_info}

            之前的事件：
            {previous_events}

            请列出所有不一致的地方，如果没有不一致，请回复"内容一致"。
            """

            prompt = ChatPromptTemplate.from_template(template)

            # 创建聊天模型
            chat_model = ChatOpenAI(temperature=0)

            # 检查一致性
            response = prompt.format_prompt(
                chapter_content=chapter_content[:2000],  # 只检查前2000个字符
                character_info=json.dumps(character_info, ensure_ascii=False),
                previous_events=json.dumps(previous_events, ensure_ascii=False)
            )

            result = chat_model.invoke(response.to_messages())

            # 解析结果
            if "内容一致" in result.content:
                return True, []
            else:
                issues = [line.strip() for line in result.content.split("\n") if line.strip()]
                return False, issues
        except Exception as e:
            logger.error(f"检查一致性失败: {e}")
            return False, [f"错误: 检查一致性失败: {e}"]



# 创建全局LangChain集成实例
langchain_integration = LangChainIntegration()

# 便捷函数
def call_llm(model: str, prompt: str, system_message: Optional[str] = None, **kwargs) -> str:
    """调用语言模型的便捷函数"""
    return langchain_integration.call_llm(model, prompt, system_message, **kwargs)

def generate_chapter(chapter_num: int, title: str, outline: str, previous_chapters: Optional[List[str]] = None) -> str:
    """生成小说章节的便捷函数"""
    return langchain_integration.generate_chapter(chapter_num, title, outline, previous_chapters)

def generate_outline(title: str, theme: str, num_chapters: int = 10) -> List[str]:
    """生成小说大纲的便捷函数"""
    return langchain_integration.generate_outline(title, theme, num_chapters)

def check_consistency(chapter_content: str, character_info: Dict[str, Any], previous_events: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
    """检查章节内容的一致性的便捷函数"""
    return langchain_integration.check_consistency(chapter_content, character_info, previous_events)

def call_agent(agent_name: str, message: Dict[str, Any]) -> Any:
    """调用智能体的便捷函数"""
    # 当前未实现
    return {"error": "调用智能体功能尚未实现"}

def generate_illustration(title: str, scene_description: str, style: str = "写实风格",
                         output_path: Optional[str] = None, model_name: Optional[str] = None) -> Tuple[bool, str]:
    """生成插图的便捷函数"""
    # 当前未实现
    return False, "生成插图功能尚未实现"

def get_langchain_integration() -> LangChainIntegration:
    """获取LangChain集成实例"""
    return langchain_integration
