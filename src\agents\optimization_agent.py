"""
优化智能体模块
负责最终优化小说内容
"""

import os
import json
from typing import Dict, List, Any, Optional, Union, cast

try:
    from src.core.agent import Agent, AGENT_REGISTRY
except ImportError:
    # 如果导入失败，创建简单的Agent类
    class Agent:
        def __init__(self, name, model=None):
            self.name = name
            self.model = model

    # 创建简单的AGENT_REGISTRY类
    class AgentRegistry:
        def __init__(self):
            self.agents = {}

        def register(self, agent):
            self.agents[agent.name] = agent
            return agent

        def get_agent(self, name):
            return self.agents.get(name)

    AGENT_REGISTRY = AgentRegistry()

try:
    from src.core.prompt_manager import PromptManager
except ImportError:
    # 如果导入失败，创建简单的PromptManager类
    class PromptManager:
        def get_prompt(self, prompt_name, **kwargs):
            return f"提示词 {prompt_name} 示例内容"

try:
    from src.core.model_config import AI_MODEL_CONFIG, DEFAULT_MODEL_NAMES
except ImportError:
    # 如果导入失败，创建简单的配置
    AI_MODEL_CONFIG = {
        "agent_models": {},
        "default_model": "glm-4-flash"
    }
    DEFAULT_MODEL_NAMES = {"glm": "glm-4-flash"}

# 初始化提示词管理器
prompt_manager = PromptManager()

class OptimizationAgent(Agent):
    """
    优化智能体，负责最终优化小说内容
    """

    def __init__(self, name: str, model: Optional[str] = None):
        """初始化优化智能体"""
        # 确保AI_MODEL_CONFIG是字典类型
        agent_models = cast(Dict[str, Any], AI_MODEL_CONFIG.get("agent_models", {}))
        default_model = cast(str, AI_MODEL_CONFIG.get("default_model", ""))

        # 获取模型名称
        model = model or agent_models.get("OptimizationAgent", default_model)
        super().__init__(name, model)
        self.prompt_manager = prompt_manager

    def handle_message(self, msg):
        """处理接收到的消息"""
        if msg['type'] == 'optimize':
            print(f"优化智能体接收到内容...")
            optimized_content = self.optimize_content(msg['content'])

            # 发送结果给下一个智能体（如果指定了）
            if 'next_agent' in msg:
                self.send_message(
                    msg['next_agent'],
                    {
                        'type': 'content',
                        'content': optimized_content,
                        'metadata': msg.get('metadata', {})
                    }
                )

            return optimized_content

    def optimize_content(self, original_text: str) -> str:
        """
        优化小说内容

        参数:
            original_text: 原始文本

        返回:
            优化后的内容
        """
        print("正在优化内容...")

        # 检查原始内容长度和质量
        original_length = len(original_text)
        print(f"原始内容长度: {original_length} 字符")

        # 检查内容是否可能是提示词或错误信息，或者内容是否过短
        if original_length < 1000 or "错误" in original_text[:100] or "抱歉" in original_text[:100]:
            print("检测到内容可能是提示词或内容过短，将生成真正的小说内容")

            # 使用直接提示生成内容
            direct_prompt = f"""
你是一位专业的中文小说作家，请创作一个完整的小说章节。

原始内容（可能包含错误或提示词）：
{original_text[:500]}...

请创作一个完整的小说章节，要求：
1. 内容丰富，情节连贯，场景描写生动
2. 包含适量的对话和心理描写
3. 字数在5000-8000字之间
4. 不要包含任何提示词或AI自我指代
5. 直接给出章节内容，不需要额外的解释

请直接给出章节内容，不需要额外的解释。
"""
            print("正在生成完整的小说章节内容...")
            try:
                optimized_content = self.call_llm(direct_prompt)
                optimized_length = len(optimized_content)
                print(f"生成的内容长度: {optimized_length} 字符")
            except Exception as e:
                print(f"生成内容时出错: {e}")
                # 如果原始内容长度足够，则使用原始内容
                if original_length > 1000:
                    return original_text
                # 否则使用简单的提示词生成内容
                simple_prompt = f"""请创作一个完整的小说章节，字数在5000-8000字之间。"""
                try:
                    optimized_content = self.call_llm(simple_prompt)
                except Exception:
                    return original_text

            # 检查生成的内容长度
            if len(optimized_content) < 3000:
                print(f"警告: 优化后的内容长度仅为 {len(optimized_content)} 字符，将尝试扩充内容")

                # 使用扩充提示词
                expansion_prompt = f"""
你是一位专业的中文小说扩写专家，请对以下小说章节进行合理扩展：

{optimized_content}

扩写要求：
1. 场景细节：增加场景的细节描写，包括环境、气氛、光线、声音等元素，使场景更加立体。
2. 角色刻画：深化角色形象，增加角色的外貌、动作、表情、心理活动等描写，使角色更加丰满。
3. 情感表达：丰富情感层次，通过细腻的描写展现角色的情感变化和内心冲突。
4. 对话扩展：适当扩展对话内容，通过对话展现角色性格和推动情节发展。
5. 背景补充：适当补充故事背景和世界观设定，增强故事的深度和可信度。
6. 字数要求：扩展后的内容应达到8000字以上。

请直接给出扩展后的内容，不需要解释扩写思路。
"""
                try:
                    expanded_content = self.call_llm(expansion_prompt)
                    expanded_length = len(expanded_content)
                    print(f"扩展后的内容长度: {expanded_length} 字符")

                    # 如果扩展后的内容比原来的长，则使用扩展后的内容
                    if expanded_length > len(optimized_content):
                        optimized_content = expanded_content
                except Exception as e:
                    print(f"扩展内容时出错: {e}")

            return optimized_content

        # 如果内容长度和质量正常，进行常规优化
        # 获取优化提示词
        prompt = self.prompt_manager.get_prompt(
            "optimization_agent",
            original_text=original_text
        )

        # 如果没有获取到提示词，使用默认提示词
        if not prompt or len(prompt) < 100:
            prompt = f"""
你是一位专业的中文小说编辑，请对以下小说内容进行最终优化和完善：

{original_text}

请重点关注以下方面：
1. 情节完整性：确保情节完整，没有断点或跳跃
2. 人物塑造：增强人物形象的立体感和真实感
3. 情感表达：深化情感描写，增强感染力
4. 语言风格：统一语言风格，提升文学性
5. 细节描写：增加细节描写，提升沉浸感
6. 节奏控制：优化节奏，确保紧张与舒缓的适当转换
7. 主题深化：强化主题表达，增加思想深度

请直接给出优化后的完整内容，不需要额外的解释或说明。
请确保优化后的内容保持原有的情节和人物设定，只是在表达和细节上进行提升。
"""

        # 调用LLM优化内容
        try:
            optimized_content = self.call_llm(prompt)

            # 检查并删除重复段落
            optimized_content = self._remove_duplicates(optimized_content)
        except Exception as e:
            print(f"优化内容时出错: {e}")
            return original_text

        # 检查优化后的内容长度
        optimized_length = len(optimized_content)

        # 仅当内容少于3000字时才进行长度检查
        if optimized_length < 3000:
            print(f"警告：优化后内容少于3000字，当前长度: {optimized_length} 字符")

            # 尝试扩展内容，简化提示词
            retry_prompt = f"""
请对以下小说内容进行扩展，使其达到3000字以上：

{optimized_content}

要求：
- 内容长度必须达到3000字以上
- 不要生成重复内容
- 保持情节连贯性

直接给出扩展后的完整内容。
"""
            try:
                retry_content = self.call_llm(retry_prompt)
                new_length = len(retry_content)
                print(f"扩展后的内容长度为 {new_length} 字符")

                # 如果扩展后的内容长度超过3000字，则使用扩展后的内容
                if new_length >= 3000:
                    optimized_content = retry_content
                    print(f"成功扩展内容长度到 {new_length} 字符")
                else:
                    print(f"扩展后内容仍然不足3000字，使用原始内容")
            except Exception as e:
                print(f"扩展内容时出错: {e}")
        else:
            print(f"优化后内容长度为 {optimized_length} 字符，超过3000字的最低要求")

        # 检查并删除重复段落
        optimized_content = self._remove_duplicates(optimized_content)

        print(f"内容优化完成，从 {original_length} 字符变为 {len(optimized_content)} 字符")
        return optimized_content

    def _remove_duplicates(self, text: str) -> str:
        """
        增强版的重复内容检测和删除函数，更严格的相似度检测

        参数:
            text: 原始文本

        返回:
            去除重复后的文本
        """
        # 按段落分割文本
        paragraphs = text.split('\n\n')

        # 如果段落数量太少，不处理
        if len(paragraphs) < 5:
            return text

        # 创建一个列表存储不重复的段落
        unique_paragraphs = []
        # 存储段落的指纹(简化内容)用于相似度检测
        paragraph_fingerprints = []
        # 存储已见过的段落内容（用于完全相同的检测）
        seen_paragraphs = set()

        # 统计重复段落
        duplicate_count = 0
        similar_count = 0

        for para in paragraphs:
            # 忽略空段落
            if not para.strip():
                unique_paragraphs.append(para)
                continue

            # 如果段落太短，直接保留
            if len(para) < 50:
                unique_paragraphs.append(para)
                continue

            # 检查是否与已有段落完全相同
            if para.strip() in seen_paragraphs:
                duplicate_count += 1
                continue

            # 创建段落的指纹 - 提取关键词和句子结构
            # 简化版：取段落中的关键词组合
            words = para.strip().split()
            if len(words) > 10:
                # 取段落中的部分关键词作为指纹
                fingerprint = ' '.join(sorted([w for w in words if len(w) > 1])[:20])
            else:
                fingerprint = para.strip()

            # 检查是否与已有段落相似
            is_similar = False
            for i, existing_fp in enumerate(paragraph_fingerprints):
                # 简单的相似度检测
                similarity = self._similarity(fingerprint, existing_fp)

                # 降低相似度阈值，更严格地检测相似内容
                similarity_threshold = 0.6  # 降低阈值，更容易检测到相似内容

                if similarity > similarity_threshold:
                    # 如果相似度超过阈值，进一步检查段落内容
                    # 获取原段落内容进行比较
                    existing_para = unique_paragraphs[i]

                    # 计算段落长度的比例，如果长度相差太大，可能不是重复内容
                    len_ratio = min(len(para), len(existing_para)) / max(len(para), len(existing_para))

                    # 如果长度比例超过0.7（长度相近）且相似度超过阈值
                    if len_ratio > 0.7 and similarity > similarity_threshold:
                        is_similar = True
                        similar_count += 1
                        break

            if not is_similar:
                # 添加到不重复段落列表
                unique_paragraphs.append(para)
                paragraph_fingerprints.append(fingerprint)
                seen_paragraphs.add(para.strip())

        # 如果检测到重复段落，打印日志
        if duplicate_count > 0 or similar_count > 0:
            print(f"检测到并删除了 {duplicate_count} 个完全重复的段落和 {similar_count} 个高度相似的段落")

        # 重新组合文本
        return '\n\n'.join(unique_paragraphs)

    def _similarity(self, str1: str, str2: str) -> float:
        """
        计算两个字符串的相似度

        参数:
            str1: 第一个字符串
            str2: 第二个字符串

        返回:
            相似度，范围0-1
        """
        # 简单的Jaccard相似度
        set1 = set(str1.split())
        set2 = set(str2.split())

        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0
