import{w as ie}from"./index-V1UhiEW8.js";const{SvelteComponent:Be,append:X,attr:C,detach:Ie,init:Se,insert:Te,noop:Y,safe_not_equal:qe,svg_element:P}=window.__gradio__svelte__internal;function je(t){let e,l,i,_;return{c(){e=P("svg"),l=P("circle"),i=P("circle"),_=P("circle"),C(l,"cx","2.5"),C(l,"cy","8"),C(l,"r","1.5"),C(l,"fill","currentColor"),C(i,"cx","8"),C(i,"cy","8"),C(i,"r","1.5"),C(i,"fill","currentColor"),C(_,"cx","13.5"),C(_,"cy","8"),C(_,"r","1.5"),C(_,"fill","currentColor"),C(e,"width","16"),C(e,"height","16"),C(e,"viewBox","0 0 16 16"),C(e,"fill","none"),C(e,"xmlns","http://www.w3.org/2000/svg")},m(r,a){Te(r,e,a),X(e,l),X(e,i),X(e,_)},p:Y,i:Y,o:Y,d(r){r&&Ie(e)}}}class Ee extends Be{constructor(e){super(),Se(this,e,null,je,qe,{})}}const{SvelteComponent:Ne,append:z,attr:m,binding_callbacks:Z,check_outros:Oe,component_subscribe:ne,create_component:Re,create_slot:Ae,destroy_component:De,destroy_each:y,detach:O,element:j,empty:he,ensure_array_like:A,flush:M,get_all_dirty_from_scope:Me,get_slot_changes:Fe,group_outros:Ge,init:He,insert:R,listen:H,mount_component:Je,run_all:Ke,safe_not_equal:Le,set_data:$,set_store_value:F,set_style:se,space:N,stop_propagation:Pe,text:x,toggle_class:S,transition_in:G,transition_out:Q,update_slot_base:Qe}=window.__gradio__svelte__internal,{setContext:Ue,createEventDispatcher:Ve,tick:We,onMount:Xe}=window.__gradio__svelte__internal;function oe(t,e,l){const i=t.slice();return i[33]=e[l],i}function _e(t,e,l){const i=t.slice();return i[33]=e[l],i[37]=l,i}function ae(t,e,l){const i=t.slice();return i[33]=e[l],i[38]=e,i[37]=l,i}function ce(t){let e,l,i,_,r,a,c,d,u,f,v,B,E,b=A(t[3]),h=[];for(let o=0;o<b.length;o+=1)h[o]=re(ae(t,b,o));let T=A(t[7]),g=[];for(let o=0;o<T.length;o+=1)g[o]=de(_e(t,T,o));d=new Ee({});let I=A(t[8]),w=[];for(let o=0;o<I.length;o+=1)w[o]=be(oe(t,I,o));return{c(){e=j("div"),l=j("div");for(let o=0;o<h.length;o+=1)h[o].c();i=N(),_=j("div");for(let o=0;o<g.length;o+=1)g[o].c();r=N(),a=j("span"),c=j("button"),Re(d.$$.fragment),u=N(),f=j("div");for(let o=0;o<w.length;o+=1)w[o].c();m(l,"class","tab-container visually-hidden svelte-1tcem6n"),m(l,"aria-hidden","true"),m(_,"class","tab-container svelte-1tcem6n"),m(_,"role","tablist"),m(c,"class","svelte-1tcem6n"),S(c,"overflow-item-selected",t[12]),m(f,"class","overflow-dropdown svelte-1tcem6n"),S(f,"hide",!t[9]),m(a,"class","overflow-menu svelte-1tcem6n"),S(a,"hide",!t[11]),m(e,"class","tab-wrapper svelte-1tcem6n")},m(o,k){R(o,e,k),z(e,l);for(let n=0;n<h.length;n+=1)h[n]&&h[n].m(l,null);z(e,i),z(e,_);for(let n=0;n<g.length;n+=1)g[n]&&g[n].m(_,null);t[28](_),z(e,r),z(e,a),z(a,c),Je(d,c,null),z(a,u),z(a,f);for(let n=0;n<w.length;n+=1)w[n]&&w[n].m(f,null);t[31](a),v=!0,B||(E=H(c,"click",Pe(t[29])),B=!0)},p(o,k){if(k[0]&40){b=A(o[3]);let n;for(n=0;n<b.length;n+=1){const q=ae(o,b,n);h[n]?h[n].p(q,k):(h[n]=re(q),h[n].c(),h[n].m(l,null))}for(;n<h.length;n+=1)h[n].d(1);h.length=b.length}if(k[0]&393408){T=A(o[7]);let n;for(n=0;n<T.length;n+=1){const q=_e(o,T,n);g[n]?g[n].p(q,k):(g[n]=de(q),g[n].c(),g[n].m(_,null))}for(;n<g.length;n+=1)g[n].d(1);g.length=T.length}if((!v||k[0]&4096)&&S(c,"overflow-item-selected",o[12]),k[0]&262464){I=A(o[8]);let n;for(n=0;n<I.length;n+=1){const q=oe(o,I,n);w[n]?w[n].p(q,k):(w[n]=be(q),w[n].c(),w[n].m(f,null))}for(;n<w.length;n+=1)w[n].d(1);w.length=I.length}(!v||k[0]&512)&&S(f,"hide",!o[9]),(!v||k[0]&2048)&&S(a,"hide",!o[11])},i(o){v||(G(d.$$.fragment,o),v=!0)},o(o){Q(d.$$.fragment,o),v=!1},d(o){o&&O(e),y(h,o),y(g,o),t[28](null),De(d),y(w,o),t[31](null),B=!1,E()}}}function fe(t){let e,l=t[33]?.label+"",i,_,r=t[33];const a=()=>t[26](e,r),c=()=>t[26](null,r);return{c(){e=j("button"),i=x(l),_=N(),m(e,"class","svelte-1tcem6n")},m(d,u){R(d,e,u),z(e,i),z(e,_),a()},p(d,u){t=d,u[0]&8&&l!==(l=t[33]?.label+"")&&$(i,l),r!==t[33]&&(c(),r=t[33],a())},d(d){d&&O(e),c()}}}function re(t){let e,l=t[33]?.visible&&fe(t);return{c(){l&&l.c(),e=he()},m(i,_){l&&l.m(i,_),R(i,e,_)},p(i,_){i[33]?.visible?l?l.p(i,_):(l=fe(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&O(e),l&&l.d(i)}}}function ue(t){let e,l=t[33].label+"",i,_,r,a,c,d,u,f,v,B;function E(){return t[27](t[33],t[37])}return{c(){e=j("button"),i=x(l),_=N(),m(e,"role","tab"),m(e,"aria-selected",r=t[33].id===t[6]),m(e,"aria-controls",a=t[33].elem_id),e.disabled=c=!t[33].interactive,m(e,"aria-disabled",d=!t[33].interactive),m(e,"id",u=t[33].elem_id?t[33].elem_id+"-button":null),m(e,"data-tab-id",f=t[33].id),m(e,"class","svelte-1tcem6n"),S(e,"selected",t[33].id===t[6])},m(b,h){R(b,e,h),z(e,i),z(e,_),v||(B=H(e,"click",E),v=!0)},p(b,h){t=b,h[0]&128&&l!==(l=t[33].label+"")&&$(i,l),h[0]&192&&r!==(r=t[33].id===t[6])&&m(e,"aria-selected",r),h[0]&128&&a!==(a=t[33].elem_id)&&m(e,"aria-controls",a),h[0]&128&&c!==(c=!t[33].interactive)&&(e.disabled=c),h[0]&128&&d!==(d=!t[33].interactive)&&m(e,"aria-disabled",d),h[0]&128&&u!==(u=t[33].elem_id?t[33].elem_id+"-button":null)&&m(e,"id",u),h[0]&128&&f!==(f=t[33].id)&&m(e,"data-tab-id",f),h[0]&192&&S(e,"selected",t[33].id===t[6])},d(b){b&&O(e),v=!1,B()}}}function de(t){let e,l=t[33]?.visible&&ue(t);return{c(){l&&l.c(),e=he()},m(i,_){l&&l.m(i,_),R(i,e,_)},p(i,_){i[33]?.visible?l?l.p(i,_):(l=ue(i),l.c(),l.m(e.parentNode,e)):l&&(l.d(1),l=null)},d(i){i&&O(e),l&&l.d(i)}}}function be(t){let e,l=t[33]?.label+"",i,_,r,a;function c(){return t[30](t[33])}return{c(){e=j("button"),i=x(l),_=N(),m(e,"class","svelte-1tcem6n"),S(e,"selected",t[33]?.id===t[6])},m(d,u){R(d,e,u),z(e,i),z(e,_),r||(a=H(e,"click",c),r=!0)},p(d,u){t=d,u[0]&256&&l!==(l=t[33]?.label+"")&&$(i,l),u[0]&320&&S(e,"selected",t[33]?.id===t[6])},d(d){d&&O(e),r=!1,a()}}}function Ye(t){let e,l,i,_,r,a,c=t[14]&&ce(t);const d=t[25].default,u=Ae(d,t,t[24],null);return{c(){e=j("div"),c&&c.c(),l=N(),u&&u.c(),m(e,"class",i="tabs "+t[2].join(" ")+" svelte-1tcem6n"),m(e,"id",t[1]),S(e,"hide",!t[0]),se(e,"flex-grow",t[13])},m(f,v){R(f,e,v),c&&c.m(e,null),z(e,l),u&&u.m(e,null),_=!0,r||(a=[H(window,"resize",t[20]),H(window,"click",t[19])],r=!0)},p(f,v){f[14]?c?(c.p(f,v),v[0]&16384&&G(c,1)):(c=ce(f),c.c(),G(c,1),c.m(e,l)):c&&(Ge(),Q(c,1,1,()=>{c=null}),Oe()),u&&u.p&&(!_||v[0]&16777216)&&Qe(u,d,f,f[24],_?Fe(d,f[24],v,null):Me(f[24]),null),(!_||v[0]&4&&i!==(i="tabs "+f[2].join(" ")+" svelte-1tcem6n"))&&m(e,"class",i),(!_||v[0]&2)&&m(e,"id",f[1]),(!_||v[0]&5)&&S(e,"hide",!f[0]),v[0]&8192&&se(e,"flex-grow",f[13])},i(f){_||(G(c),G(u,f),_=!0)},o(f){Q(c),Q(u,f),_=!1},d(f){f&&O(e),c&&c.d(),u&&u.d(f),r=!1,Ke(a)}}}const Ze={};function ye(t,e){const l={};return t.forEach(i=>{i&&(l[i.id]=e[i.id]?.getBoundingClientRect())}),l}function $e(t,e,l){let i,_,r,a,{$$slots:c={},$$scope:d}=e,{visible:u=!0}=e,{elem_id:f=""}=e,{elem_classes:v=[]}=e,{selected:B}=e,{initial_tabs:E}=e,b=[...E],h=[...E],T=[],g=!1,I,w;const o=ie(B||b[0]?.id||!1);ne(t,o,s=>l(6,a=s));const k=ie(b.findIndex(s=>s?.id===B)||0);ne(t,k,s=>l(23,r=s));const n=Ve();let q=!1,U=!1,J={};Xe(()=>{new IntersectionObserver(p=>{V()}).observe(w)}),Ue(Ze,{register_tab:(s,p)=>(l(3,b[p]=s,b),a===!1&&s.visible&&s.interactive&&(F(o,a=s.id,a),F(k,r=p,r)),p),unregister_tab:(s,p)=>{a===s.id&&F(o,a=b[0]?.id||!1,a),l(3,b[p]=null,b)},selected_tab:o,selected_tab_index:k});function K(s){const p=b.find(D=>D?.id===s);s!==void 0&&p&&p.interactive&&p.visible&&a!==p.id&&(l(21,B=s),F(o,a=s,a),F(k,r=b.findIndex(D=>D?.id===s),r),n("change"),l(9,g=!1))}function me(s){g&&I&&!I.contains(s.target)&&l(9,g=!1)}async function V(){if(!w)return;await We();const s=w.getBoundingClientRect();let p=s.width;const D=ye(b,J);let W=0;const ze=s.left;for(let L=b.length-1;L>=0;L--){const te=b[L];if(!te)continue;const le=D[te.id];if(le&&le.right-ze<p){W=L;break}}l(8,T=b.slice(W+1)),l(7,h=b.slice(0,W+1)),l(12,U=ee(a)),l(11,q=T.length>0)}function ee(s){return s===!1?!1:T.some(p=>p?.id===s)}function ve(s,p){Z[s?"unshift":"push"](()=>{J[p.id]=s,l(5,J)})}const ge=(s,p)=>{s.id!==a&&(K(s.id),n("select",{value:s.label,index:p}))};function we(s){Z[s?"unshift":"push"](()=>{w=s,l(4,w)})}const pe=()=>l(9,g=!g),ke=s=>K(s?.id);function Ce(s){Z[s?"unshift":"push"](()=>{I=s,l(10,I)})}return t.$$set=s=>{"visible"in s&&l(0,u=s.visible),"elem_id"in s&&l(1,f=s.elem_id),"elem_classes"in s&&l(2,v=s.elem_classes),"selected"in s&&l(21,B=s.selected),"initial_tabs"in s&&l(22,E=s.initial_tabs),"$$scope"in s&&l(24,d=s.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&8&&l(14,i=b.length>0),t.$$.dirty[0]&2097160&&B!==null&&K(B),t.$$.dirty[0]&56&&V(),t.$$.dirty[0]&64&&l(12,U=ee(a)),t.$$.dirty[0]&8388616&&l(13,_=b[r>=0?r:0]?.scale)},[u,f,v,b,w,J,a,h,T,g,I,q,U,_,i,o,k,n,K,me,V,B,E,r,d,c,ve,ge,we,pe,ke,Ce]}class xe extends Ne{constructor(e){super(),He(this,e,$e,Ye,Le,{visible:0,elem_id:1,elem_classes:2,selected:21,initial_tabs:22},null,[-1,-1])}get visible(){return this.$$.ctx[0]}set visible(e){this.$$set({visible:e}),M()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),M()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),M()}get selected(){return this.$$.ctx[21]}set selected(e){this.$$set({selected:e}),M()}get initial_tabs(){return this.$$.ctx[22]}set initial_tabs(e){this.$$set({initial_tabs:e}),M()}}const tt=xe;export{tt as T,Ze as a};
//# sourceMappingURL=Tabs-Cgg632kg.js.map
