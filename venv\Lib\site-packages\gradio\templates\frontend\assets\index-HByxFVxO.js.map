{"version": 3, "file": "index-HByxFVxO.js", "sources": ["../../../../node_modules/.pnpm/@lezer+html@1.3.6/node_modules/@lezer/html/dist/index.js", "../../../../node_modules/.pnpm/@codemirror+lang-html@6.4.9/node_modules/@codemirror/lang-html/dist/index.js"], "sourcesContent": ["import { ContextTracker, ExternalTokenizer, LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\nimport { parseMixed } from '@lezer/common';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst scriptText = 54,\n  StartCloseScriptTag = 1,\n  styleText = 55,\n  StartCloseStyleTag = 2,\n  textareaText = 56,\n  StartCloseTextareaTag = 3,\n  EndTag = 4,\n  SelfClosingEndTag = 5,\n  StartTag = 6,\n  StartScriptTag = 7,\n  StartStyleTag = 8,\n  StartTextareaTag = 9,\n  StartSelfClosingTag = 10,\n  StartCloseTag = 11,\n  NoMatchStartCloseTag = 12,\n  MismatchedStartCloseTag = 13,\n  missingCloseTag = 57,\n  IncompleteCloseTag = 14,\n  commentContent$1 = 58,\n  Element = 20,\n  TagName = 22,\n  Attribute = 23,\n  AttributeName = 24,\n  AttributeValue = 26,\n  UnquotedAttributeValue = 27,\n  ScriptText = 28,\n  StyleText = 31,\n  TextareaText = 34,\n  OpenTag = 36,\n  CloseTag = 37,\n  Dialect_noMatch = 0,\n  Dialect_selfClosing = 1;\n\n/* Hand-written tokenizers for HTML. */\n\nconst selfClosers = {\n  area: true, base: true, br: true, col: true, command: true,\n  embed: true, frame: true, hr: true, img: true, input: true,\n  keygen: true, link: true, meta: true, param: true, source: true,\n  track: true, wbr: true, menuitem: true\n};\n\nconst implicitlyClosed = {\n  dd: true, li: true, optgroup: true, option: true, p: true,\n  rp: true, rt: true, tbody: true, td: true, tfoot: true,\n  th: true, tr: true\n};\n\nconst closeOnOpen = {\n  dd: {dd: true, dt: true},\n  dt: {dd: true, dt: true},\n  li: {li: true},\n  option: {option: true, optgroup: true},\n  optgroup: {optgroup: true},\n  p: {\n    address: true, article: true, aside: true, blockquote: true, dir: true,\n    div: true, dl: true, fieldset: true, footer: true, form: true,\n    h1: true, h2: true, h3: true, h4: true, h5: true, h6: true,\n    header: true, hgroup: true, hr: true, menu: true, nav: true, ol: true,\n    p: true, pre: true, section: true, table: true, ul: true\n  },\n  rp: {rp: true, rt: true},\n  rt: {rp: true, rt: true},\n  tbody: {tbody: true, tfoot: true},\n  td: {td: true, th: true},\n  tfoot: {tbody: true},\n  th: {td: true, th: true},\n  thead: {tbody: true, tfoot: true},\n  tr: {tr: true}\n};\n\nfunction nameChar(ch) {\n  return ch == 45 || ch == 46 || ch == 58 || ch >= 65 && ch <= 90 || ch == 95 || ch >= 97 && ch <= 122 || ch >= 161\n}\n\nfunction isSpace(ch) {\n  return ch == 9 || ch == 10 || ch == 13 || ch == 32\n}\n\nlet cachedName = null, cachedInput = null, cachedPos = 0;\nfunction tagNameAfter(input, offset) {\n  let pos = input.pos + offset;\n  if (cachedPos == pos && cachedInput == input) return cachedName\n  let next = input.peek(offset);\n  while (isSpace(next)) next = input.peek(++offset);\n  let name = \"\";\n  for (;;) {\n    if (!nameChar(next)) break\n    name += String.fromCharCode(next);\n    next = input.peek(++offset);\n  }\n  // Undefined to signal there's a <? or <!, null for just missing\n  cachedInput = input; cachedPos = pos;\n  return cachedName = name ? name.toLowerCase() : next == question || next == bang ? undefined : null\n}\n\nconst lessThan = 60, greaterThan = 62, slash = 47, question = 63, bang = 33, dash = 45;\n\nfunction ElementContext(name, parent) {\n  this.name = name;\n  this.parent = parent;\n  this.hash = parent ? parent.hash : 0;\n  for (let i = 0; i < name.length; i++) this.hash += (this.hash << 4) + name.charCodeAt(i) + (name.charCodeAt(i) << 8);\n}\n\nconst startTagTerms = [StartTag, StartSelfClosingTag, StartScriptTag, StartStyleTag, StartTextareaTag];\n\nconst elementContext = new ContextTracker({\n  start: null,\n  shift(context, term, stack, input) {\n    return startTagTerms.indexOf(term) > -1 ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  reduce(context, term) {\n    return term == Element && context ? context.parent : context\n  },\n  reuse(context, node, stack, input) {\n    let type = node.type.id;\n    return type == StartTag || type == OpenTag\n      ? new ElementContext(tagNameAfter(input, 1) || \"\", context) : context\n  },\n  hash(context) { return context ? context.hash : 0 },\n  strict: false\n});\n\nconst tagStart = new ExternalTokenizer((input, stack) => {\n  if (input.next != lessThan) {\n    // End of file, close any open tags\n    if (input.next < 0 && stack.context) input.acceptToken(missingCloseTag);\n    return\n  }\n  input.advance();\n  let close = input.next == slash;\n  if (close) input.advance();\n  let name = tagNameAfter(input, 0);\n  if (name === undefined) return\n  if (!name) return input.acceptToken(close ? IncompleteCloseTag : StartTag)\n\n  let parent = stack.context ? stack.context.name : null;\n  if (close) {\n    if (name == parent) return input.acceptToken(StartCloseTag)\n    if (parent && implicitlyClosed[parent]) return input.acceptToken(missingCloseTag, -2)\n    if (stack.dialectEnabled(Dialect_noMatch)) return input.acceptToken(NoMatchStartCloseTag)\n    for (let cx = stack.context; cx; cx = cx.parent) if (cx.name == name) return\n    input.acceptToken(MismatchedStartCloseTag);\n  } else {\n    if (name == \"script\") return input.acceptToken(StartScriptTag)\n    if (name == \"style\") return input.acceptToken(StartStyleTag)\n    if (name == \"textarea\") return input.acceptToken(StartTextareaTag)\n    if (selfClosers.hasOwnProperty(name)) return input.acceptToken(StartSelfClosingTag)\n    if (parent && closeOnOpen[parent] && closeOnOpen[parent][name]) input.acceptToken(missingCloseTag, -1);\n    else input.acceptToken(StartTag);\n  }\n}, {contextual: true});\n\nconst commentContent = new ExternalTokenizer(input => {\n  for (let dashes = 0, i = 0;; i++) {\n    if (input.next < 0) {\n      if (i) input.acceptToken(commentContent$1);\n      break\n    }\n    if (input.next == dash) {\n      dashes++;\n    } else if (input.next == greaterThan && dashes >= 2) {\n      if (i > 3) input.acceptToken(commentContent$1, -2);\n      break\n    } else {\n      dashes = 0;\n    }\n    input.advance();\n  }\n});\n\nfunction inForeignElement(context) {\n  for (; context; context = context.parent)\n    if (context.name == \"svg\" || context.name == \"math\") return true\n  return false\n}\n\nconst endTag = new ExternalTokenizer((input, stack) => {\n  if (input.next == slash && input.peek(1) == greaterThan) {\n    let selfClosing = stack.dialectEnabled(Dialect_selfClosing) || inForeignElement(stack.context);\n    input.acceptToken(selfClosing ? SelfClosingEndTag : EndTag, 2);\n  } else if (input.next == greaterThan) {\n    input.acceptToken(EndTag, 1);\n  }\n});\n\nfunction contentTokenizer(tag, textToken, endToken) {\n  let lastState = 2 + tag.length;\n  return new ExternalTokenizer(input => {\n    // state means:\n    // - 0 nothing matched\n    // - 1 '<' matched\n    // - 2 '</' + possibly whitespace matched\n    // - 3-(1+tag.length) part of the tag matched\n    // - lastState whole tag + possibly whitespace matched\n    for (let state = 0, matchedLen = 0, i = 0;; i++) {\n      if (input.next < 0) {\n        if (i) input.acceptToken(textToken);\n        break\n      }\n      if (state == 0 && input.next == lessThan ||\n          state == 1 && input.next == slash ||\n          state >= 2 && state < lastState && input.next == tag.charCodeAt(state - 2)) {\n        state++;\n        matchedLen++;\n      } else if ((state == 2 || state == lastState) && isSpace(input.next)) {\n        matchedLen++;\n      } else if (state == lastState && input.next == greaterThan) {\n        if (i > matchedLen)\n          input.acceptToken(textToken, -matchedLen);\n        else\n          input.acceptToken(endToken, -(matchedLen - 2));\n        break\n      } else if ((input.next == 10 /* '\\n' */ || input.next == 13 /* '\\r' */) && i) {\n        input.acceptToken(textToken, 1);\n        break\n      } else {\n        state = matchedLen = 0;\n      }\n      input.advance();\n    }\n  })\n}\n\nconst scriptTokens = contentTokenizer(\"script\", scriptText, StartCloseScriptTag);\n\nconst styleTokens = contentTokenizer(\"style\", styleText, StartCloseStyleTag);\n\nconst textareaTokens = contentTokenizer(\"textarea\", textareaText, StartCloseTextareaTag);\n\nconst htmlHighlighting = styleTags({\n  \"Text RawText\": tags.content,\n  \"StartTag StartCloseTag SelfClosingEndTag EndTag\": tags.angleBracket,\n  TagName: tags.tagName,\n  \"MismatchedCloseTag/TagName\": [tags.tagName,  tags.invalid],\n  AttributeName: tags.attributeName,\n  \"AttributeValue UnquotedAttributeValue\": tags.attributeValue,\n  Is: tags.definitionOperator,\n  \"EntityReference CharacterReference\": tags.character,\n  Comment: tags.blockComment,\n  ProcessingInst: tags.processingInstruction,\n  DoctypeDecl: tags.documentMeta\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \",xOVO!rOOO!WQ#tO'#CqO!]Q#tO'#CzO!bQ#tO'#C}O!gQ#tO'#DQO!lQ#tO'#DSO!qOaO'#CpO!|ObO'#CpO#XOdO'#CpO$eO!rO'#CpOOO`'#Cp'#CpO$lO$fO'#DTO$tQ#tO'#DVO$yQ#tO'#DWOOO`'#Dk'#DkOOO`'#DY'#DYQVO!rOOO%OQ&rO,59]O%WQ&rO,59fO%`Q&rO,59iO%hQ&rO,59lO%sQ&rO,59nOOOa'#D^'#D^O%{OaO'#CxO&WOaO,59[OOOb'#D_'#D_O&`ObO'#C{O&kObO,59[OOOd'#D`'#D`O&sOdO'#DOO'OOdO,59[OOO`'#Da'#DaO'WO!rO,59[O'_Q#tO'#DROOO`,59[,59[OOOp'#Db'#DbO'dO$fO,59oOOO`,59o,59oO'lQ#|O,59qO'qQ#|O,59rOOO`-E7W-E7WO'vQ&rO'#CsOOQW'#DZ'#DZO(UQ&rO1G.wOOOa1G.w1G.wO(^Q&rO1G/QOOOb1G/Q1G/QO(fQ&rO1G/TOOOd1G/T1G/TO(nQ&rO1G/WOOO`1G/W1G/WOOO`1G/Y1G/YO(yQ&rO1G/YOOOa-E7[-E7[O)RQ#tO'#CyOOO`1G.v1G.vOOOb-E7]-E7]O)WQ#tO'#C|OOOd-E7^-E7^O)]Q#tO'#DPOOO`-E7_-E7_O)bQ#|O,59mOOOp-E7`-E7`OOO`1G/Z1G/ZOOO`1G/]1G/]OOO`1G/^1G/^O)gQ,UO,59_OOQW-E7X-E7XOOOa7+$c7+$cOOOb7+$l7+$lOOOd7+$o7+$oOOO`7+$r7+$rOOO`7+$t7+$tO)rQ#|O,59eO)wQ#|O,59hO)|Q#|O,59kOOO`1G/X1G/XO*RO7[O'#CvO*dOMhO'#CvOOQW1G.y1G.yOOO`1G/P1G/POOO`1G/S1G/SOOO`1G/V1G/VOOOO'#D['#D[O*uO7[O,59bOOQW,59b,59bOOOO'#D]'#D]O+WOMhO,59bOOOO-E7Y-E7YOOQW1G.|1G.|OOOO-E7Z-E7Z\",\n  stateData: \"+s~O!^OS~OUSOVPOWQOXROYTO[]O][O^^O`^Oa^Ob^Oc^Ox^O{_O!dZO~OfaO~OfbO~OfcO~OfdO~OfeO~O!WfOPlP!ZlP~O!XiOQoP!ZoP~O!YlORrP!ZrP~OUSOVPOWQOXROYTOZqO[]O][O^^O`^Oa^Ob^Oc^Ox^O!dZO~O!ZrO~P#dO![sO!euO~OfvO~OfwO~OS|OhyO~OS!OOhyO~OS!QOhyO~OS!SOT!TOhyO~OS!TOhyO~O!WfOPlX!ZlX~OP!WO!Z!XO~O!XiOQoX!ZoX~OQ!ZO!Z!XO~O!YlORrX!ZrX~OR!]O!Z!XO~O!Z!XO~P#dOf!_O~O![sO!e!aO~OS!bO~OS!cO~Oi!dOSgXhgXTgX~OS!fOhyO~OS!gOhyO~OS!hOhyO~OS!iOT!jOhyO~OS!jOhyO~Of!kO~Of!lO~Of!mO~OS!nO~Ok!qO!`!oO!b!pO~OS!rO~OS!sO~OS!tO~Oa!uOb!uOc!uO!`!wO!a!uO~Oa!xOb!xOc!xO!b!wO!c!xO~Oa!uOb!uOc!uO!`!{O!a!uO~Oa!xOb!xOc!xO!b!{O!c!xO~OT~bac!dx{!d~\",\n  goto: \"%p!`PPPPPPPPPPPPPPPPPPPP!a!gP!mPP!yP!|#P#S#Y#]#`#f#i#l#r#x!aP!a!aP$O$U$l$r$x%O%U%[%bPPPPPPPP%hX^OX`pXUOX`pezabcde{}!P!R!UR!q!dRhUR!XhXVOX`pRkVR!XkXWOX`pRnWR!XnXXOX`pQrXR!XpXYOX`pQ`ORx`Q{aQ}bQ!PcQ!RdQ!UeZ!e{}!P!R!UQ!v!oR!z!vQ!y!pR!|!yQgUR!VgQjVR!YjQmWR![mQpXR!^pQtZR!`tS_O`ToXp\",\n  nodeNames: \"⚠ StartCloseTag StartCloseTag StartCloseTag EndTag SelfClosingEndTag StartTag StartTag StartTag StartTag StartTag StartCloseTag StartCloseTag StartCloseTag IncompleteCloseTag Document Text EntityReference CharacterReference InvalidEntity Element OpenTag TagName Attribute AttributeName Is AttributeValue UnquotedAttributeValue ScriptText CloseTag OpenTag StyleText CloseTag OpenTag TextareaText CloseTag OpenTag CloseTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag CloseTag DoctypeDecl\",\n  maxTerm: 67,\n  context: elementContext,\n  nodeProps: [\n    [\"closedBy\", -10,1,2,3,7,8,9,10,11,12,13,\"EndTag\",6,\"EndTag SelfClosingEndTag\",-4,21,30,33,36,\"CloseTag\"],\n    [\"openedBy\", 4,\"StartTag StartCloseTag\",5,\"StartTag\",-4,29,32,35,37,\"OpenTag\"],\n    [\"group\", -9,14,17,18,19,20,39,40,41,42,\"Entity\",16,\"Entity TextContent\",-3,28,31,34,\"TextContent Entity\"]\n  ],\n  propSources: [htmlHighlighting],\n  skippedNodes: [0],\n  repeatNodeCount: 9,\n  tokenData: \"!<p!aR!YOX$qXY,QYZ,QZ[$q[]&X]^,Q^p$qpq,Qqr-_rs3_sv-_vw3}wxHYx}-_}!OH{!O!P-_!P!Q$q!Q![-_![!]Mz!]!^-_!^!_!$S!_!`!;x!`!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4U-_4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!Z$|c`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr$qrs&}sv$qvw+Pwx(tx!^$q!^!_*V!_!a&X!a#S$q#S#T&X#T;'S$q;'S;=`+z<%lO$q!R&bX`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&Xq'UV`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}P'pT`POv'kw!^'k!_;'S'k;'S;=`(P<%lO'kP(SP;=`<%l'kp([S!cpOv(Vx;'S(V;'S;=`(h<%lO(Vp(kP;=`<%l(Vq(qP;=`<%l&}a({W`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t`)jT!a`Or)esv)ew;'S)e;'S;=`)y<%lO)e`)|P;=`<%l)ea*SP;=`<%l(t!Q*^V!a`!cpOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!Q*vP;=`<%l*V!R*|P;=`<%l&XW+UYkWOX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+PW+wP;=`<%l+P!Z+}P;=`<%l$q!a,]``P!a`!cp!^^OX&XXY,QYZ,QZ]&X]^,Q^p&Xpq,Qqr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!_-ljhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q[/ebhSkWOX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+PS0rXhSqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0mS1bP;=`<%l0m[1hP;=`<%l/^!V1vchS`P!a`!cpOq&Xqr1krs&}sv1kvw0mwx(tx!P1k!P!Q&X!Q!^1k!^!_*V!_!a&X!a#s1k#s$f&X$f;'S1k;'S;=`3R<%l?Ah1k?Ah?BY&X?BY?Mn1k?MnO&X!V3UP;=`<%l1k!_3[P;=`<%l-_!Z3hV!`h`P!cpOv&}wx'kx!^&}!^!_(V!_;'S&};'S;=`(n<%lO&}!_4WihSkWc!ROX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst>]tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^/^!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!Z5zbkWOX5uXZ7SZ[5u[^7S^p5uqr5urs7Sst+Ptw5uwx7Sx!]5u!]!^7w!^!a7S!a#S5u#S#T7S#T;'S5u;'S;=`8n<%lO5u!R7VVOp7Sqs7St!]7S!]!^7l!^;'S7S;'S;=`7q<%lO7S!R7qOa!R!R7tP;=`<%l7S!Z8OYkWa!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!Z8qP;=`<%l5u!_8{ihSkWOX5uXZ7SZ[5u[^7S^p5uqr8trs7Sst/^tw8twx7Sx!P8t!P!Q5u!Q!]8t!]!^:j!^!a7S!a#S8t#S#T;{#T#s8t#s$f5u$f;'S8t;'S;=`>V<%l?Ah8t?Ah?BY5u?BY?Mn8t?MnO5u!_:sbhSkWa!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!V<QchSOp7Sqr;{rs7Sst0mtw;{wx7Sx!P;{!P!Q7S!Q!];{!]!^=]!^!a7S!a#s;{#s$f7S$f;'S;{;'S;=`>P<%l?Ah;{?Ah?BY7S?BY?Mn;{?MnO7S!V=dXhSa!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!V>SP;=`<%l;{!_>YP;=`<%l8t!_>dhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^/^!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!Z@TakWOX@OXZAYZ[@O[^AY^p@Oqr@OrsAYsw@OwxAYx!]@O!]!^Az!^!aAY!a#S@O#S#TAY#T;'S@O;'S;=`Bq<%lO@O!RA]UOpAYq!]AY!]!^Ao!^;'SAY;'S;=`At<%lOAY!RAtOb!R!RAwP;=`<%lAY!ZBRYkWb!ROX+PZ[+P^p+Pqr+Psw+Px!^+P!a#S+P#T;'S+P;'S;=`+t<%lO+P!ZBtP;=`<%l@O!_COhhSkWOX@OXZAYZ[@O[^AY^p@OqrBwrsAYswBwwxAYx!PBw!P!Q@O!Q!]Bw!]!^Dj!^!aAY!a#SBw#S#TE{#T#sBw#s$f@O$f;'SBw;'S;=`HS<%l?AhBw?Ah?BY@O?BY?MnBw?MnO@O!_DsbhSkWb!ROX+PZ[+P^p+Pqr/^sw/^x!P/^!P!Q+P!Q!^/^!a#S/^#S#T0m#T#s/^#s$f+P$f;'S/^;'S;=`1e<%l?Ah/^?Ah?BY+P?BY?Mn/^?MnO+P!VFQbhSOpAYqrE{rsAYswE{wxAYx!PE{!P!QAY!Q!]E{!]!^GY!^!aAY!a#sE{#s$fAY$f;'SE{;'S;=`G|<%l?AhE{?Ah?BYAY?BY?MnE{?MnOAY!VGaXhSb!Rqr0msw0mx!P0m!Q!^0m!a#s0m$f;'S0m;'S;=`1_<%l?Ah0m?BY?Mn0m!VHPP;=`<%lE{!_HVP;=`<%lBw!ZHcW!bx`P!a`Or(trs'ksv(tw!^(t!^!_)e!_;'S(t;'S;=`*P<%lO(t!aIYlhS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OKQ!O!P-_!P!Q$q!Q!^-_!^!_*V!_!a&X!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!aK_khS`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx!P-_!P!Q$q!Q!^-_!^!_*V!_!`&X!`!aMS!a#S-_#S#T1k#T#s-_#s$f$q$f;'S-_;'S;=`3X<%l?Ah-_?Ah?BY$q?BY?Mn-_?MnO$q!TM_X`P!a`!cp!eQOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X!aNZ!ZhSfQ`PkW!a`!cpOX$qXZ&XZ[$q[^&X^p$qpq&Xqr-_rs&}sv-_vw/^wx(tx}-_}!OMz!O!PMz!P!Q$q!Q![Mz![!]Mz!]!^-_!^!_*V!_!a&X!a!c-_!c!}Mz!}#R-_#R#SMz#S#T1k#T#oMz#o#s-_#s$f$q$f$}-_$}%OMz%O%W-_%W%oMz%o%p-_%p&aMz&a&b-_&b1pMz1p4UMz4U4dMz4d4e-_4e$ISMz$IS$I`-_$I`$IbMz$Ib$Je-_$Je$JgMz$Jg$Kh-_$Kh%#tMz%#t&/x-_&/x&EtMz&Et&FV-_&FV;'SMz;'S;:j!#|;:j;=`3X<%l?&r-_?&r?AhMz?Ah?BY$q?BY?MnMz?MnO$q!a!$PP;=`<%lMz!R!$ZY!a`!cpOq*Vqr!$yrs(Vsv*Vwx)ex!a*V!a!b!4t!b;'S*V;'S;=`*s<%lO*V!R!%Q]!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!%y!O!f*V!f!g!']!g#W*V#W#X!0`#X;'S*V;'S;=`*s<%lO*V!R!&QX!a`!cpOr*Vrs(Vsv*Vwx)ex}*V}!O!&m!O;'S*V;'S;=`*s<%lO*V!R!&vV!a`!cp!dPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!'dX!a`!cpOr*Vrs(Vsv*Vwx)ex!q*V!q!r!(P!r;'S*V;'S;=`*s<%lO*V!R!(WX!a`!cpOr*Vrs(Vsv*Vwx)ex!e*V!e!f!(s!f;'S*V;'S;=`*s<%lO*V!R!(zX!a`!cpOr*Vrs(Vsv*Vwx)ex!v*V!v!w!)g!w;'S*V;'S;=`*s<%lO*V!R!)nX!a`!cpOr*Vrs(Vsv*Vwx)ex!{*V!{!|!*Z!|;'S*V;'S;=`*s<%lO*V!R!*bX!a`!cpOr*Vrs(Vsv*Vwx)ex!r*V!r!s!*}!s;'S*V;'S;=`*s<%lO*V!R!+UX!a`!cpOr*Vrs(Vsv*Vwx)ex!g*V!g!h!+q!h;'S*V;'S;=`*s<%lO*V!R!+xY!a`!cpOr!+qrs!,hsv!+qvw!-Swx!.[x!`!+q!`!a!/j!a;'S!+q;'S;=`!0Y<%lO!+qq!,mV!cpOv!,hvx!-Sx!`!,h!`!a!-q!a;'S!,h;'S;=`!.U<%lO!,hP!-VTO!`!-S!`!a!-f!a;'S!-S;'S;=`!-k<%lO!-SP!-kO{PP!-nP;=`<%l!-Sq!-xS!cp{POv(Vx;'S(V;'S;=`(h<%lO(Vq!.XP;=`<%l!,ha!.aX!a`Or!.[rs!-Ssv!.[vw!-Sw!`!.[!`!a!.|!a;'S!.[;'S;=`!/d<%lO!.[a!/TT!a`{POr)esv)ew;'S)e;'S;=`)y<%lO)ea!/gP;=`<%l!.[!R!/sV!a`!cp{POr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!0]P;=`<%l!+q!R!0gX!a`!cpOr*Vrs(Vsv*Vwx)ex#c*V#c#d!1S#d;'S*V;'S;=`*s<%lO*V!R!1ZX!a`!cpOr*Vrs(Vsv*Vwx)ex#V*V#V#W!1v#W;'S*V;'S;=`*s<%lO*V!R!1}X!a`!cpOr*Vrs(Vsv*Vwx)ex#h*V#h#i!2j#i;'S*V;'S;=`*s<%lO*V!R!2qX!a`!cpOr*Vrs(Vsv*Vwx)ex#m*V#m#n!3^#n;'S*V;'S;=`*s<%lO*V!R!3eX!a`!cpOr*Vrs(Vsv*Vwx)ex#d*V#d#e!4Q#e;'S*V;'S;=`*s<%lO*V!R!4XX!a`!cpOr*Vrs(Vsv*Vwx)ex#X*V#X#Y!+q#Y;'S*V;'S;=`*s<%lO*V!R!4{Y!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!a!4t!a!b!:]!b;'S!4t;'S;=`!;r<%lO!4tq!5pV!cpOv!5kvx!6Vx!a!5k!a!b!7W!b;'S!5k;'S;=`!8V<%lO!5kP!6YTO!a!6V!a!b!6i!b;'S!6V;'S;=`!7Q<%lO!6VP!6lTO!`!6V!`!a!6{!a;'S!6V;'S;=`!7Q<%lO!6VP!7QOxPP!7TP;=`<%l!6Vq!7]V!cpOv!5kvx!6Vx!`!5k!`!a!7r!a;'S!5k;'S;=`!8V<%lO!5kq!7yS!cpxPOv(Vx;'S(V;'S;=`(h<%lO(Vq!8YP;=`<%l!5ka!8bX!a`Or!8]rs!6Vsv!8]vw!6Vw!a!8]!a!b!8}!b;'S!8];'S;=`!:V<%lO!8]a!9SX!a`Or!8]rs!6Vsv!8]vw!6Vw!`!8]!`!a!9o!a;'S!8];'S;=`!:V<%lO!8]a!9vT!a`xPOr)esv)ew;'S)e;'S;=`)y<%lO)ea!:YP;=`<%l!8]!R!:dY!a`!cpOr!4trs!5ksv!4tvw!6Vwx!8]x!`!4t!`!a!;S!a;'S!4t;'S;=`!;r<%lO!4t!R!;]V!a`!cpxPOr*Vrs(Vsv*Vwx)ex;'S*V;'S;=`*s<%lO*V!R!;uP;=`<%l!4t!V!<TXiS`P!a`!cpOr&Xrs&}sv&Xwx(tx!^&X!^!_*V!_;'S&X;'S;=`*y<%lO&X\",\n  tokenizers: [scriptTokens, styleTokens, textareaTokens, endTag, tagStart, commentContent, 0, 1, 2, 3, 4, 5],\n  topRules: {\"Document\":[0,15]},\n  dialects: {noMatch: 0, selfClosing: 485},\n  tokenPrec: 487\n});\n\nfunction getAttrs(openTag, input) {\n  let attrs = Object.create(null);\n  for (let att of openTag.getChildren(Attribute)) {\n    let name = att.getChild(AttributeName), value = att.getChild(AttributeValue) || att.getChild(UnquotedAttributeValue);\n    if (name) attrs[input.read(name.from, name.to)] =\n      !value ? \"\" : value.type.id == AttributeValue ? input.read(value.from + 1, value.to - 1) : input.read(value.from, value.to);\n  }\n  return attrs\n}\n\nfunction findTagName(openTag, input) {\n  let tagNameNode = openTag.getChild(TagName);\n  return tagNameNode ? input.read(tagNameNode.from, tagNameNode.to) : \" \"\n}\n\nfunction maybeNest(node, input, tags) {\n  let attrs;\n  for (let tag of tags) {\n    if (!tag.attrs || tag.attrs(attrs || (attrs = getAttrs(node.node.parent.firstChild, input))))\n      return {parser: tag.parser}\n  }\n  return null\n}\n\n// tags?: {\n//   tag: string,\n//   attrs?: ({[attr: string]: string}) => boolean,\n//   parser: Parser\n// }[]\n// attributes?: {\n//   name: string,\n//   tagName?: string,\n//   parser: Parser\n// }[]\n \nfunction configureNesting(tags = [], attributes = []) {\n  let script = [], style = [], textarea = [], other = [];\n  for (let tag of tags) {\n    let array = tag.tag == \"script\" ? script : tag.tag == \"style\" ? style : tag.tag == \"textarea\" ? textarea : other;\n    array.push(tag);\n  }\n  let attrs = attributes.length ? Object.create(null) : null;\n  for (let attr of attributes) (attrs[attr.name] || (attrs[attr.name] = [])).push(attr);\n\n  return parseMixed((node, input) => {\n    let id = node.type.id;\n    if (id == ScriptText) return maybeNest(node, input, script)\n    if (id == StyleText) return maybeNest(node, input, style)\n    if (id == TextareaText) return maybeNest(node, input, textarea)\n\n    if (id == Element && other.length) {\n      let n = node.node, open = n.firstChild, tagName = open && findTagName(open, input), attrs;\n      if (tagName) for (let tag of other) {\n        if (tag.tag == tagName && (!tag.attrs || tag.attrs(attrs || (attrs = getAttrs(n, input))))) {\n          let close = n.lastChild;\n          return {parser: tag.parser, overlay: [{from: open.to, to: close.type.id == CloseTag ? close.from : n.to}]}\n        }\n      }\n    }\n\n    if (attrs && id == Attribute) {\n      let n = node.node, nameNode;\n      if (nameNode = n.firstChild) {\n        let matches = attrs[input.read(nameNode.from, nameNode.to)];\n        if (matches) for (let attr of matches) {\n          if (attr.tagName && attr.tagName != findTagName(n.parent, input)) continue\n          let value = n.lastChild;\n          if (value.type.id == AttributeValue) {\n            let from = value.from + 1;\n            let last = value.lastChild, to = value.to - (last && last.isError ? 0 : 1);\n            if (to > from) return {parser: attr.parser, overlay: [{from, to}]}\n          } else if (value.type.id == UnquotedAttributeValue) {\n            return {parser: attr.parser, overlay: [{from: value.from, to: value.to}]}\n          }\n        }\n      }\n    }\n    return null\n  })\n}\n\nexport { configureNesting, parser };\n", "import { parser, configureNesting } from '@lezer/html';\nimport { cssLanguage, css } from '@codemirror/lang-css';\nimport { javascriptLanguage, typescriptLanguage, jsxLanguage, tsxLanguage, javascript } from '@codemirror/lang-javascript';\nimport { EditorView } from '@codemirror/view';\nimport { EditorSelection } from '@codemirror/state';\nimport { syntaxTree, LRLanguage, indentNodeProp, foldNodeProp, bracketMatchingHandle, LanguageSupport } from '@codemirror/language';\n\nconst Targets = [\"_blank\", \"_self\", \"_top\", \"_parent\"];\nconst Charsets = [\"ascii\", \"utf-8\", \"utf-16\", \"latin1\", \"latin1\"];\nconst Methods = [\"get\", \"post\", \"put\", \"delete\"];\nconst Encs = [\"application/x-www-form-urlencoded\", \"multipart/form-data\", \"text/plain\"];\nconst Bool = [\"true\", \"false\"];\nconst S = {}; // Empty tag spec\nconst Tags = {\n    a: {\n        attrs: {\n            href: null, ping: null, type: null,\n            media: null,\n            target: Targets,\n            hreflang: null\n        }\n    },\n    abbr: S,\n    address: S,\n    area: {\n        attrs: {\n            alt: null, coords: null, href: null, target: null, ping: null,\n            media: null, hreflang: null, type: null,\n            shape: [\"default\", \"rect\", \"circle\", \"poly\"]\n        }\n    },\n    article: S,\n    aside: S,\n    audio: {\n        attrs: {\n            src: null, mediagroup: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"],\n            preload: [\"none\", \"metadata\", \"auto\"],\n            autoplay: [\"autoplay\"],\n            loop: [\"loop\"],\n            controls: [\"controls\"]\n        }\n    },\n    b: S,\n    base: { attrs: { href: null, target: Targets } },\n    bdi: S,\n    bdo: S,\n    blockquote: { attrs: { cite: null } },\n    body: S,\n    br: S,\n    button: {\n        attrs: {\n            form: null, formaction: null, name: null, value: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"autofocus\"],\n            formenctype: Encs,\n            formmethod: Methods,\n            formnovalidate: [\"novalidate\"],\n            formtarget: Targets,\n            type: [\"submit\", \"reset\", \"button\"]\n        }\n    },\n    canvas: { attrs: { width: null, height: null } },\n    caption: S,\n    center: S,\n    cite: S,\n    code: S,\n    col: { attrs: { span: null } },\n    colgroup: { attrs: { span: null } },\n    command: {\n        attrs: {\n            type: [\"command\", \"checkbox\", \"radio\"],\n            label: null, icon: null, radiogroup: null, command: null, title: null,\n            disabled: [\"disabled\"],\n            checked: [\"checked\"]\n        }\n    },\n    data: { attrs: { value: null } },\n    datagrid: { attrs: { disabled: [\"disabled\"], multiple: [\"multiple\"] } },\n    datalist: { attrs: { data: null } },\n    dd: S,\n    del: { attrs: { cite: null, datetime: null } },\n    details: { attrs: { open: [\"open\"] } },\n    dfn: S,\n    div: S,\n    dl: S,\n    dt: S,\n    em: S,\n    embed: { attrs: { src: null, type: null, width: null, height: null } },\n    eventsource: { attrs: { src: null } },\n    fieldset: { attrs: { disabled: [\"disabled\"], form: null, name: null } },\n    figcaption: S,\n    figure: S,\n    footer: S,\n    form: {\n        attrs: {\n            action: null, name: null,\n            \"accept-charset\": Charsets,\n            autocomplete: [\"on\", \"off\"],\n            enctype: Encs,\n            method: Methods,\n            novalidate: [\"novalidate\"],\n            target: Targets\n        }\n    },\n    h1: S, h2: S, h3: S, h4: S, h5: S, h6: S,\n    head: {\n        children: [\"title\", \"base\", \"link\", \"style\", \"meta\", \"script\", \"noscript\", \"command\"]\n    },\n    header: S,\n    hgroup: S,\n    hr: S,\n    html: {\n        attrs: { manifest: null }\n    },\n    i: S,\n    iframe: {\n        attrs: {\n            src: null, srcdoc: null, name: null, width: null, height: null,\n            sandbox: [\"allow-top-navigation\", \"allow-same-origin\", \"allow-forms\", \"allow-scripts\"],\n            seamless: [\"seamless\"]\n        }\n    },\n    img: {\n        attrs: {\n            alt: null, src: null, ismap: null, usemap: null, width: null, height: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"]\n        }\n    },\n    input: {\n        attrs: {\n            alt: null, dirname: null, form: null, formaction: null,\n            height: null, list: null, max: null, maxlength: null, min: null,\n            name: null, pattern: null, placeholder: null, size: null, src: null,\n            step: null, value: null, width: null,\n            accept: [\"audio/*\", \"video/*\", \"image/*\"],\n            autocomplete: [\"on\", \"off\"],\n            autofocus: [\"autofocus\"],\n            checked: [\"checked\"],\n            disabled: [\"disabled\"],\n            formenctype: Encs,\n            formmethod: Methods,\n            formnovalidate: [\"novalidate\"],\n            formtarget: Targets,\n            multiple: [\"multiple\"],\n            readonly: [\"readonly\"],\n            required: [\"required\"],\n            type: [\"hidden\", \"text\", \"search\", \"tel\", \"url\", \"email\", \"password\", \"datetime\", \"date\", \"month\",\n                \"week\", \"time\", \"datetime-local\", \"number\", \"range\", \"color\", \"checkbox\", \"radio\",\n                \"file\", \"submit\", \"image\", \"reset\", \"button\"]\n        }\n    },\n    ins: { attrs: { cite: null, datetime: null } },\n    kbd: S,\n    keygen: {\n        attrs: {\n            challenge: null, form: null, name: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            keytype: [\"RSA\"]\n        }\n    },\n    label: { attrs: { for: null, form: null } },\n    legend: S,\n    li: { attrs: { value: null } },\n    link: {\n        attrs: {\n            href: null, type: null,\n            hreflang: null,\n            media: null,\n            sizes: [\"all\", \"16x16\", \"16x16 32x32\", \"16x16 32x32 64x64\"]\n        }\n    },\n    map: { attrs: { name: null } },\n    mark: S,\n    menu: { attrs: { label: null, type: [\"list\", \"context\", \"toolbar\"] } },\n    meta: {\n        attrs: {\n            content: null,\n            charset: Charsets,\n            name: [\"viewport\", \"application-name\", \"author\", \"description\", \"generator\", \"keywords\"],\n            \"http-equiv\": [\"content-language\", \"content-type\", \"default-style\", \"refresh\"]\n        }\n    },\n    meter: { attrs: { value: null, min: null, low: null, high: null, max: null, optimum: null } },\n    nav: S,\n    noscript: S,\n    object: {\n        attrs: {\n            data: null, type: null, name: null, usemap: null, form: null, width: null, height: null,\n            typemustmatch: [\"typemustmatch\"]\n        }\n    },\n    ol: { attrs: { reversed: [\"reversed\"], start: null, type: [\"1\", \"a\", \"A\", \"i\", \"I\"] },\n        children: [\"li\", \"script\", \"template\", \"ul\", \"ol\"] },\n    optgroup: { attrs: { disabled: [\"disabled\"], label: null } },\n    option: { attrs: { disabled: [\"disabled\"], label: null, selected: [\"selected\"], value: null } },\n    output: { attrs: { for: null, form: null, name: null } },\n    p: S,\n    param: { attrs: { name: null, value: null } },\n    pre: S,\n    progress: { attrs: { value: null, max: null } },\n    q: { attrs: { cite: null } },\n    rp: S,\n    rt: S,\n    ruby: S,\n    samp: S,\n    script: {\n        attrs: {\n            type: [\"text/javascript\"],\n            src: null,\n            async: [\"async\"],\n            defer: [\"defer\"],\n            charset: Charsets\n        }\n    },\n    section: S,\n    select: {\n        attrs: {\n            form: null, name: null, size: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            multiple: [\"multiple\"]\n        }\n    },\n    slot: { attrs: { name: null } },\n    small: S,\n    source: { attrs: { src: null, type: null, media: null } },\n    span: S,\n    strong: S,\n    style: {\n        attrs: {\n            type: [\"text/css\"],\n            media: null,\n            scoped: null\n        }\n    },\n    sub: S,\n    summary: S,\n    sup: S,\n    table: S,\n    tbody: S,\n    td: { attrs: { colspan: null, rowspan: null, headers: null } },\n    template: S,\n    textarea: {\n        attrs: {\n            dirname: null, form: null, maxlength: null, name: null, placeholder: null,\n            rows: null, cols: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            readonly: [\"readonly\"],\n            required: [\"required\"],\n            wrap: [\"soft\", \"hard\"]\n        }\n    },\n    tfoot: S,\n    th: { attrs: { colspan: null, rowspan: null, headers: null, scope: [\"row\", \"col\", \"rowgroup\", \"colgroup\"] } },\n    thead: S,\n    time: { attrs: { datetime: null } },\n    title: S,\n    tr: S,\n    track: {\n        attrs: {\n            src: null, label: null, default: null,\n            kind: [\"subtitles\", \"captions\", \"descriptions\", \"chapters\", \"metadata\"],\n            srclang: null\n        }\n    },\n    ul: { children: [\"li\", \"script\", \"template\", \"ul\", \"ol\"] },\n    var: S,\n    video: {\n        attrs: {\n            src: null, poster: null, width: null, height: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"],\n            preload: [\"auto\", \"metadata\", \"none\"],\n            autoplay: [\"autoplay\"],\n            mediagroup: [\"movie\"],\n            muted: [\"muted\"],\n            controls: [\"controls\"]\n        }\n    },\n    wbr: S\n};\nconst GlobalAttrs = {\n    accesskey: null,\n    class: null,\n    contenteditable: Bool,\n    contextmenu: null,\n    dir: [\"ltr\", \"rtl\", \"auto\"],\n    draggable: [\"true\", \"false\", \"auto\"],\n    dropzone: [\"copy\", \"move\", \"link\", \"string:\", \"file:\"],\n    hidden: [\"hidden\"],\n    id: null,\n    inert: [\"inert\"],\n    itemid: null,\n    itemprop: null,\n    itemref: null,\n    itemscope: [\"itemscope\"],\n    itemtype: null,\n    lang: [\"ar\", \"bn\", \"de\", \"en-GB\", \"en-US\", \"es\", \"fr\", \"hi\", \"id\", \"ja\", \"pa\", \"pt\", \"ru\", \"tr\", \"zh\"],\n    spellcheck: Bool,\n    autocorrect: Bool,\n    autocapitalize: Bool,\n    style: null,\n    tabindex: null,\n    title: null,\n    translate: [\"yes\", \"no\"],\n    rel: [\"stylesheet\", \"alternate\", \"author\", \"bookmark\", \"help\", \"license\", \"next\", \"nofollow\", \"noreferrer\", \"prefetch\", \"prev\", \"search\", \"tag\"],\n    role: /*@__PURE__*/\"alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer\".split(\" \"),\n    \"aria-activedescendant\": null,\n    \"aria-atomic\": Bool,\n    \"aria-autocomplete\": [\"inline\", \"list\", \"both\", \"none\"],\n    \"aria-busy\": Bool,\n    \"aria-checked\": [\"true\", \"false\", \"mixed\", \"undefined\"],\n    \"aria-controls\": null,\n    \"aria-describedby\": null,\n    \"aria-disabled\": Bool,\n    \"aria-dropeffect\": null,\n    \"aria-expanded\": [\"true\", \"false\", \"undefined\"],\n    \"aria-flowto\": null,\n    \"aria-grabbed\": [\"true\", \"false\", \"undefined\"],\n    \"aria-haspopup\": Bool,\n    \"aria-hidden\": Bool,\n    \"aria-invalid\": [\"true\", \"false\", \"grammar\", \"spelling\"],\n    \"aria-label\": null,\n    \"aria-labelledby\": null,\n    \"aria-level\": null,\n    \"aria-live\": [\"off\", \"polite\", \"assertive\"],\n    \"aria-multiline\": Bool,\n    \"aria-multiselectable\": Bool,\n    \"aria-owns\": null,\n    \"aria-posinset\": null,\n    \"aria-pressed\": [\"true\", \"false\", \"mixed\", \"undefined\"],\n    \"aria-readonly\": Bool,\n    \"aria-relevant\": null,\n    \"aria-required\": Bool,\n    \"aria-selected\": [\"true\", \"false\", \"undefined\"],\n    \"aria-setsize\": null,\n    \"aria-sort\": [\"ascending\", \"descending\", \"none\", \"other\"],\n    \"aria-valuemax\": null,\n    \"aria-valuemin\": null,\n    \"aria-valuenow\": null,\n    \"aria-valuetext\": null\n};\nconst eventAttributes = /*@__PURE__*/(\"beforeunload copy cut dragstart dragover dragleave dragenter dragend \" +\n    \"drag paste focus blur change click load mousedown mouseenter mouseleave \" +\n    \"mouseup keydown keyup resize scroll unload\").split(\" \").map(n => \"on\" + n);\nfor (let a of eventAttributes)\n    GlobalAttrs[a] = null;\nclass Schema {\n    constructor(extraTags, extraAttrs) {\n        this.tags = Object.assign(Object.assign({}, Tags), extraTags);\n        this.globalAttrs = Object.assign(Object.assign({}, GlobalAttrs), extraAttrs);\n        this.allTags = Object.keys(this.tags);\n        this.globalAttrNames = Object.keys(this.globalAttrs);\n    }\n}\nSchema.default = /*@__PURE__*/new Schema;\nfunction elementName(doc, tree, max = doc.length) {\n    if (!tree)\n        return \"\";\n    let tag = tree.firstChild;\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, Math.min(name.to, max)) : \"\";\n}\nfunction findParentElement(tree, skip = false) {\n    for (; tree; tree = tree.parent)\n        if (tree.name == \"Element\") {\n            if (skip)\n                skip = false;\n            else\n                return tree;\n        }\n    return null;\n}\nfunction allowedChildren(doc, tree, schema) {\n    let parentInfo = schema.tags[elementName(doc, findParentElement(tree))];\n    return (parentInfo === null || parentInfo === void 0 ? void 0 : parentInfo.children) || schema.allTags;\n}\nfunction openTags(doc, tree) {\n    let open = [];\n    for (let parent = findParentElement(tree); parent && !parent.type.isTop; parent = findParentElement(parent.parent)) {\n        let tagName = elementName(doc, parent);\n        if (tagName && parent.lastChild.name == \"CloseTag\")\n            break;\n        if (tagName && open.indexOf(tagName) < 0 && (tree.name == \"EndTag\" || tree.from >= parent.firstChild.to))\n            open.push(tagName);\n    }\n    return open;\n}\nconst identifier = /^[:\\-\\.\\w\\u00b7-\\uffff]*$/;\nfunction completeTag(state, schema, tree, from, to) {\n    let end = /\\s*>/.test(state.sliceDoc(to, to + 5)) ? \"\" : \">\";\n    let parent = findParentElement(tree, true);\n    return { from, to,\n        options: allowedChildren(state.doc, parent, schema).map(tagName => ({ label: tagName, type: \"type\" })).concat(openTags(state.doc, tree).map((tag, i) => ({ label: \"/\" + tag, apply: \"/\" + tag + end,\n            type: \"type\", boost: 99 - i }))),\n        validFor: /^\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/ };\n}\nfunction completeCloseTag(state, tree, from, to) {\n    let end = /\\s*>/.test(state.sliceDoc(to, to + 5)) ? \"\" : \">\";\n    return { from, to,\n        options: openTags(state.doc, tree).map((tag, i) => ({ label: tag, apply: tag + end, type: \"type\", boost: 99 - i })),\n        validFor: identifier };\n}\nfunction completeStartTag(state, schema, tree, pos) {\n    let options = [], level = 0;\n    for (let tagName of allowedChildren(state.doc, tree, schema))\n        options.push({ label: \"<\" + tagName, type: \"type\" });\n    for (let open of openTags(state.doc, tree))\n        options.push({ label: \"</\" + open + \">\", type: \"type\", boost: 99 - level++ });\n    return { from: pos, to: pos, options, validFor: /^<\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/ };\n}\nfunction completeAttrName(state, schema, tree, from, to) {\n    let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;\n    let localAttrs = info && info.attrs ? Object.keys(info.attrs) : [];\n    let names = info && info.globalAttrs === false ? localAttrs\n        : localAttrs.length ? localAttrs.concat(schema.globalAttrNames) : schema.globalAttrNames;\n    return { from, to,\n        options: names.map(attrName => ({ label: attrName, type: \"property\" })),\n        validFor: identifier };\n}\nfunction completeAttrValue(state, schema, tree, from, to) {\n    var _a;\n    let nameNode = (_a = tree.parent) === null || _a === void 0 ? void 0 : _a.getChild(\"AttributeName\");\n    let options = [], token = undefined;\n    if (nameNode) {\n        let attrName = state.sliceDoc(nameNode.from, nameNode.to);\n        let attrs = schema.globalAttrs[attrName];\n        if (!attrs) {\n            let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;\n            attrs = (info === null || info === void 0 ? void 0 : info.attrs) && info.attrs[attrName];\n        }\n        if (attrs) {\n            let base = state.sliceDoc(from, to).toLowerCase(), quoteStart = '\"', quoteEnd = '\"';\n            if (/^['\"]/.test(base)) {\n                token = base[0] == '\"' ? /^[^\"]*$/ : /^[^']*$/;\n                quoteStart = \"\";\n                quoteEnd = state.sliceDoc(to, to + 1) == base[0] ? \"\" : base[0];\n                base = base.slice(1);\n                from++;\n            }\n            else {\n                token = /^[^\\s<>='\"]*$/;\n            }\n            for (let value of attrs)\n                options.push({ label: value, apply: quoteStart + value + quoteEnd, type: \"constant\" });\n        }\n    }\n    return { from, to, options, validFor: token };\n}\nfunction htmlCompletionFor(schema, context) {\n    let { state, pos } = context, tree = syntaxTree(state).resolveInner(pos, -1), around = tree.resolve(pos);\n    for (let scan = pos, before; around == tree && (before = tree.childBefore(scan));) {\n        let last = before.lastChild;\n        if (!last || !last.type.isError || last.from < last.to)\n            break;\n        around = tree = before;\n        scan = last.from;\n    }\n    if (tree.name == \"TagName\") {\n        return tree.parent && /CloseTag$/.test(tree.parent.name) ? completeCloseTag(state, tree, tree.from, pos)\n            : completeTag(state, schema, tree, tree.from, pos);\n    }\n    else if (tree.name == \"StartTag\") {\n        return completeTag(state, schema, tree, pos, pos);\n    }\n    else if (tree.name == \"StartCloseTag\" || tree.name == \"IncompleteCloseTag\") {\n        return completeCloseTag(state, tree, pos, pos);\n    }\n    else if (tree.name == \"OpenTag\" || tree.name == \"SelfClosingTag\" || tree.name == \"AttributeName\") {\n        return completeAttrName(state, schema, tree, tree.name == \"AttributeName\" ? tree.from : pos, pos);\n    }\n    else if (tree.name == \"Is\" || tree.name == \"AttributeValue\" || tree.name == \"UnquotedAttributeValue\") {\n        return completeAttrValue(state, schema, tree, tree.name == \"Is\" ? pos : tree.from, pos);\n    }\n    else if (context.explicit && (around.name == \"Element\" || around.name == \"Text\" || around.name == \"Document\")) {\n        return completeStartTag(state, schema, tree, pos);\n    }\n    else {\n        return null;\n    }\n}\n/**\nHTML tag completion. Opens and closes tags and attributes in a\ncontext-aware way.\n*/\nfunction htmlCompletionSource(context) {\n    return htmlCompletionFor(Schema.default, context);\n}\n/**\nCreate a completion source for HTML extended with additional tags\nor attributes.\n*/\nfunction htmlCompletionSourceWith(config) {\n    let { extraTags, extraGlobalAttributes: extraAttrs } = config;\n    let schema = extraAttrs || extraTags ? new Schema(extraTags, extraAttrs) : Schema.default;\n    return (context) => htmlCompletionFor(schema, context);\n}\n\nconst jsonParser = /*@__PURE__*/javascriptLanguage.parser.configure({ top: \"SingleExpression\" });\nconst defaultNesting = [\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/typescript\" || attrs.lang == \"ts\",\n        parser: typescriptLanguage.parser },\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/babel\" || attrs.type == \"text/jsx\",\n        parser: jsxLanguage.parser },\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/typescript-jsx\",\n        parser: tsxLanguage.parser },\n    { tag: \"script\",\n        attrs(attrs) {\n            return /^(importmap|speculationrules|application\\/(.+\\+)?json)$/i.test(attrs.type);\n        },\n        parser: jsonParser },\n    { tag: \"script\",\n        attrs(attrs) {\n            return !attrs.type || /^(?:text|application)\\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(attrs.type);\n        },\n        parser: javascriptLanguage.parser },\n    { tag: \"style\",\n        attrs(attrs) {\n            return (!attrs.lang || attrs.lang == \"css\") && (!attrs.type || /^(text\\/)?(x-)?(stylesheet|css)$/i.test(attrs.type));\n        },\n        parser: cssLanguage.parser }\n];\nconst defaultAttrs = /*@__PURE__*/[\n    { name: \"style\",\n        parser: /*@__PURE__*/cssLanguage.parser.configure({ top: \"Styles\" }) }\n].concat(/*@__PURE__*/eventAttributes.map(name => ({ name, parser: javascriptLanguage.parser })));\n/**\nA language provider based on the [Lezer HTML\nparser](https://github.com/lezer-parser/html), extended with the\nJavaScript and CSS parsers to parse the content of `<script>` and\n`<style>` tags.\n*/\nconst htmlPlain = /*@__PURE__*/LRLanguage.define({\n    name: \"html\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Element(context) {\n                    let after = /^(\\s*)(<\\/)?/.exec(context.textAfter);\n                    if (context.node.to <= context.pos + after[0].length)\n                        return context.continue();\n                    return context.lineIndent(context.node.from) + (after[2] ? 0 : context.unit);\n                },\n                \"OpenTag CloseTag SelfClosingTag\"(context) {\n                    return context.column(context.node.from) + context.unit;\n                },\n                Document(context) {\n                    if (context.pos + /\\s*/.exec(context.textAfter)[0].length < context.node.to)\n                        return context.continue();\n                    let endElt = null, close;\n                    for (let cur = context.node;;) {\n                        let last = cur.lastChild;\n                        if (!last || last.name != \"Element\" || last.to != cur.to)\n                            break;\n                        endElt = cur = last;\n                    }\n                    if (endElt && !((close = endElt.lastChild) && (close.name == \"CloseTag\" || close.name == \"SelfClosingTag\")))\n                        return context.lineIndent(endElt.from) + context.unit;\n                    return null;\n                }\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                Element(node) {\n                    let first = node.firstChild, last = node.lastChild;\n                    if (!first || first.name != \"OpenTag\")\n                        return null;\n                    return { from: first.to, to: last.name == \"CloseTag\" ? last.from : node.to };\n                }\n            }),\n            /*@__PURE__*/bracketMatchingHandle.add({\n                \"OpenTag CloseTag\": node => node.getChild(\"TagName\")\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"<!--\", close: \"-->\" } },\n        indentOnInput: /^\\s*<\\/\\w+\\W$/,\n        wordChars: \"-._\"\n    }\n});\n/**\nA language provider based on the [Lezer HTML\nparser](https://github.com/lezer-parser/html), extended with the\nJavaScript and CSS parsers to parse the content of `<script>` and\n`<style>` tags.\n*/\nconst htmlLanguage = /*@__PURE__*/htmlPlain.configure({\n    wrap: /*@__PURE__*/configureNesting(defaultNesting, defaultAttrs)\n});\n/**\nLanguage support for HTML, including\n[`htmlCompletion`](https://codemirror.net/6/docs/ref/#lang-html.htmlCompletion) and JavaScript and\nCSS support extensions.\n*/\nfunction html(config = {}) {\n    let dialect = \"\", wrap;\n    if (config.matchClosingTags === false)\n        dialect = \"noMatch\";\n    if (config.selfClosingTags === true)\n        dialect = (dialect ? dialect + \" \" : \"\") + \"selfClosing\";\n    if (config.nestedLanguages && config.nestedLanguages.length ||\n        config.nestedAttributes && config.nestedAttributes.length)\n        wrap = configureNesting((config.nestedLanguages || []).concat(defaultNesting), (config.nestedAttributes || []).concat(defaultAttrs));\n    let lang = wrap ? htmlPlain.configure({ wrap, dialect }) : dialect ? htmlLanguage.configure({ dialect }) : htmlLanguage;\n    return new LanguageSupport(lang, [\n        htmlLanguage.data.of({ autocomplete: htmlCompletionSourceWith(config) }),\n        config.autoCloseTags !== false ? autoCloseTags : [],\n        javascript().support,\n        css().support\n    ]);\n}\nconst selfClosers = /*@__PURE__*/new Set(/*@__PURE__*/\"area base br col command embed frame hr img input keygen link meta param source track wbr menuitem\".split(\" \"));\n/**\nExtension that will automatically insert close tags when a `>` or\n`/` is typed.\n*/\nconst autoCloseTags = /*@__PURE__*/EditorView.inputHandler.of((view, from, to, text, insertTransaction) => {\n    if (view.composing || view.state.readOnly || from != to || (text != \">\" && text != \"/\") ||\n        !htmlLanguage.isActiveAt(view.state, from, -1))\n        return false;\n    let base = insertTransaction(), { state } = base;\n    let closeTags = state.changeByRange(range => {\n        var _a, _b, _c;\n        let didType = state.doc.sliceString(range.from - 1, range.to) == text;\n        let { head } = range, after = syntaxTree(state).resolveInner(head, -1), name;\n        if (didType && text == \">\" && after.name == \"EndTag\") {\n            let tag = after.parent;\n            if (((_b = (_a = tag.parent) === null || _a === void 0 ? void 0 : _a.lastChild) === null || _b === void 0 ? void 0 : _b.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag.parent, head)) &&\n                !selfClosers.has(name)) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `</${name}>`;\n                return { range, changes: { from: head, to, insert } };\n            }\n        }\n        else if (didType && text == \"/\" && after.name == \"IncompleteCloseTag\") {\n            let tag = after.parent;\n            if (after.from == head - 2 && ((_c = tag.lastChild) === null || _c === void 0 ? void 0 : _c.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag, head)) && !selfClosers.has(name)) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `${name}>`;\n                return {\n                    range: EditorSelection.cursor(head + insert.length, -1),\n                    changes: { from: head, to, insert }\n                };\n            }\n        }\n        return { range };\n    });\n    if (closeTags.changes.empty)\n        return false;\n    view.dispatch([\n        base,\n        state.update(closeTags, {\n            userEvent: \"input.complete\",\n            scrollIntoView: true\n        })\n    ]);\n    return true;\n});\n\nexport { autoCloseTags, html, htmlCompletionSource, htmlCompletionSourceWith, htmlLanguage, htmlPlain };\n"], "names": ["scriptText", "StartCloseScriptTag", "styleText", "StartCloseStyleTag", "textareaText", "StartCloseTextareaTag", "EndTag", "SelfClosingEndTag", "StartTag", "StartScriptTag", "StartStyleTag", "StartTextareaTag", "StartSelfClosingTag", "StartCloseTag", "NoMatchStartCloseTag", "MismatchedStartCloseTag", "missingCloseTag", "IncompleteCloseTag", "commentContent$1", "Element", "TagName", "Attribute", "AttributeName", "AttributeValue", "UnquotedAttributeValue", "ScriptText", "StyleText", "TextareaText", "OpenTag", "CloseTag", "Dialect_noMatch", "Dialect_selfClosing", "selfClosers", "implicitlyClosed", "closeOnOpen", "nameChar", "ch", "isSpace", "cachedName", "cachedInput", "cachedPos", "tagNameAfter", "input", "offset", "pos", "next", "name", "question", "bang", "lessThan", "greaterThan", "slash", "dash", "ElementContext", "parent", "i", "startTagTerms", "elementContext", "ContextTracker", "context", "term", "stack", "node", "type", "tagStart", "ExternalTokenizer", "close", "cx", "commentContent", "dashes", "inForeignElement", "endTag", "selfClosing", "contentTokenizer", "tag", "textToken", "endToken", "lastState", "state", "matchedLen", "scriptTokens", "styleTokens", "textareaTokens", "htmlHighlighting", "styleTags", "tags", "parser", "<PERSON><PERSON><PERSON><PERSON>", "getAttrs", "openTag", "attrs", "att", "value", "findTagName", "tagNameNode", "maybeNest", "configureNesting", "attributes", "script", "style", "textarea", "other", "attr", "parseMixed", "id", "n", "open", "tagName", "nameNode", "matches", "from", "last", "to", "Targets", "Charsets", "Methods", "Encs", "Bool", "S", "Tags", "GlobalAttrs", "eventAttributes", "a", "<PERSON><PERSON><PERSON>", "extraTags", "extraAttrs", "elementName", "doc", "tree", "max", "findParentElement", "skip", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "schema", "parentInfo", "openTags", "identifier", "completeTag", "end", "completeCloseTag", "completeStartTag", "options", "level", "completeAttrName", "elt", "info", "localAttrs", "names", "attrName", "completeAttrValue", "_a", "token", "base", "quoteStart", "quoteEnd", "htmlCompletionFor", "syntaxTree", "around", "scan", "before", "htmlCompletionSource", "htmlCompletionSourceWith", "config", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "javascriptLanguage", "defaultNesting", "typescriptLanguage", "jsxLanguage", "tsxLanguage", "cssLanguage", "defaultAttrs", "htmlPlain", "LRLanguage", "indentNodeProp", "after", "endElt", "cur", "foldNodeProp", "first", "bracketMatchingHandle", "htmlLanguage", "html", "dialect", "wrap", "lang", "LanguageSupport", "autoCloseTags", "javascript", "css", "Editor<PERSON><PERSON><PERSON>", "view", "text", "insertTransaction", "closeTags", "range", "_b", "_c", "didType", "head", "insert", "EditorSelection"], "mappings": "goCAKA,MAAMA,GAAa,GACjBC,GAAsB,EACtBC,GAAY,GACZC,GAAqB,EACrBC,GAAe,GACfC,GAAwB,EACxBC,EAAS,EACTC,GAAoB,EACpBC,EAAW,EACXC,GAAiB,EACjBC,GAAgB,EAChBC,GAAmB,EACnBC,GAAsB,GACtBC,GAAgB,GAChBC,GAAuB,GACvBC,GAA0B,GAC1BC,EAAkB,GAClBC,GAAqB,GACrBC,EAAmB,GACnBC,GAAU,GACVC,GAAU,GACVC,GAAY,GACZC,GAAgB,GAChBC,EAAiB,GACjBC,GAAyB,GACzBC,GAAa,GACbC,GAAY,GACZC,GAAe,GACfC,GAAU,GACVC,GAAW,GACXC,GAAkB,EAClBC,GAAsB,EAIlBC,GAAc,CAClB,KAAM,GAAM,KAAM,GAAM,GAAI,GAAM,IAAK,GAAM,QAAS,GACtD,MAAO,GAAM,MAAO,GAAM,GAAI,GAAM,IAAK,GAAM,MAAO,GACtD,OAAQ,GAAM,KAAM,GAAM,KAAM,GAAM,MAAO,GAAM,OAAQ,GAC3D,MAAO,GAAM,IAAK,GAAM,SAAU,EACpC,EAEMC,GAAmB,CACvB,GAAI,GAAM,GAAI,GAAM,SAAU,GAAM,OAAQ,GAAM,EAAG,GACrD,GAAI,GAAM,GAAI,GAAM,MAAO,GAAM,GAAI,GAAM,MAAO,GAClD,GAAI,GAAM,GAAI,EAChB,EAEMC,EAAc,CAClB,GAAI,CAAC,GAAI,GAAM,GAAI,EAAI,EACvB,GAAI,CAAC,GAAI,GAAM,GAAI,EAAI,EACvB,GAAI,CAAC,GAAI,EAAI,EACb,OAAQ,CAAC,OAAQ,GAAM,SAAU,EAAI,EACrC,SAAU,CAAC,SAAU,EAAI,EACzB,EAAG,CACD,QAAS,GAAM,QAAS,GAAM,MAAO,GAAM,WAAY,GAAM,IAAK,GAClE,IAAK,GAAM,GAAI,GAAM,SAAU,GAAM,OAAQ,GAAM,KAAM,GACzD,GAAI,GAAM,GAAI,GAAM,GAAI,GAAM,GAAI,GAAM,GAAI,GAAM,GAAI,GACtD,OAAQ,GAAM,OAAQ,GAAM,GAAI,GAAM,KAAM,GAAM,IAAK,GAAM,GAAI,GACjE,EAAG,GAAM,IAAK,GAAM,QAAS,GAAM,MAAO,GAAM,GAAI,EACrD,EACD,GAAI,CAAC,GAAI,GAAM,GAAI,EAAI,EACvB,GAAI,CAAC,GAAI,GAAM,GAAI,EAAI,EACvB,MAAO,CAAC,MAAO,GAAM,MAAO,EAAI,EAChC,GAAI,CAAC,GAAI,GAAM,GAAI,EAAI,EACvB,MAAO,CAAC,MAAO,EAAI,EACnB,GAAI,CAAC,GAAI,GAAM,GAAI,EAAI,EACvB,MAAO,CAAC,MAAO,GAAM,MAAO,EAAI,EAChC,GAAI,CAAC,GAAI,EAAI,CACf,EAEA,SAASC,GAASC,EAAI,CACpB,OAAOA,GAAM,IAAMA,GAAM,IAAMA,GAAM,IAAMA,GAAM,IAAMA,GAAM,IAAMA,GAAM,IAAMA,GAAM,IAAMA,GAAM,KAAOA,GAAM,GAChH,CAEA,SAASC,GAAQD,EAAI,CACnB,OAAOA,GAAM,GAAKA,GAAM,IAAMA,GAAM,IAAMA,GAAM,EAClD,CAEA,IAAIE,EAAa,KAAMC,EAAc,KAAMC,EAAY,EACvD,SAASC,EAAaC,EAAOC,EAAQ,CACnC,IAAIC,EAAMF,EAAM,IAAMC,EACtB,GAAIH,GAAaI,GAAOL,GAAeG,EAAO,OAAOJ,EACrD,IAAIO,EAAOH,EAAM,KAAKC,CAAM,EAC5B,KAAON,GAAQQ,CAAI,GAAGA,EAAOH,EAAM,KAAK,EAAEC,CAAM,EAChD,IAAIG,EAAO,GACX,KACOX,GAASU,CAAI,GAClBC,GAAQ,OAAO,aAAaD,CAAI,EAChCA,EAAOH,EAAM,KAAK,EAAEC,CAAM,EAG5B,OAAAJ,EAAcG,EAAOF,EAAYI,EAC1BN,EAAaQ,EAAOA,EAAK,YAAa,EAAGD,GAAQE,IAAYF,GAAQG,GAAO,OAAY,IACjG,CAEA,MAAMC,GAAW,GAAIC,EAAc,GAAIC,EAAQ,GAAIJ,GAAW,GAAIC,GAAO,GAAII,GAAO,GAEpF,SAASC,EAAeP,EAAMQ,EAAQ,CACpC,KAAK,KAAOR,EACZ,KAAK,OAASQ,EACd,KAAK,KAAOA,EAASA,EAAO,KAAO,EACnC,QAASC,EAAI,EAAGA,EAAIT,EAAK,OAAQS,IAAK,KAAK,OAAS,KAAK,MAAQ,GAAKT,EAAK,WAAWS,CAAC,GAAKT,EAAK,WAAWS,CAAC,GAAK,EACpH,CAEA,MAAMC,GAAgB,CAAChD,EAAUI,GAAqBH,GAAgBC,GAAeC,EAAgB,EAE/F8C,GAAiB,IAAIC,GAAe,CACxC,MAAO,KACP,MAAMC,EAASC,EAAMC,EAAOnB,EAAO,CACjC,OAAOc,GAAc,QAAQI,CAAI,EAAI,GAAK,IAAIP,EAAeZ,EAAaC,EAAO,CAAC,GAAK,GAAIiB,CAAO,EAAIA,CACvG,EACD,OAAOA,EAASC,EAAM,CACpB,OAAOA,GAAQzC,IAAWwC,EAAUA,EAAQ,OAASA,CACtD,EACD,MAAMA,EAASG,EAAMD,EAAOnB,EAAO,CACjC,IAAIqB,EAAOD,EAAK,KAAK,GACrB,OAAOC,GAAQvD,GAAYuD,GAAQnC,GAC/B,IAAIyB,EAAeZ,EAAaC,EAAO,CAAC,GAAK,GAAIiB,CAAO,EAAIA,CACjE,EACD,KAAKA,EAAS,CAAE,OAAOA,EAAUA,EAAQ,KAAO,CAAG,EACnD,OAAQ,EACV,CAAC,EAEKK,GAAW,IAAIC,EAAkB,CAACvB,EAAOmB,IAAU,CACvD,GAAInB,EAAM,MAAQO,GAAU,CAEtBP,EAAM,KAAO,GAAKmB,EAAM,SAASnB,EAAM,YAAY1B,CAAe,EACtE,MACD,CACD0B,EAAM,QAAO,EACb,IAAIwB,EAAQxB,EAAM,MAAQS,EACtBe,GAAOxB,EAAM,UACjB,IAAII,EAAOL,EAAaC,EAAO,CAAC,EAChC,GAAII,IAAS,OAAW,OACxB,GAAI,CAACA,EAAM,OAAOJ,EAAM,YAAYwB,EAAQjD,GAAqBT,CAAQ,EAEzE,IAAI8C,EAASO,EAAM,QAAUA,EAAM,QAAQ,KAAO,KAClD,GAAIK,EAAO,CACT,GAAIpB,GAAQQ,EAAQ,OAAOZ,EAAM,YAAY7B,EAAa,EAC1D,GAAIyC,GAAUrB,GAAiBqB,CAAM,EAAG,OAAOZ,EAAM,YAAY1B,EAAiB,EAAE,EACpF,GAAI6C,EAAM,eAAe/B,EAAe,EAAG,OAAOY,EAAM,YAAY5B,EAAoB,EACxF,QAASqD,EAAKN,EAAM,QAASM,EAAIA,EAAKA,EAAG,OAAQ,GAAIA,EAAG,MAAQrB,EAAM,OACtEJ,EAAM,YAAY3B,EAAuB,CAC7C,KAAS,CACL,GAAI+B,GAAQ,SAAU,OAAOJ,EAAM,YAAYjC,EAAc,EAC7D,GAAIqC,GAAQ,QAAS,OAAOJ,EAAM,YAAYhC,EAAa,EAC3D,GAAIoC,GAAQ,WAAY,OAAOJ,EAAM,YAAY/B,EAAgB,EACjE,GAAIqB,GAAY,eAAec,CAAI,EAAG,OAAOJ,EAAM,YAAY9B,EAAmB,EAC9E0C,GAAUpB,EAAYoB,CAAM,GAAKpB,EAAYoB,CAAM,EAAER,CAAI,EAAGJ,EAAM,YAAY1B,EAAiB,EAAE,EAChG0B,EAAM,YAAYlC,CAAQ,CAChC,CACH,EAAG,CAAC,WAAY,EAAI,CAAC,EAEf4D,GAAiB,IAAIH,EAAkBvB,GAAS,CACpD,QAAS2B,EAAS,EAAGd,EAAI,GAAIA,IAAK,CAChC,GAAIb,EAAM,KAAO,EAAG,CACda,GAAGb,EAAM,YAAYxB,CAAgB,EACzC,KACD,CACD,GAAIwB,EAAM,MAAQU,GAChBiB,YACS3B,EAAM,MAAQQ,GAAemB,GAAU,EAAG,CAC/Cd,EAAI,GAAGb,EAAM,YAAYxB,EAAkB,EAAE,EACjD,KACN,MACMmD,EAAS,EAEX3B,EAAM,QAAO,CACd,CACH,CAAC,EAED,SAAS4B,GAAiBX,EAAS,CACjC,KAAOA,EAASA,EAAUA,EAAQ,OAChC,GAAIA,EAAQ,MAAQ,OAASA,EAAQ,MAAQ,OAAQ,MAAO,GAC9D,MAAO,EACT,CAEA,MAAMY,GAAS,IAAIN,EAAkB,CAACvB,EAAOmB,IAAU,CACrD,GAAInB,EAAM,MAAQS,GAAST,EAAM,KAAK,CAAC,GAAKQ,EAAa,CACvD,IAAIsB,EAAcX,EAAM,eAAe9B,EAAmB,GAAKuC,GAAiBT,EAAM,OAAO,EAC7FnB,EAAM,YAAY8B,EAAcjE,GAAoBD,EAAQ,CAAC,CACjE,MAAaoC,EAAM,MAAQQ,GACvBR,EAAM,YAAYpC,EAAQ,CAAC,CAE/B,CAAC,EAED,SAASmE,EAAiBC,EAAKC,EAAWC,EAAU,CAClD,IAAIC,EAAY,EAAIH,EAAI,OACxB,OAAO,IAAIT,EAAkBvB,GAAS,CAOpC,QAASoC,EAAQ,EAAGC,EAAa,EAAG,EAAI,GAAI,IAAK,CAC/C,GAAIrC,EAAM,KAAO,EAAG,CACd,GAAGA,EAAM,YAAYiC,CAAS,EAClC,KACD,CACD,GAAIG,GAAS,GAAKpC,EAAM,MAAQO,IAC5B6B,GAAS,GAAKpC,EAAM,MAAQS,GAC5B2B,GAAS,GAAKA,EAAQD,GAAanC,EAAM,MAAQgC,EAAI,WAAWI,EAAQ,CAAC,EAC3EA,IACAC,aACUD,GAAS,GAAKA,GAASD,IAAcxC,GAAQK,EAAM,IAAI,EACjEqC,YACSD,GAASD,GAAanC,EAAM,MAAQQ,EAAa,CACtD,EAAI6B,EACNrC,EAAM,YAAYiC,EAAW,CAACI,CAAU,EAExCrC,EAAM,YAAYkC,EAAU,EAAEG,EAAa,EAAE,EAC/C,KACR,UAAkBrC,EAAM,MAAQ,IAAiBA,EAAM,MAAQ,KAAkB,EAAG,CAC5EA,EAAM,YAAYiC,EAAW,CAAC,EAC9B,KACR,MACQG,EAAQC,EAAa,EAEvBrC,EAAM,QAAO,CACd,CACL,CAAG,CACH,CAEA,MAAMsC,GAAeP,EAAiB,SAAUzE,GAAYC,EAAmB,EAEzEgF,GAAcR,EAAiB,QAASvE,GAAWC,EAAkB,EAErE+E,GAAiBT,EAAiB,WAAYrE,GAAcC,EAAqB,EAEjF8E,GAAmBC,GAAU,CACjC,eAAgBC,EAAK,QACrB,kDAAmDA,EAAK,aACxD,QAASA,EAAK,QACd,6BAA8B,CAACA,EAAK,QAAUA,EAAK,OAAO,EAC1D,cAAeA,EAAK,cACpB,wCAAyCA,EAAK,eAC9C,GAAIA,EAAK,mBACT,qCAAsCA,EAAK,UAC3C,QAASA,EAAK,aACd,eAAgBA,EAAK,sBACrB,YAAaA,EAAK,YACpB,CAAC,EAGKC,GAASC,GAAS,YAAY,CAClC,QAAS,GACT,OAAQ,wgCACR,UAAW,+kBACX,KAAM,uRACN,UAAW,qfACX,QAAS,GACT,QAAS9B,GACT,UAAW,CACT,CAAC,WAAY,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,SAAS,EAAE,2BAA2B,GAAG,GAAG,GAAG,GAAG,GAAG,UAAU,EACxG,CAAC,WAAY,EAAE,yBAAyB,EAAE,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,SAAS,EAC7E,CAAC,QAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,SAAS,GAAG,qBAAqB,GAAG,GAAG,GAAG,GAAG,oBAAoB,CAC1G,EACD,YAAa,CAAC0B,EAAgB,EAC9B,aAAc,CAAC,CAAC,EAChB,gBAAiB,EACjB,UAAW,ioMACX,WAAY,CAACH,GAAcC,GAAaC,GAAgBX,GAAQP,GAAUI,GAAgB,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAC1G,SAAU,CAAC,SAAW,CAAC,EAAE,EAAE,CAAC,EAC5B,SAAU,CAAC,QAAS,EAAG,YAAa,GAAG,EACvC,UAAW,GACb,CAAC,EAED,SAASoB,GAASC,EAAS/C,EAAO,CAChC,IAAIgD,EAAQ,OAAO,OAAO,IAAI,EAC9B,QAASC,KAAOF,EAAQ,YAAYpE,EAAS,EAAG,CAC9C,IAAIyB,EAAO6C,EAAI,SAASrE,EAAa,EAAGsE,EAAQD,EAAI,SAASpE,CAAc,GAAKoE,EAAI,SAASnE,EAAsB,EAC/GsB,IAAM4C,EAAMhD,EAAM,KAAKI,EAAK,KAAMA,EAAK,EAAE,CAAC,EAC3C8C,EAAaA,EAAM,KAAK,IAAMrE,EAAiBmB,EAAM,KAAKkD,EAAM,KAAO,EAAGA,EAAM,GAAK,CAAC,EAAIlD,EAAM,KAAKkD,EAAM,KAAMA,EAAM,EAAE,EAAjH,GACZ,CACD,OAAOF,CACT,CAEA,SAASG,EAAYJ,EAAS/C,EAAO,CACnC,IAAIoD,EAAcL,EAAQ,SAASrE,EAAO,EAC1C,OAAO0E,EAAcpD,EAAM,KAAKoD,EAAY,KAAMA,EAAY,EAAE,EAAI,GACtE,CAEA,SAASC,EAAUjC,EAAMpB,EAAO2C,EAAM,CACpC,IAAIK,EACJ,QAAShB,KAAOW,EACd,GAAI,CAACX,EAAI,OAASA,EAAI,MAAMgB,IAAUA,EAAQF,GAAS1B,EAAK,KAAK,OAAO,WAAYpB,CAAK,EAAE,EACzF,MAAO,CAAC,OAAQgC,EAAI,MAAM,EAE9B,OAAO,IACT,CAaA,SAASsB,GAAiBX,EAAO,GAAIY,EAAa,CAAA,EAAI,CACpD,IAAIC,EAAS,CAAE,EAAEC,EAAQ,CAAA,EAAIC,EAAW,CAAE,EAAEC,EAAQ,GACpD,QAAS3B,KAAOW,GACFX,EAAI,KAAO,SAAWwB,EAASxB,EAAI,KAAO,QAAUyB,EAAQzB,EAAI,KAAO,WAAa0B,EAAWC,GACrG,KAAK3B,CAAG,EAEhB,IAAIgB,EAAQO,EAAW,OAAS,OAAO,OAAO,IAAI,EAAI,KACtD,QAASK,KAAQL,GAAaP,EAAMY,EAAK,IAAI,IAAMZ,EAAMY,EAAK,IAAI,EAAI,CAAA,IAAK,KAAKA,CAAI,EAEpF,OAAOC,GAAW,CAACzC,EAAMpB,IAAU,CACjC,IAAI8D,EAAK1C,EAAK,KAAK,GACnB,GAAI0C,GAAM/E,GAAY,OAAOsE,EAAUjC,EAAMpB,EAAOwD,CAAM,EAC1D,GAAIM,GAAM9E,GAAW,OAAOqE,EAAUjC,EAAMpB,EAAOyD,CAAK,EACxD,GAAIK,GAAM7E,GAAc,OAAOoE,EAAUjC,EAAMpB,EAAO0D,CAAQ,EAE9D,GAAII,GAAMrF,IAAWkF,EAAM,OAAQ,CACjC,IAAII,EAAI3C,EAAK,KAAM4C,EAAOD,EAAE,WAAYE,EAAUD,GAAQb,EAAYa,EAAMhE,CAAK,EAAGgD,EACpF,GAAIiB,GAAS,QAASjC,KAAO2B,EAC3B,GAAI3B,EAAI,KAAOiC,IAAY,CAACjC,EAAI,OAASA,EAAI,MAAMgB,IAAUA,EAAQF,GAASiB,EAAG/D,CAAK,EAAE,GAAI,CAC1F,IAAIwB,EAAQuC,EAAE,UACd,MAAO,CAAC,OAAQ/B,EAAI,OAAQ,QAAS,CAAC,CAAC,KAAMgC,EAAK,GAAI,GAAIxC,EAAM,KAAK,IAAMrC,GAAWqC,EAAM,KAAOuC,EAAE,EAAE,CAAC,CAAC,CAC1G,EAEJ,CAED,GAAIf,GAASc,GAAMnF,GAAW,CAC5B,IAAIoF,EAAI3C,EAAK,KAAM8C,EACnB,GAAIA,EAAWH,EAAE,WAAY,CAC3B,IAAII,EAAUnB,EAAMhD,EAAM,KAAKkE,EAAS,KAAMA,EAAS,EAAE,CAAC,EAC1D,GAAIC,EAAS,QAASP,KAAQO,EAAS,CACrC,GAAIP,EAAK,SAAWA,EAAK,SAAWT,EAAYY,EAAE,OAAQ/D,CAAK,EAAG,SAClE,IAAIkD,EAAQa,EAAE,UACd,GAAIb,EAAM,KAAK,IAAMrE,EAAgB,CACnC,IAAIuF,EAAOlB,EAAM,KAAO,EACpBmB,EAAOnB,EAAM,UAAWoB,EAAKpB,EAAM,IAAMmB,GAAQA,EAAK,QAAU,EAAI,GACxE,GAAIC,EAAKF,EAAM,MAAO,CAAC,OAAQR,EAAK,OAAQ,QAAS,CAAC,CAAC,KAAAQ,EAAM,GAAAE,CAAE,CAAC,CAAC,CAClE,SAAUpB,EAAM,KAAK,IAAMpE,GAC1B,MAAO,CAAC,OAAQ8E,EAAK,OAAQ,QAAS,CAAC,CAAC,KAAMV,EAAM,KAAM,GAAIA,EAAM,EAAE,CAAC,CAAC,CAE3E,CACF,CACF,CACD,OAAO,IACX,CAAG,CACH,CC1VA,MAAMqB,EAAU,CAAC,SAAU,QAAS,OAAQ,SAAS,EAC/CC,EAAW,CAAC,QAAS,QAAS,SAAU,SAAU,QAAQ,EAC1DC,EAAU,CAAC,MAAO,OAAQ,MAAO,QAAQ,EACzCC,EAAO,CAAC,oCAAqC,sBAAuB,YAAY,EAChFC,EAAO,CAAC,OAAQ,OAAO,EACvBC,EAAI,CAAA,EACJC,GAAO,CACT,EAAG,CACC,MAAO,CACH,KAAM,KAAM,KAAM,KAAM,KAAM,KAC9B,MAAO,KACP,OAAQN,EACR,SAAU,IACb,CACJ,EACD,KAAMK,EACN,QAASA,EACT,KAAM,CACF,MAAO,CACH,IAAK,KAAM,OAAQ,KAAM,KAAM,KAAM,OAAQ,KAAM,KAAM,KACzD,MAAO,KAAM,SAAU,KAAM,KAAM,KACnC,MAAO,CAAC,UAAW,OAAQ,SAAU,MAAM,CAC9C,CACJ,EACD,QAASA,EACT,MAAOA,EACP,MAAO,CACH,MAAO,CACH,IAAK,KAAM,WAAY,KACvB,YAAa,CAAC,YAAa,iBAAiB,EAC5C,QAAS,CAAC,OAAQ,WAAY,MAAM,EACpC,SAAU,CAAC,UAAU,EACrB,KAAM,CAAC,MAAM,EACb,SAAU,CAAC,UAAU,CACxB,CACJ,EACD,EAAGA,EACH,KAAM,CAAE,MAAO,CAAE,KAAM,KAAM,OAAQL,EAAW,EAChD,IAAKK,EACL,IAAKA,EACL,WAAY,CAAE,MAAO,CAAE,KAAM,IAAI,CAAI,EACrC,KAAMA,EACN,GAAIA,EACJ,OAAQ,CACJ,MAAO,CACH,KAAM,KAAM,WAAY,KAAM,KAAM,KAAM,MAAO,KACjD,UAAW,CAAC,WAAW,EACvB,SAAU,CAAC,WAAW,EACtB,YAAaF,EACb,WAAYD,EACZ,eAAgB,CAAC,YAAY,EAC7B,WAAYF,EACZ,KAAM,CAAC,SAAU,QAAS,QAAQ,CACrC,CACJ,EACD,OAAQ,CAAE,MAAO,CAAE,MAAO,KAAM,OAAQ,KAAQ,EAChD,QAASK,EACT,OAAQA,EACR,KAAMA,EACN,KAAMA,EACN,IAAK,CAAE,MAAO,CAAE,KAAM,IAAI,CAAI,EAC9B,SAAU,CAAE,MAAO,CAAE,KAAM,IAAI,CAAI,EACnC,QAAS,CACL,MAAO,CACH,KAAM,CAAC,UAAW,WAAY,OAAO,EACrC,MAAO,KAAM,KAAM,KAAM,WAAY,KAAM,QAAS,KAAM,MAAO,KACjE,SAAU,CAAC,UAAU,EACrB,QAAS,CAAC,SAAS,CACtB,CACJ,EACD,KAAM,CAAE,MAAO,CAAE,MAAO,IAAI,CAAI,EAChC,SAAU,CAAE,MAAO,CAAE,SAAU,CAAC,UAAU,EAAG,SAAU,CAAC,UAAU,EAAK,EACvE,SAAU,CAAE,MAAO,CAAE,KAAM,IAAI,CAAI,EACnC,GAAIA,EACJ,IAAK,CAAE,MAAO,CAAE,KAAM,KAAM,SAAU,KAAQ,EAC9C,QAAS,CAAE,MAAO,CAAE,KAAM,CAAC,MAAM,CAAC,CAAI,EACtC,IAAKA,EACL,IAAKA,EACL,GAAIA,EACJ,GAAIA,EACJ,GAAIA,EACJ,MAAO,CAAE,MAAO,CAAE,IAAK,KAAM,KAAM,KAAM,MAAO,KAAM,OAAQ,IAAI,CAAI,EACtE,YAAa,CAAE,MAAO,CAAE,IAAK,IAAI,CAAI,EACrC,SAAU,CAAE,MAAO,CAAE,SAAU,CAAC,UAAU,EAAG,KAAM,KAAM,KAAM,KAAQ,EACvE,WAAYA,EACZ,OAAQA,EACR,OAAQA,EACR,KAAM,CACF,MAAO,CACH,OAAQ,KAAM,KAAM,KACpB,iBAAkBJ,EAClB,aAAc,CAAC,KAAM,KAAK,EAC1B,QAASE,EACT,OAAQD,EACR,WAAY,CAAC,YAAY,EACzB,OAAQF,CACX,CACJ,EACD,GAAIK,EAAG,GAAIA,EAAG,GAAIA,EAAG,GAAIA,EAAG,GAAIA,EAAG,GAAIA,EACvC,KAAM,CACF,SAAU,CAAC,QAAS,OAAQ,OAAQ,QAAS,OAAQ,SAAU,WAAY,SAAS,CACvF,EACD,OAAQA,EACR,OAAQA,EACR,GAAIA,EACJ,KAAM,CACF,MAAO,CAAE,SAAU,IAAM,CAC5B,EACD,EAAGA,EACH,OAAQ,CACJ,MAAO,CACH,IAAK,KAAM,OAAQ,KAAM,KAAM,KAAM,MAAO,KAAM,OAAQ,KAC1D,QAAS,CAAC,uBAAwB,oBAAqB,cAAe,eAAe,EACrF,SAAU,CAAC,UAAU,CACxB,CACJ,EACD,IAAK,CACD,MAAO,CACH,IAAK,KAAM,IAAK,KAAM,MAAO,KAAM,OAAQ,KAAM,MAAO,KAAM,OAAQ,KACtE,YAAa,CAAC,YAAa,iBAAiB,CAC/C,CACJ,EACD,MAAO,CACH,MAAO,CACH,IAAK,KAAM,QAAS,KAAM,KAAM,KAAM,WAAY,KAClD,OAAQ,KAAM,KAAM,KAAM,IAAK,KAAM,UAAW,KAAM,IAAK,KAC3D,KAAM,KAAM,QAAS,KAAM,YAAa,KAAM,KAAM,KAAM,IAAK,KAC/D,KAAM,KAAM,MAAO,KAAM,MAAO,KAChC,OAAQ,CAAC,UAAW,UAAW,SAAS,EACxC,aAAc,CAAC,KAAM,KAAK,EAC1B,UAAW,CAAC,WAAW,EACvB,QAAS,CAAC,SAAS,EACnB,SAAU,CAAC,UAAU,EACrB,YAAaF,EACb,WAAYD,EACZ,eAAgB,CAAC,YAAY,EAC7B,WAAYF,EACZ,SAAU,CAAC,UAAU,EACrB,SAAU,CAAC,UAAU,EACrB,SAAU,CAAC,UAAU,EACrB,KAAM,CAAC,SAAU,OAAQ,SAAU,MAAO,MAAO,QAAS,WAAY,WAAY,OAAQ,QACtF,OAAQ,OAAQ,iBAAkB,SAAU,QAAS,QAAS,WAAY,QAC1E,OAAQ,SAAU,QAAS,QAAS,QAAQ,CACnD,CACJ,EACD,IAAK,CAAE,MAAO,CAAE,KAAM,KAAM,SAAU,KAAQ,EAC9C,IAAKK,EACL,OAAQ,CACJ,MAAO,CACH,UAAW,KAAM,KAAM,KAAM,KAAM,KACnC,UAAW,CAAC,WAAW,EACvB,SAAU,CAAC,UAAU,EACrB,QAAS,CAAC,KAAK,CAClB,CACJ,EACD,MAAO,CAAE,MAAO,CAAE,IAAK,KAAM,KAAM,KAAQ,EAC3C,OAAQA,EACR,GAAI,CAAE,MAAO,CAAE,MAAO,IAAI,CAAI,EAC9B,KAAM,CACF,MAAO,CACH,KAAM,KAAM,KAAM,KAClB,SAAU,KACV,MAAO,KACP,MAAO,CAAC,MAAO,QAAS,cAAe,mBAAmB,CAC7D,CACJ,EACD,IAAK,CAAE,MAAO,CAAE,KAAM,IAAI,CAAI,EAC9B,KAAMA,EACN,KAAM,CAAE,MAAO,CAAE,MAAO,KAAM,KAAM,CAAC,OAAQ,UAAW,SAAS,EAAK,EACtE,KAAM,CACF,MAAO,CACH,QAAS,KACT,QAASJ,EACT,KAAM,CAAC,WAAY,mBAAoB,SAAU,cAAe,YAAa,UAAU,EACvF,aAAc,CAAC,mBAAoB,eAAgB,gBAAiB,SAAS,CAChF,CACJ,EACD,MAAO,CAAE,MAAO,CAAE,MAAO,KAAM,IAAK,KAAM,IAAK,KAAM,KAAM,KAAM,IAAK,KAAM,QAAS,KAAQ,EAC7F,IAAKI,EACL,SAAUA,EACV,OAAQ,CACJ,MAAO,CACH,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAAQ,KAAM,KAAM,KAAM,MAAO,KAAM,OAAQ,KACnF,cAAe,CAAC,eAAe,CAClC,CACJ,EACD,GAAI,CAAE,MAAO,CAAE,SAAU,CAAC,UAAU,EAAG,MAAO,KAAM,KAAM,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,CAAG,EACjF,SAAU,CAAC,KAAM,SAAU,WAAY,KAAM,IAAI,CAAG,EACxD,SAAU,CAAE,MAAO,CAAE,SAAU,CAAC,UAAU,EAAG,MAAO,KAAQ,EAC5D,OAAQ,CAAE,MAAO,CAAE,SAAU,CAAC,UAAU,EAAG,MAAO,KAAM,SAAU,CAAC,UAAU,EAAG,MAAO,IAAI,CAAI,EAC/F,OAAQ,CAAE,MAAO,CAAE,IAAK,KAAM,KAAM,KAAM,KAAM,KAAQ,EACxD,EAAGA,EACH,MAAO,CAAE,MAAO,CAAE,KAAM,KAAM,MAAO,KAAQ,EAC7C,IAAKA,EACL,SAAU,CAAE,MAAO,CAAE,MAAO,KAAM,IAAK,KAAQ,EAC/C,EAAG,CAAE,MAAO,CAAE,KAAM,IAAI,CAAI,EAC5B,GAAIA,EACJ,GAAIA,EACJ,KAAMA,EACN,KAAMA,EACN,OAAQ,CACJ,MAAO,CACH,KAAM,CAAC,iBAAiB,EACxB,IAAK,KACL,MAAO,CAAC,OAAO,EACf,MAAO,CAAC,OAAO,EACf,QAASJ,CACZ,CACJ,EACD,QAASI,EACT,OAAQ,CACJ,MAAO,CACH,KAAM,KAAM,KAAM,KAAM,KAAM,KAC9B,UAAW,CAAC,WAAW,EACvB,SAAU,CAAC,UAAU,EACrB,SAAU,CAAC,UAAU,CACxB,CACJ,EACD,KAAM,CAAE,MAAO,CAAE,KAAM,IAAI,CAAI,EAC/B,MAAOA,EACP,OAAQ,CAAE,MAAO,CAAE,IAAK,KAAM,KAAM,KAAM,MAAO,KAAQ,EACzD,KAAMA,EACN,OAAQA,EACR,MAAO,CACH,MAAO,CACH,KAAM,CAAC,UAAU,EACjB,MAAO,KACP,OAAQ,IACX,CACJ,EACD,IAAKA,EACL,QAASA,EACT,IAAKA,EACL,MAAOA,EACP,MAAOA,EACP,GAAI,CAAE,MAAO,CAAE,QAAS,KAAM,QAAS,KAAM,QAAS,KAAQ,EAC9D,SAAUA,EACV,SAAU,CACN,MAAO,CACH,QAAS,KAAM,KAAM,KAAM,UAAW,KAAM,KAAM,KAAM,YAAa,KACrE,KAAM,KAAM,KAAM,KAClB,UAAW,CAAC,WAAW,EACvB,SAAU,CAAC,UAAU,EACrB,SAAU,CAAC,UAAU,EACrB,SAAU,CAAC,UAAU,EACrB,KAAM,CAAC,OAAQ,MAAM,CACxB,CACJ,EACD,MAAOA,EACP,GAAI,CAAE,MAAO,CAAE,QAAS,KAAM,QAAS,KAAM,QAAS,KAAM,MAAO,CAAC,MAAO,MAAO,WAAY,UAAU,EAAK,EAC7G,MAAOA,EACP,KAAM,CAAE,MAAO,CAAE,SAAU,IAAI,CAAI,EACnC,MAAOA,EACP,GAAIA,EACJ,MAAO,CACH,MAAO,CACH,IAAK,KAAM,MAAO,KAAM,QAAS,KACjC,KAAM,CAAC,YAAa,WAAY,eAAgB,WAAY,UAAU,EACtE,QAAS,IACZ,CACJ,EACD,GAAI,CAAE,SAAU,CAAC,KAAM,SAAU,WAAY,KAAM,IAAI,CAAG,EAC1D,IAAKA,EACL,MAAO,CACH,MAAO,CACH,IAAK,KAAM,OAAQ,KAAM,MAAO,KAAM,OAAQ,KAC9C,YAAa,CAAC,YAAa,iBAAiB,EAC5C,QAAS,CAAC,OAAQ,WAAY,MAAM,EACpC,SAAU,CAAC,UAAU,EACrB,WAAY,CAAC,OAAO,EACpB,MAAO,CAAC,OAAO,EACf,SAAU,CAAC,UAAU,CACxB,CACJ,EACD,IAAKA,CACT,EACME,GAAc,CAChB,UAAW,KACX,MAAO,KACP,gBAAiBH,EACjB,YAAa,KACb,IAAK,CAAC,MAAO,MAAO,MAAM,EAC1B,UAAW,CAAC,OAAQ,QAAS,MAAM,EACnC,SAAU,CAAC,OAAQ,OAAQ,OAAQ,UAAW,OAAO,EACrD,OAAQ,CAAC,QAAQ,EACjB,GAAI,KACJ,MAAO,CAAC,OAAO,EACf,OAAQ,KACR,SAAU,KACV,QAAS,KACT,UAAW,CAAC,WAAW,EACvB,SAAU,KACV,KAAM,CAAC,KAAM,KAAM,KAAM,QAAS,QAAS,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACrG,WAAYA,EACZ,YAAaA,EACb,eAAgBA,EAChB,MAAO,KACP,SAAU,KACV,MAAO,KACP,UAAW,CAAC,MAAO,IAAI,EACvB,IAAK,CAAC,aAAc,YAAa,SAAU,WAAY,OAAQ,UAAW,OAAQ,WAAY,aAAc,WAAY,OAAQ,SAAU,KAAK,EAC/I,KAAmB,sPAAsP,MAAM,GAAG,EAClR,wBAAyB,KACzB,cAAeA,EACf,oBAAqB,CAAC,SAAU,OAAQ,OAAQ,MAAM,EACtD,YAAaA,EACb,eAAgB,CAAC,OAAQ,QAAS,QAAS,WAAW,EACtD,gBAAiB,KACjB,mBAAoB,KACpB,gBAAiBA,EACjB,kBAAmB,KACnB,gBAAiB,CAAC,OAAQ,QAAS,WAAW,EAC9C,cAAe,KACf,eAAgB,CAAC,OAAQ,QAAS,WAAW,EAC7C,gBAAiBA,EACjB,cAAeA,EACf,eAAgB,CAAC,OAAQ,QAAS,UAAW,UAAU,EACvD,aAAc,KACd,kBAAmB,KACnB,aAAc,KACd,YAAa,CAAC,MAAO,SAAU,WAAW,EAC1C,iBAAkBA,EAClB,uBAAwBA,EACxB,YAAa,KACb,gBAAiB,KACjB,eAAgB,CAAC,OAAQ,QAAS,QAAS,WAAW,EACtD,gBAAiBA,EACjB,gBAAiB,KACjB,gBAAiBA,EACjB,gBAAiB,CAAC,OAAQ,QAAS,WAAW,EAC9C,eAAgB,KAChB,YAAa,CAAC,YAAa,aAAc,OAAQ,OAAO,EACxD,gBAAiB,KACjB,gBAAiB,KACjB,gBAAiB,KACjB,iBAAkB,IACtB,EACMI,GAAgC,0LAEY,MAAM,GAAG,EAAE,IAAIhB,GAAK,KAAOA,CAAC,EAC9E,QAASiB,KAAKD,GACVD,GAAYE,CAAC,EAAI,KACrB,MAAMC,CAAO,CACT,YAAYC,EAAWC,EAAY,CAC/B,KAAK,KAAO,OAAO,OAAO,OAAO,OAAO,GAAIN,EAAI,EAAGK,CAAS,EAC5D,KAAK,YAAc,OAAO,OAAO,OAAO,OAAO,GAAIJ,EAAW,EAAGK,CAAU,EAC3E,KAAK,QAAU,OAAO,KAAK,KAAK,IAAI,EACpC,KAAK,gBAAkB,OAAO,KAAK,KAAK,WAAW,CACtD,CACL,CACAF,EAAO,QAAuB,IAAIA,EAClC,SAASG,EAAYC,EAAKC,EAAMC,EAAMF,EAAI,OAAQ,CAC9C,GAAI,CAACC,EACD,MAAO,GACX,IAAItD,EAAMsD,EAAK,WACXlF,EAAO4B,GAAOA,EAAI,SAAS,SAAS,EACxC,OAAO5B,EAAOiF,EAAI,YAAYjF,EAAK,KAAM,KAAK,IAAIA,EAAK,GAAImF,CAAG,CAAC,EAAI,EACvE,CACA,SAASC,EAAkBF,EAAMG,EAAO,GAAO,CAC3C,KAAOH,EAAMA,EAAOA,EAAK,OACrB,GAAIA,EAAK,MAAQ,UACb,GAAIG,EACAA,EAAO,OAEP,QAAOH,EAEnB,OAAO,IACX,CACA,SAASI,GAAgBL,EAAKC,EAAMK,EAAQ,CACxC,IAAIC,EAAaD,EAAO,KAAKP,EAAYC,EAAKG,EAAkBF,CAAI,CAAC,CAAC,EACtE,OAAgEM,GAAW,UAAaD,EAAO,OACnG,CACA,SAASE,EAASR,EAAKC,EAAM,CACzB,IAAItB,EAAO,CAAA,EACX,QAASpD,EAAS4E,EAAkBF,CAAI,EAAG1E,GAAU,CAACA,EAAO,KAAK,MAAOA,EAAS4E,EAAkB5E,EAAO,MAAM,EAAG,CAChH,IAAIqD,EAAUmB,EAAYC,EAAKzE,CAAM,EACrC,GAAIqD,GAAWrD,EAAO,UAAU,MAAQ,WACpC,MACAqD,GAAWD,EAAK,QAAQC,CAAO,EAAI,IAAMqB,EAAK,MAAQ,UAAYA,EAAK,MAAQ1E,EAAO,WAAW,KACjGoD,EAAK,KAAKC,CAAO,CACxB,CACD,OAAOD,CACX,CACA,MAAM8B,GAAa,4BACnB,SAASC,EAAY3D,EAAOuD,EAAQL,EAAMlB,EAAME,EAAI,CAChD,IAAI0B,EAAM,OAAO,KAAK5D,EAAM,SAASkC,EAAIA,EAAK,CAAC,CAAC,EAAI,GAAK,IACrD1D,EAAS4E,EAAkBF,EAAM,EAAI,EACzC,MAAO,CAAE,KAAAlB,EAAM,GAAAE,EACX,QAASoB,GAAgBtD,EAAM,IAAKxB,EAAQ+E,CAAM,EAAE,IAAI1B,IAAY,CAAE,MAAOA,EAAS,KAAM,MAAM,EAAG,EAAE,OAAO4B,EAASzD,EAAM,IAAKkD,CAAI,EAAE,IAAI,CAACtD,EAAKnB,KAAO,CAAE,MAAO,IAAMmB,EAAK,MAAO,IAAMA,EAAMgE,EAC5L,KAAM,OAAQ,MAAO,GAAKnF,CAAC,EAAG,CAAC,EACnC,SAAU,8BAA8B,CAChD,CACA,SAASoF,EAAiB7D,EAAOkD,EAAMlB,EAAME,EAAI,CAC7C,IAAI0B,EAAM,OAAO,KAAK5D,EAAM,SAASkC,EAAIA,EAAK,CAAC,CAAC,EAAI,GAAK,IACzD,MAAO,CAAE,KAAAF,EAAM,GAAAE,EACX,QAASuB,EAASzD,EAAM,IAAKkD,CAAI,EAAE,IAAI,CAACtD,EAAKnB,KAAO,CAAE,MAAOmB,EAAK,MAAOA,EAAMgE,EAAK,KAAM,OAAQ,MAAO,GAAKnF,CAAC,EAAG,EAClH,SAAUiF,EAAU,CAC5B,CACA,SAASI,GAAiB9D,EAAOuD,EAAQL,EAAMpF,EAAK,CAChD,IAAIiG,EAAU,CAAA,EAAIC,EAAQ,EAC1B,QAASnC,KAAWyB,GAAgBtD,EAAM,IAAKkD,EAAMK,CAAM,EACvDQ,EAAQ,KAAK,CAAE,MAAO,IAAMlC,EAAS,KAAM,MAAM,CAAE,EACvD,QAASD,KAAQ6B,EAASzD,EAAM,IAAKkD,CAAI,EACrCa,EAAQ,KAAK,CAAE,MAAO,KAAOnC,EAAO,IAAK,KAAM,OAAQ,MAAO,GAAKoC,GAAS,CAAA,EAChF,MAAO,CAAE,KAAMlG,EAAK,GAAIA,EAAK,QAAAiG,EAAS,SAAU,gCACpD,CACA,SAASE,GAAiBjE,EAAOuD,EAAQL,EAAMlB,EAAME,EAAI,CACrD,IAAIgC,EAAMd,EAAkBF,CAAI,EAAGiB,EAAOD,EAAMX,EAAO,KAAKP,EAAYhD,EAAM,IAAKkE,CAAG,CAAC,EAAI,KACvFE,EAAaD,GAAQA,EAAK,MAAQ,OAAO,KAAKA,EAAK,KAAK,EAAI,GAC5DE,EAAQF,GAAQA,EAAK,cAAgB,GAAQC,EAC3CA,EAAW,OAASA,EAAW,OAAOb,EAAO,eAAe,EAAIA,EAAO,gBAC7E,MAAO,CAAE,KAAAvB,EAAM,GAAAE,EACX,QAASmC,EAAM,IAAIC,IAAa,CAAE,MAAOA,EAAU,KAAM,UAAU,EAAG,EACtE,SAAUZ,EAAU,CAC5B,CACA,SAASa,GAAkBvE,EAAOuD,EAAQL,EAAMlB,EAAME,EAAI,CACtD,IAAIsC,EACJ,IAAI1C,GAAY0C,EAAKtB,EAAK,UAAY,MAAQsB,IAAO,OAAS,OAASA,EAAG,SAAS,eAAe,EAC9FT,EAAU,CAAA,EAAIU,EAClB,GAAI3C,EAAU,CACV,IAAIwC,EAAWtE,EAAM,SAAS8B,EAAS,KAAMA,EAAS,EAAE,EACpDlB,EAAQ2C,EAAO,YAAYe,CAAQ,EACvC,GAAI,CAAC1D,EAAO,CACR,IAAIsD,EAAMd,EAAkBF,CAAI,EAAGiB,EAAOD,EAAMX,EAAO,KAAKP,EAAYhD,EAAM,IAAKkE,CAAG,CAAC,EAAI,KAC3FtD,EAAqDuD,GAAK,OAAUA,EAAK,MAAMG,CAAQ,CAC1F,CACD,GAAI1D,EAAO,CACP,IAAI8D,EAAO1E,EAAM,SAASgC,EAAME,CAAE,EAAE,YAAW,EAAIyC,EAAa,IAAKC,EAAW,IAC5E,QAAQ,KAAKF,CAAI,GACjBD,EAAQC,EAAK,CAAC,GAAK,IAAM,UAAY,UACrCC,EAAa,GACbC,EAAW5E,EAAM,SAASkC,EAAIA,EAAK,CAAC,GAAKwC,EAAK,CAAC,EAAI,GAAKA,EAAK,CAAC,EAC9DA,EAAOA,EAAK,MAAM,CAAC,EACnB1C,KAGAyC,EAAQ,gBAEZ,QAAS3D,KAASF,EACdmD,EAAQ,KAAK,CAAE,MAAOjD,EAAO,MAAO6D,EAAa7D,EAAQ8D,EAAU,KAAM,UAAY,CAAA,CAC5F,CACJ,CACD,MAAO,CAAE,KAAA5C,EAAM,GAAAE,EAAI,QAAA6B,EAAS,SAAUU,CAAK,CAC/C,CACA,SAASI,GAAkBtB,EAAQ1E,EAAS,CACxC,GAAI,CAAE,MAAAmB,EAAO,IAAAlC,CAAK,EAAGe,EAASqE,EAAO4B,EAAW9E,CAAK,EAAE,aAAalC,EAAK,EAAE,EAAGiH,EAAS7B,EAAK,QAAQpF,CAAG,EACvG,QAASkH,EAAOlH,EAAKmH,EAAQF,GAAU7B,IAAS+B,EAAS/B,EAAK,YAAY8B,CAAI,IAAK,CAC/E,IAAI/C,EAAOgD,EAAO,UAClB,GAAI,CAAChD,GAAQ,CAACA,EAAK,KAAK,SAAWA,EAAK,KAAOA,EAAK,GAChD,MACJ8C,EAAS7B,EAAO+B,EAChBD,EAAO/C,EAAK,IACf,CACD,OAAIiB,EAAK,MAAQ,UACNA,EAAK,QAAU,YAAY,KAAKA,EAAK,OAAO,IAAI,EAAIW,EAAiB7D,EAAOkD,EAAMA,EAAK,KAAMpF,CAAG,EACjG6F,EAAY3D,EAAOuD,EAAQL,EAAMA,EAAK,KAAMpF,CAAG,EAEhDoF,EAAK,MAAQ,WACXS,EAAY3D,EAAOuD,EAAQL,EAAMpF,EAAKA,CAAG,EAE3CoF,EAAK,MAAQ,iBAAmBA,EAAK,MAAQ,qBAC3CW,EAAiB7D,EAAOkD,EAAMpF,EAAKA,CAAG,EAExCoF,EAAK,MAAQ,WAAaA,EAAK,MAAQ,kBAAoBA,EAAK,MAAQ,gBACtEe,GAAiBjE,EAAOuD,EAAQL,EAAMA,EAAK,MAAQ,gBAAkBA,EAAK,KAAOpF,EAAKA,CAAG,EAE3FoF,EAAK,MAAQ,MAAQA,EAAK,MAAQ,kBAAoBA,EAAK,MAAQ,yBACjEqB,GAAkBvE,EAAOuD,EAAQL,EAAMA,EAAK,MAAQ,KAAOpF,EAAMoF,EAAK,KAAMpF,CAAG,EAEjFe,EAAQ,WAAakG,EAAO,MAAQ,WAAaA,EAAO,MAAQ,QAAUA,EAAO,MAAQ,YACvFjB,GAAiB9D,EAAOuD,EAAQL,EAAMpF,CAAG,EAGzC,IAEf,CAKA,SAASoH,GAAqBrG,EAAS,CACnC,OAAOgG,GAAkBhC,EAAO,QAAShE,CAAO,CACpD,CAKA,SAASsG,GAAyBC,EAAQ,CACtC,GAAI,CAAE,UAAAtC,EAAW,sBAAuBC,CAAU,EAAKqC,EACnD7B,EAASR,GAAcD,EAAY,IAAID,EAAOC,EAAWC,CAAU,EAAIF,EAAO,QAClF,OAAQhE,GAAYgG,GAAkBtB,EAAQ1E,CAAO,CACzD,CAEA,MAAMwG,GAA0BC,EAAmB,OAAO,UAAU,CAAE,IAAK,kBAAkB,CAAE,EACzFC,GAAiB,CACnB,CAAE,IAAK,SACH,MAAO3E,GAASA,EAAM,MAAQ,mBAAqBA,EAAM,MAAQ,KACjE,OAAQ4E,GAAmB,MAAQ,EACvC,CAAE,IAAK,SACH,MAAO5E,GAASA,EAAM,MAAQ,cAAgBA,EAAM,MAAQ,WAC5D,OAAQ6E,GAAY,MAAQ,EAChC,CAAE,IAAK,SACH,MAAO7E,GAASA,EAAM,MAAQ,sBAC9B,OAAQ8E,GAAY,MAAQ,EAChC,CAAE,IAAK,SACH,MAAM9E,EAAO,CACT,MAAO,2DAA2D,KAAKA,EAAM,IAAI,CACpF,EACD,OAAQyE,EAAY,EACxB,CAAE,IAAK,SACH,MAAMzE,EAAO,CACT,MAAO,CAACA,EAAM,MAAQ,kEAAkE,KAAKA,EAAM,IAAI,CAC1G,EACD,OAAQ0E,EAAmB,MAAQ,EACvC,CAAE,IAAK,QACH,MAAM1E,EAAO,CACT,OAAQ,CAACA,EAAM,MAAQA,EAAM,MAAQ,SAAW,CAACA,EAAM,MAAQ,oCAAoC,KAAKA,EAAM,IAAI,EACrH,EACD,OAAQ+E,EAAY,MAAQ,CACpC,EACMC,GAA4B,CAC9B,CAAE,KAAM,QACJ,OAAqBD,EAAY,OAAO,UAAU,CAAE,IAAK,QAAQ,CAAE,CAAG,CAC9E,EAAE,OAAoBhD,GAAgB,IAAI3E,IAAS,CAAE,KAAAA,EAAM,OAAQsH,EAAmB,MAAQ,EAAC,CAAC,EAO1FO,GAAyBC,GAAW,OAAO,CAC7C,KAAM,OACN,OAAqBtF,GAAO,UAAU,CAClC,MAAO,CACUuF,GAAe,IAAI,CAC5B,QAAQlH,EAAS,CACb,IAAImH,EAAQ,eAAe,KAAKnH,EAAQ,SAAS,EACjD,OAAIA,EAAQ,KAAK,IAAMA,EAAQ,IAAMmH,EAAM,CAAC,EAAE,OACnCnH,EAAQ,WACZA,EAAQ,WAAWA,EAAQ,KAAK,IAAI,GAAKmH,EAAM,CAAC,EAAI,EAAInH,EAAQ,KAC1E,EACD,kCAAkCA,EAAS,CACvC,OAAOA,EAAQ,OAAOA,EAAQ,KAAK,IAAI,EAAIA,EAAQ,IACtD,EACD,SAASA,EAAS,CACd,GAAIA,EAAQ,IAAM,MAAM,KAAKA,EAAQ,SAAS,EAAE,CAAC,EAAE,OAASA,EAAQ,KAAK,GACrE,OAAOA,EAAQ,WACnB,IAAIoH,EAAS,KAAM7G,EACnB,QAAS8G,EAAMrH,EAAQ,OAAQ,CAC3B,IAAIoD,EAAOiE,EAAI,UACf,GAAI,CAACjE,GAAQA,EAAK,MAAQ,WAAaA,EAAK,IAAMiE,EAAI,GAClD,MACJD,EAASC,EAAMjE,CAClB,CACD,OAAIgE,GAAU,GAAG7G,EAAQ6G,EAAO,aAAe7G,EAAM,MAAQ,YAAcA,EAAM,MAAQ,mBAC9EP,EAAQ,WAAWoH,EAAO,IAAI,EAAIpH,EAAQ,KAC9C,IACV,CACjB,CAAa,EACYsH,GAAa,IAAI,CAC1B,QAAQnH,EAAM,CACV,IAAIoH,EAAQpH,EAAK,WAAYiD,EAAOjD,EAAK,UACzC,MAAI,CAACoH,GAASA,EAAM,MAAQ,UACjB,KACJ,CAAE,KAAMA,EAAM,GAAI,GAAInE,EAAK,MAAQ,WAAaA,EAAK,KAAOjD,EAAK,EAAE,CAC7E,CACjB,CAAa,EACYqH,GAAsB,IAAI,CACnC,mBAAoBrH,GAAQA,EAAK,SAAS,SAAS,CACnE,CAAa,CACJ,CACT,CAAK,EACD,aAAc,CACV,cAAe,CAAE,MAAO,CAAE,KAAM,OAAQ,MAAO,MAAS,EACxD,cAAe,gBACf,UAAW,KACd,CACL,CAAC,EAOKsH,EAA4BT,GAAU,UAAU,CAClD,KAAmB3E,GAAiBqE,GAAgBK,EAAY,CACpE,CAAC,EAMD,SAASW,GAAKnB,EAAS,GAAI,CACvB,IAAIoB,EAAU,GAAIC,EACdrB,EAAO,mBAAqB,KAC5BoB,EAAU,WACVpB,EAAO,kBAAoB,KAC3BoB,GAAWA,EAAUA,EAAU,IAAM,IAAM,gBAC3CpB,EAAO,iBAAmBA,EAAO,gBAAgB,QACjDA,EAAO,kBAAoBA,EAAO,iBAAiB,UACnDqB,EAAOvF,IAAkBkE,EAAO,iBAAmB,CAAA,GAAI,OAAOG,EAAc,GAAIH,EAAO,kBAAoB,CAAA,GAAI,OAAOQ,EAAY,CAAC,GACvI,IAAIc,EAAOD,EAAOZ,GAAU,UAAU,CAAE,KAAAY,EAAM,QAAAD,CAAO,CAAE,EAAIA,EAAUF,EAAa,UAAU,CAAE,QAAAE,CAAS,CAAA,EAAIF,EAC3G,OAAO,IAAIK,GAAgBD,EAAM,CAC7BJ,EAAa,KAAK,GAAG,CAAE,aAAcnB,GAAyBC,CAAM,EAAG,EACvEA,EAAO,gBAAkB,GAAQwB,GAAgB,CAAE,EACnDC,GAAY,EAAC,QACbC,GAAK,EAAC,OACd,CAAK,CACL,CACA,MAAM5J,EAA2B,IAAI,IAAiB,qGAAqG,MAAM,GAAG,CAAC,EAK/J0J,GAA6BG,GAAW,aAAa,GAAG,CAACC,EAAMhF,EAAME,EAAI+E,EAAMC,IAAsB,CACvG,GAAIF,EAAK,WAAaA,EAAK,MAAM,UAAYhF,GAAQE,GAAO+E,GAAQ,KAAOA,GAAQ,KAC/E,CAACX,EAAa,WAAWU,EAAK,MAAOhF,EAAM,EAAE,EAC7C,MAAO,GACX,IAAI0C,EAAOwC,EAAiB,EAAI,CAAE,MAAAlH,CAAK,EAAK0E,EACxCyC,EAAYnH,EAAM,cAAcoH,GAAS,CACzC,IAAI5C,EAAI6C,EAAIC,EACZ,IAAIC,EAAUvH,EAAM,IAAI,YAAYoH,EAAM,KAAO,EAAGA,EAAM,EAAE,GAAKH,EAC7D,CAAE,KAAAO,CAAI,EAAKJ,EAAOpB,EAAQlB,EAAW9E,CAAK,EAAE,aAAawH,EAAM,EAAE,EAAGxJ,EACxE,GAAIuJ,GAAWN,GAAQ,KAAOjB,EAAM,MAAQ,SAAU,CAClD,IAAIpG,EAAMoG,EAAM,OAChB,KAAMqB,GAAM7C,EAAK5E,EAAI,UAAY,MAAQ4E,IAAO,OAAS,OAASA,EAAG,aAAe,MAAQ6C,IAAO,OAAS,OAASA,EAAG,OAAS,aAC5HrJ,EAAOgF,EAAYhD,EAAM,IAAKJ,EAAI,OAAQ4H,CAAI,IAC/C,CAACtK,EAAY,IAAIc,CAAI,EAAG,CACxB,IAAIkE,EAAKsF,GAAQxH,EAAM,IAAI,YAAYwH,EAAMA,EAAO,CAAC,IAAM,IAAM,EAAI,GACjEC,EAAS,KAAKzJ,CAAI,IACtB,MAAO,CAAE,MAAAoJ,EAAO,QAAS,CAAE,KAAMI,EAAM,GAAAtF,EAAI,OAAAuF,CAAM,EACpD,CACJ,SACQF,GAAWN,GAAQ,KAAOjB,EAAM,MAAQ,qBAAsB,CACnE,IAAIpG,EAAMoG,EAAM,OAChB,GAAIA,EAAM,MAAQwB,EAAO,KAAOF,EAAK1H,EAAI,aAAe,MAAQ0H,IAAO,OAAS,OAASA,EAAG,OAAS,aAChGtJ,EAAOgF,EAAYhD,EAAM,IAAKJ,EAAK4H,CAAI,IAAM,CAACtK,EAAY,IAAIc,CAAI,EAAG,CACtE,IAAIkE,EAAKsF,GAAQxH,EAAM,IAAI,YAAYwH,EAAMA,EAAO,CAAC,IAAM,IAAM,EAAI,GACjEC,EAAS,GAAGzJ,CAAI,IACpB,MAAO,CACH,MAAO0J,GAAgB,OAAOF,EAAOC,EAAO,OAAQ,EAAE,EACtD,QAAS,CAAE,KAAMD,EAAM,GAAAtF,EAAI,OAAAuF,CAAQ,CACvD,CACa,CACJ,CACD,MAAO,CAAE,MAAAL,CAAK,CACtB,CAAK,EACD,OAAID,EAAU,QAAQ,MACX,IACXH,EAAK,SAAS,CACVtC,EACA1E,EAAM,OAAOmH,EAAW,CACpB,UAAW,iBACX,eAAgB,EAC5B,CAAS,CACT,CAAK,EACM,GACX,CAAC", "x_google_ignoreList": [0, 1]}