import{h as Be}from"./index-V1UhiEW8.js";/* empty css                                             */const{SvelteComponent:Je,append:Re,attr:T,detach:Ge,init:Ke,insert:Qe,noop:$,safe_not_equal:Xe,svg_element:de}=window.__gradio__svelte__internal;function Ye(t){let e,l;return{c(){e=de("svg"),l=de("path"),T(l,"fill","currentColor"),T(l,"d","M13.75 2a2.25 2.25 0 0 1 2.236 2.002V4h1.764A2.25 2.25 0 0 1 20 6.25V11h-1.5V6.25a.75.75 0 0 0-.75-.75h-2.129c-.404.603-1.091 1-1.871 1h-3.5c-.78 0-1.467-.397-1.871-1H6.25a.75.75 0 0 0-.75.75v13.5c0 .414.336.75.75.75h4.78a4 4 0 0 0 .505 1.5H6.25A2.25 2.25 0 0 1 4 19.75V6.25A2.25 2.25 0 0 1 6.25 4h1.764a2.25 2.25 0 0 1 2.236-2zm2.245 2.096L16 4.25q0-.078-.005-.154M13.75 3.5h-3.5a.75.75 0 0 0 0 1.5h3.5a.75.75 0 0 0 0-1.5M15 12a3 3 0 0 0-3 3v5c0 .556.151 1.077.415 1.524l3.494-3.494a2.25 2.25 0 0 1 3.182 0l3.494 3.494c.264-.447.415-.968.415-1.524v-5a3 3 0 0 0-3-3zm0 11a3 3 0 0 1-1.524-.415l3.494-3.494a.75.75 0 0 1 1.06 0l3.494 3.494A3 3 0 0 1 20 23zm5-7a1 1 0 1 1 0-2 1 1 0 0 1 0 2"),T(e,"xmlns","http://www.w3.org/2000/svg"),T(e,"viewBox","0 0 24 24"),T(e,"width","100%"),T(e,"height","100%")},m(n,r){Qe(n,e,r),Re(e,l)},p:$,i:$,o:$,d(n){n&&Ge(e)}}}class Ut extends Je{constructor(e){super(),Ke(this,e,null,Ye,Xe,{})}}const{SvelteComponent:Ze,append:ee,attr:C,detach:xe,init:$e,insert:et,noop:te,safe_not_equal:tt,svg_element:N}=window.__gradio__svelte__internal;function lt(t){let e,l,n,r;return{c(){e=N("svg"),l=N("path"),n=N("polyline"),r=N("line"),C(l,"d","M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"),C(n,"points","17 8 12 3 7 8"),C(r,"x1","12"),C(r,"y1","3"),C(r,"x2","12"),C(r,"y2","15"),C(e,"xmlns","http://www.w3.org/2000/svg"),C(e,"width","90%"),C(e,"height","90%"),C(e,"viewBox","0 0 24 24"),C(e,"fill","none"),C(e,"stroke","currentColor"),C(e,"stroke-width","2"),C(e,"stroke-linecap","round"),C(e,"stroke-linejoin","round"),C(e,"class","feather feather-upload")},m(f,a){et(f,e,a),ee(e,l),ee(e,n),ee(e,r)},p:te,i:te,o:te,d(f){f&&xe(e)}}}let Dt=class extends Ze{constructor(e){super(),$e(this,e,null,lt,tt,{})}};const{SvelteComponent:nt,append:z,attr:D,detach:pe,element:M,flush:j,init:it,insert:be,noop:_e,safe_not_equal:rt,set_data:B,set_style:le,space:ne,text:O,toggle_class:he}=window.__gradio__svelte__internal,{onMount:st,createEventDispatcher:at,onDestroy:ot}=window.__gradio__svelte__internal;function ce(t){let e,l,n,r,f=V(t[2])+"",a,_,u,s,o=t[2].orig_name+"",h;return{c(){e=M("div"),l=M("span"),n=M("div"),r=M("progress"),a=O(f),u=ne(),s=M("span"),h=O(o),le(r,"visibility","hidden"),le(r,"height","0"),le(r,"width","0"),r.value=_=V(t[2]),D(r,"max","100"),D(r,"class","svelte-1vsfomn"),D(n,"class","progress-bar svelte-1vsfomn"),D(s,"class","file-name svelte-1vsfomn"),D(e,"class","file svelte-1vsfomn")},m(g,b){be(g,e,b),z(e,l),z(l,n),z(n,r),z(r,a),z(e,u),z(e,s),z(s,h)},p(g,b){b&4&&f!==(f=V(g[2])+"")&&B(a,f),b&4&&_!==(_=V(g[2]))&&(r.value=_),b&4&&o!==(o=g[2].orig_name+"")&&B(h,o)},d(g){g&&pe(e)}}}function ut(t){let e,l,n,r=t[0].length+"",f,a,_=t[0].length>1?"files":"file",u,s,o,h=t[2]&&ce(t);return{c(){e=M("div"),l=M("span"),n=O("Uploading "),f=O(r),a=ne(),u=O(_),s=O("..."),o=ne(),h&&h.c(),D(l,"class","uploading svelte-1vsfomn"),D(e,"class","wrap svelte-1vsfomn"),he(e,"progress",t[1])},m(g,b){be(g,e,b),z(e,l),z(l,n),z(l,f),z(l,a),z(l,u),z(l,s),z(e,o),h&&h.m(e,null)},p(g,[b]){b&1&&r!==(r=g[0].length+"")&&B(f,r),b&1&&_!==(_=g[0].length>1?"files":"file")&&B(u,_),g[2]?h?h.p(g,b):(h=ce(g),h.c(),h.m(e,null)):h&&(h.d(1),h=null),b&2&&he(e,"progress",g[1])},i:_e,o:_e,d(g){g&&pe(e),h&&h.d()}}}function V(t){return t.progress*100/(t.size||0)||0}function ft(t){let e=0;return t.forEach(l=>{e+=V(l)}),document.documentElement.style.setProperty("--upload-progress-width",(e/t.length).toFixed(2)+"%"),e/t.length}function dt(t,e,l){let{upload_id:n}=e,{root:r}=e,{files:f}=e,{stream_handler:a}=e,_,u=!1,s,o,h=f.map(c=>({...c,progress:0}));const g=at();function b(c,d){l(0,h=h.map(m=>(m.orig_name===c&&(m.progress+=d),m)))}return st(async()=>{if(_=await a(new URL(`${r}/gradio_api/upload_progress?upload_id=${n}`)),_==null)throw new Error("Event source is not defined");_.onmessage=async function(c){const d=JSON.parse(c.data);u||l(1,u=!0),d.msg==="done"?(_?.close(),g("done")):(l(7,s=d),b(d.orig_name,d.chunk_size))}}),ot(()=>{(_!=null||_!=null)&&_.close()}),t.$$set=c=>{"upload_id"in c&&l(3,n=c.upload_id),"root"in c&&l(4,r=c.root),"files"in c&&l(5,f=c.files),"stream_handler"in c&&l(6,a=c.stream_handler)},t.$$.update=()=>{t.$$.dirty&1&&ft(h),t.$$.dirty&129&&l(2,o=s||h[0])},[h,u,o,n,r,f,a,s]}class _t extends nt{constructor(e){super(),it(this,e,dt,ut,rt,{upload_id:3,root:4,files:5,stream_handler:6})}get upload_id(){return this.$$.ctx[3]}set upload_id(e){this.$$set({upload_id:e}),j()}get root(){return this.$$.ctx[4]}set root(e){this.$$set({root:e}),j()}get files(){return this.$$.ctx[5]}set files(e){this.$$set({files:e}),j()}get stream_handler(){return this.$$.ctx[6]}set stream_handler(e){this.$$set({stream_handler:e}),j()}}const{SvelteComponent:ht,append:ge,attr:v,binding_callbacks:ct,bubble:F,check_outros:we,create_component:gt,create_slot:ve,destroy_component:mt,detach:R,element:ie,empty:ye,flush:y,get_all_dirty_from_scope:ke,get_slot_changes:Ae,group_outros:Ce,init:pt,insert:G,listen:W,mount_component:bt,prevent_default:E,run_all:wt,safe_not_equal:vt,set_style:J,space:yt,stop_propagation:U,toggle_class:w,transition_in:q,transition_out:I,update_slot_base:ze}=window.__gradio__svelte__internal,{createEventDispatcher:kt,tick:At,getContext:It}=window.__gradio__svelte__internal;function Ct(t){let e,l,n,r,f,a,_,u,s,o,h,g;const b=t[30].default,c=ve(b,t,t[29],null);return{c(){e=ie("button"),c&&c.c(),l=yt(),n=ie("input"),v(n,"aria-label","File upload"),v(n,"data-testid","file-upload"),v(n,"type","file"),v(n,"accept",r=t[19]||void 0),n.multiple=f=t[6]==="multiple"||void 0,v(n,"webkitdirectory",a=t[6]==="directory"||void 0),v(n,"mozdirectory",_=t[6]==="directory"||void 0),v(n,"class","svelte-1b742ao"),v(e,"tabindex",u=t[9]?-1:0),v(e,"aria-label",s=t[14]||"Click to upload or drop files"),v(e,"aria-dropeffect","copy"),v(e,"class","svelte-1b742ao"),w(e,"hidden",t[9]),w(e,"center",t[4]),w(e,"boundedheight",t[3]),w(e,"flex",t[5]),w(e,"disable_click",t[7]),w(e,"icon-mode",t[12]),J(e,"height",t[12]?"":t[13]?typeof t[13]=="number"?t[13]+"px":t[13]:"100%")},m(d,m){G(d,e,m),c&&c.m(e,null),ge(e,l),ge(e,n),t[38](n),o=!0,h||(g=[W(n,"change",t[21]),W(e,"drag",U(E(t[31]))),W(e,"dragstart",U(E(t[32]))),W(e,"dragend",U(E(t[33]))),W(e,"dragover",U(E(t[34]))),W(e,"dragenter",U(E(t[35]))),W(e,"dragleave",U(E(t[36]))),W(e,"drop",U(E(t[37]))),W(e,"click",t[16]),W(e,"drop",t[22]),W(e,"dragenter",t[20]),W(e,"dragleave",t[20])],h=!0)},p(d,m){c&&c.p&&(!o||m[0]&536870912)&&ze(c,b,d,d[29],o?Ae(b,d[29],m,null):ke(d[29]),null),(!o||m[0]&524288&&r!==(r=d[19]||void 0))&&v(n,"accept",r),(!o||m[0]&64&&f!==(f=d[6]==="multiple"||void 0))&&(n.multiple=f),(!o||m[0]&64&&a!==(a=d[6]==="directory"||void 0))&&v(n,"webkitdirectory",a),(!o||m[0]&64&&_!==(_=d[6]==="directory"||void 0))&&v(n,"mozdirectory",_),(!o||m[0]&512&&u!==(u=d[9]?-1:0))&&v(e,"tabindex",u),(!o||m[0]&16384&&s!==(s=d[14]||"Click to upload or drop files"))&&v(e,"aria-label",s),(!o||m[0]&512)&&w(e,"hidden",d[9]),(!o||m[0]&16)&&w(e,"center",d[4]),(!o||m[0]&8)&&w(e,"boundedheight",d[3]),(!o||m[0]&32)&&w(e,"flex",d[5]),(!o||m[0]&128)&&w(e,"disable_click",d[7]),(!o||m[0]&4096)&&w(e,"icon-mode",d[12]),m[0]&12288&&J(e,"height",d[12]?"":d[13]?typeof d[13]=="number"?d[13]+"px":d[13]:"100%")},i(d){o||(q(c,d),o=!0)},o(d){I(c,d),o=!1},d(d){d&&R(e),c&&c.d(d),t[38](null),h=!1,wt(g)}}}function zt(t){let e,l,n=!t[9]&&me(t);return{c(){n&&n.c(),e=ye()},m(r,f){n&&n.m(r,f),G(r,e,f),l=!0},p(r,f){r[9]?n&&(Ce(),I(n,1,1,()=>{n=null}),we()):n?(n.p(r,f),f[0]&512&&q(n,1)):(n=me(r),n.c(),q(n,1),n.m(e.parentNode,e))},i(r){l||(q(n),l=!0)},o(r){I(n),l=!1},d(r){r&&R(e),n&&n.d(r)}}}function Wt(t){let e,l,n,r,f,a;const _=t[30].default,u=ve(_,t,t[29],null);return{c(){e=ie("button"),u&&u.c(),v(e,"tabindex",l=t[9]?-1:0),v(e,"aria-label",n=t[14]||"Paste from clipboard"),v(e,"class","svelte-1b742ao"),w(e,"hidden",t[9]),w(e,"center",t[4]),w(e,"boundedheight",t[3]),w(e,"flex",t[5]),w(e,"icon-mode",t[12]),J(e,"height",t[12]?"":t[13]?typeof t[13]=="number"?t[13]+"px":t[13]:"100%")},m(s,o){G(s,e,o),u&&u.m(e,null),r=!0,f||(a=W(e,"click",t[15]),f=!0)},p(s,o){u&&u.p&&(!r||o[0]&536870912)&&ze(u,_,s,s[29],r?Ae(_,s[29],o,null):ke(s[29]),null),(!r||o[0]&512&&l!==(l=s[9]?-1:0))&&v(e,"tabindex",l),(!r||o[0]&16384&&n!==(n=s[14]||"Paste from clipboard"))&&v(e,"aria-label",n),(!r||o[0]&512)&&w(e,"hidden",s[9]),(!r||o[0]&16)&&w(e,"center",s[4]),(!r||o[0]&8)&&w(e,"boundedheight",s[3]),(!r||o[0]&32)&&w(e,"flex",s[5]),(!r||o[0]&4096)&&w(e,"icon-mode",s[12]),o[0]&12288&&J(e,"height",s[12]?"":s[13]?typeof s[13]=="number"?s[13]+"px":s[13]:"100%")},i(s){r||(q(u,s),r=!0)},o(s){I(u,s),r=!1},d(s){s&&R(e),u&&u.d(s),f=!1,a()}}}function me(t){let e,l;return e=new _t({props:{root:t[8],upload_id:t[17],files:t[18],stream_handler:t[11]}}),{c(){gt(e.$$.fragment)},m(n,r){bt(e,n,r),l=!0},p(n,r){const f={};r[0]&256&&(f.root=n[8]),r[0]&131072&&(f.upload_id=n[17]),r[0]&262144&&(f.files=n[18]),r[0]&2048&&(f.stream_handler=n[11]),e.$set(f)},i(n){l||(q(e.$$.fragment,n),l=!0)},o(n){I(e.$$.fragment,n),l=!1},d(n){mt(e,n)}}}function Pt(t){let e,l,n,r;const f=[Wt,zt,Ct],a=[];function _(u,s){return u[0]==="clipboard"?0:u[1]&&u[10]?1:2}return e=_(t),l=a[e]=f[e](t),{c(){l.c(),n=ye()},m(u,s){a[e].m(u,s),G(u,n,s),r=!0},p(u,s){let o=e;e=_(u),e===o?a[e].p(u,s):(Ce(),I(a[o],1,1,()=>{a[o]=null}),we(),l=a[e],l?l.p(u,s):(l=a[e]=f[e](u),l.c()),q(l,1),l.m(n.parentNode,n))},i(u){r||(q(l),r=!0)},o(u){I(l),r=!1},d(u){u&&R(n),a[e].d(u)}}}function St(t,e,l){if(!t||t==="*"||t==="file/*"||Array.isArray(t)&&t.some(r=>r==="*"||r==="file/*"))return!0;let n;if(typeof t=="string")n=t.split(",").map(r=>r.trim());else if(Array.isArray(t))n=t;else return!1;return n.includes(e)||n.some(r=>{const[f]=r.split("/").map(a=>a.trim());return r.endsWith("/*")&&l.startsWith(f+"/")})}function qt(t,e,l){let n,{$$slots:r={},$$scope:f}=e,{filetype:a=null}=e,{dragging:_=!1}=e,{boundedheight:u=!0}=e,{center:s=!0}=e,{flex:o=!0}=e,{file_count:h="single"}=e,{disable_click:g=!1}=e,{root:b}=e,{hidden:c=!1}=e,{format:d="file"}=e,{uploading:m=!1}=e,{hidden_upload:P=null}=e,{show_progress:re=!0}=e,{max_file_size:K=null}=e,{upload:Q}=e,{stream_handler:se}=e,{icon_upload:ae=!1}=e,{height:oe=void 0}=e,{aria_label:ue=void 0}=e,X,Y,L,fe=null;const We=()=>{if(typeof navigator<"u"){const i=navigator.userAgent.toLowerCase();return i.indexOf("iphone")>-1||i.indexOf("ipad")>-1}return!1},S=kt(),Pe=["image","video","audio","text","file"],Z=i=>n&&i.startsWith(".")?(fe=!0,i):n&&i.includes("file/*")?"*":i.startsWith(".")||i.endsWith("/*")?i:Pe.includes(i)?i+"/*":"."+i;function Se(){l(23,_=!_)}function qe(){navigator.clipboard.read().then(async i=>{for(let p=0;p<i.length;p++){const k=i[p].types.find(A=>A.startsWith("image/"));if(k){i[p].getType(k).then(async A=>{const x=new File([A],`clipboard.${k.replace("image/","")}`);await H([x])});break}}})}function Fe(){g||P&&(l(2,P.value="",P),P.click())}async function Ee(i){await At(),l(17,X=Math.random().toString(36).substring(2,15)),l(1,m=!0);try{const p=await Q(i,b,X,K??1/0);return S("load",h==="single"?p?.[0]:p),l(1,m=!1),p||[]}catch(p){return S("error",p.message),l(1,m=!1),[]}}async function H(i){if(!i.length)return;let p=i.map(k=>new File([k],k instanceof File?k.name:"file",{type:k.type}));return n&&fe&&(p=p.filter(k=>Ue(k)?!0:(S("error",`Invalid file type: ${k.name}. Only ${a} allowed.`),!1)),p.length===0)?[]:(l(18,Y=await Be(p)),await Ee(Y))}function Ue(i){return a?(Array.isArray(a)?a:[a]).some(k=>{const A=Z(k);if(A.startsWith("."))return i.name.toLowerCase().endsWith(A.toLowerCase());if(A==="*")return!0;if(A.endsWith("/*")){const[x]=A.split("/");return i.type.startsWith(x+"/")}return i.type===A}):!0}async function De(i){const p=i.target;if(p.files)if(d!="blob")await H(Array.from(p.files));else{if(h==="single"){S("load",p.files[0]);return}S("load",p.files)}}async function Me(i){if(l(23,_=!1),!i.dataTransfer?.files)return;const p=Array.from(i.dataTransfer.files).filter(k=>{const A="."+k.name.split(".").pop();return A&&St(L,A,k.type)||(A&&Array.isArray(a)?a.includes(A):A===a)?!0:(S("error",`Invalid file type only ${a} allowed.`),!1)});if(d!="blob")await H(p);else{if(h==="single"){S("load",p[0]);return}S("load",p)}}function Ie(i){F.call(this,t,i)}function Le(i){F.call(this,t,i)}function Te(i){F.call(this,t,i)}function Oe(i){F.call(this,t,i)}function Ve(i){F.call(this,t,i)}function He(i){F.call(this,t,i)}function Ne(i){F.call(this,t,i)}function je(i){ct[i?"unshift":"push"](()=>{P=i,l(2,P)})}return t.$$set=i=>{"filetype"in i&&l(0,a=i.filetype),"dragging"in i&&l(23,_=i.dragging),"boundedheight"in i&&l(3,u=i.boundedheight),"center"in i&&l(4,s=i.center),"flex"in i&&l(5,o=i.flex),"file_count"in i&&l(6,h=i.file_count),"disable_click"in i&&l(7,g=i.disable_click),"root"in i&&l(8,b=i.root),"hidden"in i&&l(9,c=i.hidden),"format"in i&&l(24,d=i.format),"uploading"in i&&l(1,m=i.uploading),"hidden_upload"in i&&l(2,P=i.hidden_upload),"show_progress"in i&&l(10,re=i.show_progress),"max_file_size"in i&&l(25,K=i.max_file_size),"upload"in i&&l(26,Q=i.upload),"stream_handler"in i&&l(11,se=i.stream_handler),"icon_upload"in i&&l(12,ae=i.icon_upload),"height"in i&&l(13,oe=i.height),"aria_label"in i&&l(14,ue=i.aria_label),"$$scope"in i&&l(29,f=i.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&268435457&&(a==null?l(19,L=null):typeof a=="string"?l(19,L=Z(a)):n&&a.includes("file/*")?l(19,L="*"):(l(0,a=a.map(Z)),l(19,L=a.join(", "))))},l(28,n=We()),[a,m,P,u,s,o,h,g,b,c,re,se,ae,oe,ue,qe,Fe,X,Y,L,Se,De,Me,_,d,K,Q,H,n,f,r,Ie,Le,Te,Oe,Ve,He,Ne,je]}class Lt extends ht{constructor(e){super(),pt(this,e,qt,Pt,vt,{filetype:0,dragging:23,boundedheight:3,center:4,flex:5,file_count:6,disable_click:7,root:8,hidden:9,format:24,uploading:1,hidden_upload:2,show_progress:10,max_file_size:25,upload:26,stream_handler:11,icon_upload:12,height:13,aria_label:14,paste_clipboard:15,open_file_upload:16,load_files:27},null,[-1,-1])}get filetype(){return this.$$.ctx[0]}set filetype(e){this.$$set({filetype:e}),y()}get dragging(){return this.$$.ctx[23]}set dragging(e){this.$$set({dragging:e}),y()}get boundedheight(){return this.$$.ctx[3]}set boundedheight(e){this.$$set({boundedheight:e}),y()}get center(){return this.$$.ctx[4]}set center(e){this.$$set({center:e}),y()}get flex(){return this.$$.ctx[5]}set flex(e){this.$$set({flex:e}),y()}get file_count(){return this.$$.ctx[6]}set file_count(e){this.$$set({file_count:e}),y()}get disable_click(){return this.$$.ctx[7]}set disable_click(e){this.$$set({disable_click:e}),y()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),y()}get hidden(){return this.$$.ctx[9]}set hidden(e){this.$$set({hidden:e}),y()}get format(){return this.$$.ctx[24]}set format(e){this.$$set({format:e}),y()}get uploading(){return this.$$.ctx[1]}set uploading(e){this.$$set({uploading:e}),y()}get hidden_upload(){return this.$$.ctx[2]}set hidden_upload(e){this.$$set({hidden_upload:e}),y()}get show_progress(){return this.$$.ctx[10]}set show_progress(e){this.$$set({show_progress:e}),y()}get max_file_size(){return this.$$.ctx[25]}set max_file_size(e){this.$$set({max_file_size:e}),y()}get upload(){return this.$$.ctx[26]}set upload(e){this.$$set({upload:e}),y()}get stream_handler(){return this.$$.ctx[11]}set stream_handler(e){this.$$set({stream_handler:e}),y()}get icon_upload(){return this.$$.ctx[12]}set icon_upload(e){this.$$set({icon_upload:e}),y()}get height(){return this.$$.ctx[13]}set height(e){this.$$set({height:e}),y()}get aria_label(){return this.$$.ctx[14]}set aria_label(e){this.$$set({aria_label:e}),y()}get paste_clipboard(){return this.$$.ctx[15]}get open_file_upload(){return this.$$.ctx[16]}get load_files(){return this.$$.ctx[27]}}export{Ut as I,Dt as U,Lt as a};
//# sourceMappingURL=Upload--7Xp8CSk.js.map
