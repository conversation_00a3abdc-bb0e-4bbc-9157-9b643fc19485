"""
提示词管理器模块
用于管理和获取各种提示词模板
"""

from typing import Dict, Any, Optional

class PromptManager:
    """提示词管理器，用于管理和获取各种提示词模板"""

    def __init__(self):
        """初始化提示词管理器"""
        self.prompts = {
            # 写作智能体提示词
            "writing_agent_outline": """
你是一位专业的中文小说创作者，请为以下小说创作一个详细且结构化的内容大纲：

标题：{title}
类型：{genre}
风格：{style}
场景：{scene}
角色：{characters}

请提供以下内容：
1. 小说的核心主题和中心思想（100-200字）
2. 主要角色的详细设定，包括性格特点、背景故事和成长轨迹（每个角色200-300字）
3. 故事的整体结构，包括开端、发展、高潮和结局（300-500字）
4. 情节发展的关键点和转折点（200-300字）

请确保角色设定具体且一致，情节发展合理且连贯，符合{genre}类型和{style}风格的特点。
""",

            # 章节过渡提示词
            "chapter_transition": """
你是一位专业的中文小说写作家，擅长创作自然流畅的章节过渡。请根据以下信息，创作一个从前一章到下一章的过渡段落：

前一章结尾：
{previous_chapter_ending}

下一章大纲：
{chapter_outline}

要求：
1. 创作一个自然流畅的过渡段落，连接前一章的结尾和下一章的开始
2. 过渡段落应该为第{chapter_num}章（{chapter_title}）的开头
3. 保持情节的连贯性和角色的一致性
4. 过渡段落长度控制在200-300字左右
5. 不要直接重复前一章的内容，而是自然地承接前文并引出新的情节

请直接给出过渡段落，不需要额外的解释。
""",

            # 章节生成提示词
            "chapter_generation": """
你是一位专业的中文小说写作家，请根据以下信息创作一个完整的小说章节：

标题：{title}
类型：{genre}
风格：{style}
场景：{scene}
角色：{characters}

章节信息：
- 章节编号：第{chapter_num}章，共{total_chapters}章
- 章节标题：{chapter_title}

章节大纲：
{chapter_outline}

{previous_chapter_summary}

{transition}

要求：
1. 创作一个完整的章节，字数至少5000字
2. 包含生动的场景描写、自然的对话和丰富的情感表达
3. 确保章节开头与前一章结尾无缝衔接
4. 章节结尾设置悬念引导读者继续阅读
5. 内容要符合小说的风格和基调
6. 包含丰富的感官描写，增强沉浸感

请直接给出章节内容，不需要额外的解释。
""",

            # 润色提示词
            "polishing": """
你是一位专业的中文小说编辑，请对以下小说章节进行润色和完善：

标题：{title}
类型：{genre}
风格：{style}

原始内容：
{content}

润色要求：
1. 提升语言的流畅性和文学性，修正不自然的表达
2. 确保情节的连贯性和合理性
3. 保持角色形象和性格的一致性
4. 增强场景描写的细节和氛围感
5. 优化对话的自然度和个性化
6. 确保章节结尾有足够的悬念
7. 避免出现任何提示词痕迹

请直接给出润色后的完整内容，不需要解释你做了哪些修改。
""",

            # 扩展提示词
            "expansion": """
你是一位专业的中文小说扩写专家。请对以下小说章节进行合理扩展：

原文：
{content}

扩展要求：
1. 保持原有情节不变，只进行内容丰富和细节扩展
2. 重点扩展场景描写、人物对话和心理活动
3. 增加感官描写（视觉、听觉、嗅觉等）增强沉浸感
4. 适当增加环境氛围的描写，使场景更加立体
5. 扩展后的总字数应达到3000字以上，但质量比字数更重要
6. 保持写作风格的一致性
7. 避免重复：不要为了凑字数而生成重复内容，确保每个段落都有独特的信息

请直接给出扩展后的完整内容，不需要解释你的扩展思路。
""",

            # 优化提示词
            "optimization": """
你是一位专业的中文小说优化专家。请对以下小说章节进行全面优化：

原文：
{content}

优化要求：
1. 确保情节的连贯性和合理性，修正任何逻辑漏洞
2. 优化人物对话，使其更加自然、生动，符合角色性格
3. 增强场景描写的细节和氛围感，提升沉浸感
4. 优化章节结尾，确保有足够的悬念引导读者继续阅读
5. 提升语言的文学性和表现力，避免平淡或重复的表达
6. 确保没有任何提示词痕迹或AI自我指代
7. 如果有下一章的信息，确保本章结尾能够自然过渡到下一章

请直接给出优化后的完整内容，不需要解释你的优化思路。
""",

            # 审核提示词
            "review": """
你是一位专业的中文小说审核编辑。请对以下小说章节进行全面审核：

章节内容：
{content}

审核要点：
1. 情节连贯性：检查情节是否连贯，是否有逻辑漏洞或矛盾
2. 角色一致性：检查角色的言行是否符合其设定和性格
3. 语言质量：检查语言是否流畅、生动，是否有不自然的表达
4. 结构平衡：检查章节结构是否合理，是否有明显的节奏问题
5. 悬念设置：检查章节结尾是否有足够的悬念引导读者继续阅读
6. AI痕迹：检查是否有明显的AI生成痕迹或提示词残留

请提供一份简洁的审核报告，指出章节的优点和需要改进的地方。
""",

            # 插图生成提示词
            "illustration": """
你是一位专业的小说插图艺术家。请根据以下场景描述，创作一幅精美的插图：

小说标题：{title}
场景描述：{scene}
风格要求：{style}

请创作一幅能够准确表现场景氛围和情感的插图，符合{style}的艺术风格。
插图应该具有强烈的视觉冲击力，能够吸引读者的注意力。
"""
        }

    def get_prompt(self, prompt_name: str, **kwargs) -> str:
        """
        获取指定名称的提示词，并填充参数

        参数:
            prompt_name: 提示词名称
            **kwargs: 提示词中需要填充的参数

        返回:
            填充参数后的提示词
        """
        prompt_template = self.prompts.get(prompt_name, "")
        if not prompt_template:
            return f"错误: 未找到名为 {prompt_name} 的提示词模板"

        try:
            return prompt_template.format(**kwargs)
        except KeyError as e:
            return f"错误: 提示词模板 {prompt_name} 缺少必要参数: {e}"
        except Exception as e:
            return f"错误: 填充提示词模板 {prompt_name} 时出错: {e}"

    def add_prompt(self, name: str, template: str) -> None:
        """
        添加新的提示词模板

        参数:
            name: 提示词名称
            template: 提示词模板
        """
        self.prompts[name] = template

    def update_prompt(self, name: str, template: str) -> bool:
        """
        更新现有的提示词模板

        参数:
            name: 提示词名称
            template: 新的提示词模板

        返回:
            更新是否成功
        """
        if name in self.prompts:
            self.prompts[name] = template
            return True
        return False

    def get_all_prompt_names(self) -> list:
        """
        获取所有提示词名称

        返回:
            提示词名称列表
        """
        return list(self.prompts.keys())
