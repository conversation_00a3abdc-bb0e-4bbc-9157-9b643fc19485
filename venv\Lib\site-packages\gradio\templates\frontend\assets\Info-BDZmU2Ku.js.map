{"version": 3, "file": "Info-BDZmU2Ku.js", "sources": ["../../../../js/atoms/src/Info.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\texport let info: string;\n\texport let root: string;\n</script>\n\n<div>\n\t<Markdown {root} message={info} sanitize_html={true} />\n</div>\n\n<style>\n\tdiv > :global(.md.prose) {\n\t\tfont-weight: var(--block-info-text-weight);\n\t\tfont-size: var(--block-info-text-size);\n\t\tline-height: var(--line-sm);\n\t}\n\tdiv > :global(.md.prose *) {\n\t\tcolor: var(--block-info-text-color);\n\t}\n\tdiv {\n\t\tmargin-bottom: var(--spacing-md);\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "div", "anchor", "info", "$$props", "root"], "mappings": "8YAO2BA,EAAI,CAAA,gBAAiB,4EADhDC,EAEKC,EAAAC,EAAAC,CAAA,2EADsBJ,EAAI,CAAA,qHALnB,GAAA,CAAA,KAAAK,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD"}