{".class": "MypyFile", "_fullname": "src.agents.novel_manager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ADVANCED_FEATURES_ENABLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "src.agents.novel_manager.ADVANCED_FEATURES_ENABLED", "name": "ADVANCED_FEATURES_ENABLED", "type": "builtins.bool"}}, "AGENT_REGISTRY": {".class": "SymbolTableNode", "cross_ref": "src.core.agent.AGENT_REGISTRY", "kind": "Gdef"}, "AI_MODEL_CONFIG": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.AI_MODEL_CONFIG", "kind": "Gdef"}, "Agent": {".class": "SymbolTableNode", "cross_ref": "src.core.agent.Agent", "kind": "Gdef"}, "AgentRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.agents.novel_manager.AgentRegistry", "name": "AgentRegistry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.AgentRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.agents.novel_manager", "mro": ["src.agents.novel_manager.AgentRegistry", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.AgentRegistry.__init__", "name": "__init__", "type": null}}, "agents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.AgentRegistry.agents", "name": "agents", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_agent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.AgentRegistry.get_agent", "name": "get_agent", "type": null}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "agent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.AgentRegistry.register", "name": "register", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.agents.novel_manager.AgentRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.agents.novel_manager.AgentRegistry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Chapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.agents.novel_manager.Chapter", "name": "Chapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Chapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.agents.novel_manager", "mro": ["src.agents.novel_manager.Chapter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "number", "title", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Chapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "number", "title", "content"], "arg_types": ["src.agents.novel_manager.Chapter", "builtins.int", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Chapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Chapter.content", "name": "content", "type": "builtins.str"}}, "created_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Chapter.created_at", "name": "created_at", "type": "builtins.str"}}, "from_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.agents.novel_manager.Chapter.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "data"], "arg_types": [{".class": "TypeType", "item": "src.agents.novel_manager.Chapter"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dict of Chapter", "ret_type": "src.agents.novel_manager.Chapter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "src.agents.novel_manager.Chapter.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "data"], "arg_types": [{".class": "TypeType", "item": "src.agents.novel_manager.Chapter"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dict of Chapter", "ret_type": "src.agents.novel_manager.Chapter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "illustrations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Chapter.illustrations", "name": "illustrations", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Chapter.number", "name": "number", "type": "builtins.int"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Chapter.status", "name": "status", "type": "builtins.str"}}, "title": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Chapter.title", "name": "title", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Chapter.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.agents.novel_manager.Chapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of Chapter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "updated_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Chapter.updated_at", "name": "updated_at", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.agents.novel_manager.Chapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.agents.novel_manager.Chapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DEFAULT_MODEL_NAMES": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.DEFAULT_MODEL_NAMES", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "docx.api.Document", "kind": "Gdef"}, "Inches": {".class": "SymbolTableNode", "cross_ref": "docx.shared.Inches", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mm": {".class": "SymbolTableNode", "cross_ref": "docx.shared.Mm", "kind": "Gdef"}, "Novel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.agents.novel_manager.Novel", "name": "Novel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Novel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.agents.novel_manager", "mro": ["src.agents.novel_manager.Novel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "title", "author", "genre", "style", "outline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Novel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "title", "author", "genre", "style", "outline"], "arg_types": ["src.agents.novel_manager.Novel", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Novel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_chapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Novel.add_chapter", "name": "add_chapter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chapter"], "arg_types": ["src.agents.novel_manager.Novel", "src.agents.novel_manager.Chapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_chapter of Novel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "author": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.author", "name": "author", "type": "builtins.str"}}, "average_chapter_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.average_chapter_length", "name": "average_chapter_length", "type": "builtins.int"}}, "chapter_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Novel.chapter_files", "name": "chapter_files", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "chapter_outlines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Novel.chapter_outlines", "name": "chapter_outlines", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "chapter_status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Novel.chapter_status", "name": "chapter_status", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "chapter_word_counts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Novel.chapter_word_counts", "name": "chapter_word_counts", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "chapters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Novel.chapters", "name": "chapters", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "characters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.characters", "name": "characters", "type": "builtins.str"}}, "completed_chapters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.completed_chapters", "name": "completed_chapters", "type": "builtins.int"}}, "created_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.created_at", "name": "created_at", "type": "builtins.str"}}, "current_chapter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.current_chapter", "name": "current_chapter", "type": "builtins.int"}}, "estimated_completion_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.estimated_completion_time", "name": "estimated_completion_time", "type": "builtins.str"}}, "from_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "src.agents.novel_manager.Novel.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "data"], "arg_types": [{".class": "TypeType", "item": "src.agents.novel_manager.Novel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dict of Novel", "ret_type": "src.agents.novel_manager.Novel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "src.agents.novel_manager.Novel.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "data"], "arg_types": [{".class": "TypeType", "item": "src.agents.novel_manager.Novel"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dict of Novel", "ret_type": "src.agents.novel_manager.Novel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "generation_attempts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Novel.generation_attempts", "name": "generation_attempts", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "genre": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.genre", "name": "genre", "type": "builtins.str"}}, "get_chapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Novel.get_chapter", "name": "get_chapter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "number"], "arg_types": ["src.agents.novel_manager.Novel", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_chapter of Novel", "ret_type": {".class": "UnionType", "items": ["src.agents.novel_manager.Chapter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_chapter_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Novel.is_chapter_complete", "name": "is_chapter_complete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chapter"], "arg_types": ["src.agents.novel_manager.Novel", "src.agents.novel_manager.Chapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_chapter_complete of Novel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "last_chapter_completed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.last_chapter_completed", "name": "last_chapter_completed", "type": "builtins.int"}}, "last_error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.last_error", "name": "last_error", "type": "builtins.str"}}, "last_writing_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.last_writing_time", "name": "last_writing_time", "type": "builtins.str"}}, "main_plot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.main_plot", "name": "main_plot", "type": "builtins.str"}}, "max_chapter_in_outline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.max_chapter_in_outline", "name": "max_chapter_in_outline", "type": "builtins.int"}}, "min_chapter_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.min_chapter_length", "name": "min_chapter_length", "type": "builtins.int"}}, "notes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.notes", "name": "notes", "type": "builtins.str"}}, "novel_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.novel_dir", "name": "novel_dir", "type": "builtins.str"}}, "outline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.outline", "name": "outline", "type": "builtins.str"}}, "outline_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.outline_file", "name": "outline_file", "type": "builtins.str"}}, "outline_updated_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.outline_updated_at", "name": "outline_updated_at", "type": "builtins.str"}}, "revision_history": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Novel.revision_history", "name": "revision_history", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "save_as_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "output_path", "include_illustrations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Novel.save_as_document", "name": "save_as_document", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "output_path", "include_illustrations"], "arg_types": ["src.agents.novel_manager.Novel", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_as_document of Novel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scene": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.scene", "name": "scene", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.status", "name": "status", "type": "builtins.str"}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.style", "name": "style", "type": "builtins.str"}}, "sub_plots": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Novel.sub_plots", "name": "sub_plots", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.Novel.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "target_audience": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.target_audience", "name": "target_audience", "type": "builtins.str"}}, "title": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.title", "name": "title", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.Novel.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["src.agents.novel_manager.Novel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of Novel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "total_chapters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.total_chapters", "name": "total_chapters", "type": "builtins.int"}}, "total_word_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.total_word_count", "name": "total_word_count", "type": "builtins.int"}}, "updated_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.updated_at", "name": "updated_at", "type": "builtins.str"}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.version", "name": "version", "type": "builtins.str"}}, "writing_progress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.writing_progress", "name": "writing_progress", "type": "builtins.float"}}, "writing_speed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.Novel.writing_speed", "name": "writing_speed", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.agents.novel_manager.Novel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.agents.novel_manager.Novel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NovelManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "src.agents.novel_manager.NovelManager", "name": "NovelManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.NovelManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "src.agents.novel_manager", "mro": ["src.agents.novel_manager.NovelManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.NovelManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "name"], "arg_types": ["src.agents.novel_manager.NovelManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NovelManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "continue_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "novel_dir", "chapters", "progress_callback", "illustration_style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.NovelManager.continue_writing", "name": "continue_writing", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "novel_dir", "chapters", "progress_callback", "illustration_style"], "arg_types": ["src.agents.novel_manager.NovelManager", "builtins.str", "builtins.int", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "continue_writing of NovelManager", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_novel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "title", "author", "genre", "style", "outline", "total_chapters", "output_dir", "scene", "characters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.NovelManager.create_novel", "name": "create_novel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "title", "author", "genre", "style", "outline", "total_chapters", "output_dir", "scene", "characters"], "arg_types": ["src.agents.novel_manager.NovelManager", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_novel of NovelManager", "ret_type": "src.agents.novel_manager.Novel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_chapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "novel", "chapter_number", "output_dir", "illustration_style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.NovelManager.generate_chapter", "name": "generate_chapter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "novel", "chapter_number", "output_dir", "illustration_style"], "arg_types": ["src.agents.novel_manager.NovelManager", "src.agents.novel_manager.Novel", "builtins.int", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_chapter of NovelManager", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_novel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "novel_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.NovelManager.load_novel", "name": "load_novel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "novel_dir"], "arg_types": ["src.agents.novel_manager.NovelManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_novel of NovelManager", "ret_type": {".class": "UnionType", "items": ["src.agents.novel_manager.Novel", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.NovelManager.name", "name": "name", "type": "builtins.str"}}, "novels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "src.agents.novel_manager.NovelManager.novels", "name": "novels", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "prompt_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.NovelManager.prompt_manager", "name": "prompt_manager", "type": "src.core.prompt_manager.PromptManager"}}, "save_novel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "novel", "output_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "src.agents.novel_manager.NovelManager.save_novel", "name": "save_novel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "novel", "output_dir"], "arg_types": ["src.agents.novel_manager.NovelManager", "src.agents.novel_manager.Novel", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_novel of NovelManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "src.agents.novel_manager.NovelManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "src.agents.novel_manager.NovelManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PromptManager": {".class": "SymbolTableNode", "cross_ref": "src.core.prompt_manager.PromptManager", "kind": "Gdef"}, "Pt": {".class": "SymbolTableNode", "cross_ref": "docx.shared.Pt", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WD_ALIGN_PARAGRAPH": {".class": "SymbolTableNode", "cross_ref": "docx.enum.text.WD_ALIGN_PARAGRAPH", "kind": "Gdef"}, "WD_ORIENT": {".class": "SymbolTableNode", "cross_ref": "docx.enum.section.WD_ORIENT", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.novel_manager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.novel_manager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.novel_manager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.novel_manager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.novel_manager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.novel_manager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_segment_to_memory": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.add_segment_to_memory", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "clear_short_term_memory": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.clear_short_term_memory", "kind": "Gdef"}, "consistency_check_consistency": {".class": "SymbolTableNode", "cross_ref": "src.consistency.consistency_checker.check_consistency", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "src.agents.novel_manager.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "enhance_prompt_with_memory": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.enhance_prompt_with_memory", "kind": "Gdef"}, "generate_branch_options": {".class": "SymbolTableNode", "cross_ref": "src.narrative.narrative_controller.generate_branch_options", "kind": "Gdef"}, "generate_next_chapter_outline": {".class": "SymbolTableNode", "cross_ref": "src.narrative.narrative_controller.generate_next_chapter_outline", "kind": "Gdef"}, "get_consistency_checker": {".class": "SymbolTableNode", "cross_ref": "src.consistency.consistency_checker.get_consistency_checker", "kind": "Gdef"}, "get_memory_manager": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.get_memory_manager", "kind": "Gdef"}, "get_narrative_controller": {".class": "SymbolTableNode", "cross_ref": "src.narrative.narrative_controller.get_narrative_controller", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "memory_check_consistency": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.check_consistency", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "prompt_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.agents.novel_manager.prompt_manager", "name": "prompt_manager", "type": "src.core.prompt_manager.PromptManager"}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "save_consistency_log": {".class": "SymbolTableNode", "cross_ref": "src.consistency.consistency_checker.save_consistency_log", "kind": "Gdef"}, "select_optimal_path": {".class": "SymbolTableNode", "cross_ref": "src.narrative.narrative_controller.select_optimal_path", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "update_memory_from_chapter": {".class": "SymbolTableNode", "cross_ref": "src.memory.memory_integration.update_memory_from_chapter", "kind": "Gdef"}}, "path": "E:\\storymc\\src\\agents\\novel_manager.py"}