"""
Chapter Generator Module - Handles the generation of novel chapters
"""

import os
import re
import json
import traceback
from typing import Dict, List, Any, Optional, Tuple, Union, cast
from datetime import datetime

class ChapterGenerator:
    """
    Handles the generation of novel chapters with improved transitions and adherence to outlines
    """
    
    def __init__(self, llm_caller, output_dir: str):
        """
        Initialize the chapter generator
        
        Args:
            llm_caller: Function to call the language model
            output_dir: Directory to save output files
        """
        self.llm_caller = llm_caller
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def extract_previous_chapter_ending(self, previous_content: str) -> str:
        """
        Extract the ending of the previous chapter for better transitions
        
        Args:
            previous_content: Content of the previous chapter
            
        Returns:
            The ending portion of the previous chapter
        """
        if not previous_content:
            return ""
            
        # Try to split by paragraphs first
        paragraphs = previous_content.split('\n\n')
        
        if len(paragraphs) >= 3:
            # Get the last three paragraphs for better context
            previous_ending = '\n\n'.join(paragraphs[-3:])
            print(f"Extracted the last three paragraphs from previous chapter, length: {len(previous_ending)} characters")
        else:
            # If not enough paragraphs, take the last 1000 characters
            previous_ending = previous_content[-1000:] if len(previous_content) > 1000 else previous_content
            print(f"Extracted the last portion of previous chapter, length: {len(previous_ending)} characters")
            
        return previous_ending
        
    def extract_or_generate_chapter_outline(self, 
                                           novel_outline: str, 
                                           chapter_num: int, 
                                           context: Dict[str, Any]) -> str:
        """
        Extract chapter outline from novel outline or generate a new one if missing
        
        Args:
            novel_outline: The complete novel outline
            chapter_num: Current chapter number
            context: Context information for chapter generation
            
        Returns:
            Chapter outline for the current chapter
        """
        if not novel_outline:
            return self._generate_new_chapter_outline(chapter_num, context)
            
        try:
            # Try to extract the current chapter's outline from the novel outline
            chapter_pattern = rf"第{chapter_num}章.*?(?=第{chapter_num+1}章|$)"
            chapter_match = re.search(chapter_pattern, novel_outline, re.DOTALL)
            
            if chapter_match:
                chapter_outline = chapter_match.group(0).strip()
                print(f"Successfully extracted outline for Chapter {chapter_num}")
                return chapter_outline
            else:
                print(f"No outline found for Chapter {chapter_num}, generating a new one")
                return self._generate_new_chapter_outline(chapter_num, context)
                
        except Exception as e:
            print(f"Error extracting chapter outline: {e}")
            return self._generate_new_chapter_outline(chapter_num, context)
    
    def _generate_new_chapter_outline(self, chapter_num: int, context: Dict[str, Any]) -> str:
        """
        Generate a new chapter outline when one is missing
        
        Args:
            chapter_num: Current chapter number
            context: Context information for chapter generation
            
        Returns:
            Newly generated chapter outline
        """
        title = context.get("title", "")
        genre = context.get("genre", "")
        style = context.get("style", "")
        scene = context.get("scene", "")
        characters = context.get("characters", "")
        novel_outline = context.get("novel_outline", "")
        previous_content = context.get("previous_chapter_content", "")
        
        # Create a prompt to generate a new chapter outline
        prompt = f"""
You are a professional Chinese novel outline editor. Please create a detailed outline for Chapter {chapter_num} of the novel "{title}".

Novel information:
- Genre: {genre}
- Style: {style}
- Main setting: {scene}
- Main characters: {characters}

Current novel outline:
{novel_outline}
"""

        # Add previous chapter content if available
        if previous_content:
            # Extract a summary and ending of the previous chapter
            previous_summary = previous_content[:500] + "..." if len(previous_content) > 500 else previous_content
            previous_ending = previous_content[-500:] if len(previous_content) > 500 else previous_content
            
            prompt += f"""
Summary of previous chapter:
{previous_summary}

Ending of previous chapter:
{previous_ending}
"""

        prompt += """
Please create a detailed outline for this chapter that:
1. Naturally continues from the previous chapter
2. Follows the overall style and direction of the novel
3. Incorporates Chinese cultural elements to appeal to Chinese readers
4. Ends with a compelling hook or cliffhanger

Format your response as:
第{chapter_num}章 [Chapter Title]
[Chapter outline content]
"""

        try:
            # Generate the new outline
            new_outline = self.llm_caller(prompt)
            
            # Save the new outline to the novel outline file
            if new_outline and len(new_outline) > 50:
                outline_path = os.path.join(self.output_dir, "novel_outline.txt")
                try:
                    # Append to existing outline file if it exists
                    if os.path.exists(outline_path):
                        with open(outline_path, "a", encoding="utf-8") as f:
                            f.write("\n\n" + new_outline)
                            print(f"Added new outline to novel outline file: {outline_path}")
                except Exception as append_error:
                    print(f"Error appending new outline to file: {append_error}")
                    
            return new_outline
        except Exception as e:
            print(f"Error generating new chapter outline: {e}")
            return f"第{chapter_num}章 (自动生成的大纲)"
    
    def generate_chapter_content(self, chapter_context: Dict[str, Any]) -> str:
        """
        Generate content for a chapter based on context and outline
        
        Args:
            chapter_context: Dictionary containing chapter context information
            
        Returns:
            Generated chapter content
        """
        title = chapter_context.get("title", "")
        genre = chapter_context.get("genre", "")
        style = chapter_context.get("style", "")
        scene = chapter_context.get("scene", "")
        characters = chapter_context.get("characters", "")
        chapter_num = chapter_context.get("chapter_number", 1)
        chapter_outline = chapter_context.get("chapter_outline", "")
        previous_content = chapter_context.get("previous_chapter_content", "")
        previous_ending = chapter_context.get("previous_chapter_ending", "")
        
        # If previous_ending is not provided, extract it
        if not previous_ending and previous_content:
            previous_ending = self.extract_previous_chapter_ending(previous_content)
            
        # Create a comprehensive prompt for chapter generation
        prompt = f"""
You are a professional Chinese novelist. Please write Chapter {chapter_num} for the novel "{title}" based on the following information.

Novel information:
- Genre: {genre}
- Style: {style}
- Main setting: {scene}
- Main characters: {characters}

Chapter outline:
{chapter_outline}
"""

        # Add previous chapter ending if available
        if previous_ending:
            prompt += f"""
Ending of previous chapter (ensure a natural transition):
{previous_ending}
"""

        prompt += """
Writing requirements:
1. Strictly follow the chapter outline
2. Maintain character consistency
3. Chapter length should be 3000-5000 words
4. Include rich scene descriptions, dialogue, and inner thoughts
5. Naturally incorporate Chinese cultural elements, historical background, or traditional values to appeal to Chinese readers
6. Use "* * *" to mark scene transitions
7. End the chapter with a compelling hook or cliffhanger that makes readers eager to continue
8. Do not resolve all conflicts at the end; leave some suspense and unresolved issues
9. Avoid any meta-references or AI self-references
10. Do not introduce major characters or plot twists not mentioned in the outline

Please write the chapter content directly without any explanations or suggestions.
"""

        try:
            # Generate the chapter content
            content = self.llm_caller(prompt)
            
            # Check if the content is too short or contains suspicious phrases
            if not content or len(content) < 1000 or "you can" in content.lower() or "you should" in content.lower():
                print(f"Warning: Generated content may be inadequate, length: {len(content)} characters")
                
                # Try a more direct approach
                direct_prompt = f"""
You are a professional Chinese novelist. Write Chapter {chapter_num} for the novel "{title}".

Basic information:
- Genre: {genre}
- Style: {style}
- Setting: {scene}
- Characters: {characters}

Chapter outline:
{chapter_outline}

Previous chapter ending:
{previous_ending}

Requirements:
1. Write a complete novel chapter of 3000-5000 words
2. Follow the outline strictly
3. Create a natural transition from the previous chapter
4. Include rich Chinese cultural elements
5. End with a compelling cliffhanger
6. Write the actual novel content, not suggestions or instructions

Write the chapter content directly without any explanations.
"""
                content = self.llm_caller(direct_prompt)
                
            return content
        except Exception as e:
            print(f"Error generating chapter content: {e}")
            return ""
    
    def optimize_chapter_content(self, content: str, chapter_num: int, context: Dict[str, Any]) -> str:
        """
        Optimize and polish the chapter content
        
        Args:
            content: Raw chapter content
            chapter_num: Current chapter number
            context: Context information
            
        Returns:
            Optimized chapter content
        """
        if not content or len(content) < 500:
            print("Content too short to optimize")
            return content
            
        # Check if content contains suggestions rather than actual novel content
        suspicious_phrases = [
            "提示词", "建议", "你可以", "你应该", "你需要", "你可能想要",
            "you can", "you should", "you may", "you might", "you could"
        ]
        
        is_prompt_not_content = any(phrase in content.lower() for phrase in suspicious_phrases)
        
        if is_prompt_not_content:
            print("Warning: Content appears to contain suggestions rather than novel content")
            
            # Extract key information and regenerate
            extract_prompt = f"""
Extract the key information from this text, including characters, settings, and plot points:

{content}

Return only the extracted information without explanations.
"""
            extracted_info = self.llm_caller(extract_prompt)
            
            # Generate proper novel content
            generation_prompt = f"""
You are a professional Chinese novelist. Using the following information, write Chapter {chapter_num} as an actual novel chapter, not instructions or suggestions:

{extracted_info}

Requirements:
1. Write 3000-5000 words of actual novel content
2. Include vivid descriptions, natural dialogue, and character development
3. Incorporate Chinese cultural elements
4. End with a compelling hook or cliffhanger
5. Do NOT include any meta-text like "you can" or "you should"

Write the chapter content directly.
"""
            return self.llm_caller(generation_prompt)
        
        # For normal content, enhance it
        optimize_prompt = f"""
You are a professional Chinese novel editor. Please enhance the following chapter:

{content}

Enhancement requirements:
1. Improve the overall writing quality with more vivid descriptions and dialogue
2. Ensure plot coherence and character consistency
3. Make sure the chapter ending has a compelling hook or cliffhanger
4. Do not resolve all conflicts at the end
5. The ending should make readers eager to continue to the next chapter
6. You may add subtle foreshadowing or mysterious elements at the end

Return the enhanced content directly without explanations.
"""
        optimized_content = self.llm_caller(optimize_prompt)
        
        # Check if the optimized content is too short
        if len(optimized_content) < 3000:
            print(f"Warning: Optimized content is too short ({len(optimized_content)} characters), expanding...")
            
            expansion_prompt = f"""
Please expand this novel chapter to at least 5000 words by adding more:
- Detailed scene descriptions
- Character dialogue and interactions
- Inner thoughts and emotions
- Cultural and historical context

Maintain the original plot and character development, just make it richer and more engaging:

{optimized_content}

Return the expanded content directly.
"""
            optimized_content = self.llm_caller(expansion_prompt)
            
        return optimized_content
    
    def save_chapter(self, chapter_num: int, content: str, is_final: bool = False) -> str:
        """
        Save chapter content to a file
        
        Args:
            chapter_num: Chapter number
            content: Chapter content
            is_final: Whether this is the final version
            
        Returns:
            Path to the saved file
        """
        if not content:
            print("No content to save")
            return ""
            
        # Create the file name based on whether it's final or not
        file_name = f"chapter_{chapter_num}_final.txt" if is_final else f"chapter_{chapter_num}_draft.txt"
        file_path = os.path.join(self.output_dir, file_name)
        
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"Saved chapter to {file_path}")
            return file_path
        except Exception as e:
            print(f"Error saving chapter: {e}")
            return ""
    
    def process_chapter(self, chapter_num: int, context: Dict[str, Any], save_intermediates: bool = False) -> Tuple[str, str]:
        """
        Process a complete chapter from outline to final content
        
        Args:
            chapter_num: Chapter number
            context: Context information
            save_intermediates: Whether to save intermediate files
            
        Returns:
            Tuple of (final content, chapter outline)
        """
        print(f"\n=== Processing Chapter {chapter_num} ===\n")
        
        # 1. Extract or generate chapter outline
        novel_outline = context.get("novel_outline", "")
        chapter_outline = self.extract_or_generate_chapter_outline(novel_outline, chapter_num, context)
        context["chapter_outline"] = chapter_outline
        
        # 2. Generate initial content
        print(f"\n--- Generating initial content ---")
        initial_content = self.generate_chapter_content(context)
        if save_intermediates and initial_content:
            self.save_chapter(chapter_num, initial_content, is_final=False)
            
        # 3. Optimize content
        print(f"\n--- Optimizing content ---")
        final_content = self.optimize_chapter_content(initial_content, chapter_num, context)
        
        # 4. Save final content
        if final_content:
            self.save_chapter(chapter_num, final_content, is_final=True)
            
        return final_content, chapter_outline
