{".class": "MypyFile", "_fullname": "src.agents", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AGENT_REGISTRY": {".class": "SymbolTableNode", "cross_ref": "src.core.agent.AGENT_REGISTRY", "kind": "Gdef", "module_public": false}, "AI_MODEL_CONFIG": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.AI_MODEL_CONFIG", "kind": "Gdef", "module_public": false}, "Agent": {".class": "SymbolTableNode", "cross_ref": "src.core.agent.Agent", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef", "module_public": false}, "Chapter": {".class": "SymbolTableNode", "cross_ref": "src.agents.novel_manager.Chapter", "kind": "Gdef", "module_public": false}, "ChapterGenerator": {".class": "SymbolTableNode", "cross_ref": "src.agents.chapter_generator.ChapterGenerator", "kind": "Gdef"}, "ChapterTransition": {".class": "SymbolTableNode", "cross_ref": "src.agents.chapter_transition.ChapterTransition", "kind": "Gdef"}, "DEFAULT_MODEL_NAMES": {".class": "SymbolTableNode", "cross_ref": "src.core.model_config.DEFAULT_MODEL_NAMES", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "ExpansionAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.expansion_agent.ExpansionAgent", "kind": "Gdef"}, "IllustrationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.illustration_agent.IllustrationAgent", "kind": "Gdef"}, "IllustrationGenerator": {".class": "SymbolTableNode", "cross_ref": "src.agents.illustration_generator.IllustrationGenerator", "kind": "Gdef"}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef", "module_public": false}, "ImageDraw": {".class": "SymbolTableNode", "cross_ref": "PIL.ImageDraw", "kind": "Gdef", "module_public": false}, "ImageFont": {".class": "SymbolTableNode", "cross_ref": "PIL.ImageFont", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "MEDICAL_SUPPORT_AVAILABLE": {".class": "SymbolTableNode", "cross_ref": "src.agents.chapter_generator.MEDICAL_SUPPORT_AVAILABLE", "kind": "Gdef", "module_public": false}, "Novel": {".class": "SymbolTableNode", "cross_ref": "src.agents.novel_manager.Novel", "kind": "Gdef", "module_public": false}, "NovelManager": {".class": "SymbolTableNode", "cross_ref": "src.agents.novel_manager.NovelManager", "kind": "Gdef"}, "OptimizationAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.optimization_agent.OptimizationAgent", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "OutlineGeneratorAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.outline_generator.OutlineGeneratorAgent", "kind": "Gdef"}, "PolishingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.polishing_agent.PolishingAgent", "kind": "Gdef"}, "PromptManager": {".class": "SymbolTableNode", "cross_ref": "src.core.prompt_manager.PromptManager", "kind": "Gdef", "module_public": false}, "ReviewAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.review_agent.ReviewAgent", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WritingAgent": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_agent.WritingAgent", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "src.agents.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "src.agents.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "check_chapter_exists": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.check_chapter_exists", "kind": "Gdef", "module_public": false}, "create_writing_pipeline": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.create_writing_pipeline", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "medical_report_generator": {".class": "SymbolTableNode", "cross_ref": "src.core.medical_report_generator.medical_report_generator", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "register_outline_generator_agent": {".class": "SymbolTableNode", "cross_ref": "src.agents.outline_generator.register_outline_generator_agent", "kind": "Gdef"}, "register_writing_agents": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.register_writing_agents", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef", "module_public": false}, "run_pipeline": {".class": "SymbolTableNode", "cross_ref": "src.agents.writing_pipeline.run_pipeline", "kind": "Gdef", "module_public": false}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef", "module_public": false}}, "path": "E:\\storymc\\src\\agents\\__init__.py"}